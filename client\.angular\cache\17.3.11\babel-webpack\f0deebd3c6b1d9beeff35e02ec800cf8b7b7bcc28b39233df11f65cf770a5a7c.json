{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Albanian [sq]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/flakerimi\n//! author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n//! author : <PERSON><PERSON> : https://github.com/oerd\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var sq = moment.defineLocale('sq', {\n    months: 'Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor'.split('_'),\n    monthsShort: 'Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj'.split('_'),\n    weekdays: 'E Diel_E Hënë_E Mart<PERSON>_E <PERSON>ë_E Enjte_E Premte_E Shtunë'.split('_'),\n    weekdaysShort: 'Die_Hën_Mar_Mër_Enj_Pre_Sht'.split('_'),\n    weekdaysMin: 'D_H_Ma_Më_E_P_Sh'.split('_'),\n    weekdaysParseExact: true,\n    meridiemParse: /PD|MD/,\n    isPM: function (input) {\n      return input.charAt(0) === 'M';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      return hours < 12 ? 'PD' : 'MD';\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Sot në] LT',\n      nextDay: '[Nesër në] LT',\n      nextWeek: 'dddd [në] LT',\n      lastDay: '[Dje në] LT',\n      lastWeek: 'dddd [e kaluar në] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'në %s',\n      past: '%s më parë',\n      s: 'disa sekonda',\n      ss: '%d sekonda',\n      m: 'një minutë',\n      mm: '%d minuta',\n      h: 'një orë',\n      hh: '%d orë',\n      d: 'një ditë',\n      dd: '%d ditë',\n      M: 'një muaj',\n      MM: '%d muaj',\n      y: 'një vit',\n      yy: '%d vite'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return sq;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "sq", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "meridiemParse", "isPM", "input", "char<PERSON>t", "meridiem", "hours", "minutes", "isLower", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/moment/locale/sq.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Albanian [sq]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/flakerimi\n//! author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n//! author : <PERSON><PERSON> : https://github.com/oerd\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var sq = moment.defineLocale('sq', {\n        months: 'Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj'.split('_'),\n        weekdays: 'E Diel_E Hënë_E Martë_E <PERSON>ë_E Enjte_E Premte_E Shtunë'.split(\n            '_'\n        ),\n        weekdaysShort: 'Die_Hën_Mar_Mër_Enj_Pre_Sht'.split('_'),\n        weekdaysMin: 'D_H_Ma_Më_E_P_Sh'.split('_'),\n        weekdaysParseExact: true,\n        meridiemParse: /PD|MD/,\n        isPM: function (input) {\n            return input.charAt(0) === 'M';\n        },\n        meridiem: function (hours, minutes, isLower) {\n            return hours < 12 ? 'PD' : 'MD';\n        },\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Sot në] LT',\n            nextDay: '[Nesër në] LT',\n            nextWeek: 'dddd [në] LT',\n            lastDay: '[Dje në] LT',\n            lastWeek: 'dddd [e kaluar në] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'në %s',\n            past: '%s më parë',\n            s: 'disa sekonda',\n            ss: '%d sekonda',\n            m: 'një minutë',\n            mm: '%d minuta',\n            h: 'një orë',\n            hh: '%d orë',\n            d: 'një ditë',\n            dd: '%d ditë',\n            M: 'një muaj',\n            MM: '%d muaj',\n            y: 'një vit',\n            yy: '%d vite',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return sq;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,+EAA+E,CAACC,KAAK,CACzF,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,2DAA2D,CAACF,KAAK,CACvE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,kBAAkB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC1CK,kBAAkB,EAAE,IAAI;IACxBC,aAAa,EAAE,OAAO;IACtBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IAClC,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,OAAOF,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IACnC,CAAC;IACDG,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,YAAY;MAClBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOlD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}