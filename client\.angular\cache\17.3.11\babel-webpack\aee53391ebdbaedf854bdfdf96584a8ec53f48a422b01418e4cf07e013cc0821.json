{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../supplier.service\";\nimport * as i3 from \"./supplier-text.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/ripple\";\nfunction SupplierTextComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SupplierTextComponent_ng_template_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 15);\n    i0.ɵɵelement(5, \"i\", 16);\n    i0.ɵɵelementStart(6, \"input\", 17, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SupplierTextComponent_ng_template_4_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.globalSearchTerm, $event) || (ctx_r1.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function SupplierTextComponent_ng_template_4_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const dt1_r3 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(ctx_r1.onGlobalFilter(dt1_r3, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi pi-fw \", ctx_r1.isExpanded ? \"pi-minus\" : \"pi-plus\", \"\");\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.isExpanded ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.globalSearchTerm);\n  }\n}\nfunction SupplierTextComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 18);\n    i0.ɵɵelementStart(2, \"th\", 19);\n    i0.ɵɵtext(3, \" Text ID \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 21);\n    i0.ɵɵtext(6, \" Text \");\n    i0.ɵɵelement(7, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 23);\n    i0.ɵɵtext(9, \" Supplier ID \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 24);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SupplierTextComponent_ng_template_6_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const s_text_r5 = ctx_r3.$implicit;\n    const expanded_r6 = ctx_r3.expanded;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", s_text_r5)(\"icon\", expanded_r6 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (s_text_r5 == null ? null : s_text_r5.long_text_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (s_text_r5 == null ? null : s_text_r5.long_text) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (s_text_r5 == null ? null : s_text_r5.supplier_id) || \"-\", \" \");\n  }\n}\nfunction SupplierTextComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SupplierTextComponent_ng_template_6_tr_0_Template, 9, 5, \"tr\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.supplier_text == null ? null : ctx_r1.supplier_text.length) > 0);\n  }\n}\nfunction SupplierTextComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 18);\n    i0.ɵɵelementStart(2, \"td\", 27)(3, \"div\", 28)(4, \"div\", 29)(5, \"span\", 30);\n    i0.ɵɵtext(6, \"Text ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 29)(10, \"span\", 30);\n    i0.ɵɵtext(11, \"Long Text\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"span\", 30);\n    i0.ɵɵtext(16, \"Supplier ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 31);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const s_text_r7 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", (s_text_r7 == null ? null : s_text_r7.long_text_id) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (s_text_r7 == null ? null : s_text_r7.long_text) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (s_text_r7 == null ? null : s_text_r7.supplier_id) || \"-\", \" \");\n  }\n}\nfunction SupplierTextComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32);\n    i0.ɵɵtext(2, \" Supplier Text details are not available for this record. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SupplierTextComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \"Loading text data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SupplierTextComponent {\n  constructor(route, supplierservice, suppliertextService) {\n    this.route = route;\n    this.supplierservice = supplierservice;\n    this.suppliertextService = suppliertextService;\n    this.unsubscribe$ = new Subject();\n    this.supplier_text = null;\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.globalSearchTerm = '';\n    this.totalRecords = 0;\n    this.loading = true;\n    this.id = '';\n  }\n  ngOnInit() {\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.supplierservice.supplier.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.supplier_text = data?.texts;\n    });\n  }\n  expandAll() {\n    if (!this.isExpanded) {\n      this.supplier_text.forEach(text => text?.id ? this.expandedRows[text.id] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n    this.isExpanded = !this.isExpanded;\n  }\n  loadSupplierText(event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      _this.suppliertextService.getSupplierText(_this.id, page, pageSize, sortField, sortOrder, _this.globalSearchTerm).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: response => {\n          _this.supplier_text = response?.data || [];\n          _this.totalRecords = response?.meta?.pagination.total;\n          _this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching Supplier Text', error);\n          _this.loading = false;\n        }\n      });\n    })();\n  }\n  onGlobalFilter(table, event) {\n    this.loadSupplierText({\n      first: 0,\n      rows: 10\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SupplierTextComponent_Factory(t) {\n      return new (t || SupplierTextComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.SupplierService), i0.ɵɵdirectiveInject(i3.SupplierTextService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierTextComponent,\n      selectors: [[\"app-supplier-text\"]],\n      decls: 10,\n      vars: 3,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"expandedRowKeys\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"gap-2\", \"justify-content-between\"], [\"pButton\", \"\", 3, \"click\", \"icon\", \"label\"], [1, \"flex\", \"table-header\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Supplier Text\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"long_text_id\"], [\"field\", \"long_text_id\"], [\"pSortableColumn\", \"long_text\"], [\"field\", \"long_text\"], [\"pSortableColumn\", \"supplier_id\"], [\"field\", \"supplier_id\"], [4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pRowToggler\", \"icon\"], [\"colspan\", \"3\"], [1, \"grid\", \"mx-0\", \"border-1\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"colspan\", \"6\"], [\"colspan\", \"8\"]],\n      template: function SupplierTextComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"p-table\", 4, 0);\n          i0.ɵɵtemplate(4, SupplierTextComponent_ng_template_4_Template, 8, 4, \"ng-template\", 5)(5, SupplierTextComponent_ng_template_5_Template, 11, 0, \"ng-template\", 6)(6, SupplierTextComponent_ng_template_6_Template, 1, 1, \"ng-template\", 7)(7, SupplierTextComponent_ng_template_7_Template, 19, 3, \"ng-template\", 8)(8, SupplierTextComponent_ng_template_8_Template, 3, 0, \"ng-template\", 9)(9, SupplierTextComponent_ng_template_9_Template, 3, 0, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.supplier_text)(\"rows\", 10)(\"expandedRowKeys\", ctx.expandedRows);\n        }\n      },\n      dependencies: [i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.Table, i7.PrimeTemplate, i6.SortableColumn, i6.RowToggler, i6.SortIcon, i8.ButtonDirective, i9.InputText, i10.Ripple],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "SupplierTextComponent_ng_template_4_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "expandAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵtwoWayListener", "SupplierTextComponent_ng_template_4_Template_input_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "globalSearchTerm", "SupplierTextComponent_ng_template_4_Template_input_input_6_listener", "dt1_r3", "ɵɵreference", "onGlobalFilter", "ɵɵadvance", "ɵɵpropertyInterpolate1", "isExpanded", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "ɵɵtext", "ɵɵproperty", "s_text_r5", "expanded_r6", "ɵɵtextInterpolate1", "long_text_id", "long_text", "supplier_id", "ɵɵtemplate", "SupplierTextComponent_ng_template_6_tr_0_Template", "supplier_text", "length", "s_text_r7", "SupplierTextComponent", "constructor", "route", "supplierservice", "suppliertextService", "unsubscribe$", "expandedRows", "totalRecords", "loading", "id", "ngOnInit", "parent", "snapshot", "paramMap", "get", "supplier", "pipe", "subscribe", "data", "texts", "for<PERSON>ach", "text", "loadSupplierText", "event", "_this", "_asyncToGenerator", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getSupplierText", "next", "response", "meta", "pagination", "total", "error", "console", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "SupplierService", "i3", "SupplierTextService", "selectors", "decls", "vars", "consts", "template", "SupplierTextComponent_Template", "rf", "ctx", "SupplierTextComponent_ng_template_4_Template", "SupplierTextComponent_ng_template_5_Template", "SupplierTextComponent_ng_template_6_Template", "SupplierTextComponent_ng_template_7_Template", "SupplierTextComponent_ng_template_8_Template", "SupplierTextComponent_ng_template_9_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\supplier\\supplier-details\\supplier-text\\supplier-text.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\supplier\\supplier-details\\supplier-text\\supplier-text.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Table } from 'primeng/table';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { SupplierService } from '../../supplier.service';\r\nimport { SupplierTextService } from './supplier-text.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-supplier-text',\r\n  templateUrl: './supplier-text.component.html',\r\n  styleUrl: './supplier-text.component.scss',\r\n})\r\nexport class SupplierTextComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public supplier_text: any = null;\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public globalSearchTerm: string = '';\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public id: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private supplierservice: SupplierService,\r\n    private suppliertextService: SupplierTextService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.supplierservice.supplier\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.supplier_text = data?.texts;\r\n      });\r\n  }\r\n\r\n  expandAll() {\r\n    if (!this.isExpanded) {\r\n      this.supplier_text.forEach((text: any) =>\r\n        text?.id ? (this.expandedRows[text.id] = true) : ''\r\n      );\r\n    } else {\r\n      this.expandedRows = {};\r\n    }\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  async loadSupplierText(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.suppliertextService\r\n      .getSupplierText(\r\n        this.id,\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.supplier_text = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching Supplier Text', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadSupplierText({ first: 0, rows: 10 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12\">\r\n    <div class=\"card\">\r\n        <p-table #dt1 [value]=\"supplier_text\" dataKey=\"id\" [rows]=\"10\" [expandedRowKeys]=\"expandedRows\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"caption\">\r\n                <div class=\"flex justify-content-between flex-column sm:flex-row gap-2\">\r\n                    <div class=\"flex flex-row gap-2 justify-content-between\">\r\n                        <button pButton icon=\"pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}\"\r\n                            label=\"{{ isExpanded ? 'Collapse All' : 'Expand All' }}\" (click)=\"expandAll()\"></button>\r\n                        <div class=\"flex table-header\"></div>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                            (input)=\"onGlobalFilter(dt1, $event)\" placeholder=\"Search Supplier Text\" class=\"w-full\" />\r\n                    </span>\r\n                </div>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"width: 3rem\"></th>\r\n                    <th pSortableColumn=\"long_text_id\">\r\n                        Text ID <p-sortIcon field=\"long_text_id\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"long_text\">\r\n                        Text <p-sortIcon field=\"long_text\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"supplier_id\">\r\n                        Supplier ID <p-sortIcon field=\"supplier_id\"></p-sortIcon>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-s_text let-expanded=\"expanded\">\r\n                <tr *ngIf=\"supplier_text?.length > 0\">\r\n                    <td>\r\n                        <button type=\"button\" pButton pRipple [pRowToggler]=\"s_text\"\r\n                            class=\"p-button-text p-button-rounded p-button-plain\"\r\n                            [icon]=\"expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\"></button>\r\n                    </td>\r\n                    <td>\r\n                        {{ s_text?.long_text_id || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ s_text?.long_text || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ s_text?.supplier_id || \"-\" }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"rowexpansion\" let-s_text>\r\n                <tr>\r\n                    <td style=\"width: 3rem\"></td>\r\n                    <td colspan=\"3\">\r\n                        <div class=\"grid mx-0 border-1\">\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Text ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ s_text?.long_text_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Long Text</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ s_text?.long_text || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Supplier ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ s_text?.supplier_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">\r\n                        Supplier Text details are not available for this record.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading text data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICKjBC,EAFR,CAAAC,cAAA,cAAwE,cACX,iBAE8B;IAAtBD,EAAA,CAAAE,UAAA,mBAAAC,qEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAS;IAC5FV,EAAA,CAAAW,SAAA,cAAqC;IACzCX,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAW,SAAA,YAA4B;IAC5BX,EAAA,CAAAC,cAAA,mBAC8F;IADxDD,EAAA,CAAAY,gBAAA,2BAAAC,4EAAAC,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAe,kBAAA,CAAAT,MAAA,CAAAU,gBAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,gBAAA,GAAAF,MAAA;MAAA,OAAAd,EAAA,CAAAQ,WAAA,CAAAM,MAAA;IAAA,EAA8B;IAChEd,EAAA,CAAAE,UAAA,mBAAAe,oEAAAH,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,MAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,cAAA,CAAAF,MAAA,EAAAJ,MAAA,CAA2B;IAAA,EAAC;IAEjDd,EAHQ,CAAAU,YAAA,EAC8F,EAC3F,EACL;;;;IATkBV,EAAA,CAAAqB,SAAA,GAAyD;IAAzDrB,EAAA,CAAAsB,sBAAA,sBAAAhB,MAAA,CAAAiB,UAAA,8BAAyD;IACrEvB,EAAA,CAAAwB,qBAAA,UAAAlB,MAAA,CAAAiB,UAAA,iCAAwD;IAKtBvB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAyB,gBAAA,YAAAnB,MAAA,CAAAU,gBAAA,CAA8B;;;;;IAM5EhB,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAC7BX,EAAA,CAAAC,cAAA,aAAmC;IAC/BD,EAAA,CAAA0B,MAAA,gBAAQ;IAAA1B,EAAA,CAAAW,SAAA,qBAA8C;IAC1DX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAgC;IAC5BD,EAAA,CAAA0B,MAAA,aAAK;IAAA1B,EAAA,CAAAW,SAAA,qBAA2C;IACpDX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAA0B,MAAA,oBAAY;IAAA1B,EAAA,CAAAW,SAAA,sBAA6C;IAEjEX,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAsC,SAC9B;IACAD,EAAA,CAAAW,SAAA,iBAE8E;IAClFX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IAAA1B,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IAAA1B,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IACJ1B,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;;IAbyCV,EAAA,CAAAqB,SAAA,GAAsB;IAExDrB,EAFkC,CAAA2B,UAAA,gBAAAC,SAAA,CAAsB,SAAAC,WAAA,gDAEQ;IAGpE7B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAF,SAAA,kBAAAA,SAAA,CAAAG,YAAA,cACJ;IAEI/B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAF,SAAA,kBAAAA,SAAA,CAAAI,SAAA,cACJ;IAEIhC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAF,SAAA,kBAAAA,SAAA,CAAAK,WAAA,cACJ;;;;;IAdJjC,EAAA,CAAAkC,UAAA,IAAAC,iDAAA,iBAAsC;;;;IAAjCnC,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA8B,aAAA,kBAAA9B,MAAA,CAAA8B,aAAA,CAAAC,MAAA,MAA+B;;;;;IAkBpCrC,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAIjBX,EAHZ,CAAAC,cAAA,aAAgB,cACoB,cACC,eAC+B;IAAAD,EAAA,CAAA0B,MAAA,cAAO;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACtEV,EAAA,CAAAC,cAAA,eAA8C;IAC1CD,EAAA,CAAA0B,MAAA,GACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,cAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,iBAAS;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACxEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,mBAAW;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IAC1EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IAIhB1B,EAJgB,CAAAU,YAAA,EAAO,EACL,EACJ,EACL,EACJ;;;;IAjBeV,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,SAAA,kBAAAA,SAAA,CAAAP,YAAA,cACJ;IAKI/B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,SAAA,kBAAAA,SAAA,CAAAN,SAAA,cACJ;IAKIhC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,SAAA,kBAAAA,SAAA,CAAAL,WAAA,cACJ;;;;;IAQZjC,EADJ,CAAAC,cAAA,SAAI,aACgB;IACZD,EAAA,CAAA0B,MAAA,iEACJ;IACJ1B,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAA0B,MAAA,wCAAiC;IACrD1B,EADqD,CAAAU,YAAA,EAAK,EACrD;;;ADvErB,OAAM,MAAO6B,qBAAqB;EAUhCC,YACUC,KAAqB,EACrBC,eAAgC,EAChCC,mBAAwC;IAFxC,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAZrB,KAAAC,YAAY,GAAG,IAAI9C,OAAO,EAAQ;IACnC,KAAAsC,aAAa,GAAQ,IAAI;IACzB,KAAAb,UAAU,GAAY,KAAK;IAC3B,KAAAsB,YAAY,GAAiB,EAAE;IAC/B,KAAA7B,gBAAgB,GAAW,EAAE;IAC7B,KAAA8B,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,EAAE,GAAW,EAAE;EAMnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,EAAE,GAAG,IAAI,CAACP,KAAK,CAACS,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC9D,IAAI,CAACX,eAAe,CAACY,QAAQ,CAC1BC,IAAI,CAACxD,SAAS,CAAC,IAAI,CAAC6C,YAAY,CAAC,CAAC,CAClCY,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACrB,aAAa,GAAGqB,IAAI,EAAEC,KAAK;IAClC,CAAC,CAAC;EACN;EAEAjD,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACc,UAAU,EAAE;MACpB,IAAI,CAACa,aAAa,CAACuB,OAAO,CAAEC,IAAS,IACnCA,IAAI,EAAEZ,EAAE,GAAI,IAAI,CAACH,YAAY,CAACe,IAAI,CAACZ,EAAE,CAAC,GAAG,IAAI,GAAI,EAAE,CACpD;IACH,CAAC,MAAM;MACL,IAAI,CAACH,YAAY,GAAG,EAAE;IACxB;IACA,IAAI,CAACtB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEMsC,gBAAgBA,CAACC,KAAU;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC/BD,KAAI,CAAChB,OAAO,GAAG,IAAI;MACnB,MAAMkB,IAAI,GAAGH,KAAK,CAACI,KAAK,GAAGJ,KAAK,CAACK,IAAI,GAAG,CAAC;MACzC,MAAMC,QAAQ,GAAGN,KAAK,CAACK,IAAI;MAC3B,MAAME,SAAS,GAAGP,KAAK,CAACO,SAAS;MACjC,MAAMC,SAAS,GAAGR,KAAK,CAACQ,SAAS;MAEjCP,KAAI,CAACpB,mBAAmB,CACrB4B,eAAe,CACdR,KAAI,CAACf,EAAE,EACPiB,IAAI,EACJG,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTP,KAAI,CAAC/C,gBAAgB,CACtB,CACAuC,IAAI,CAACxD,SAAS,CAACgE,KAAI,CAACnB,YAAY,CAAC,CAAC,CAClCY,SAAS,CAAC;QACTgB,IAAI,EAAGC,QAAa,IAAI;UACtBV,KAAI,CAAC3B,aAAa,GAAGqC,QAAQ,EAAEhB,IAAI,IAAI,EAAE;UACzCM,KAAI,CAACjB,YAAY,GAAG2B,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;UACpDb,KAAI,CAAChB,OAAO,GAAG,KAAK;QACtB,CAAC;QACD8B,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpDd,KAAI,CAAChB,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;IAAC;EACP;EAEA3B,cAAcA,CAAC2D,KAAY,EAAEjB,KAAY;IACvC,IAAI,CAACD,gBAAgB,CAAC;MAAEK,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEAa,WAAWA,CAAA;IACT,IAAI,CAACpC,YAAY,CAAC4B,IAAI,EAAE;IACxB,IAAI,CAAC5B,YAAY,CAACqC,QAAQ,EAAE;EAC9B;;;uBAzEW1C,qBAAqB,EAAAvC,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtF,EAAA,CAAAkF,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBjD,qBAAqB;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd1B/F,EAFR,CAAAC,cAAA,aAAoB,aACE,oBAEgB;UAiF1BD,EAhFA,CAAAkC,UAAA,IAAA+D,4CAAA,yBAAiC,IAAAC,4CAAA,0BAcD,IAAAC,4CAAA,yBAciC,IAAAC,4CAAA,0BAkBhB,IAAAC,4CAAA,yBA2BX,IAAAC,4CAAA,0BAOD;UAOjDtG,EAFQ,CAAAU,YAAA,EAAU,EACR,EACJ;;;UAzFgBV,EAAA,CAAAqB,SAAA,GAAuB;UAA0BrB,EAAjD,CAAA2B,UAAA,UAAAqE,GAAA,CAAA5D,aAAA,CAAuB,YAAyB,oBAAA4D,GAAA,CAAAnD,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}