{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport { StoreComponent } from './store.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StoreComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      data: {\n        breadcrumb: 'Home'\n      },\n      loadChildren: () => import('./home/<USER>').then(m => m.HomeModule)\n    }, {\n      path: 'vendor-account',\n      data: {\n        breadcrumb: 'Vendor Account'\n      },\n      loadChildren: () => import('./vendor-account/vendor-account.module').then(m => m.VendorAccountModule)\n    }, {\n      path: 'invoice',\n      data: {\n        breadcrumb: 'Invoices'\n      },\n      loadChildren: () => import('./invoice/invoice.module').then(m => m.InvoiceModule)\n    }, {\n      path: 'payment-history',\n      data: {\n        breadcrumb: 'Payment History'\n      },\n      loadChildren: () => import('./payment-history/payment-history.module').then(m => m.PaymentHistoryModule)\n    }, {\n      path: 'payment-details',\n      data: {\n        breadcrumb: 'Payment Details'\n      },\n      loadChildren: () => import('./payment-details/payment-details.module').then(m => m.PaymentDetailsModule)\n    }, {\n      path: 'resource-center',\n      data: {\n        breadcrumb: 'Resource Center'\n      },\n      loadChildren: () => import('./resource-center/resource-center.module').then(m => m.ResourceCenterModule)\n    }, {\n      path: 'profile',\n      data: {\n        breadcrumb: 'Profile'\n      },\n      loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class StoreRoutingModule {\n  static {\n    this.ɵfac = function StoreRoutingModule_Factory(t) {\n      return new (t || StoreRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StoreRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StoreRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppLayoutComponent", "StoreComponent", "routes", "path", "component", "children", "data", "breadcrumb", "loadChildren", "then", "m", "HomeModule", "VendorAccountModule", "InvoiceModule", "PaymentHistoryModule", "PaymentDetailsModule", "ResourceCenterModule", "ProfileModule", "redirectTo", "StoreRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\store-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\nimport { StoreComponent } from './store.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: StoreComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            data: { breadcrumb: 'Home' },\r\n            loadChildren: () =>\r\n              import('./home/<USER>').then((m) => m.HomeModule),\r\n          },\r\n          {\r\n            path: 'vendor-account',\r\n            data: { breadcrumb: 'Vendor Account' },\r\n            loadChildren: () =>\r\n              import('./vendor-account/vendor-account.module').then(\r\n                (m) => m.VendorAccountModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'invoice',\r\n            data: { breadcrumb: 'Invoices' },\r\n            loadChildren: () =>\r\n              import('./invoice/invoice.module').then(\r\n                (m) => m.InvoiceModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'payment-history',\r\n            data: { breadcrumb: 'Payment History' },\r\n            loadChildren: () =>\r\n              import('./payment-history/payment-history.module').then(\r\n                (m) => m.PaymentHistoryModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'payment-details',\r\n            data: { breadcrumb: 'Payment Details' },\r\n            loadChildren: () =>\r\n              import('./payment-details/payment-details.module').then(\r\n                (m) => m.PaymentDetailsModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'resource-center',\r\n            data: { breadcrumb: 'Resource Center' },\r\n            loadChildren: () =>\r\n              import('./resource-center/resource-center.module').then(\r\n                (m) => m.ResourceCenterModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'profile',\r\n            data: { breadcrumb: 'Profile' },\r\n            loadChildren: () =>\r\n              import('./profile/profile.module').then(\r\n                (m) => m.ProfileModule\r\n              ),\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class StoreRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,cAAc,QAAQ,mBAAmB;;;AAElD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,cAAc;EACzBI,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEJ,kBAAkB;IAC7BK,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAC5BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU;KACxD,EACD;MACER,IAAI,EAAE,gBAAgB;MACtBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAgB,CAAE;MACtCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACE,mBAAmB;KAEjC,EACD;MACET,IAAI,EAAE,SAAS;MACfG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CACpCC,CAAC,IAAKA,CAAC,CAACG,aAAa;KAE3B,EACD;MACEV,IAAI,EAAE,iBAAiB;MACvBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAiB,CAAE;MACvCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACI,oBAAoB;KAElC,EACD;MACEX,IAAI,EAAE,iBAAiB;MACvBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAiB,CAAE;MACvCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACK,oBAAoB;KAElC,EACD;MACEZ,IAAI,EAAE,iBAAiB;MACvBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAiB,CAAE;MACvCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACM,oBAAoB;KAElC,EACD;MACEb,IAAI,EAAE,SAAS;MACfG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAC/BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CACpCC,CAAC,IAAKA,CAAC,CAACO,aAAa;KAE3B;GAEJ,EACD;IAAEd,IAAI,EAAE,IAAI;IAAEe,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBpB,YAAY,CAACqB,QAAQ,CAAClB,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXoB,kBAAkB;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAFnBxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}