{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../vendor.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/toast\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/listbox\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/inputtext\";\nfunction VendorEmailComponent_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 19);\n    i0.ɵɵtext(3, \"No records found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction VendorEmailComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function VendorEmailComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template_button_click_3_listener() {\n      const email_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.removeEmail(email_r5));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const email_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(email_r5);\n  }\n}\nfunction VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 21)(2, \"p-listbox\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template_p_listbox_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedEmail, $event) || (ctx_r1.selectedEmail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(3, VendorEmailComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template, 4, 1, \"ng-template\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template_button_click_4_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.updateSettings(item_r6));\n    });\n    i0.ɵɵtext(5, \" Submit \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.editVendorAdmin.vendor_admin_user_emails);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedEmail);\n  }\n}\nfunction VendorEmailComponent_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template, 6, 2, \"tr\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.Emails);\n  }\n}\nfunction VendorEmailComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"table\", 14)(3, \"thead\", 15)(4, \"tr\")(5, \"th\", 16);\n    i0.ɵɵtext(6, \" Vendor Admin Email \");\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function VendorEmailComponent_ng_container_5_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openDialog());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"tbody\", 18);\n    i0.ɵɵtemplate(9, VendorEmailComponent_ng_container_5_ng_container_9_Template, 4, 0, \"ng-container\", 4)(10, VendorEmailComponent_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.Emails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.Emails.length);\n  }\n}\nfunction VendorEmailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class VendorEmailComponent {\n  constructor(vendorservice, messageservice, route) {\n    this.vendorservice = vendorservice;\n    this.messageservice = messageservice;\n    this.route = route;\n    this.unsubscribe$ = new Subject();\n    this.settingType = '';\n    this.settingTitle = '';\n    this.selectedEmail = '';\n    this.newEmail = '';\n    this.loading = false;\n    this.Emails = [];\n    this.moduleurl = 'settings';\n    this.editVendorAdmin = {\n      vendor_admin_user_emails: ''\n    };\n    this.displayDialog = false;\n  }\n  ngOnInit() {\n    const routeData = this.route.snapshot.data;\n    this.settingType = routeData['type'];\n    this.settingTitle = routeData['title'];\n    this.vendorservice.vendor.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.getSettingData();\n    });\n  }\n  openDialog() {\n    this.displayDialog = true;\n  }\n  addEmail() {\n    if (this.newEmail.trim()) {\n      if (!Array.isArray(this.editVendorAdmin.vendor_admin_user_emails)) {\n        this.editVendorAdmin.vendor_admin_user_emails = [];\n      }\n      this.editVendorAdmin.vendor_admin_user_emails = [...this.editVendorAdmin.vendor_admin_user_emails, this.newEmail.trim()];\n      this.newEmail = '';\n      this.displayDialog = false;\n      this.messageservice.add({\n        severity: 'success',\n        summary: 'Added',\n        detail: 'Email added successfully'\n      });\n    }\n  }\n  removeEmail(email) {\n    this.editVendorAdmin.vendor_admin_user_emails = this.editVendorAdmin.vendor_admin_user_emails.filter(e => e !== email);\n    this.messageservice.add({\n      severity: 'warn',\n      summary: 'Removed',\n      detail: 'Email Removed successfully! '\n    });\n  }\n  getSettingData() {\n    this.loading = true;\n    this.vendorservice.get(this.settingType, this.moduleurl).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: value => {\n        this.loading = false;\n        if (value.data?.length) {\n          for (let i = 0; i < value.data.length; i++) {\n            const element = value.data[i];\n            element.vendor_admin_user_emails = Array.isArray(element.vendor_admin_user_emails) ? element.vendor_admin_user_emails : [];\n            this.editVendorAdmin.vendor_admin_user_emails = element.vendor_admin_user_emails;\n          }\n          this.Emails = value.data;\n        } else {\n          this.Emails = [];\n        }\n      },\n      error: () => {\n        this.loading = false;\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  updateSettings(item) {\n    const obj = {\n      ...this.editVendorAdmin\n    };\n    this.vendorservice.update(obj, item.documentId, this.moduleurl).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        item.editing = false;\n        item.vendor_admin_user_emails = this.editVendorAdmin.vendor_admin_user_emails;\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Updated Successfully!'\n        });\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function VendorEmailComponent_Factory(t) {\n      return new (t || VendorEmailComponent)(i0.ɵɵdirectiveInject(i1.VendorService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VendorEmailComponent,\n      selectors: [[\"app-vendor-email\"]],\n      decls: 16,\n      vars: 8,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"card\"], [1, \"grid\", \"mx-0\"], [1, \"p-2\", \"fw-bold\"], [4, \"ngIf\"], [\"header\", \"Add New Email\", \"position\", \"center\", 1, \"add-email-dialog\", 3, \"visibleChange\", \"visible\", \"modal\", \"closable\"], [1, \"p-fluid\", \"mt-3\"], [\"for\", \"newItem\"], [1, \"p-inputgroup\", \"mt-2\"], [\"id\", \"newItem\", \"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Enter new email...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"mt-3\"], [\"pButton\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", 1, \"p-button-secondary\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"label\", \"Add\", \"icon\", \"pi pi-check\", 1, \"p-button-success\", 3, \"click\"], [1, \"table-responsive\"], [1, \"p-datatable\"], [1, \"p-datatable-thead\"], [1, \"d-flex\", \"align-items-center\"], [\"pButton\", \"\", \"icon\", \"pi pi-plus\", 1, \"p-button-primary\", \"add-button\", 3, \"click\"], [1, \"p-datatable-tbody\"], [\"colspan\", \"3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"p-datatable-row\"], [1, \"email-listbox\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"item\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"mt-3\", \"submit-button\", 3, \"click\"], [1, \"listbox-item\"], [\"pButton\", \"\", \"icon\", \"pi pi-trash\", 1, \"p-button-danger\", \"p-button-sm\", \"delete-button\", 3, \"click\"]],\n      template: function VendorEmailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, VendorEmailComponent_ng_container_5_Template, 11, 2, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, VendorEmailComponent_div_6_Template, 2, 0, \"div\", 4);\n          i0.ɵɵelementStart(7, \"p-dialog\", 5);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function VendorEmailComponent_Template_p_dialog_visibleChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.displayDialog, $event) || (ctx.displayDialog = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"label\", 7);\n          i0.ɵɵtext(10, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function VendorEmailComponent_Template_input_ngModelChange_12_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newEmail, $event) || (ctx.newEmail = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function VendorEmailComponent_Template_button_click_14_listener() {\n            return ctx.displayDialog = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function VendorEmailComponent_Template_button_click_15_listener() {\n            return ctx.addEmail();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.settingTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.displayDialog);\n          i0.ɵɵproperty(\"modal\", true)(\"closable\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newEmail);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.Toast, i2.PrimeTemplate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.ButtonDirective, i8.Listbox, i9.Dialog, i10.InputText],\n      styles: [\".p-datatable[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.p-datatable[_ngcontent-%COMP%]   .p-datatable-thead[_ngcontent-%COMP%]    > tr[_ngcontent-%COMP%]    > th[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n}\\n\\n.email-listbox[_ngcontent-%COMP%] {\\n  width: 600px;\\n}\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: block;\\n}\\n\\n.add-email-dialog[_ngcontent-%COMP%] {\\n  width: 500px !important;\\n}\\n\\n.email-listbox[_ngcontent-%COMP%]   .p-listbox-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.listbox-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  padding: 8px;\\n}\\n\\n.add-button[_ngcontent-%COMP%] {\\n  margin-left: 83%;\\n}\\n\\n.delete-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmFja29mZmljZS92ZW5kb3IvdmVuZG9yLWVtYWlsL3ZlbmRvci1lbWFpbC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLFdBQUE7QUFDSjs7QUFFQTtFQUNJLGNBQUE7QUFDSjs7QUFHQTtFQUNJLFlBQUE7QUFBSjs7QUFHQTtFQUNJLGlCQUFBO0VBQ0EsY0FBQTtBQUFKOztBQUdBO0VBQ0ksdUJBQUE7QUFBSjs7QUFHQTtFQUNJLGFBQUE7RUFDQSxzQkFBQTtBQUFKOztBQUdBO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtBQUFKOztBQUdBO0VBQ0ksZ0JBQUE7QUFBSjs7QUFHQTtFQUNJLGlCQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyIucC1kYXRhdGFibGUge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi5wLWRhdGF0YWJsZSAucC1kYXRhdGFibGUtdGhlYWQ+dHI+dGgge1xyXG4gICAgY29sb3I6ICNmZmZmZmY7XHJcbn1cclxuXHJcblxyXG4uZW1haWwtbGlzdGJveCB7XHJcbiAgICB3aWR0aDogNjAwcHg7XHJcbn1cclxuXHJcbi5zdWJtaXQtYnV0dG9uIHtcclxuICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xyXG4gICAgZGlzcGxheTogYmxvY2s7XHJcbn1cclxuXHJcbi5hZGQtZW1haWwtZGlhbG9nIHtcclxuICAgIHdpZHRoOiA1MDBweCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uZW1haWwtbGlzdGJveCAucC1saXN0Ym94LWxpc3Qge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbn1cclxuXHJcbi5saXN0Ym94LWl0ZW0ge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIHBhZGRpbmc6IDhweDtcclxufVxyXG5cclxuLmFkZC1idXR0b24ge1xyXG4gICAgbWFyZ2luLWxlZnQ6IDgzJTtcclxufVxyXG5cclxuLmRlbGV0ZS1idXR0b24ge1xyXG4gICAgbWFyZ2luLWxlZnQ6IGF1dG87XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "VendorEmailComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template_button_click_3_listener", "email_r5", "ɵɵrestoreView", "_r4", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "removeEmail", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtwoWayListener", "VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template_p_listbox_ngModelChange_2_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "selectedEmail", "ɵɵtemplate", "VendorEmailComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template", "VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template_button_click_4_listener", "item_r6", "updateSettings", "ɵɵproperty", "editVendor<PERSON>dmin", "vendor_admin_user_emails", "ɵɵtwoWayProperty", "VendorEmailComponent_ng_container_5_ng_container_10_tr_1_Template", "Emails", "VendorEmailComponent_ng_container_5_Template_button_click_7_listener", "_r1", "openDialog", "VendorEmailComponent_ng_container_5_ng_container_9_Template", "VendorEmailComponent_ng_container_5_ng_container_10_Template", "length", "VendorEmailComponent", "constructor", "vendorservice", "messageservice", "route", "unsubscribe$", "settingType", "setting<PERSON>itle", "newEmail", "loading", "<PERSON><PERSON><PERSON>", "displayDialog", "ngOnInit", "routeData", "snapshot", "data", "vendor", "pipe", "subscribe", "getSettingData", "addEmail", "trim", "Array", "isArray", "add", "severity", "summary", "detail", "email", "filter", "e", "get", "next", "value", "i", "element", "error", "item", "obj", "update", "documentId", "editing", "ɵɵdirectiveInject", "i1", "VendorService", "i2", "MessageService", "i3", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "VendorEmailComponent_Template", "rf", "ctx", "ɵɵelement", "VendorEmailComponent_ng_container_5_Template", "VendorEmailComponent_div_6_Template", "VendorEmailComponent_Template_p_dialog_visibleChange_7_listener", "VendorEmailComponent_Template_input_ngModelChange_12_listener", "VendorEmailComponent_Template_button_click_14_listener", "VendorEmailComponent_Template_button_click_15_listener"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\vendor\\vendor-email\\vendor-email.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\vendor\\vendor-email\\vendor-email.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { VendorService } from '../vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-vendor-email',\r\n  templateUrl: './vendor-email.component.html',\r\n  styleUrl: './vendor-email.component.scss',\r\n})\r\nexport class VendorEmailComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  settingType: string = '';\r\n  settingTitle: string = '';\r\n  selectedEmail: string = '';\r\n  newEmail: string = '';\r\n  loading = false;\r\n  Emails: any = [];\r\n  moduleurl = 'settings';\r\n  editVendorAdmin: any = {\r\n    vendor_admin_user_emails: '',\r\n  };\r\n\r\n  displayDialog = false;\r\n\r\n  constructor(\r\n    private vendorservice: VendorService,\r\n    private messageservice: MessageService,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const routeData = this.route.snapshot.data;\r\n    this.settingType = routeData['type'];\r\n    this.settingTitle = routeData['title'];\r\n    this.vendorservice.vendor\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(() => {\r\n        this.getSettingData();\r\n      });\r\n  }\r\n\r\n  openDialog() {\r\n    this.displayDialog = true;\r\n  }\r\n\r\n  addEmail() {\r\n    if (this.newEmail.trim()) {\r\n      if (!Array.isArray(this.editVendorAdmin.vendor_admin_user_emails)) {\r\n        this.editVendorAdmin.vendor_admin_user_emails = [];\r\n      }\r\n      this.editVendorAdmin.vendor_admin_user_emails = [\r\n        ...this.editVendorAdmin.vendor_admin_user_emails,\r\n        this.newEmail.trim(),\r\n      ];\r\n      this.newEmail = '';\r\n      this.displayDialog = false;\r\n      this.messageservice.add({\r\n        severity: 'success',\r\n        summary: 'Added',\r\n        detail: 'Email added successfully',\r\n      });\r\n    }\r\n  }\r\n\r\n  removeEmail(email: string) {\r\n    this.editVendorAdmin.vendor_admin_user_emails =\r\n      this.editVendorAdmin.vendor_admin_user_emails.filter(\r\n        (e: any) => e !== email\r\n      );\r\n    this.messageservice.add({\r\n      severity: 'warn',\r\n      summary: 'Removed',\r\n      detail: 'Email Removed successfully! ',\r\n    });\r\n  }\r\n\r\n  getSettingData() {\r\n    this.loading = true;\r\n    this.vendorservice\r\n      .get(this.settingType, this.moduleurl)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (value) => {\r\n          this.loading = false;\r\n          if (value.data?.length) {\r\n            for (let i = 0; i < value.data.length; i++) {\r\n              const element = value.data[i];\r\n              element.vendor_admin_user_emails = Array.isArray(\r\n                element.vendor_admin_user_emails\r\n              )\r\n                ? element.vendor_admin_user_emails\r\n                : [];\r\n              this.editVendorAdmin.vendor_admin_user_emails =\r\n                element.vendor_admin_user_emails;\r\n            }\r\n            this.Emails = value.data;\r\n          } else {\r\n            this.Emails = [];\r\n          }\r\n        },\r\n        error: () => {\r\n          this.loading = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  updateSettings(item: any) {\r\n    const obj: any = {\r\n      ...this.editVendorAdmin,\r\n    };\r\n    this.vendorservice\r\n      .update(obj, item.documentId, this.moduleurl)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          item.editing = false;\r\n          item.vendor_admin_user_emails =\r\n            this.editVendorAdmin.vendor_admin_user_emails;\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Updated Successfully!',\r\n          });\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n\r\n<div class=\"card\">\r\n    <div class=\"grid mx-0\">\r\n        <h5 class=\"p-2 fw-bold\">{{ settingTitle }}</h5>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"!loading\">\r\n        <div class=\"table-responsive\">\r\n            <table class=\"p-datatable\">\r\n                <thead class=\"p-datatable-thead\">\r\n                    <tr>\r\n                        <th class=\"d-flex align-items-center\">\r\n                            Vendor Admin Email\r\n                            <button pButton icon=\"pi pi-plus\" class=\"p-button-primary add-button\"\r\n                                (click)=\"openDialog()\"></button>\r\n                        </th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody class=\"p-datatable-tbody\">\r\n                    <ng-container *ngIf=\"!Emails.length\">\r\n                        <tr>\r\n                            <td colspan=\"3\">No records found.</td>\r\n                        </tr>\r\n                    </ng-container>\r\n                    <ng-container *ngIf=\"Emails.length\">\r\n                        <tr *ngFor=\"let item of Emails; let i = index\">\r\n                            <td class=\"p-datatable-row\">\r\n                                <p-listbox [options]=\"editVendorAdmin.vendor_admin_user_emails\"\r\n                                    [(ngModel)]=\"selectedEmail\" class=\"email-listbox\">\r\n                                    <ng-template let-email let-index=\"index\" pTemplate=\"item\">\r\n                                        <div class=\"listbox-item\">\r\n                                            <span>{{ email }}</span>\r\n                                            <button pButton icon=\"pi pi-trash\"\r\n                                                class=\"p-button-danger p-button-sm delete-button\"\r\n                                                (click)=\"removeEmail(email)\"></button>\r\n                                        </div>\r\n                                    </ng-template>\r\n                                </p-listbox>\r\n\r\n                                <button pButton type=\"button\" class=\"mt-3 submit-button\" (click)=\"updateSettings(item)\">\r\n                                    Submit\r\n                                </button>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-container>\r\n                </tbody>\r\n            </table>\r\n        </div>\r\n    </ng-container>\r\n</div>\r\n\r\n<div *ngIf=\"loading\">Loading...</div>\r\n\r\n<p-dialog [(visible)]=\"displayDialog\" header=\"Add New Email\" position=\"center\" [modal]=\"true\" [closable]=\"false\"\r\n    class=\"add-email-dialog\">\r\n    <div class=\"p-fluid mt-3\">\r\n        <label for=\"newItem\">Email</label>\r\n        <div class=\"p-inputgroup mt-2\">\r\n            <input id=\"newItem\" type=\"text\" pInputText [(ngModel)]=\"newEmail\" placeholder=\"Enter new email...\" />\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"d-flex justify-content-end  mt-3\">\r\n        <button pButton label=\"Cancel\" icon=\"pi pi-times\" class=\"p-button-secondary mr-2\"\r\n            (click)=\"displayDialog = false\"></button>\r\n        <button pButton label=\"Add\" icon=\"pi pi-check\" class=\"p-button-success\" (click)=\"addEmail()\"></button>\r\n    </div>\r\n</p-dialog>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICgBrBC,EAAA,CAAAC,uBAAA,GAAqC;IAE7BD,EADJ,CAAAE,cAAA,SAAI,aACgB;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACrCH,EADqC,CAAAI,YAAA,EAAK,EACrC;;;;;;;IASeJ,EADJ,CAAAE,cAAA,cAA0B,WAChB;IAAAF,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAE,cAAA,iBAEiC;IAA7BF,EAAA,CAAAK,UAAA,mBAAAC,wGAAA;MAAA,MAAAC,QAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,QAAA,CAAkB;IAAA,EAAC;IACpCP,EADqC,CAAAI,YAAA,EAAS,EACxC;;;;IAJIJ,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAT,QAAA,CAAW;;;;;;IAJ7BP,EAFR,CAAAE,cAAA,SAA+C,aACf,oBAE8B;IAAlDF,EAAA,CAAAiB,gBAAA,2BAAAC,qGAAAC,MAAA;MAAAnB,EAAA,CAAAQ,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,EAAAH,MAAA,MAAAR,MAAA,CAAAW,aAAA,GAAAH,MAAA;MAAA,OAAAnB,EAAA,CAAAa,WAAA,CAAAM,MAAA;IAAA,EAA2B;IAC3BnB,EAAA,CAAAuB,UAAA,IAAAC,+EAAA,0BAA0D;IAQ9DxB,EAAA,CAAAI,YAAA,EAAY;IAEZJ,EAAA,CAAAE,cAAA,iBAAwF;IAA/BF,EAAA,CAAAK,UAAA,mBAAAoB,0FAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAQ,aAAA,CAAAY,GAAA,EAAAV,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAgB,cAAA,CAAAD,OAAA,CAAoB;IAAA,EAAC;IACnF1B,EAAA,CAAAG,MAAA,eACJ;IAERH,EAFQ,CAAAI,YAAA,EAAS,EACR,EACJ;;;;IAhBcJ,EAAA,CAAAe,SAAA,GAAoD;IAApDf,EAAA,CAAA4B,UAAA,YAAAjB,MAAA,CAAAkB,eAAA,CAAAC,wBAAA,CAAoD;IAC3D9B,EAAA,CAAA+B,gBAAA,YAAApB,MAAA,CAAAW,aAAA,CAA2B;;;;;IAJ3CtB,EAAA,CAAAC,uBAAA,GAAoC;IAChCD,EAAA,CAAAuB,UAAA,IAAAS,iEAAA,iBAA+C;;;;;IAA1BhC,EAAA,CAAAe,SAAA,EAAW;IAAXf,EAAA,CAAA4B,UAAA,YAAAjB,MAAA,CAAAsB,MAAA,CAAW;;;;;;IAnBpDjC,EAAA,CAAAC,uBAAA,GAA+B;IAKXD,EAJhB,CAAAE,cAAA,cAA8B,gBACC,gBACU,SACzB,aACsC;IAClCF,EAAA,CAAAG,MAAA,2BACA;IAAAH,EAAA,CAAAE,cAAA,iBAC2B;IAAvBF,EAAA,CAAAK,UAAA,mBAAA6B,qEAAA;MAAAlC,EAAA,CAAAQ,aAAA,CAAA2B,GAAA;MAAA,MAAAxB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAyB,UAAA,EAAY;IAAA,EAAC;IAGtCpC,EAHuC,CAAAI,YAAA,EAAS,EACnC,EACJ,EACD;IACRJ,EAAA,CAAAE,cAAA,gBAAiC;IAM7BF,EALA,CAAAuB,UAAA,IAAAc,2DAAA,0BAAqC,KAAAC,4DAAA,0BAKD;IAuBhDtC,EAFQ,CAAAI,YAAA,EAAQ,EACJ,EACN;;;;;IA5BqBJ,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAA4B,UAAA,UAAAjB,MAAA,CAAAsB,MAAA,CAAAM,MAAA,CAAoB;IAKpBvC,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAA4B,UAAA,SAAAjB,MAAA,CAAAsB,MAAA,CAAAM,MAAA,CAAmB;;;;;IA2BtDvC,EAAA,CAAAE,cAAA,UAAqB;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;ADzCrC,OAAM,MAAOoC,oBAAoB;EAe/BC,YACUC,aAA4B,EAC5BC,cAA8B,EAC9BC,KAAqB;IAFrB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IAjBP,KAAAC,YAAY,GAAG,IAAI/C,OAAO,EAAQ;IAC1C,KAAAgD,WAAW,GAAW,EAAE;IACxB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAzB,aAAa,GAAW,EAAE;IAC1B,KAAA0B,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAhB,MAAM,GAAQ,EAAE;IAChB,KAAAiB,SAAS,GAAG,UAAU;IACtB,KAAArB,eAAe,GAAQ;MACrBC,wBAAwB,EAAE;KAC3B;IAED,KAAAqB,aAAa,GAAG,KAAK;EAMlB;EAEHC,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,IAAI;IAC1C,IAAI,CAACT,WAAW,GAAGO,SAAS,CAAC,MAAM,CAAC;IACpC,IAAI,CAACN,YAAY,GAAGM,SAAS,CAAC,OAAO,CAAC;IACtC,IAAI,CAACX,aAAa,CAACc,MAAM,CACtBC,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC,MAAK;MACd,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CAAC;EACN;EAEAvB,UAAUA,CAAA;IACR,IAAI,CAACe,aAAa,GAAG,IAAI;EAC3B;EAEAS,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,QAAQ,CAACa,IAAI,EAAE,EAAE;MACxB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAClC,eAAe,CAACC,wBAAwB,CAAC,EAAE;QACjE,IAAI,CAACD,eAAe,CAACC,wBAAwB,GAAG,EAAE;MACpD;MACA,IAAI,CAACD,eAAe,CAACC,wBAAwB,GAAG,CAC9C,GAAG,IAAI,CAACD,eAAe,CAACC,wBAAwB,EAChD,IAAI,CAACkB,QAAQ,CAACa,IAAI,EAAE,CACrB;MACD,IAAI,CAACb,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACG,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACR,cAAc,CAACqB,GAAG,CAAC;QACtBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,OAAO;QAChBC,MAAM,EAAE;OACT,CAAC;IACJ;EACF;EAEArD,WAAWA,CAACsD,KAAa;IACvB,IAAI,CAACvC,eAAe,CAACC,wBAAwB,GAC3C,IAAI,CAACD,eAAe,CAACC,wBAAwB,CAACuC,MAAM,CACjDC,CAAM,IAAKA,CAAC,KAAKF,KAAK,CACxB;IACH,IAAI,CAACzB,cAAc,CAACqB,GAAG,CAAC;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAR,cAAcA,CAAA;IACZ,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,aAAa,CACf6B,GAAG,CAAC,IAAI,CAACzB,WAAW,EAAE,IAAI,CAACI,SAAS,CAAC,CACrCO,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTc,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACxB,OAAO,GAAG,KAAK;QACpB,IAAIwB,KAAK,CAAClB,IAAI,EAAEhB,MAAM,EAAE;UACtB,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAClB,IAAI,CAAChB,MAAM,EAAEmC,CAAC,EAAE,EAAE;YAC1C,MAAMC,OAAO,GAAGF,KAAK,CAAClB,IAAI,CAACmB,CAAC,CAAC;YAC7BC,OAAO,CAAC7C,wBAAwB,GAAGgC,KAAK,CAACC,OAAO,CAC9CY,OAAO,CAAC7C,wBAAwB,CACjC,GACG6C,OAAO,CAAC7C,wBAAwB,GAChC,EAAE;YACN,IAAI,CAACD,eAAe,CAACC,wBAAwB,GAC3C6C,OAAO,CAAC7C,wBAAwB;UACpC;UACA,IAAI,CAACG,MAAM,GAAGwC,KAAK,CAAClB,IAAI;QAC1B,CAAC,MAAM;UACL,IAAI,CAACtB,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACD2C,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACN,cAAc,CAACqB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBE,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAxC,cAAcA,CAACkD,IAAS;IACtB,MAAMC,GAAG,GAAQ;MACf,GAAG,IAAI,CAACjD;KACT;IACD,IAAI,CAACa,aAAa,CACfqC,MAAM,CAACD,GAAG,EAAED,IAAI,CAACG,UAAU,EAAE,IAAI,CAAC9B,SAAS,CAAC,CAC5CO,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTc,IAAI,EAAEA,CAAA,KAAK;QACTK,IAAI,CAACI,OAAO,GAAG,KAAK;QACpBJ,IAAI,CAAC/C,wBAAwB,GAC3B,IAAI,CAACD,eAAe,CAACC,wBAAwB;QAC/C,IAAI,CAACa,cAAc,CAACqB,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBE,MAAM,EAAE;SACT,CAAC;MACJ,CAAC;MACDS,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACjC,cAAc,CAACqB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBE,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;;;uBA7HW3B,oBAAoB,EAAAxC,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtF,EAAA,CAAAkF,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAApBhD,oBAAoB;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXjC/F,EAAA,CAAAiG,SAAA,iBAAsD;UAI9CjG,EAFR,CAAAE,cAAA,aAAkB,aACS,YACK;UAAAF,EAAA,CAAAG,MAAA,GAAkB;UAC9CH,EAD8C,CAAAI,YAAA,EAAK,EAC7C;UAENJ,EAAA,CAAAuB,UAAA,IAAA2E,4CAAA,2BAA+B;UA2CnClG,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAuB,UAAA,IAAA4E,mCAAA,iBAAqB;UAErBnG,EAAA,CAAAE,cAAA,kBAC6B;UADnBF,EAAA,CAAAiB,gBAAA,2BAAAmF,gEAAAjF,MAAA;YAAAnB,EAAA,CAAAqB,kBAAA,CAAA2E,GAAA,CAAA7C,aAAA,EAAAhC,MAAA,MAAA6E,GAAA,CAAA7C,aAAA,GAAAhC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAG7BnB,EADJ,CAAAE,cAAA,aAA0B,eACD;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE9BJ,EADJ,CAAAE,cAAA,cAA+B,gBAC0E;UAA1DF,EAAA,CAAAiB,gBAAA,2BAAAoF,8DAAAlF,MAAA;YAAAnB,EAAA,CAAAqB,kBAAA,CAAA2E,GAAA,CAAAhD,QAAA,EAAA7B,MAAA,MAAA6E,GAAA,CAAAhD,QAAA,GAAA7B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsB;UAEzEnB,EAFQ,CAAAI,YAAA,EAAqG,EACnG,EACJ;UAGFJ,EADJ,CAAAE,cAAA,eAA8C,kBAEN;UAAhCF,EAAA,CAAAK,UAAA,mBAAAiG,uDAAA;YAAA,OAAAN,GAAA,CAAA7C,aAAA,GAAyB,KAAK;UAAA,EAAC;UAACnD,EAAA,CAAAI,YAAA,EAAS;UAC7CJ,EAAA,CAAAE,cAAA,kBAA6F;UAArBF,EAAA,CAAAK,UAAA,mBAAAkG,uDAAA;YAAA,OAASP,GAAA,CAAApC,QAAA,EAAU;UAAA,EAAC;UAEpG5D,EAFqG,CAAAI,YAAA,EAAS,EACpG,EACC;;;UApEmBJ,EAAA,CAAA4B,UAAA,cAAa;UAIX5B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAAgB,iBAAA,CAAAgF,GAAA,CAAAjD,YAAA,CAAkB;UAG/B/C,EAAA,CAAAe,SAAA,EAAc;UAAdf,EAAA,CAAA4B,UAAA,UAAAoE,GAAA,CAAA/C,OAAA,CAAc;UA6C3BjD,EAAA,CAAAe,SAAA,EAAa;UAAbf,EAAA,CAAA4B,UAAA,SAAAoE,GAAA,CAAA/C,OAAA,CAAa;UAETjD,EAAA,CAAAe,SAAA,EAA2B;UAA3Bf,EAAA,CAAA+B,gBAAA,YAAAiE,GAAA,CAAA7C,aAAA,CAA2B;UAAyDnD,EAAf,CAAA4B,UAAA,eAAc,mBAAmB;UAKzD5B,EAAA,CAAAe,SAAA,GAAsB;UAAtBf,EAAA,CAAA+B,gBAAA,YAAAiE,GAAA,CAAAhD,QAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}