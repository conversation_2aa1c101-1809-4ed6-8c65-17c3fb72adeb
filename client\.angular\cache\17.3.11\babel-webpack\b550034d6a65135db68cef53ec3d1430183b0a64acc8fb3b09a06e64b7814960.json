{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PartnerComponent } from './partner.component';\nimport { PartnerDetailsComponent } from './partner-details/partner-details.component';\nimport { PartnerInfoComponent } from './partner-details/partner-info/partner-info.component';\nimport { PartnerBankComponent } from './partner-details/partner-bank/partner-bank.component';\nimport { PartnerPaymentCardComponent } from './partner-details/partner-payment-card/partner-payment-card.component';\nimport { PartnerContactComponent } from './partner-details/partner-contact/partner-contact.component';\nimport { PartnerAddressComponent } from './partner-details/partner-address/partner-address.component';\nimport { PartnerRelationshipComponent } from './partner-details/partner-relationship/partner-relationship.component';\nimport { PartnerRoleComponent } from './partner-details/partner-role/partner-role.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PartnerComponent\n}, {\n  path: ':id',\n  component: PartnerDetailsComponent,\n  children: [{\n    path: 'general',\n    component: PartnerInfoComponent\n  }, {\n    path: 'role',\n    component: PartnerRoleComponent\n  }, {\n    path: 'address',\n    component: PartnerAddressComponent\n  }, {\n    path: 'bank',\n    component: PartnerBankComponent\n  }, {\n    path: 'card',\n    component: PartnerPaymentCardComponent\n  }, {\n    path: 'contact',\n    component: PartnerContactComponent\n  }, {\n    path: 'relationship',\n    component: PartnerRelationshipComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport class PartnerRoutingModule {\n  static {\n    this.ɵfac = function PartnerRoutingModule_Factory(t) {\n      return new (t || PartnerRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PartnerRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PartnerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PartnerComponent", "PartnerDetailsComponent", "PartnerInfoComponent", "PartnerBankComponent", "PartnerPaymentCardComponent", "PartnerContactComponent", "PartnerAddressComponent", "PartnerRelationshipComponent", "PartnerRoleComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "PartnerRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { PartnerComponent } from './partner.component';\r\nimport { PartnerDetailsComponent } from './partner-details/partner-details.component';\r\nimport { PartnerInfoComponent } from './partner-details/partner-info/partner-info.component';\r\nimport { PartnerBankComponent } from './partner-details/partner-bank/partner-bank.component';\r\nimport { PartnerPaymentCardComponent } from './partner-details/partner-payment-card/partner-payment-card.component';\r\nimport { PartnerContactComponent } from './partner-details/partner-contact/partner-contact.component';\r\nimport { PartnerAddressComponent } from './partner-details/partner-address/partner-address.component';\r\nimport { PartnerRelationshipComponent } from './partner-details/partner-relationship/partner-relationship.component';\r\nimport { PartnerRoleComponent } from './partner-details/partner-role/partner-role.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: PartnerComponent },\r\n  {\r\n    path: ':id',\r\n    component: PartnerDetailsComponent,\r\n    children: [\r\n      { path: 'general', component: PartnerInfoComponent },\r\n      { path: 'role', component: PartnerRoleComponent },\r\n      { path: 'address', component: PartnerAddressComponent },\r\n      { path: 'bank', component: PartnerBankComponent },\r\n      { path: 'card', component: PartnerPaymentCardComponent },\r\n      { path: 'contact', component: PartnerContactComponent },\r\n      { path: 'relationship', component: PartnerRelationshipComponent },\r\n      { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class PartnerRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,4BAA4B,QAAQ,uEAAuE;AACpH,SAASC,oBAAoB,QAAQ,uDAAuD;;;AAE5F,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEX;AAAgB,CAAE,EACzC;EACEU,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEV,uBAAuB;EAClCW,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAET;EAAoB,CAAE,EACpD;IAAEQ,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEH;EAAoB,CAAE,EACjD;IAAEE,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEL;EAAuB,CAAE,EACvD;IAAEI,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAER;EAAoB,CAAE,EACjD;IAAEO,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEP;EAA2B,CAAE,EACxD;IAAEM,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEN;EAAuB,CAAE,EACvD;IAAEK,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEJ;EAA4B,CAAE,EACjE;IAAEG,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE3D,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBhB,YAAY,CAACiB,QAAQ,CAACP,MAAM,CAAC,EAC7BV,YAAY;IAAA;EAAA;;;2EAEXgB,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAnB,YAAA;IAAAoB,OAAA,GAFrBpB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}