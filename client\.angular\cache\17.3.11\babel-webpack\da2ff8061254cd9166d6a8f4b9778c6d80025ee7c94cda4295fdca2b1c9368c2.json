{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SupplierPartnerService {\n  constructor(http) {\n    this.http = http;\n  }\n  getPartnerFunction(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][purchasing_organization][$containsi]', searchTerm).set('filters[$or][1][partner_function][$containsi]', searchTerm).set('filters[$or][2][plant][$containsi]', searchTerm).set('filters[$or][3][authorization_group][$containsi]', searchTerm).set('filters[$or][4][supplier_subrange][$containsi]', searchTerm);\n    }\n    if (id) {\n      params = params.set('filters[$and][0][supplier_id][$eq]', id);\n    }\n    return this.http.get(`${CMS_APIContstant.SUPPLIER_PARTNER_FUNCTION}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function SupplierPartnerService_Factory(t) {\n      return new (t || SupplierPartnerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SupplierPartnerService,\n      factory: SupplierPartnerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "CMS_APIContstant", "SupplierPartnerService", "constructor", "http", "getPartnerFunction", "id", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "SUPPLIER_PARTNER_FUNCTION", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\supplier\\supplier-details\\supplier-partner\\supplier-partner.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SupplierPartnerService {\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getPartnerFunction(\r\n    id: string,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][purchasing_organization][$containsi]', searchTerm)\r\n        .set('filters[$or][1][partner_function][$containsi]', searchTerm)\r\n        .set('filters[$or][2][plant][$containsi]', searchTerm)\r\n        .set('filters[$or][3][authorization_group][$containsi]', searchTerm)\r\n        .set('filters[$or][4][supplier_subrange][$containsi]', searchTerm);\r\n    }\r\n\r\n    if (id) {\r\n      params = params.set('filters[$and][0][supplier_id][$eq]', id);\r\n    }\r\n    \r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.SUPPLIER_PARTNER_FUNCTION}`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAG7D,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,sBAAsB;EACjCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,kBAAkBA,CAChBC,EAAU,EACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIZ,UAAU,EAAE,CAC1Ba,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,sDAAsD,EAAEF,UAAU,CAAC,CACvEE,GAAG,CAAC,+CAA+C,EAAEF,UAAU,CAAC,CAChEE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,kDAAkD,EAAEF,UAAU,CAAC,CACnEE,GAAG,CAAC,gDAAgD,EAAEF,UAAU,CAAC;IACtE;IAEA,IAAIL,EAAE,EAAE;MACNM,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,oCAAoC,EAAEP,EAAE,CAAC;IAC/D;IAEA,OAAO,IAAI,CAACF,IAAI,CAACa,GAAG,CAClB,GAAGhB,gBAAgB,CAACiB,yBAAyB,EAAE,EAC/C;MACEN;KACD,CACF;EACH;;;uBArCWV,sBAAsB,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAtBpB,sBAAsB;MAAAqB,OAAA,EAAtBrB,sBAAsB,CAAAsB,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}