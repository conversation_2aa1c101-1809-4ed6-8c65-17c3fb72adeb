{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ConfigurationService {\n  constructor(http) {\n    this.http = http;\n    this.configurationSubject = new BehaviorSubject(null);\n    this.configuration = this.configurationSubject.asObservable();\n  }\n  get(type, moduleurl) {\n    let params = new HttpParams().set('pagination[pageSize]', 100);\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`, {\n      params\n    });\n  }\n  save(data, moduleurl) {\n    return this.http.post(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`, {\n      data: data\n    });\n  }\n  update(data, id, moduleurl) {\n    return this.http.put(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`, {\n      data\n    });\n  }\n  delete(id, moduleurl) {\n    return this.http.delete(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`);\n  }\n  static {\n    this.ɵfac = function ConfigurationService_Factory(t) {\n      return new (t || ConfigurationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ConfigurationService,\n      factory: ConfigurationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "ConfigurationService", "constructor", "http", "configurationSubject", "configuration", "asObservable", "get", "type", "<PERSON><PERSON><PERSON>", "params", "set", "CMSAPI_END_POINT", "save", "data", "post", "update", "id", "put", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\configuration\\configuration.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ConfigurationService {\r\n  public configurationSubject = new BehaviorSubject<any>(null);\r\n  public configuration = this.configurationSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  get(type: string, moduleurl: string) {\r\n    let params = new HttpParams().set('pagination[pageSize]', 100);\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`,\r\n      { params }\r\n    );\r\n  }\r\n\r\n  save(data: any, moduleurl: string) {\r\n    return this.http.post<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`,\r\n      { data: data }\r\n    );\r\n  }\r\n\r\n  update(data: any, id: string, moduleurl: string) {\r\n    return this.http.put<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`,\r\n      { data }\r\n    );\r\n  }\r\n\r\n  delete(id: string, moduleurl: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,oBAAoB,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IACrD,KAAAM,aAAa,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;EAExB;EAEvCC,GAAGA,CAACC,IAAY,EAAEC,SAAiB;IACjC,IAAIC,MAAM,GAAG,IAAIZ,UAAU,EAAE,CAACa,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC;IAC9D,OAAO,IAAI,CAACR,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAACY,gBAAgB,IAAIH,SAAS,EAAE,EACnD;MAAEC;IAAM,CAAE,CACX;EACH;EAEAG,IAAIA,CAACC,IAAS,EAAEL,SAAiB;IAC/B,OAAO,IAAI,CAACN,IAAI,CAACY,IAAI,CACnB,GAAGf,gBAAgB,CAACY,gBAAgB,IAAIH,SAAS,EAAE,EACnD;MAAEK,IAAI,EAAEA;IAAI,CAAE,CACf;EACH;EAEAE,MAAMA,CAACF,IAAS,EAAEG,EAAU,EAAER,SAAiB;IAC7C,OAAO,IAAI,CAACN,IAAI,CAACe,GAAG,CAClB,GAAGlB,gBAAgB,CAACY,gBAAgB,IAAIH,SAAS,IAAIQ,EAAE,EAAE,EACzD;MAAEH;IAAI,CAAE,CACT;EACH;EAEAK,MAAMA,CAACF,EAAU,EAAER,SAAiB;IAClC,OAAO,IAAI,CAACN,IAAI,CAACgB,MAAM,CACrB,GAAGnB,gBAAgB,CAACY,gBAAgB,IAAIH,SAAS,IAAIQ,EAAE,EAAE,CAC1D;EACH;;;uBAhCWhB,oBAAoB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBtB,oBAAoB;MAAAuB,OAAA,EAApBvB,oBAAoB,CAAAwB,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}