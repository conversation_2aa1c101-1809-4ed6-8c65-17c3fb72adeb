{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ContactComponent } from './contact.component';\nimport { RegisterContactComponent } from './register-contact/register-contact/register-contact.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ContactComponent\n}, {\n  path: 'register',\n  component: RegisterContactComponent\n}];\nexport class ContactRoutingModule {\n  static {\n    this.ɵfac = function ContactRoutingModule_Factory(t) {\n      return new (t || ContactRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContactRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContactRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ContactComponent", "RegisterContactComponent", "routes", "path", "component", "ContactRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\contacts\\contact-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ContactComponent } from './contact.component';\r\nimport { RegisterContactComponent } from './register-contact/register-contact/register-contact.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ContactComponent },\r\n  { path: 'register', component: RegisterContactComponent },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ContactRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,wBAAwB,QAAQ,gEAAgE;;;AAEzG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ;AAAgB,CAAE,EACzC;EAAEG,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEH;AAAwB,CAAE,CAC1D;AAMD,OAAM,MAAOI,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXM,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAFrBV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}