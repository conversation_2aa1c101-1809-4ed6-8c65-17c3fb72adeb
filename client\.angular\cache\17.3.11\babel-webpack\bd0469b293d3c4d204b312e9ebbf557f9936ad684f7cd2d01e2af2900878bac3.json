{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PartnerService {\n  constructor(http) {\n    this.http = http;\n    this.partnerSubject = new BehaviorSubject(null);\n    this.partner = this.partnerSubject.asObservable();\n  }\n  getPartners(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][email][$containsi]', searchTerm).set('filters[$or][3][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  getPartnerByID(id) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', id).set('populate[0]', 'roles').set('populate[1]', 'addresses').set('populate[2]', 'banks').set('populate[3]', 'payment_cards').set('populate[4]', 'relationships1').set('populate[5]', 'contact_persons');\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  getPartnerByIDName(partnerdata) {\n    let params = new HttpParams();\n    if (partnerdata) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', partnerdata).set('filters[$or][1][bp_full_name][$containsi]', partnerdata);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function PartnerService_Factory(t) {\n      return new (t || PartnerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PartnerService,\n      factory: PartnerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "PartnerService", "constructor", "http", "partnerSubject", "partner", "asObservable", "getPartners", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS", "getPartnerByID", "id", "getPartnerByIDName", "partnerdata", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PartnerService {\r\n  public partnerSubject = new BehaviorSubject<any>(null);\r\n  public partner = this.partnerSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getPartners(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)\r\n        .set('filters[$or][2][email][$containsi]', searchTerm)\r\n        .set('filters[$or][3][phone][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getPartnerByID(id: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[bp_id][$eq]', id)\r\n      .set('populate[0]', 'roles')\r\n      .set('populate[1]', 'addresses')\r\n      .set('populate[2]', 'banks')\r\n      .set('populate[3]', 'payment_cards')\r\n      .set('populate[4]', 'relationships1')\r\n      .set('populate[5]', 'contact_persons');\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params });\r\n  }\r\n\r\n  getPartnerByIDName(partnerdata: string): Observable<any[]> {\r\n    let params = new HttpParams();\r\n    if (partnerdata) {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', partnerdata)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', partnerdata);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, {\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,cAAc;EAIzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAM,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC1BgB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,2CAA2C,EAAEF,UAAU,CAAC,CAC5DE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;IAC1D;IACA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,QAAQ,EAAE,EAAE;MAC1DN;KACD,CAAC;EACJ;EAEAO,cAAcA,CAACC,EAAU;IACvB,MAAMR,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC5BgB,GAAG,CAAC,qBAAqB,EAAEO,EAAE,CAAC,CAC9BP,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAC3BA,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAC/BA,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAC3BA,GAAG,CAAC,aAAa,EAAE,eAAe,CAAC,CACnCA,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAC,CACpCA,GAAG,CAAC,aAAa,EAAE,iBAAiB,CAAC;IAExC,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,QAAQ,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC;EACzE;EAEAS,kBAAkBA,CAACC,WAAmB;IACpC,IAAIV,MAAM,GAAG,IAAIf,UAAU,EAAE;IAC7B,IAAIyB,WAAW,EAAE;MACfV,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAES,WAAW,CAAC,CACtDT,GAAG,CAAC,2CAA2C,EAAES,WAAW,CAAC;IAClE;IAEA,OAAO,IAAI,CAACpB,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,QAAQ,EAAE,EAAE;MAC1DN;KACD,CAAC;EACJ;;;uBAxDWZ,cAAc,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd1B,cAAc;MAAA2B,OAAA,EAAd3B,cAAc,CAAA4B,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}