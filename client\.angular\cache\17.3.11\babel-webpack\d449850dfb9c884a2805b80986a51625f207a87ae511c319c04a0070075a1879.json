{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, catchError, forkJoin, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ConfigurationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api/configurations';\n    this.headers = new HttpHeaders({\n      Authorization: 'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695' // Replace with your actual token\n    });\n    this.customerSubject = new BehaviorSubject(null);\n    this.customer = this.customerSubject.asObservable();\n  }\n  getTabsConfig() {\n    return [{\n      title: 'Invoices',\n      subTabs: [{\n        hasErrors: false,\n        id: 'invoice-statuses',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Invoice Statuses',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'INVOICE_STATUS',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'invoice-types',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Transaction Types',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'INVOICE_TYPE',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'invoice-form-types',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Invoice Form Types',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'INVOICE_FORM_TYPE',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }]\n    }, {\n      title: 'Orders',\n      subTabs: [{\n        hasErrors: false,\n        id: 'order-statuses',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Sale Order Statuses',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'ORDER_STATUS',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'order-types',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Transaction Types',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'ORDER_TYPE',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }, {\n          name: 'Use For Creation',\n          value: 'is_active',\n          required: false,\n          newValue: 'newIsCreation',\n          itemValue: false,\n          type: 'checkbox'\n        }]\n      }, {\n        hasErrors: false,\n        id: 'special-instructions',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Special Instructions',\n        data: [],\n        loading: false,\n        type: 'SingleValue',\n        url: this.apiUrl,\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: '',\n          length: 4\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: '',\n          length: 25\n        }]\n      }]\n    }, {\n      title: 'General Settings',\n      subTabs: [{\n        hasErrors: false,\n        id: 'relationship-types',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Recommendation Types',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'catalogs',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Catalogs',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        columns: [{\n          name: 'Name',\n          value: 'name',\n          required: true,\n          newValue: 'newName',\n          itemValue: ''\n        }, {\n          name: 'WebStore',\n          value: 'web_store',\n          required: false,\n          newValue: 'newweb_store',\n          itemValue: ''\n        }, {\n          name: 'Language',\n          value: 'language',\n          required: true,\n          newValue: 'newLanguage',\n          itemValue: '',\n          type: 'dd',\n          options: [{\n            name: 'English',\n            value: 'English'\n          }],\n          multiple: false\n        },\n        //{ name: 'Categories', value: 'categories', required: false, newValue: 'newCategories', itemValue: '', type: 'dd', options: [], multiple: true },\n        {\n          name: 'Status',\n          value: 'catalog_status',\n          required: true,\n          newValue: 'newStatus',\n          itemValue: '',\n          type: 'radio',\n          options: [{\n            name: 'Active',\n            value: 'Active'\n          }, {\n            name: 'Inactive',\n            value: 'Inactive'\n          }]\n        }]\n      }, {\n        hasErrors: false,\n        id: 'categories',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Categories',\n        data: [],\n        loading: false,\n        url: `${this.apiUrl}`,\n        columns: [{\n          name: 'Name',\n          value: 'name',\n          required: true,\n          newValue: 'newName',\n          itemValue: ''\n        }, {\n          name: 'Parent Category',\n          value: 'parent_category_id',\n          required: false,\n          newValue: 'newparent_category_id',\n          itemValue: '',\n          type: 'dd',\n          options: [],\n          multiple: false\n        }]\n      }, {\n        hasErrors: false,\n        id: 'stock',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Stock',\n        data: [],\n        type: 'SingleValue',\n        loading: false,\n        url: this.apiUrl,\n        columns: [{\n          name: 'Low Stock Quantity',\n          value: 'low_stock_qty',\n          required: true,\n          newValue: 'lowStockQuantity',\n          itemValue: 1,\n          type: 'number'\n        }]\n      }, {\n        hasErrors: false,\n        id: 'notification',\n        saving: false,\n        updation: true,\n        deletion: false,\n        creation: false,\n        title: 'Notifications',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'NOTIFICATION',\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: '',\n          readonly: true\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: '',\n          readonly: true\n        }, {\n          name: 'Allow Notification',\n          value: 'is_active',\n          required: false,\n          newValue: 'newIsSend',\n          itemValue: false,\n          type: 'checkbox'\n        }]\n      }, {\n        hasErrors: false,\n        id: 'conditions',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Conditions',\n        data: [],\n        type: 'multiValue',\n        loading: false,\n        url: this.apiUrl,\n        columns: [{\n          name: 'Base Price',\n          value: 'base_price_code',\n          newValue: 'newbase_price_code',\n          type: 'string',\n          itemValue: '',\n          maxlength: 4\n        }, {\n          name: 'Tax',\n          value: 'tax_code',\n          newValue: 'newtax_code',\n          type: 'string',\n          itemValue: '',\n          maxlength: 4\n        }, {\n          name: 'Shipping',\n          value: 'shipping_code',\n          newValue: 'newshipping_code',\n          type: 'string',\n          itemValue: '',\n          maxlength: 4\n        }, {\n          name: 'Discount',\n          value: 'discount_code',\n          newValue: 'newdiscount_code',\n          type: 'string',\n          itemValue: '',\n          maxlength: 4\n        }]\n      }]\n    }, {\n      title: 'Service',\n      subTabs: [{\n        hasErrors: false,\n        id: 'ticket-statuses',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Ticket Status',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'TICKET_STATUS',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }]\n    }, {\n      title: 'Quote',\n      subTabs: [{\n        hasErrors: false,\n        id: 'quote-statuses',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Quote Status',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'QUOTE_STATUS',\n        columns: [{\n          name: 'Status Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Status Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'approval-amount',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Approval Amount',\n        data: [],\n        loading: false,\n        type: 'SingleValue',\n        url: this.apiUrl,\n        columns: [{\n          name: 'Approval amount',\n          value: 'min_quote_price',\n          required: true,\n          newValue: 'minQuotePrice',\n          itemValue: 1,\n          type: 'number'\n        }]\n      }, {\n        hasErrors: false,\n        id: 'quote-transaction-type',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Transaction Type',\n        data: [],\n        loading: false,\n        type: 'SingleValue',\n        url: this.apiUrl,\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'quote-description',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Quote Description',\n        data: [],\n        loading: false,\n        type: 'SingleValue',\n        url: this.apiUrl,\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: '',\n          length: 4\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: '',\n          length: 25\n        }]\n      }]\n    }, {\n      title: 'Returns',\n      subTabs: [{\n        hasErrors: false,\n        id: 'return-status',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Return Status',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'RETURN_STATUS',\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'refund-progress',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Refund Progress',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'RETURN_REFUND_PROGRESS',\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: '',\n          length: '1'\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'return-transaction-type',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Transaction Type',\n        data: [],\n        loading: false,\n        type: 'SingleValue',\n        url: this.apiUrl,\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }, {\n        hasErrors: false,\n        id: 'return-reason',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Return Reason',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'RETURN_REASON',\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }]\n    }, {\n      title: 'Customer',\n      subTabs: [{\n        hasErrors: false,\n        id: 'cust-text-type',\n        saving: false,\n        updation: true,\n        deletion: true,\n        creation: true,\n        title: 'Text Type',\n        data: [],\n        loading: false,\n        url: this.apiUrl,\n        configType: 'CUSTOMER_TEXT_DESC',\n        columns: [{\n          name: 'Code',\n          value: 'code',\n          required: true,\n          newValue: 'newCode',\n          itemValue: ''\n        }, {\n          name: 'Description',\n          value: 'description',\n          required: true,\n          newValue: 'newDescription',\n          itemValue: ''\n        }]\n      }]\n    }];\n  }\n  getAll(tabs) {\n    const obj = {};\n    console.log('sds656' + tabs.length);\n    for (let i = 0; i < tabs.length; i++) {\n      const tabDetails = tabs[i];\n      let url = tabDetails.url;\n      if (tabDetails.url.includes('product-categories')) {\n        url = `${url}?populate=*`;\n      } else if (tabDetails.url.includes('product-catalogs')) {\n        url = `${url}?populate=*`;\n      }\n      obj[tabDetails.id] = this.get(url).pipe(catchError(error => of(error)));\n    }\n    return forkJoin(obj);\n  }\n  get(url) {\n    return this.http.get(url, {\n      headers: this.headers\n    });\n  }\n  save(url, data) {\n    return this.http.post(url, {\n      data\n    }, {\n      headers: this.headers\n    });\n  }\n  update(url, data, id) {\n    return this.http.put(`${url}/${id}`, {\n      data\n    }, {\n      headers: this.headers\n    });\n  }\n  delete(url, id) {\n    return this.http.delete(`${url}/${id}`, {\n      headers: this.headers\n    });\n  }\n  saveSetting(tab, data) {\n    return this.http.put(`${tab.url}/${tab.data.documentId}`, {\n      data\n    }, {\n      headers: this.headers\n    });\n  }\n  static {\n    this.ɵfac = function ConfigurationService_Factory(t) {\n      return new (t || ConfigurationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ConfigurationService,\n      factory: ConfigurationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "catchError", "fork<PERSON><PERSON>n", "of", "ConfigurationService", "constructor", "http", "apiUrl", "headers", "Authorization", "customerSubject", "customer", "asObservable", "getTabsConfig", "title", "subTabs", "hasErrors", "id", "saving", "updation", "deletion", "creation", "data", "loading", "url", "configType", "columns", "name", "value", "required", "newValue", "itemValue", "type", "length", "options", "multiple", "readonly", "maxlength", "getAll", "tabs", "obj", "console", "log", "i", "tabDetails", "includes", "get", "pipe", "error", "save", "post", "update", "put", "delete", "saveSetting", "tab", "documentId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\configuration\\configuration.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, catchError, forkJoin, of } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ConfigurationService {\r\n  private apiUrl =\r\n    'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api/configurations';\r\n  private headers = new HttpHeaders({\r\n    Authorization:\r\n      'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695', // Replace with your actual token\r\n  });\r\n\r\n  public customerSubject = new BehaviorSubject<any>(null);\r\n  public customer = this.customerSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getTabsConfig() {\r\n    return [\r\n      {\r\n        title: 'Invoices',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'invoice-statuses',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Invoice Statuses',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'INVOICE_STATUS',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'invoice-types',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Transaction Types',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'INVOICE_TYPE',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'invoice-form-types',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Invoice Form Types',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'INVOICE_FORM_TYPE',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Orders',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'order-statuses',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Sale Order Statuses',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'ORDER_STATUS',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'order-types',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Transaction Types',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'ORDER_TYPE',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Use For Creation',\r\n                value: 'is_active',\r\n                required: false,\r\n                newValue: 'newIsCreation',\r\n                itemValue: false,\r\n                type: 'checkbox',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'special-instructions',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Special Instructions',\r\n            data: [],\r\n            loading: false,\r\n            type: 'SingleValue',\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n                length: 4,\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n                length: 25,\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        title: 'General Settings',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'relationship-types',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Recommendation Types',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'catalogs',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Catalogs',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Name',\r\n                value: 'name',\r\n                required: true,\r\n                newValue: 'newName',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'WebStore',\r\n                value: 'web_store',\r\n                required: false,\r\n                newValue: 'newweb_store',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Language',\r\n                value: 'language',\r\n                required: true,\r\n                newValue: 'newLanguage',\r\n                itemValue: '',\r\n                type: 'dd',\r\n                options: [\r\n                  {\r\n                    name: 'English',\r\n                    value: 'English',\r\n                  },\r\n                ],\r\n                multiple: false,\r\n              },\r\n              //{ name: 'Categories', value: 'categories', required: false, newValue: 'newCategories', itemValue: '', type: 'dd', options: [], multiple: true },\r\n              {\r\n                name: 'Status',\r\n                value: 'catalog_status',\r\n                required: true,\r\n                newValue: 'newStatus',\r\n                itemValue: '',\r\n                type: 'radio',\r\n                options: [\r\n                  {\r\n                    name: 'Active',\r\n                    value: 'Active',\r\n                  },\r\n                  {\r\n                    name: 'Inactive',\r\n                    value: 'Inactive',\r\n                  },\r\n                ],\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'categories',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Categories',\r\n            data: [],\r\n            loading: false,\r\n            url: `${this.apiUrl}`,\r\n            columns: [\r\n              {\r\n                name: 'Name',\r\n                value: 'name',\r\n                required: true,\r\n                newValue: 'newName',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Parent Category',\r\n                value: 'parent_category_id',\r\n                required: false,\r\n                newValue: 'newparent_category_id',\r\n                itemValue: '',\r\n                type: 'dd',\r\n                options: [],\r\n                multiple: false,\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'stock',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Stock',\r\n            data: [],\r\n            type: 'SingleValue',\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Low Stock Quantity',\r\n                value: 'low_stock_qty',\r\n                required: true,\r\n                newValue: 'lowStockQuantity',\r\n                itemValue: 1,\r\n                type: 'number',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'notification',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: false,\r\n            creation: false,\r\n            title: 'Notifications',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'NOTIFICATION',\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n                readonly: true,\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n                readonly: true,\r\n              },\r\n              {\r\n                name: 'Allow Notification',\r\n                value: 'is_active',\r\n                required: false,\r\n                newValue: 'newIsSend',\r\n                itemValue: false,\r\n                type: 'checkbox',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'conditions',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Conditions',\r\n            data: [],\r\n            type: 'multiValue',\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Base Price',\r\n                value: 'base_price_code',\r\n                newValue: 'newbase_price_code',\r\n                type: 'string',\r\n                itemValue: '',\r\n                maxlength: 4,\r\n              },\r\n              {\r\n                name: 'Tax',\r\n                value: 'tax_code',\r\n                newValue: 'newtax_code',\r\n                type: 'string',\r\n                itemValue: '',\r\n                maxlength: 4,\r\n              },\r\n              {\r\n                name: 'Shipping',\r\n                value: 'shipping_code',\r\n                newValue: 'newshipping_code',\r\n                type: 'string',\r\n                itemValue: '',\r\n                maxlength: 4,\r\n              },\r\n              {\r\n                name: 'Discount',\r\n                value: 'discount_code',\r\n                newValue: 'newdiscount_code',\r\n                type: 'string',\r\n                itemValue: '',\r\n                maxlength: 4,\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Service',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'ticket-statuses',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Ticket Status',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'TICKET_STATUS',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Quote',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'quote-statuses',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Quote Status',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'QUOTE_STATUS',\r\n            columns: [\r\n              {\r\n                name: 'Status Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Status Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'approval-amount',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Approval Amount',\r\n            data: [],\r\n            loading: false,\r\n            type: 'SingleValue',\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Approval amount',\r\n                value: 'min_quote_price',\r\n                required: true,\r\n                newValue: 'minQuotePrice',\r\n                itemValue: 1,\r\n                type: 'number',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'quote-transaction-type',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Transaction Type',\r\n            data: [],\r\n            loading: false,\r\n            type: 'SingleValue',\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'quote-description',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Quote Description',\r\n            data: [],\r\n            loading: false,\r\n            type: 'SingleValue',\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n                length: 4,\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n                length: 25,\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Returns',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'return-status',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Return Status',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'RETURN_STATUS',\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'refund-progress',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Refund Progress',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'RETURN_REFUND_PROGRESS',\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n                length: '1',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'return-transaction-type',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Transaction Type',\r\n            data: [],\r\n            loading: false,\r\n            type: 'SingleValue',\r\n            url: this.apiUrl,\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            hasErrors: false,\r\n            id: 'return-reason',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Return Reason',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'RETURN_REASON',\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Customer',\r\n        subTabs: [\r\n          {\r\n            hasErrors: false,\r\n            id: 'cust-text-type',\r\n            saving: false,\r\n            updation: true,\r\n            deletion: true,\r\n            creation: true,\r\n            title: 'Text Type',\r\n            data: [],\r\n            loading: false,\r\n            url: this.apiUrl,\r\n            configType: 'CUSTOMER_TEXT_DESC',\r\n            columns: [\r\n              {\r\n                name: 'Code',\r\n                value: 'code',\r\n                required: true,\r\n                newValue: 'newCode',\r\n                itemValue: '',\r\n              },\r\n              {\r\n                name: 'Description',\r\n                value: 'description',\r\n                required: true,\r\n                newValue: 'newDescription',\r\n                itemValue: '',\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n    ];\r\n  }\r\n\r\n  getAll(tabs: Array<any>) {\r\n    const obj: any = {};\r\n    console.log('sds656' + tabs.length);\r\n    for (let i = 0; i < tabs.length; i++) {\r\n      const tabDetails = tabs[i];\r\n      let url = tabDetails.url;\r\n      if (tabDetails.url.includes('product-categories')) {\r\n        url = `${url}?populate=*`;\r\n      } else if (tabDetails.url.includes('product-catalogs')) {\r\n        url = `${url}?populate=*`;\r\n      }\r\n      obj[tabDetails.id] = this.get(url).pipe(catchError((error) => of(error)));\r\n    }\r\n    return forkJoin(obj);\r\n  }\r\n\r\n  get(url: string) {\r\n    return this.http.get<any>(url, { headers: this.headers });\r\n  }\r\n\r\n  save(url: string, data: any) {\r\n    return this.http.post<any>(url, { data }, { headers: this.headers });\r\n  }\r\n\r\n  update(url: string, data: any, id: string) {\r\n    return this.http.put<any>(\r\n      `${url}/${id}`,\r\n      { data },\r\n      { headers: this.headers }\r\n    );\r\n  }\r\n\r\n  delete(url: string, id: string) {\r\n    return this.http.delete<any>(`${url}/${id}`, { headers: this.headers });\r\n  }\r\n\r\n  saveSetting(tab: any, data: any) {\r\n    return this.http.put<any>(\r\n      `${tab.url}/${tab.data.documentId}`,\r\n      { data },\r\n      { headers: this.headers }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAoB,sBAAsB;AAE1E,SAASC,eAAe,EAAcC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;;;AAK5E,OAAM,MAAOC,oBAAoB;EAW/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAVhB,KAAAC,MAAM,GACZ,iFAAiF;IAC3E,KAAAC,OAAO,GAAG,IAAIT,WAAW,CAAC;MAChCU,aAAa,EACX,yQAAyQ,CAAE;KAC9Q,CAAC;IAEK,KAAAC,eAAe,GAAG,IAAIV,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAW,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEd;EAEvCC,aAAaA,CAAA;IACX,OAAO,CACL;MACEC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,kBAAkB;QACtBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,kBAAkB;QACzBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,gBAAgB;QAC5BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,eAAe;QACnBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,mBAAmB;QAC1BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,cAAc;QAC1BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,oBAAoB;QACxBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,oBAAoB;QAC3BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,mBAAmB;QAC/BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ;KAEJ,EACD;MACEjB,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,gBAAgB;QACpBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,qBAAqB;QAC5BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,cAAc;QAC1BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,aAAa;QACjBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,mBAAmB;QAC1BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,YAAY;QACxBC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,kBAAkB;UACxBC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,eAAe;UACzBC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE;SACP;OAEJ,EACD;QACEhB,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,sBAAsB;QAC1BC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,sBAAsB;QAC7BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdS,IAAI,EAAE,aAAa;QACnBR,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE,EAAE;UACbE,MAAM,EAAE;SACT,EACD;UACEN,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE,EAAE;UACbE,MAAM,EAAE;SACT;OAEJ;KAEJ,EACD;MACEnB,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,oBAAoB;QACxBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,sBAAsB;QAC7BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,UAAU;QACdC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,UAAU;QACjBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,cAAc;UACxBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,aAAa;UACvBC,SAAS,EAAE,EAAE;UACbC,IAAI,EAAE,IAAI;UACVE,OAAO,EAAE,CACP;YACEP,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE;WACR,CACF;UACDO,QAAQ,EAAE;SACX;QACD;QACA;UACER,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAE,gBAAgB;UACvBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,EAAE;UACbC,IAAI,EAAE,OAAO;UACbE,OAAO,EAAE,CACP;YACEP,IAAI,EAAE,QAAQ;YACdC,KAAK,EAAE;WACR,EACD;YACED,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE;WACR;SAEJ;OAEJ,EACD;QACEZ,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,YAAY;QAChBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,YAAY;QACnBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,GAAG,IAAI,CAACjB,MAAM,EAAE;QACrBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,iBAAiB;UACvBC,KAAK,EAAE,oBAAoB;UAC3BC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,EAAE;UACbC,IAAI,EAAE,IAAI;UACVE,OAAO,EAAE,EAAE;UACXC,QAAQ,EAAE;SACX;OAEJ,EACD;QACEnB,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,OAAO;QACXC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,OAAO;QACdQ,IAAI,EAAE,EAAE;QACRU,IAAI,EAAE,aAAa;QACnBT,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,eAAe;UACtBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,kBAAkB;UAC5BC,SAAS,EAAE,CAAC;UACZC,IAAI,EAAE;SACP;OAEJ,EACD;QACEhB,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,cAAc;QAClBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,KAAK;QACfP,KAAK,EAAE,eAAe;QACtBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,cAAc;QAC1BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE,EAAE;UACbK,QAAQ,EAAE;SACX,EACD;UACET,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE,EAAE;UACbK,QAAQ,EAAE;SACX,EACD;UACET,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE;SACP;OAEJ,EACD;QACEhB,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,YAAY;QAChBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,YAAY;QACnBQ,IAAI,EAAE,EAAE;QACRU,IAAI,EAAE,YAAY;QAClBT,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,iBAAiB;UACxBE,QAAQ,EAAE,oBAAoB;UAC9BE,IAAI,EAAE,QAAQ;UACdD,SAAS,EAAE,EAAE;UACbM,SAAS,EAAE;SACZ,EACD;UACEV,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,UAAU;UACjBE,QAAQ,EAAE,aAAa;UACvBE,IAAI,EAAE,QAAQ;UACdD,SAAS,EAAE,EAAE;UACbM,SAAS,EAAE;SACZ,EACD;UACEV,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,eAAe;UACtBE,QAAQ,EAAE,kBAAkB;UAC5BE,IAAI,EAAE,QAAQ;UACdD,SAAS,EAAE,EAAE;UACbM,SAAS,EAAE;SACZ,EACD;UACEV,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,eAAe;UACtBE,QAAQ,EAAE,kBAAkB;UAC5BE,IAAI,EAAE,QAAQ;UACdD,SAAS,EAAE,EAAE;UACbM,SAAS,EAAE;SACZ;OAEJ;KAEJ,EACD;MACEvB,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,iBAAiB;QACrBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,eAAe;QACtBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,eAAe;QAC3BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ;KAEJ,EACD;MACEjB,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,gBAAgB;QACpBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,cAAc;QACrBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,cAAc;QAC1BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,iBAAiB;QACrBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,iBAAiB;QACxBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdS,IAAI,EAAE,aAAa;QACnBR,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,iBAAiB;UACvBC,KAAK,EAAE,iBAAiB;UACxBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,eAAe;UACzBC,SAAS,EAAE,CAAC;UACZC,IAAI,EAAE;SACP;OAEJ,EACD;QACEhB,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,wBAAwB;QAC5BC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,kBAAkB;QACzBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdS,IAAI,EAAE,aAAa;QACnBR,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,mBAAmB;QACvBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,mBAAmB;QAC1BQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdS,IAAI,EAAE,aAAa;QACnBR,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE,EAAE;UACbE,MAAM,EAAE;SACT,EACD;UACEN,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE,EAAE;UACbE,MAAM,EAAE;SACT;OAEJ;KAEJ,EACD;MACEnB,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,eAAe;QACnBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,eAAe;QACtBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,eAAe;QAC3BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,iBAAiB;QACrBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,iBAAiB;QACxBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,wBAAwB;QACpCC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE,EAAE;UACbE,MAAM,EAAE;SACT,EACD;UACEN,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,yBAAyB;QAC7BC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,kBAAkB;QACzBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdS,IAAI,EAAE,aAAa;QACnBR,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBmB,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ,EACD;QACEf,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,eAAe;QACnBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,eAAe;QACtBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,eAAe;QAC3BC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ;KAEJ,EACD;MACEjB,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAE,CACP;QACEC,SAAS,EAAE,KAAK;QAChBC,EAAE,EAAE,gBAAgB;QACpBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdP,KAAK,EAAE,WAAW;QAClBQ,IAAI,EAAE,EAAE;QACRC,OAAO,EAAE,KAAK;QACdC,GAAG,EAAE,IAAI,CAACjB,MAAM;QAChBkB,UAAU,EAAE,oBAAoB;QAChCC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,SAAS;UACnBC,SAAS,EAAE;SACZ,EACD;UACEJ,IAAI,EAAE,aAAa;UACnBC,KAAK,EAAE,aAAa;UACpBC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,gBAAgB;UAC1BC,SAAS,EAAE;SACZ;OAEJ;KAEJ,CACF;EACH;EAEAO,MAAMA,CAACC,IAAgB;IACrB,MAAMC,GAAG,GAAQ,EAAE;IACnBC,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAGH,IAAI,CAACN,MAAM,CAAC;IACnC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACN,MAAM,EAAEU,CAAC,EAAE,EAAE;MACpC,MAAMC,UAAU,GAAGL,IAAI,CAACI,CAAC,CAAC;MAC1B,IAAInB,GAAG,GAAGoB,UAAU,CAACpB,GAAG;MACxB,IAAIoB,UAAU,CAACpB,GAAG,CAACqB,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACjDrB,GAAG,GAAG,GAAGA,GAAG,aAAa;MAC3B,CAAC,MAAM,IAAIoB,UAAU,CAACpB,GAAG,CAACqB,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACtDrB,GAAG,GAAG,GAAGA,GAAG,aAAa;MAC3B;MACAgB,GAAG,CAACI,UAAU,CAAC3B,EAAE,CAAC,GAAG,IAAI,CAAC6B,GAAG,CAACtB,GAAG,CAAC,CAACuB,IAAI,CAAC9C,UAAU,CAAE+C,KAAK,IAAK7C,EAAE,CAAC6C,KAAK,CAAC,CAAC,CAAC;IAC3E;IACA,OAAO9C,QAAQ,CAACsC,GAAG,CAAC;EACtB;EAEAM,GAAGA,CAACtB,GAAW;IACb,OAAO,IAAI,CAAClB,IAAI,CAACwC,GAAG,CAAMtB,GAAG,EAAE;MAAEhB,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;EAC3D;EAEAyC,IAAIA,CAACzB,GAAW,EAAEF,IAAS;IACzB,OAAO,IAAI,CAAChB,IAAI,CAAC4C,IAAI,CAAM1B,GAAG,EAAE;MAAEF;IAAI,CAAE,EAAE;MAAEd,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;EACtE;EAEA2C,MAAMA,CAAC3B,GAAW,EAAEF,IAAS,EAAEL,EAAU;IACvC,OAAO,IAAI,CAACX,IAAI,CAAC8C,GAAG,CAClB,GAAG5B,GAAG,IAAIP,EAAE,EAAE,EACd;MAAEK;IAAI,CAAE,EACR;MAAEd,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;EACH;EAEA6C,MAAMA,CAAC7B,GAAW,EAAEP,EAAU;IAC5B,OAAO,IAAI,CAACX,IAAI,CAAC+C,MAAM,CAAM,GAAG7B,GAAG,IAAIP,EAAE,EAAE,EAAE;MAAET,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC;EACzE;EAEA8C,WAAWA,CAACC,GAAQ,EAAEjC,IAAS;IAC7B,OAAO,IAAI,CAAChB,IAAI,CAAC8C,GAAG,CAClB,GAAGG,GAAG,CAAC/B,GAAG,IAAI+B,GAAG,CAACjC,IAAI,CAACkC,UAAU,EAAE,EACnC;MAAElC;IAAI,CAAE,EACR;MAAEd,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;EACH;;;uBA5xBWJ,oBAAoB,EAAAqD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBxD,oBAAoB;MAAAyD,OAAA,EAApBzD,oBAAoB,CAAA0D,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}