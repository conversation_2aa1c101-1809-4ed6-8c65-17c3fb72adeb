{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/shared/services/content-vendor.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/button\";\nfunction ResourceCenterComponent_div_4_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"img\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"h4\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"p-button\", 21);\n    i0.ɵɵlistener(\"click\", function ResourceCenterComponent_div_4_div_7_div_2_Template_p_button_click_10_listener() {\n      const detail_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.downloadFile((detail_r4.File == null ? null : detail_r4.File.url) || \"\"));\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const detail_r4 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(detail_r4.Title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(detail_r4.Description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction ResourceCenterComponent_div_4_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, ResourceCenterComponent_div_4_div_7_div_2_Template, 11, 3, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", section_r2.Resource_detail);\n  }\n}\nfunction ResourceCenterComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"h3\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function ResourceCenterComponent_div_4_Template_button_click_4_listener() {\n      const section_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      return i0.ɵɵresetView(section_r2.open = !section_r2.open);\n    });\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6, \"keyboard_arrow_down\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, ResourceCenterComponent_div_4_div_7_Template, 3, 1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const section_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(section_r2.Title);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", section_r2.open);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", section_r2.open);\n  }\n}\nexport class ResourceCenterComponent {\n  constructor(route, CMSservice) {\n    this.route = route;\n    this.CMSservice = CMSservice;\n  }\n  ngOnInit() {\n    const content = this.route.snapshot.data['content'];\n    this.content = this.CMSservice.getDataByComponentName(content.body, \"vendor.resource-section\");\n  }\n  downloadFile(url) {\n    const anchor = document.createElement('a');\n    anchor.href = url;\n    anchor.download = \"Resource\";\n    document.body.appendChild(anchor);\n    anchor.click();\n    document.body.removeChild(anchor);\n  }\n  static {\n    this.ɵfac = function ResourceCenterComponent_Factory(t) {\n      return new (t || ResourceCenterComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContentVendorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResourceCenterComponent,\n      selectors: [[\"app-resource-center\"]],\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-5\"], [1, \"m-0\"], [\"class\", \"card shadow-1 p-4 h-full flex flex-column gap-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\", \"shadow-1\", \"p-4\", \"h-full\", \"flex\", \"flex-column\", \"gap-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"block\", \"font-bold\", \"text-xl\", \"m-0\", \"text-primary\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\", 3, \"click\"], [1, \"material-symbols-rounded\"], [\"class\", \"resource-center-box mt-2 pt-4 border-solid border-black-alpha-20 border-none border-top-1\", 4, \"ngIf\"], [1, \"resource-center-box\", \"mt-2\", \"pt-4\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"resource-center-list\", \"grid\", \"relative\"], [\"class\", \"col-12 lg:col-4 md:col-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"lg:col-4\", \"md:col-6\"], [1, \"card\", \"flex\", \"align-items-start\", \"gap-3\", \"m-0\", \"p-3\", \"overflow-hidden\", \"border-1\", \"border-solid\", \"surface-border\", \"hover:shadow-1\", \"h-full\"], [1, \"icon-box\", \"w-8rem\", \"flex\", \"align-items-center\", \"justify-content-start\"], [\"src\", \"assets/layout/images/pdf.svg\", \"alt\", \"\", 1, \"w-4rem\"], [1, \"content\", \"h-full\", \"flex\", \"flex-column\", \"justify-content-between\"], [1, \"mb-2\"], [1, \"mb-4\"], [1, \"flex\", \"justify-content-end\", \"align-items-end\", \"flex-grow-1\"], [\"label\", \"Download\", \"severity\", \"primary\", \"icon\", \"pi pi-file-pdf\", 3, \"click\", \"outlined\"]],\n      template: function ResourceCenterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"Resource Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, ResourceCenterComponent_div_4_Template, 8, 4, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.content);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.Button],\n      styles: [\".resource-center-list[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border-left: 7px solid var(--indigo-200) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvcmVzb3VyY2UtY2VudGVyL3Jlc291cmNlLWNlbnRlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNJLG1EQUFBO0FBQVIiLCJzb3VyY2VzQ29udGVudCI6WyIucmVzb3VyY2UtY2VudGVyLWxpc3Qge1xyXG4gICAgLmNhcmQge1xyXG4gICAgICAgIGJvcmRlci1sZWZ0OiA3cHggc29saWQgdmFyKC0taW5kaWdvLTIwMCkgIWltcG9ydGFudDtcclxuICAgIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "ResourceCenterComponent_div_4_div_7_div_2_Template_p_button_click_10_listener", "detail_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "downloadFile", "File", "url", "ɵɵadvance", "ɵɵtextInterpolate", "Title", "Description", "ɵɵproperty", "ɵɵtemplate", "ResourceCenterComponent_div_4_div_7_div_2_Template", "section_r2", "Resource_detail", "ResourceCenterComponent_div_4_Template_button_click_4_listener", "_r1", "open", "ResourceCenterComponent_div_4_div_7_Template", "ɵɵclassProp", "ResourceCenterComponent", "constructor", "route", "CMSservice", "ngOnInit", "content", "snapshot", "data", "getDataByComponentName", "body", "anchor", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ContentVendorService", "selectors", "decls", "vars", "consts", "template", "ResourceCenterComponent_Template", "rf", "ctx", "ResourceCenterComponent_div_4_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\resource-center\\resource-center.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\resource-center\\resource-center.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { ContentVendorService } from 'src/app/shared/services/content-vendor.service';\r\n\r\n@Component({\r\n  selector: 'app-resource-center',\r\n  templateUrl: './resource-center.component.html',\r\n  styleUrl: './resource-center.component.scss'\r\n})\r\nexport class ResourceCenterComponent implements OnInit {\r\n\r\n  content!: any;\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentVendorService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    const content = this.route.snapshot.data['content'];\r\n    this.content = this.CMSservice.getDataByComponentName(content.body, \"vendor.resource-section\");\r\n  }\r\n\r\n  downloadFile(url: string) {\r\n    const anchor = document.createElement('a');\r\n    anchor.href = url;\r\n    anchor.download = \"Resource\";\r\n    document.body.appendChild(anchor);\r\n    anchor.click();\r\n    document.body.removeChild(anchor);\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-4 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-5\">\r\n        <h3 class=\"m-0\">Resource Center</h3>\r\n    </div>\r\n    <div class=\"card shadow-1 p-4 h-full flex flex-column gap-3\" *ngFor=\"let section of content\">\r\n        <div class=\"flex align-items-center justify-content-between\">\r\n            <h3 class=\"block font-bold text-xl m-0 text-primary\">{{ section.Title }}</h3>\r\n            <button (click)=\"section.open = !section.open\"\r\n                class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\"\r\n                [class.active]=\"section.open\">\r\n                <span class='material-symbols-rounded'>keyboard_arrow_down</span>\r\n            </button>\r\n        </div>\r\n        <div *ngIf=\"section.open\" class=\"resource-center-box mt-2 pt-4 border-solid border-black-alpha-20 border-none border-top-1\">\r\n            <div class=\"resource-center-list grid relative\">\r\n                <div class=\"col-12 lg:col-4 md:col-6\" *ngFor=\"let detail of section.Resource_detail\">\r\n                    <div\r\n                        class=\"card flex align-items-start gap-3 m-0 p-3 overflow-hidden border-1 border-solid surface-border hover:shadow-1 h-full\">\r\n                        <div class=\"icon-box w-8rem flex align-items-center justify-content-start\">\r\n                            <img src=\"assets/layout/images/pdf.svg\" class=\"w-4rem\" alt=\"\" />\r\n                        </div>\r\n        \r\n                        <div class=\"content h-full flex flex-column justify-content-between\">\r\n                            <h4 class=\"mb-2\">{{ detail.Title }}</h4>\r\n                            <p class=\"mb-4\">{{ detail.Description }}</p>\r\n                            <div class=\"flex justify-content-end align-items-end flex-grow-1\"><p-button label=\"Download\" (click)=\"downloadFile(detail.File?.url || '')\"\r\n                                    severity=\"primary\" icon=\"pi pi-file-pdf\" [outlined]=\"true\" /></div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;ICkBwBA,EAHR,CAAAC,cAAA,cAAqF,cAEgD,cAClD;IACvED,EAAA,CAAAE,SAAA,cAAgE;IACpEF,EAAA,CAAAG,YAAA,EAAM;IAGFH,EADJ,CAAAC,cAAA,cAAqE,aAChD;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAI,MAAA,GAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACsBH,EAAlE,CAAAC,cAAA,cAAkE,oBACG;IADwBD,EAAA,CAAAK,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAAP,SAAA,CAAAQ,IAAA,kBAAAR,SAAA,CAAAQ,IAAA,CAAAC,GAAA,KAAiC,EAAE,CAAC;IAAA,EAAC;IAIvJhB,EAJ8E,CAAAG,YAAA,EACG,EAAM,EACzE,EACJ,EACJ;;;;IANuBH,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,iBAAA,CAAAX,SAAA,CAAAY,KAAA,CAAkB;IACnBnB,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAkB,iBAAA,CAAAX,SAAA,CAAAa,WAAA,CAAwB;IAESpB,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAqB,UAAA,kBAAiB;;;;;IAZlFrB,EADJ,CAAAC,cAAA,cAA4H,cACxE;IAC5CD,EAAA,CAAAsB,UAAA,IAAAC,kDAAA,mBAAqF;IAgB7FvB,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAhB2DH,EAAA,CAAAiB,SAAA,GAA0B;IAA1BjB,EAAA,CAAAqB,UAAA,YAAAG,UAAA,CAAAC,eAAA,CAA0B;;;;;;IATvFzB,EAFR,CAAAC,cAAA,aAA6F,aAC5B,YACJ;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,gBAEkC;IAF1BD,EAAA,CAAAK,UAAA,mBAAAqB,+DAAA;MAAA,MAAAF,UAAA,GAAAxB,EAAA,CAAAQ,aAAA,CAAAmB,GAAA,EAAAjB,SAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAAW,UAAA,CAAAI,IAAA,IAAAJ,UAAA,CAAAI,IAAA;IAAA,EAAsC;IAG1C5B,EAAA,CAAAC,cAAA,cAAuC;IAAAD,EAAA,CAAAI,MAAA,0BAAmB;IAElEJ,EAFkE,CAAAG,YAAA,EAAO,EAC5D,EACP;IACNH,EAAA,CAAAsB,UAAA,IAAAO,4CAAA,iBAA4H;IAmBhI7B,EAAA,CAAAG,YAAA,EAAM;;;;IA1BuDH,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,iBAAA,CAAAM,UAAA,CAAAL,KAAA,CAAmB;IAGpEnB,EAAA,CAAAiB,SAAA,EAA6B;IAA7BjB,EAAA,CAAA8B,WAAA,WAAAN,UAAA,CAAAI,IAAA,CAA6B;IAI/B5B,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAqB,UAAA,SAAAG,UAAA,CAAAI,IAAA,CAAkB;;;ADJhC,OAAM,MAAOG,uBAAuB;EAGlCC,YACUC,KAAqB,EACrBC,UAAgC;IADhC,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;EAChB;EAEJC,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IACnD,IAAI,CAACF,OAAO,GAAG,IAAI,CAACF,UAAU,CAACK,sBAAsB,CAACH,OAAO,CAACI,IAAI,EAAE,yBAAyB,CAAC;EAChG;EAEA1B,YAAYA,CAACE,GAAW;IACtB,MAAMyB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC1CF,MAAM,CAACG,IAAI,GAAG5B,GAAG;IACjByB,MAAM,CAACI,QAAQ,GAAG,UAAU;IAC5BH,QAAQ,CAACF,IAAI,CAACM,WAAW,CAACL,MAAM,CAAC;IACjCA,MAAM,CAACM,KAAK,EAAE;IACdL,QAAQ,CAACF,IAAI,CAACQ,WAAW,CAACP,MAAM,CAAC;EACnC;;;uBApBWV,uBAAuB,EAAA/B,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnD,EAAA,CAAAiD,iBAAA,CAAAG,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAvBtB,uBAAuB;MAAAuB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5B5D,EAFR,CAAAC,cAAA,aAA2E,aACc,YACjE;UAAAD,EAAA,CAAAI,MAAA,sBAAe;UACnCJ,EADmC,CAAAG,YAAA,EAAK,EAClC;UACNH,EAAA,CAAAsB,UAAA,IAAAwC,sCAAA,iBAA6F;UA6BjG9D,EAAA,CAAAG,YAAA,EAAM;;;UA7B+EH,EAAA,CAAAiB,SAAA,GAAU;UAAVjB,EAAA,CAAAqB,UAAA,YAAAwC,GAAA,CAAAzB,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}