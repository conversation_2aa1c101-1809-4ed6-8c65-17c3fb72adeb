{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup, FormControl, Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../product.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"src/app/shared/pipes/group-by.pipe\";\nfunction MediaComponent_div_15_div_5_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Document Name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MediaComponent_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, MediaComponent_div_15_div_5_small_1_Template, 2, 0, \"small\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.media_name.errors == null ? null : ctx_r0.f.media_name.errors[\"required\"]);\n  }\n}\nfunction MediaComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"span\", 3);\n    i0.ɵɵtext(2, \"Document Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 4);\n    i0.ɵɵelement(4, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MediaComponent_div_15_div_5_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.f.media_name == null ? null : ctx_r0.f.media_name.touched) && ctx_r0.f.media_name.hasError(\"required\"));\n  }\n}\nfunction MediaComponent_div_16_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 26);\n    i0.ɵɵtext(1, \" 96X96 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MediaComponent_div_16_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 27);\n    i0.ɵɵtext(1, \" 300X300 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MediaComponent_div_16_div_11_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Dimension is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MediaComponent_div_16_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, MediaComponent_div_16_div_11_small_1_Template, 2, 0, \"small\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.media_type.errors == null ? null : ctx_r0.f.media_type.errors[\"required\"]);\n  }\n}\nfunction MediaComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"span\", 3);\n    i0.ɵɵtext(2, \"Select dimenssion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 4)(4, \"select\", 21)(5, \"option\", 22);\n    i0.ɵɵtext(6, \"Select Dimenssion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, MediaComponent_div_16_option_7_Template, 2, 0, \"option\", 23)(8, MediaComponent_div_16_option_8_Template, 2, 0, \"option\", 24);\n    i0.ɵɵelementStart(9, \"option\", 25);\n    i0.ɵɵtext(10, \"1200X1200\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, MediaComponent_div_16_div_11_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.mediaForm.get(\"doc_type\")) == null ? null : tmp_1_0.value) === \"IMAGE\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.mediaForm.get(\"doc_type\")) == null ? null : tmp_2_0.value) === \"IMAGE\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.f.media_type == null ? null : ctx_r0.f.media_type.touched) && ctx_r0.f.media_type.hasError(\"required\"));\n  }\n}\nfunction MediaComponent_div_22_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Url is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MediaComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, MediaComponent_div_22_small_1_Template, 2, 0, \"small\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f.image.errors == null ? null : ctx_r0.f.image.errors[\"required\"]);\n  }\n}\nfunction MediaComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"span\", 3);\n    i0.ɵɵtext(2, \"Cover Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 4);\n    i0.ɵɵelement(4, \"input\", 28);\n    i0.ɵɵelementStart(5, \"label\", 29);\n    i0.ɵɵtext(6, \" Mark as cover image \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MediaComponent_div_28_ng_container_1_div_3_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 37);\n    i0.ɵɵtext(1, \"Cover Image\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MediaComponent_div_28_ng_container_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function MediaComponent_div_28_ng_container_1_div_3_Template_a_click_3_listener() {\n      const imgv_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.removeImg(imgv_r3.documentId));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MediaComponent_div_28_ng_container_1_div_3_p_5_Template, 2, 0, \"p\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imgv_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.trustUrl(imgv_r3.url), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", imgv_r3.is_cover_image);\n  }\n}\nfunction MediaComponent_div_28_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MediaComponent_div_28_ng_container_1_div_3_Template, 6, 2, \"div\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const img_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", img_r4.key, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", img_r4.value);\n  }\n}\nfunction MediaComponent_div_28_ng_container_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"a\", 35);\n    i0.ɵɵlistener(\"click\", function MediaComponent_div_28_ng_container_2_div_3_Template_a_click_3_listener() {\n      const imgv_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.removeImg(imgv_r6.documentId));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const imgv_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.trustUrl(imgv_r6.url), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MediaComponent_div_28_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MediaComponent_div_28_ng_container_2_div_3_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const img_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", img_r4.key, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", img_r4.value);\n  }\n}\nfunction MediaComponent_div_28_ng_container_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵelementStart(3, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function MediaComponent_div_28_ng_container_3_div_3_Template_a_click_3_listener() {\n      const imgv_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.removeImg(imgv_r8.documentId));\n    });\n    i0.ɵɵtext(4, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const imgv_r8 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(imgv_r8.media_name);\n  }\n}\nfunction MediaComponent_div_28_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MediaComponent_div_28_ng_container_3_div_3_Template, 7, 1, \"div\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const img_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", img_r4.key, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", img_r4.value);\n  }\n}\nfunction MediaComponent_div_28_ng_container_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 33)(2, \"a\", 40);\n    i0.ɵɵlistener(\"click\", function MediaComponent_div_28_ng_container_4_div_3_Template_a_click_2_listener() {\n      const imgv_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.removeImg(imgv_r10.documentId));\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const imgv_r10 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(imgv_r10.media_name);\n  }\n}\nfunction MediaComponent_div_28_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MediaComponent_div_28_ng_container_4_div_3_Template, 6, 1, \"div\", 32);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const img_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", img_r4.key, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", img_r4.value);\n  }\n}\nfunction MediaComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, MediaComponent_div_28_ng_container_1_Template, 4, 2, \"ng-container\", 20)(2, MediaComponent_div_28_ng_container_2_Template, 4, 2, \"ng-container\", 20)(3, MediaComponent_div_28_ng_container_3_Template, 4, 2, \"ng-container\", 20)(4, MediaComponent_div_28_ng_container_4_Template, 4, 2, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const img_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", img_r4.key === \"IMAGE\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", img_r4.key === \"SPECIFICATION\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", img_r4.key === \"VIDEO\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", img_r4.key === \"PDF\");\n  }\n}\nexport class MediaComponent {\n  constructor(fb, productservice, messageservice) {\n    this.fb = fb;\n    this.productservice = productservice;\n    this.messageservice = messageservice;\n    this.mediamodel = null;\n    this.unsubscribe$ = new Subject();\n    this.onUpdate = new EventEmitter();\n    this.subject = new Subject();\n    this.refreshing = false;\n    this.mediaForm = this.fb.group({\n      product_id: '',\n      media_name: [''],\n      doc_type: ['IMAGE', Validators.required],\n      media_type: ['', Validators.required],\n      image: ['', Validators.required],\n      is_cover_image: [false]\n    });\n  }\n  ngOnInit() {\n    this.productservice.product.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.mediamodel = data;\n      this.updateData();\n    });\n  }\n  updateData() {\n    this.mediaForm.patchValue(this.mediamodel);\n  }\n  removeImg(documentId) {\n    this.productservice.removeMedia(documentId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'info',\n          detail: 'Media Removed Successfully!'\n        });\n        this.subject.next(0);\n        this.refresh();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  submitMediaForm() {\n    if (this.mediaForm.valid) {\n      const formValue = this.mediaForm.value;\n      const obj = {\n        product_id: formValue.product_id,\n        media_name: formValue.media_name,\n        dimension: formValue.media_type,\n        media_type: formValue.doc_type,\n        url: formValue.image,\n        is_cover_image: formValue.is_cover_image,\n        product: {\n          connect: [this.mediamodel.documentId]\n        }\n      };\n      this.productservice.submitMediaForm(obj).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: res => {\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Media Saved Successfully!'\n          });\n          this.mediaForm.patchValue({\n            media_name: '',\n            media_type: '',\n            image: '',\n            is_cover_image: false\n          });\n          this.mediaForm.markAsUntouched();\n          this.subject.next(0);\n          this.refresh();\n        },\n        error: () => {\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n  }\n  changeDocType(e) {\n    this.mediaForm.patchValue({\n      media_name: '',\n      media_type: '',\n      image: '',\n      is_cover_image: false\n    });\n    if (this.mediaForm.controls.doc_type.value !== 'IMAGE' && this.mediaForm.controls.doc_type.value !== 'SPECIFICATION') {\n      this.mediaForm.controls.media_type.clearValidators();\n      this.mediaForm.controls.media_type.updateValueAndValidity();\n      this.mediaForm.controls.media_name.setValidators(Validators.required);\n      this.mediaForm.controls.media_name.updateValueAndValidity();\n    } else {\n      this.mediaForm.controls.media_type.setValidators(Validators.required);\n      this.mediaForm.controls.media_type.updateValueAndValidity();\n      this.mediaForm.controls.media_name.clearValidators();\n      this.mediaForm.controls.media_name.updateValueAndValidity();\n    }\n  }\n  _markAsTouched(group) {\n    group.markAsTouched({\n      onlySelf: true\n    });\n    Object.keys(group.controls).map(field => {\n      const control = group.get(field);\n      if (control instanceof FormControl) {\n        control.markAsTouched({\n          onlySelf: true\n        });\n      } else if (control instanceof FormGroup) {\n        this._markAsTouched(control);\n      }\n    });\n  }\n  trustUrl(url = '') {\n    return (url || '').replace(/\\(/g, '\\\\(').replace(/\\)/g, '\\\\)');\n  }\n  get f() {\n    return this.mediaForm.controls;\n  }\n  refresh() {\n    if (!this.mediamodel?.product_id) {\n      return;\n    }\n    this.refreshing = true;\n    this.productservice.getById(this.mediamodel.documentId).subscribe({\n      next: value => {\n        this.refreshing = false;\n        this.mediamodel = value.data;\n        this.onUpdate.emit(this.mediamodel);\n        this.updateData();\n      },\n      error: err => {\n        this.refreshing = false;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function MediaComponent_Factory(t) {\n      return new (t || MediaComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MediaComponent,\n      selectors: [[\"app-media\"]],\n      outputs: {\n        onUpdate: \"onUpdate\"\n      },\n      decls: 30,\n      vars: 9,\n      consts: [[3, \"formGroup\"], [1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-3\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\", \"p-fluid\"], [\"pInputText\", \"\", \"formControlName\", \"doc_type\", 1, \"dropdown-class\", 3, \"change\"], [\"value\", \"IMAGE\"], [\"value\", \"PDF\"], [\"value\", \"VIDEO\"], [\"value\", \"SPECIFICATION\"], [\"class\", \"col-12 lg:col-3\", 4, \"ngIf\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"image\", 1, \"form-control\"], [\"class\", \"text-error\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-10\"], [1, \"block\", \"font-large\", \"mb-3\", \"text-600\", \"p-custom-button\"], [\"type\", \"submit\", \"label\", \"SUBMIT\", 1, \"p-button-primary\", 3, \"click\"], [1, \"col-12\", \"lg:col-12\"], [\"class\", \"d-flex gap-3 my-3\", 4, \"ngFor\", \"ngForOf\"], [\"pInputText\", \"\", \"type\", \"text\", \"formControlName\", \"media_name\", 1, \"form-control\"], [1, \"text-error\"], [4, \"ngIf\"], [\"pInputText\", \"\", \"formControlName\", \"media_type\", 1, \"dropdown-class\"], [\"value\", \"\"], [\"value\", \"96X96\", 4, \"ngIf\"], [\"value\", \"300X300\", 4, \"ngIf\"], [\"value\", \"1200X1200\"], [\"value\", \"96X96\"], [\"value\", \"300X300\"], [\"type\", \"checkbox\", \"value\", \"\", \"id\", \"flexCheckChecked\", \"checked\", \"\", \"formControlName\", \"is_cover_image\", 1, \"form-check-input\"], [1, \"ml-2\"], [1, \"d-flex\", \"gap-3\", \"my-3\"], [1, \"image-dimenssion\", \"d-flex\", \"align-items-center\", \"fw-bold\"], [4, \"ngFor\", \"ngForOf\"], [1, \"thumbnail\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"p-2\", \"flex-column\", \"position-relative\", \"border\"], [\"width\", \"100\", 3, \"src\"], [1, \"remove-image\", \"d-none\", \"position-absolute\", 2, \"display\", \"inline !important\", 3, \"click\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"pi\", \"pi-video\", 2, \"font-size\", \"2em\"], [\"icon\", \"pi pi-video\", 1, \"remove-image\", \"d-none\", \"position-absolute\", 2, \"display\", \"inline !important\", 3, \"click\"], [\"icon\", \"pi pi-file-pdf\", 1, \"remove-image\", \"d-none\", \"position-absolute\", 2, \"display\", \"inline !important\", 3, \"click\"]],\n      template: function MediaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n          i0.ɵɵtext(4, \"Doc Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 4)(6, \"select\", 5);\n          i0.ɵɵlistener(\"change\", function MediaComponent_Template_select_change_6_listener($event) {\n            return ctx.changeDocType($event);\n          });\n          i0.ɵɵelementStart(7, \"option\", 6);\n          i0.ɵɵtext(8, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"option\", 7);\n          i0.ɵɵtext(10, \"PDF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"option\", 8);\n          i0.ɵɵtext(12, \"Video\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"option\", 9);\n          i0.ɵɵtext(14, \"Specification\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(15, MediaComponent_div_15_Template, 6, 1, \"div\", 10)(16, MediaComponent_div_16_Template, 12, 3, \"div\", 10);\n          i0.ɵɵelementStart(17, \"div\", 2)(18, \"span\", 3);\n          i0.ɵɵtext(19, \"Url\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\", 4);\n          i0.ɵɵelement(21, \"input\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, MediaComponent_div_22_Template, 2, 1, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, MediaComponent_div_23_Template, 7, 0, \"div\", 10);\n          i0.ɵɵelementStart(24, \"div\", 13)(25, \"span\", 14)(26, \"p-button\", 15);\n          i0.ɵɵlistener(\"click\", function MediaComponent_Template_p_button_click_26_listener() {\n            return ctx.submitMediaForm();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 16);\n          i0.ɵɵtemplate(28, MediaComponent_div_28_Template, 5, 4, \"div\", 17);\n          i0.ɵɵpipe(29, \"groupBy\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_4_0;\n          i0.ɵɵproperty(\"formGroup\", ctx.mediaForm);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.mediaForm.get(\"doc_type\")) == null ? null : tmp_1_0.value) !== \"IMAGE\" && ((tmp_1_0 = ctx.mediaForm.get(\"doc_type\")) == null ? null : tmp_1_0.value) !== \"SPECIFICATION\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.mediaForm.get(\"doc_type\")) == null ? null : tmp_2_0.value) === \"IMAGE\" || ((tmp_2_0 = ctx.mediaForm.get(\"doc_type\")) == null ? null : tmp_2_0.value) === \"SPECIFICATION\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", (ctx.f.image == null ? null : ctx.f.image.touched) && ctx.f.image.hasError(\"required\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.mediaForm.get(\"doc_type\")) == null ? null : tmp_4_0.value) === \"IMAGE\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(29, 6, ctx.mediamodel == null ? null : ctx.mediamodel.medias, \"media_type\"));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i5.Button, i6.InputText, i1.FormGroupDirective, i1.FormControlName, i7.GroupByPipe],\n      styles: [\".dropdown-class[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #333;\\n}\\n\\n.dropdown-class[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n  color: #007bff;\\n}\\n\\n.text-error[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n.p-custom-button[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: flex-end;\\n}\\n\\n.remove-image[_ngcontent-%COMP%] {\\n  font-size: 20px !important;\\n  padding: 5px !important;\\n}\\n\\n.d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.image-dimenssion[_ngcontent-%COMP%] {\\n  width: 120px;\\n}\\n\\n.thumbnail[_ngcontent-%COMP%] {\\n  background-color: white;\\n  width: 116px;\\n  height: 116px;\\n  border-radius: 10px;\\n}\\n.thumbnail[_ngcontent-%COMP%]    > i[_ngcontent-%COMP%] {\\n  font-size: var(--snyj-font-size-5);\\n  margin: 30px;\\n}\\n.thumbnail[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: auto;\\n  max-height: 100%;\\n  aspect-ratio: auto;\\n  width: auto;\\n  max-width: 100%;\\n}\\n\\n.remove-image[_ngcontent-%COMP%] {\\n  top: -10px;\\n  right: -10px;\\n  border-radius: 10em;\\n  padding: 2px 6px 3px;\\n  text-decoration: none;\\n  font: 700 21px/20px sans-serif;\\n  background: #555;\\n  border: 3px solid var(--snjy-color-white);\\n  color: var(--snjy-color-white);\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5), inset 0 2px 4px rgba(0, 0, 0, 0.3);\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\\n  transition: background 0.5s;\\n}\\n\\n.remove-image[_ngcontent-%COMP%]:hover {\\n  background: #E54E4E;\\n  padding: 3px 7px 5px;\\n  top: -11px;\\n  right: -11px;\\n}\\n\\n.remove-image[_ngcontent-%COMP%]:active {\\n  background: #E54E4E;\\n  top: -10px;\\n  right: -11px;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n.form-check[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  padding: 2px 0 0 !important;\\n}\\n.form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\" !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FormGroup", "FormControl", "Validators", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "MediaComponent_div_15_div_5_small_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "f", "media_name", "errors", "ɵɵelement", "MediaComponent_div_15_div_5_Template", "touched", "<PERSON><PERSON><PERSON><PERSON>", "MediaComponent_div_16_div_11_small_1_Template", "media_type", "MediaComponent_div_16_option_7_Template", "MediaComponent_div_16_option_8_Template", "MediaComponent_div_16_div_11_Template", "tmp_1_0", "mediaForm", "get", "value", "tmp_2_0", "MediaComponent_div_22_small_1_Template", "image", "ɵɵlistener", "MediaComponent_div_28_ng_container_1_div_3_Template_a_click_3_listener", "imgv_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "removeImg", "documentId", "MediaComponent_div_28_ng_container_1_div_3_p_5_Template", "trustUrl", "url", "ɵɵsanitizeUrl", "is_cover_image", "ɵɵelementContainerStart", "MediaComponent_div_28_ng_container_1_div_3_Template", "ɵɵtextInterpolate1", "img_r4", "key", "MediaComponent_div_28_ng_container_2_div_3_Template_a_click_3_listener", "imgv_r6", "_r5", "MediaComponent_div_28_ng_container_2_div_3_Template", "MediaComponent_div_28_ng_container_3_div_3_Template_a_click_3_listener", "imgv_r8", "_r7", "ɵɵtextInterpolate", "MediaComponent_div_28_ng_container_3_div_3_Template", "MediaComponent_div_28_ng_container_4_div_3_Template_a_click_2_listener", "imgv_r10", "_r9", "MediaComponent_div_28_ng_container_4_div_3_Template", "MediaComponent_div_28_ng_container_1_Template", "MediaComponent_div_28_ng_container_2_Template", "MediaComponent_div_28_ng_container_3_Template", "MediaComponent_div_28_ng_container_4_Template", "MediaComponent", "constructor", "fb", "productservice", "messageservice", "mediamodel", "unsubscribe$", "onUpdate", "subject", "refreshing", "group", "product_id", "doc_type", "required", "ngOnInit", "product", "pipe", "subscribe", "data", "updateData", "patchValue", "removeMedia", "next", "add", "severity", "detail", "refresh", "error", "submitMediaForm", "valid", "formValue", "obj", "dimension", "connect", "res", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDocType", "e", "controls", "clearValidators", "updateValueAndValidity", "setValidators", "_markAsTouched", "<PERSON><PERSON><PERSON><PERSON>ched", "onlySelf", "Object", "keys", "map", "field", "control", "replace", "getById", "emit", "err", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "MessageService", "selectors", "outputs", "decls", "vars", "consts", "template", "MediaComponent_Template", "rf", "ctx", "MediaComponent_Template_select_change_6_listener", "$event", "MediaComponent_div_15_Template", "MediaComponent_div_16_Template", "MediaComponent_div_22_Template", "MediaComponent_div_23_Template", "MediaComponent_Template_p_button_click_26_listener", "MediaComponent_div_28_Template", "tmp_4_0", "ɵɵpipeBind2", "medias"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\media\\media.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\media\\media.component.html"], "sourcesContent": ["import { Component, Output, OnInit,EventEmitter } from '@angular/core';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormArray,\r\n  FormControl,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { ProductService } from '../../product.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\nexport interface ProductImage {\r\n  id?: number;\r\n  product_id?: string;\r\n  media_name?: string;\r\n  media_type?: string;\r\n  image?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-media',\r\n  templateUrl: './media.component.html',\r\n  styleUrl: './media.component.scss',\r\n})\r\nexport class MediaComponent implements OnInit {\r\n  public mediamodel: any = null;\r\n  private unsubscribe$ = new Subject<void>();\r\n  @Output() onUpdate = new EventEmitter<any>();\r\n  subject = new Subject();\r\n  refreshing = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private productservice: ProductService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  mediaForm = this.fb.group({\r\n    product_id: '',\r\n    media_name: [''],\r\n    doc_type: ['IMAGE', Validators.required],\r\n    media_type: ['', Validators.required],\r\n    image: ['', Validators.required],\r\n    is_cover_image: [false],\r\n  });\r\n\r\n  ngOnInit(): void {\r\n    this.productservice.product\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.mediamodel = data;\r\n        this.updateData();\r\n      });\r\n  }\r\n\r\n  updateData() {\r\n    this.mediaForm.patchValue(this.mediamodel);\r\n  }\r\n\r\n  removeImg(documentId: string) {\r\n    this.productservice\r\n      .removeMedia(documentId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'info',\r\n            detail: 'Media Removed Successfully!',\r\n          });\r\n          this.subject.next(0);\r\n          this.refresh();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  submitMediaForm() {\r\n    if (this.mediaForm.valid) {\r\n      const formValue = this.mediaForm.value;\r\n      const obj = {\r\n        product_id: formValue.product_id,\r\n        media_name: formValue.media_name,\r\n        dimension: formValue.media_type,\r\n        media_type: formValue.doc_type,\r\n        url: formValue.image,\r\n        is_cover_image: formValue.is_cover_image,\r\n        product: {\r\n          connect: [this.mediamodel.documentId],\r\n        },\r\n      };\r\n      this.productservice\r\n        .submitMediaForm(obj)\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          next: (res) => {\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Media Saved Successfully!',\r\n            });\r\n            this.mediaForm.patchValue({\r\n              media_name: '',\r\n              media_type: '',\r\n              image: '',\r\n              is_cover_image: false,\r\n            });\r\n            this.mediaForm.markAsUntouched();\r\n            this.subject.next(0);\r\n            this.refresh();\r\n          },\r\n          error: () => {\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  changeDocType(e: any) {\r\n    this.mediaForm.patchValue({\r\n      media_name: '',\r\n      media_type: '',\r\n      image: '',\r\n      is_cover_image: false,\r\n    });\r\n    if (\r\n      this.mediaForm.controls.doc_type.value !== 'IMAGE' &&\r\n      this.mediaForm.controls.doc_type.value !== 'SPECIFICATION'\r\n    ) {\r\n      this.mediaForm.controls.media_type.clearValidators();\r\n      this.mediaForm.controls.media_type.updateValueAndValidity();\r\n      this.mediaForm.controls.media_name.setValidators(Validators.required);\r\n      this.mediaForm.controls.media_name.updateValueAndValidity();\r\n    } else {\r\n      this.mediaForm.controls.media_type.setValidators(Validators.required);\r\n      this.mediaForm.controls.media_type.updateValueAndValidity();\r\n      this.mediaForm.controls.media_name.clearValidators();\r\n      this.mediaForm.controls.media_name.updateValueAndValidity();\r\n    }\r\n  }\r\n\r\n  private _markAsTouched(group: FormGroup | FormArray) {\r\n    group.markAsTouched({ onlySelf: true });\r\n\r\n    Object.keys(group.controls).map((field) => {\r\n      const control = group.get(field);\r\n      if (control instanceof FormControl) {\r\n        control.markAsTouched({ onlySelf: true });\r\n      } else if (control instanceof FormGroup) {\r\n        this._markAsTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  trustUrl(url: string = '') {\r\n    return (url || '').replace(/\\(/g, '\\\\(').replace(/\\)/g, '\\\\)');\r\n  }\r\n\r\n  get f() {\r\n    return this.mediaForm.controls;\r\n  }\r\n  refresh() {\r\n    if (!this.mediamodel?.product_id) {\r\n      return;\r\n    }\r\n    this.refreshing = true;\r\n    this.productservice.getById(this.mediamodel.documentId).subscribe({\r\n      next: (value) => {\r\n        this.refreshing = false;\r\n        this.mediamodel = value.data;\r\n        this.onUpdate.emit(this.mediamodel);\r\n        this.updateData();\r\n      },\r\n      error: (err) => {\r\n        this.refreshing = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<form [formGroup]=\"mediaForm\">\r\n  <div class=\"grid mx-0\">\r\n    <div class=\"col-12 lg:col-3\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Doc Type</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <select\r\n          pInputText\r\n          formControlName=\"doc_type\"\r\n          class=\"dropdown-class\"\r\n          (change)=\"changeDocType($event)\"\r\n        >\r\n          <option value=\"IMAGE\">Image</option>\r\n          <option value=\"PDF\">PDF</option>\r\n          <option value=\"VIDEO\">Video</option>\r\n          <option value=\"SPECIFICATION\">Specification</option>\r\n        </select>\r\n      </span>\r\n    </div>\r\n    <div\r\n      class=\"col-12 lg:col-3\"\r\n      *ngIf=\"\r\n        mediaForm.get('doc_type')?.value !== 'IMAGE' &&\r\n        mediaForm.get('doc_type')?.value !== 'SPECIFICATION'\r\n      \"\r\n    >\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n        >Document Name</span\r\n      >\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          pInputText\r\n          class=\"form-control\"\r\n          type=\"text\"\r\n          formControlName=\"media_name\"\r\n        />\r\n      </span>\r\n      <div\r\n        *ngIf=\"f.media_name?.touched && f.media_name.hasError('required')\"\r\n        class=\"text-error\"\r\n      >\r\n        <small *ngIf=\"f.media_name.errors?.['required']\"\r\n          >Document Name is required.</small\r\n        >\r\n      </div>\r\n    </div>\r\n    <div\r\n      class=\"col-12 lg:col-3\"\r\n      *ngIf=\"\r\n        mediaForm.get('doc_type')?.value === 'IMAGE' ||\r\n        mediaForm.get('doc_type')?.value === 'SPECIFICATION'\r\n      \"\r\n    >\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n        >Select dimenssion</span\r\n      >\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <select\r\n          pInputText\r\n          formControlName=\"media_type\"\r\n          class=\"dropdown-class\"\r\n        >\r\n          <option value=\"\">Select Dimenssion</option>\r\n          <option\r\n            value=\"96X96\"\r\n            *ngIf=\"mediaForm.get('doc_type')?.value === 'IMAGE'\"\r\n          >\r\n            96X96\r\n          </option>\r\n          <option\r\n            value=\"300X300\"\r\n            *ngIf=\"mediaForm.get('doc_type')?.value === 'IMAGE'\"\r\n          >\r\n            300X300\r\n          </option>\r\n          <option value=\"1200X1200\">1200X1200</option>\r\n        </select>\r\n      </span>\r\n      <div\r\n        *ngIf=\"f.media_type?.touched && f.media_type.hasError('required')\"\r\n        class=\"text-error\"\r\n      >\r\n        <small *ngIf=\"f.media_type.errors?.['required']\"\r\n          >Dimension is required.</small\r\n        >\r\n      </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-3\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Url</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          pInputText\r\n          class=\"form-control\"\r\n          type=\"text\"\r\n          formControlName=\"image\"\r\n        />\r\n      </span>\r\n      <div\r\n        *ngIf=\"f.image?.touched && f.image.hasError('required')\"\r\n        class=\"text-error\"\r\n      >\r\n        <small *ngIf=\"f.image.errors?.['required']\">Url is required.</small>\r\n      </div>\r\n    </div>\r\n    <div\r\n      class=\"col-12 lg:col-3\"\r\n      *ngIf=\"mediaForm.get('doc_type')?.value === 'IMAGE'\"\r\n    >\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Cover Image</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          class=\"form-check-input\"\r\n          type=\"checkbox\"\r\n          value=\"\"\r\n          id=\"flexCheckChecked\"\r\n          checked\r\n          formControlName=\"is_cover_image\"\r\n        />\r\n        <label class=\"ml-2\"> Mark as cover image </label>\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-10\">\r\n      <span class=\"block font-large mb-3 text-600 p-custom-button\">\r\n        <p-button\r\n          type=\"submit\"\r\n          class=\"p-button-primary\"\r\n          label=\"SUBMIT\"\r\n          (click)=\"submitMediaForm()\"\r\n        ></p-button>\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12\">\r\n      <div\r\n        *ngFor=\"let img of mediamodel?.medias | groupBy : 'media_type'\"\r\n        class=\"d-flex gap-3 my-3\"\r\n      >\r\n        <ng-container *ngIf=\"img.key === 'IMAGE'\">\r\n          <div class=\"image-dimenssion d-flex align-items-center fw-bold\">\r\n            {{ img.key }}\r\n          </div>\r\n          <div *ngFor=\"let imgv of img.value\">\r\n            <div\r\n              class=\"thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border\"\r\n            >\r\n              <img [src]=\"trustUrl(imgv.url)\" width=\"100\" />\r\n              <a\r\n                class=\"remove-image d-none position-absolute\"\r\n                style=\"display: inline !important\"\r\n                (click)=\"removeImg(imgv.documentId)\"\r\n                >&#215;</a\r\n              >\r\n            </div>\r\n            <p *ngIf=\"imgv.is_cover_image\" class=\"text-center\">Cover Image</p>\r\n          </div>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"img.key === 'SPECIFICATION'\">\r\n          <div class=\"image-dimenssion d-flex align-items-center fw-bold\">\r\n            {{ img.key }}\r\n          </div>\r\n          <div *ngFor=\"let imgv of img.value\">\r\n            <div\r\n              class=\"thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border\"\r\n            >\r\n              <img [src]=\"trustUrl(imgv.url)\" width=\"100\" />\r\n              <a\r\n                class=\"remove-image d-none position-absolute\"\r\n                style=\"display: inline !important\"\r\n                (click)=\"removeImg(imgv.documentId)\"\r\n                >&#215;</a\r\n              >\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"img.key === 'VIDEO'\">\r\n          <div class=\"image-dimenssion d-flex align-items-center fw-bold\">\r\n            {{ img.key }}\r\n          </div>\r\n          <div *ngFor=\"let imgv of img.value\">\r\n            <div\r\n              class=\"thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border\"\r\n              >\r\n              <i class=\"pi pi-video\" style=\"font-size: 2em;\"></i>\r\n              <a\r\n                class=\"remove-image d-none position-absolute\"\r\n                style=\"display: inline !important\"\r\n                (click)=\"removeImg(imgv.documentId)\"\r\n                icon =\"pi pi-video\"\r\n                >&#215;</a\r\n              >\r\n            </div>\r\n            <p class=\"text-center\">{{ imgv.media_name }}</p>\r\n          </div>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"img.key === 'PDF'\">\r\n          <div class=\"image-dimenssion d-flex align-items-center fw-bold\">\r\n            {{ img.key }}\r\n          </div>\r\n          <div *ngFor=\"let imgv of img.value\">\r\n            <div\r\n              class=\"thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border\"\r\n            >\r\n              <a\r\n                class=\"remove-image d-none position-absolute\"\r\n                style=\"display: inline !important\"\r\n                (click)=\"removeImg(imgv.documentId)\"\r\n                icon=\"pi pi-file-pdf\"\r\n                >&#215;</a\r\n              >\r\n            </div>\r\n            <p class=\"text-center\">{{ imgv.media_name }}</p>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</form>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAEEC,SAAS,EAETC,WAAW,EACXC,UAAU,QACL,gBAAgB;AAGvB,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;IC8BjCC,EAAA,CAAAC,cAAA,YACG;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAC5B;;;;;IANHH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,oBACG;IAELL,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,CAAAC,UAAA,CAAAC,MAAA,kBAAAH,MAAA,CAAAC,CAAA,CAAAC,UAAA,CAAAC,MAAA,aAAuC;;;;;IAfjDX,EAPF,CAAAC,cAAA,aAMC,cAEI;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EACf;IACDH,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAY,SAAA,gBAKE;IACJZ,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAI,UAAA,IAAAS,oCAAA,kBAGC;IAKHb,EAAA,CAAAG,YAAA,EAAM;;;;IAPDH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAO,UAAA,UAAAC,MAAA,CAAAC,CAAA,CAAAC,UAAA,kBAAAF,MAAA,CAAAC,CAAA,CAAAC,UAAA,CAAAI,OAAA,KAAAN,MAAA,CAAAC,CAAA,CAAAC,UAAA,CAAAK,QAAA,aAAgE;;;;;IAyB/Df,EAAA,CAAAC,cAAA,iBAGC;IACCD,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,iBAGC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAQXH,EAAA,CAAAC,cAAA,YACG;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EACxB;;;;;IANHH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,UAAA,IAAAY,6CAAA,oBACG;IAELhB,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,CAAAQ,UAAA,CAAAN,MAAA,kBAAAH,MAAA,CAAAC,CAAA,CAAAQ,UAAA,CAAAN,MAAA,aAAuC;;;;;IA7BjDX,EAPF,CAAAC,cAAA,aAMC,cAEI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EACnB;IAOGH,EANJ,CAAAC,cAAA,cAAsD,iBAKnD,iBACkB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAO3CH,EANA,CAAAI,UAAA,IAAAc,uCAAA,qBAGC,IAAAC,uCAAA,qBAMA;IAGDnB,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAEvCF,EAFuC,CAAAG,YAAA,EAAS,EACrC,EACJ;IACPH,EAAA,CAAAI,UAAA,KAAAgB,qCAAA,kBAGC;IAKHpB,EAAA,CAAAG,YAAA,EAAM;;;;;;IArBGH,EAAA,CAAAM,SAAA,GAAkD;IAAlDN,EAAA,CAAAO,UAAA,WAAAc,OAAA,GAAAb,MAAA,CAAAc,SAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,cAAkD;IAMlDxB,EAAA,CAAAM,SAAA,EAAkD;IAAlDN,EAAA,CAAAO,UAAA,WAAAkB,OAAA,GAAAjB,MAAA,CAAAc,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,KAAA,cAAkD;IAQtDxB,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAO,UAAA,UAAAC,MAAA,CAAAC,CAAA,CAAAQ,UAAA,kBAAAT,MAAA,CAAAC,CAAA,CAAAQ,UAAA,CAAAH,OAAA,KAAAN,MAAA,CAAAC,CAAA,CAAAQ,UAAA,CAAAF,QAAA,aAAgE;;;;;IAsBjEf,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAJtEH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,UAAA,IAAAsB,sCAAA,oBAA4C;IAC9C1B,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,CAAAkB,KAAA,CAAAhB,MAAA,kBAAAH,MAAA,CAAAC,CAAA,CAAAkB,KAAA,CAAAhB,MAAA,aAAkC;;;;;IAO5CX,EAJF,CAAAC,cAAA,aAGC,cACyD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1EH,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAY,SAAA,gBAOE;IACFZ,EAAA,CAAAC,cAAA,gBAAoB;IAACD,EAAA,CAAAE,MAAA,4BAAoB;IAE7CF,EAF6C,CAAAG,YAAA,EAAQ,EAC5C,EACH;;;;;IAgCEH,EAAA,CAAAC,cAAA,YAAmD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAXlEH,EADF,CAAAC,cAAA,UAAoC,cAGjC;IACCD,EAAA,CAAAY,SAAA,cAA8C;IAC9CZ,EAAA,CAAAC,cAAA,YAIG;IADDD,EAAA,CAAA4B,UAAA,mBAAAC,uEAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAA+B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAzB,MAAA,GAAAR,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAAS3B,MAAA,CAAA4B,SAAA,CAAAN,OAAA,CAAAO,UAAA,CAA0B;IAAA,EAAC;IACnCrC,EAAA,CAAAE,MAAA,aAAM;IAEXF,EAFW,CAAAG,YAAA,EACR,EACG;IACNH,EAAA,CAAAI,UAAA,IAAAkC,uDAAA,gBAAmD;IACrDtC,EAAA,CAAAG,YAAA,EAAM;;;;;IATGH,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAO,UAAA,QAAAC,MAAA,CAAA+B,QAAA,CAAAT,OAAA,CAAAU,GAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAA0B;IAQ7BzC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,SAAAuB,OAAA,CAAAY,cAAA,CAAyB;;;;;IAhBjC1C,EAAA,CAAA2C,uBAAA,GAA0C;IACxC3C,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,IAAAwC,mDAAA,kBAAoC;;;;;IAFlC5C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA6C,kBAAA,MAAAC,MAAA,CAAAC,GAAA,MACF;IACsB/C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAuC,MAAA,CAAAtB,KAAA,CAAY;;;;;;IAoBhCxB,EADF,CAAAC,cAAA,UAAoC,cAGjC;IACCD,EAAA,CAAAY,SAAA,cAA8C;IAC9CZ,EAAA,CAAAC,cAAA,YAIG;IADDD,EAAA,CAAA4B,UAAA,mBAAAoB,uEAAA;MAAA,MAAAC,OAAA,GAAAjD,EAAA,CAAA+B,aAAA,CAAAmB,GAAA,EAAAjB,SAAA;MAAA,MAAAzB,MAAA,GAAAR,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAAS3B,MAAA,CAAA4B,SAAA,CAAAa,OAAA,CAAAZ,UAAA,CAA0B;IAAA,EAAC;IACnCrC,EAAA,CAAAE,MAAA,aAAM;IAGbF,EAHa,CAAAG,YAAA,EACR,EACG,EACF;;;;;IARGH,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAO,UAAA,QAAAC,MAAA,CAAA+B,QAAA,CAAAU,OAAA,CAAAT,GAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAA0B;;;;;IARrCzC,EAAA,CAAA2C,uBAAA,GAAkD;IAChD3C,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,IAAA+C,mDAAA,kBAAoC;;;;;IAFlCnD,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA6C,kBAAA,MAAAC,MAAA,CAAAC,GAAA,MACF;IACsB/C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAuC,MAAA,CAAAtB,KAAA,CAAY;;;;;;IAmBhCxB,EADF,CAAAC,cAAA,UAAoC,cAG/B;IACDD,EAAA,CAAAY,SAAA,YAAmD;IACnDZ,EAAA,CAAAC,cAAA,YAKG;IAFDD,EAAA,CAAA4B,UAAA,mBAAAwB,uEAAA;MAAA,MAAAC,OAAA,GAAArD,EAAA,CAAA+B,aAAA,CAAAuB,GAAA,EAAArB,SAAA;MAAA,MAAAzB,MAAA,GAAAR,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAAS3B,MAAA,CAAA4B,SAAA,CAAAiB,OAAA,CAAAhB,UAAA,CAA0B;IAAA,EAAC;IAEnCrC,EAAA,CAAAE,MAAA,aAAM;IAEXF,EAFW,CAAAG,YAAA,EACR,EACG;IACNH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;;;;IADmBH,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAuD,iBAAA,CAAAF,OAAA,CAAA3C,UAAA,CAAqB;;;;;IAjBhDV,EAAA,CAAA2C,uBAAA,GAA0C;IACxC3C,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,IAAAoD,mDAAA,kBAAoC;;;;;IAFlCxD,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA6C,kBAAA,MAAAC,MAAA,CAAAC,GAAA,MACF;IACsB/C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAuC,MAAA,CAAAtB,KAAA,CAAY;;;;;;IAwB9BxB,EAJJ,CAAAC,cAAA,UAAoC,cAGjC,YAMI;IAFDD,EAAA,CAAA4B,UAAA,mBAAA6B,uEAAA;MAAA,MAAAC,QAAA,GAAA1D,EAAA,CAAA+B,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAzB,MAAA,GAAAR,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAAS3B,MAAA,CAAA4B,SAAA,CAAAsB,QAAA,CAAArB,UAAA,CAA0B;IAAA,EAAC;IAEnCrC,EAAA,CAAAE,MAAA,aAAM;IAEXF,EAFW,CAAAG,YAAA,EACR,EACG;IACNH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC9CF,EAD8C,CAAAG,YAAA,EAAI,EAC5C;;;;IADmBH,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAuD,iBAAA,CAAAG,QAAA,CAAAhD,UAAA,CAAqB;;;;;IAhBhDV,EAAA,CAAA2C,uBAAA,GAAwC;IACtC3C,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,IAAAwD,mDAAA,kBAAoC;;;;;IAFlC5D,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA6C,kBAAA,MAAAC,MAAA,CAAAC,GAAA,MACF;IACsB/C,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAuC,MAAA,CAAAtB,KAAA,CAAY;;;;;IAjEtCxB,EAAA,CAAAC,cAAA,cAGC;IA0DCD,EAzDA,CAAAI,UAAA,IAAAyD,6CAAA,2BAA0C,IAAAC,6CAAA,2BAmBQ,IAAAC,6CAAA,2BAkBR,IAAAC,6CAAA,2BAoBF;IAmB1ChE,EAAA,CAAAG,YAAA,EAAM;;;;IA5EWH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAO,UAAA,SAAAuC,MAAA,CAAAC,GAAA,aAAyB;IAmBzB/C,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,UAAA,SAAAuC,MAAA,CAAAC,GAAA,qBAAiC;IAkBjC/C,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAO,UAAA,SAAAuC,MAAA,CAAAC,GAAA,aAAyB;IAoBzB/C,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAuC,MAAA,CAAAC,GAAA,WAAuB;;;ADvK9C,OAAM,MAAOkB,cAAc;EAOzBC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IATjB,KAAAC,UAAU,GAAQ,IAAI;IACrB,KAAAC,YAAY,GAAG,IAAIzE,OAAO,EAAQ;IAChC,KAAA0E,QAAQ,GAAG,IAAI9E,YAAY,EAAO;IAC5C,KAAA+E,OAAO,GAAG,IAAI3E,OAAO,EAAE;IACvB,KAAA4E,UAAU,GAAG,KAAK;IAQlB,KAAApD,SAAS,GAAG,IAAI,CAAC6C,EAAE,CAACQ,KAAK,CAAC;MACxBC,UAAU,EAAE,EAAE;MACdlE,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBmE,QAAQ,EAAE,CAAC,OAAO,EAAEhF,UAAU,CAACiF,QAAQ,CAAC;MACxC7D,UAAU,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACiF,QAAQ,CAAC;MACrCnD,KAAK,EAAE,CAAC,EAAE,EAAE9B,UAAU,CAACiF,QAAQ,CAAC;MAChCpC,cAAc,EAAE,CAAC,KAAK;KACvB,CAAC;EATC;EAWHqC,QAAQA,CAAA;IACN,IAAI,CAACX,cAAc,CAACY,OAAO,CACxBC,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACwE,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACb,UAAU,GAAGa,IAAI;MACtB,IAAI,CAACC,UAAU,EAAE;IACnB,CAAC,CAAC;EACN;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAC9D,SAAS,CAAC+D,UAAU,CAAC,IAAI,CAACf,UAAU,CAAC;EAC5C;EAEAlC,SAASA,CAACC,UAAkB;IAC1B,IAAI,CAAC+B,cAAc,CAChBkB,WAAW,CAACjD,UAAU,CAAC,CACvB4C,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACwE,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;MACTK,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAClB,cAAc,CAACmB,GAAG,CAAC;UACtBC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACjB,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC;QACpB,IAAI,CAACI,OAAO,EAAE;MAChB,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACvB,cAAc,CAACmB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAG,eAAeA,CAAA;IACb,IAAI,IAAI,CAACvE,SAAS,CAACwE,KAAK,EAAE;MACxB,MAAMC,SAAS,GAAG,IAAI,CAACzE,SAAS,CAACE,KAAK;MACtC,MAAMwE,GAAG,GAAG;QACVpB,UAAU,EAAEmB,SAAS,CAACnB,UAAU;QAChClE,UAAU,EAAEqF,SAAS,CAACrF,UAAU;QAChCuF,SAAS,EAAEF,SAAS,CAAC9E,UAAU;QAC/BA,UAAU,EAAE8E,SAAS,CAAClB,QAAQ;QAC9BrC,GAAG,EAAEuD,SAAS,CAACpE,KAAK;QACpBe,cAAc,EAAEqD,SAAS,CAACrD,cAAc;QACxCsC,OAAO,EAAE;UACPkB,OAAO,EAAE,CAAC,IAAI,CAAC5B,UAAU,CAACjC,UAAU;;OAEvC;MACD,IAAI,CAAC+B,cAAc,CAChByB,eAAe,CAACG,GAAG,CAAC,CACpBf,IAAI,CAAClF,SAAS,CAAC,IAAI,CAACwE,YAAY,CAAC,CAAC,CAClCW,SAAS,CAAC;QACTK,IAAI,EAAGY,GAAG,IAAI;UACZ,IAAI,CAAC9B,cAAc,CAACmB,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAACpE,SAAS,CAAC+D,UAAU,CAAC;YACxB3E,UAAU,EAAE,EAAE;YACdO,UAAU,EAAE,EAAE;YACdU,KAAK,EAAE,EAAE;YACTe,cAAc,EAAE;WACjB,CAAC;UACF,IAAI,CAACpB,SAAS,CAAC8E,eAAe,EAAE;UAChC,IAAI,CAAC3B,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC;UACpB,IAAI,CAACI,OAAO,EAAE;QAChB,CAAC;QACDC,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACvB,cAAc,CAACmB,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IACN;EACF;EAEAW,aAAaA,CAACC,CAAM;IAClB,IAAI,CAAChF,SAAS,CAAC+D,UAAU,CAAC;MACxB3E,UAAU,EAAE,EAAE;MACdO,UAAU,EAAE,EAAE;MACdU,KAAK,EAAE,EAAE;MACTe,cAAc,EAAE;KACjB,CAAC;IACF,IACE,IAAI,CAACpB,SAAS,CAACiF,QAAQ,CAAC1B,QAAQ,CAACrD,KAAK,KAAK,OAAO,IAClD,IAAI,CAACF,SAAS,CAACiF,QAAQ,CAAC1B,QAAQ,CAACrD,KAAK,KAAK,eAAe,EAC1D;MACA,IAAI,CAACF,SAAS,CAACiF,QAAQ,CAACtF,UAAU,CAACuF,eAAe,EAAE;MACpD,IAAI,CAAClF,SAAS,CAACiF,QAAQ,CAACtF,UAAU,CAACwF,sBAAsB,EAAE;MAC3D,IAAI,CAACnF,SAAS,CAACiF,QAAQ,CAAC7F,UAAU,CAACgG,aAAa,CAAC7G,UAAU,CAACiF,QAAQ,CAAC;MACrE,IAAI,CAACxD,SAAS,CAACiF,QAAQ,CAAC7F,UAAU,CAAC+F,sBAAsB,EAAE;IAC7D,CAAC,MAAM;MACL,IAAI,CAACnF,SAAS,CAACiF,QAAQ,CAACtF,UAAU,CAACyF,aAAa,CAAC7G,UAAU,CAACiF,QAAQ,CAAC;MACrE,IAAI,CAACxD,SAAS,CAACiF,QAAQ,CAACtF,UAAU,CAACwF,sBAAsB,EAAE;MAC3D,IAAI,CAACnF,SAAS,CAACiF,QAAQ,CAAC7F,UAAU,CAAC8F,eAAe,EAAE;MACpD,IAAI,CAAClF,SAAS,CAACiF,QAAQ,CAAC7F,UAAU,CAAC+F,sBAAsB,EAAE;IAC7D;EACF;EAEQE,cAAcA,CAAChC,KAA4B;IACjDA,KAAK,CAACiC,aAAa,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAEvCC,MAAM,CAACC,IAAI,CAACpC,KAAK,CAAC4B,QAAQ,CAAC,CAACS,GAAG,CAAEC,KAAK,IAAI;MACxC,MAAMC,OAAO,GAAGvC,KAAK,CAACpD,GAAG,CAAC0F,KAAK,CAAC;MAChC,IAAIC,OAAO,YAAYtH,WAAW,EAAE;QAClCsH,OAAO,CAACN,aAAa,CAAC;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MAC3C,CAAC,MAAM,IAAIK,OAAO,YAAYvH,SAAS,EAAE;QACvC,IAAI,CAACgH,cAAc,CAACO,OAAO,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAEA3E,QAAQA,CAACC,GAAA,GAAc,EAAE;IACvB,OAAO,CAACA,GAAG,IAAI,EAAE,EAAE2E,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;EAChE;EAEA,IAAI1G,CAACA,CAAA;IACH,OAAO,IAAI,CAACa,SAAS,CAACiF,QAAQ;EAChC;EACAZ,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACrB,UAAU,EAAEM,UAAU,EAAE;MAChC;IACF;IACA,IAAI,CAACF,UAAU,GAAG,IAAI;IACtB,IAAI,CAACN,cAAc,CAACgD,OAAO,CAAC,IAAI,CAAC9C,UAAU,CAACjC,UAAU,CAAC,CAAC6C,SAAS,CAAC;MAChEK,IAAI,EAAG/D,KAAK,IAAI;QACd,IAAI,CAACkD,UAAU,GAAG,KAAK;QACvB,IAAI,CAACJ,UAAU,GAAG9C,KAAK,CAAC2D,IAAI;QAC5B,IAAI,CAACX,QAAQ,CAAC6C,IAAI,CAAC,IAAI,CAAC/C,UAAU,CAAC;QACnC,IAAI,CAACc,UAAU,EAAE;MACnB,CAAC;MACDQ,KAAK,EAAG0B,GAAG,IAAI;QACb,IAAI,CAAC5C,UAAU,GAAG,KAAK;MACzB;KACD,CAAC;EACJ;EAEA6C,WAAWA,CAAA;IACT,IAAI,CAAChD,YAAY,CAACgB,IAAI,EAAE;IACxB,IAAI,CAAChB,YAAY,CAACiD,QAAQ,EAAE;EAC9B;;;uBApKWvD,cAAc,EAAAjE,EAAA,CAAAyH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAyH,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA7H,EAAA,CAAAyH,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAd9D,cAAc;MAAA+D,SAAA;MAAAC,OAAA;QAAAzD,QAAA;MAAA;MAAA0D,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBrBvI,EAHN,CAAAC,cAAA,cAA8B,aACL,aACQ,cAC6B;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErEH,EADF,CAAAC,cAAA,cAAsD,gBAMnD;UADCD,EAAA,CAAA4B,UAAA,oBAAA6G,iDAAAC,MAAA;YAAA,OAAUF,GAAA,CAAAnC,aAAA,CAAAqC,MAAA,CAAqB;UAAA,EAAC;UAEhC1I,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,YAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,gBAAoB;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChCH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAGjDF,EAHiD,CAAAG,YAAA,EAAS,EAC7C,EACJ,EACH;UA4BNH,EA3BA,CAAAI,UAAA,KAAAuI,8BAAA,kBAMC,KAAAC,8BAAA,mBA2BA;UAoCC5I,EADF,CAAAC,cAAA,cAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClEH,EAAA,CAAAC,cAAA,eAAsD;UACpDD,EAAA,CAAAY,SAAA,iBAKE;UACJZ,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAI,UAAA,KAAAyI,8BAAA,kBAGC;UAGH7I,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,UAAA,KAAA0I,8BAAA,kBAGC;UAgBG9I,EAFJ,CAAAC,cAAA,eAA8B,gBACiC,oBAM1D;UADCD,EAAA,CAAA4B,UAAA,mBAAAmH,mDAAA;YAAA,OAASP,GAAA,CAAA3C,eAAA,EAAiB;UAAA,EAAC;UAGjC7F,EAFK,CAAAG,YAAA,EAAW,EACP,EACH;UACNH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAI,UAAA,KAAA4I,8BAAA,kBAGC;;UAgFPhJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACD;;;;;;UAtNDH,EAAA,CAAAO,UAAA,cAAAiI,GAAA,CAAAlH,SAAA,CAAuB;UAoBtBtB,EAAA,CAAAM,SAAA,IAGD;UAHCN,EAAA,CAAAO,UAAA,WAAAc,OAAA,GAAAmH,GAAA,CAAAlH,SAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,mBAAAH,OAAA,GAAAmH,GAAA,CAAAlH,SAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,sBAGD;UAwBCxB,EAAA,CAAAM,SAAA,EAGD;UAHCN,EAAA,CAAAO,UAAA,WAAAkB,OAAA,GAAA+G,GAAA,CAAAlH,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,KAAA,mBAAAC,OAAA,GAAA+G,GAAA,CAAAlH,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,KAAA,sBAGD;UA+CGxB,EAAA,CAAAM,SAAA,GAAsD;UAAtDN,EAAA,CAAAO,UAAA,UAAAiI,GAAA,CAAA/H,CAAA,CAAAkB,KAAA,kBAAA6G,GAAA,CAAA/H,CAAA,CAAAkB,KAAA,CAAAb,OAAA,KAAA0H,GAAA,CAAA/H,CAAA,CAAAkB,KAAA,CAAAZ,QAAA,aAAsD;UAQxDf,EAAA,CAAAM,SAAA,EAAkD;UAAlDN,EAAA,CAAAO,UAAA,WAAA0I,OAAA,GAAAT,GAAA,CAAAlH,SAAA,CAAAC,GAAA,+BAAA0H,OAAA,CAAAzH,KAAA,cAAkD;UA2BjCxB,EAAA,CAAAM,SAAA,GAA8C;UAA9CN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAkJ,WAAA,QAAAV,GAAA,CAAAlE,UAAA,kBAAAkE,GAAA,CAAAlE,UAAA,CAAA6E,MAAA,gBAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}