{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../customer.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/table\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/ripple\";\nfunction CustomerSalesAreaComponent_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CustomerSalesAreaComponent_ng_template_3_ng_container_0_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi pi-fw \", ctx_r1.isExpanded ? \"pi-minus\" : \"pi-plus\", \"\");\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.isExpanded ? \"Collapse All\" : \"Expand All\");\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerSalesAreaComponent_ng_template_3_ng_container_0_Template, 3, 3, \"ng-container\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.sales_area == null ? null : ctx_r1.sales_area.length) > 0);\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_4_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 11);\n    i0.ɵɵelementStart(2, \"th\", 12);\n    i0.ɵɵtext(3, \" Organization \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerSalesAreaComponent_ng_template_4_tr_0_Template, 4, 0, \"tr\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.sales_area == null ? null : ctx_r1.sales_area.length) > 0);\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_5_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"button\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const sale_area_r4 = ctx_r2.$implicit;\n    const expanded_r5 = ctx_r2.expanded;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", sale_area_r4)(\"icon\", expanded_r5 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", sale_area_r4 == null ? null : sale_area_r4.sales_organization, \" \");\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerSalesAreaComponent_ng_template_5_tr_0_Template, 5, 3, \"tr\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.sales_area == null ? null : ctx_r1.sales_area.length) > 0);\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 11);\n    i0.ɵɵelementStart(2, \"td\", 14)(3, \"div\", 15)(4, \"div\", 16)(5, \"span\", 17);\n    i0.ɵɵtext(6, \"Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 18);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"span\", 17);\n    i0.ɵɵtext(11, \"Distribution Channel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 18);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 16)(15, \"span\", 17);\n    i0.ɵɵtext(16, \"Division\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 18);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 16)(20, \"span\", 17);\n    i0.ɵɵtext(21, \"Currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 18);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 16)(25, \"span\", 17);\n    i0.ɵɵtext(26, \"Account Assignment Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 18);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 16)(30, \"span\", 17);\n    i0.ɵɵtext(31, \"Customer Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 18);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"span\", 17);\n    i0.ɵɵtext(36, \"Customer Price Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 18);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 16)(40, \"span\", 17);\n    i0.ɵɵtext(41, \"Customer Pricing Procedure \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 18);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 16)(45, \"span\", 17);\n    i0.ɵɵtext(46, \"Delivery Blocked For Customer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 18);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 16)(50, \"span\", 17);\n    i0.ɵɵtext(51, \"Deletion Indicator \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 18);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 16)(55, \"span\", 17);\n    i0.ɵɵtext(56, \"Sales Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 18);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 16)(60, \"span\", 17);\n    i0.ɵɵtext(61, \"Sales Office \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"span\", 18);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 16)(65, \"span\", 17);\n    i0.ɵɵtext(66, \"Shipping Condition \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"span\", 18);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 16)(70, \"span\", 17);\n    i0.ɵɵtext(71, \"Supplying Plant \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"span\", 18);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 16)(75, \"span\", 17);\n    i0.ɵɵtext(76, \"Account Group \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"span\", 18);\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const sale_area_r6 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.sales_organization) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.distribution_channel) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.division) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.currency) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.customer_account_assignment_group) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.customer_group) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.customer_price_group) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.customer_pricing_procedure) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.delivery_is_blocked_for_customer) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.deletion_indicator) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.sales_group) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.sales_office) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.shipping_condition) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.supplying_plant) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (sale_area_r6 == null ? null : sale_area_r6.customer_account_group) || \"-\", \" \");\n  }\n}\nfunction CustomerSalesAreaComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19);\n    i0.ɵɵtext(2, \"There are no Sales Area Available for this record.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CustomerSalesAreaComponent {\n  constructor(customerservice) {\n    this.customerservice = customerservice;\n    this.unsubscribe$ = new Subject();\n    this.sales_area = null;\n    this.isExpanded = false;\n    this.expandedRows = {};\n  }\n  ngOnInit() {\n    this.customerservice.customer.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.sales_area = data?.sales_areas;\n    });\n  }\n  expandAll() {\n    if (!this.isExpanded) {\n      this.sales_area.forEach(area => area?.id ? this.expandedRows[area.id] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n    this.isExpanded = !this.isExpanded;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function CustomerSalesAreaComponent_Factory(t) {\n      return new (t || CustomerSalesAreaComponent)(i0.ɵɵdirectiveInject(i1.CustomerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerSalesAreaComponent,\n      selectors: [[\"app-customer-sales-area\"]],\n      decls: 8,\n      vars: 2,\n      consts: [[1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"expandedRowKeys\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pTemplate\", \"emptymessage\"], [4, \"ngIf\"], [\"pButton\", \"\", 3, \"click\", \"icon\", \"label\"], [1, \"flex\", \"table-header\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"name\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pRowToggler\", \"icon\"], [\"colspan\", \"2\"], [1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"colspan\", \"6\"]],\n      template: function CustomerSalesAreaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p-table\", 2);\n          i0.ɵɵtemplate(3, CustomerSalesAreaComponent_ng_template_3_Template, 1, 1, \"ng-template\", 3)(4, CustomerSalesAreaComponent_ng_template_4_Template, 1, 1, \"ng-template\", 4)(5, CustomerSalesAreaComponent_ng_template_5_Template, 1, 1, \"ng-template\", 5)(6, CustomerSalesAreaComponent_ng_template_6_Template, 79, 15, \"ng-template\", 6)(7, CustomerSalesAreaComponent_ng_template_7_Template, 3, 0, \"ng-template\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.sales_area)(\"expandedRowKeys\", ctx.expandedRows);\n        }\n      },\n      dependencies: [i2.NgIf, i3.Table, i4.PrimeTemplate, i3.SortableColumn, i3.RowToggler, i5.ButtonDirective, i6.Ripple],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "CustomerSalesAreaComponent_ng_template_3_ng_container_0_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "expandAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵpropertyInterpolate1", "isExpanded", "ɵɵpropertyInterpolate", "ɵɵtemplate", "CustomerSalesAreaComponent_ng_template_3_ng_container_0_Template", "ɵɵproperty", "sales_area", "length", "ɵɵtext", "CustomerSalesAreaComponent_ng_template_4_tr_0_Template", "sale_area_r4", "expanded_r5", "ɵɵtextInterpolate1", "sales_organization", "CustomerSalesAreaComponent_ng_template_5_tr_0_Template", "sale_area_r6", "distribution_channel", "division", "currency", "customer_account_assignment_group", "customer_group", "customer_price_group", "customer_pricing_procedure", "delivery_is_blocked_for_customer", "deletion_indicator", "sales_group", "sales_office", "shipping_condition", "supplying_plant", "customer_account_group", "CustomerSalesAreaComponent", "constructor", "customerservice", "unsubscribe$", "expandedRows", "ngOnInit", "customer", "pipe", "subscribe", "data", "sales_areas", "for<PERSON>ach", "area", "id", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "CustomerService", "selectors", "decls", "vars", "consts", "template", "CustomerSalesAreaComponent_Template", "rf", "ctx", "CustomerSalesAreaComponent_ng_template_3_Template", "CustomerSalesAreaComponent_ng_template_4_Template", "CustomerSalesAreaComponent_ng_template_5_Template", "CustomerSalesAreaComponent_ng_template_6_Template", "CustomerSalesAreaComponent_ng_template_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer-details\\customer-sales-area\\customer-sales-area.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer-details\\customer-sales-area\\customer-sales-area.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { CustomerService } from '../../customer.service';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-customer-sales-area',\r\n  templateUrl: './customer-sales-area.component.html',\r\n  styleUrl: './customer-sales-area.component.scss',\r\n})\r\nexport class CustomerSalesAreaComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public sales_area: any = null;\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n\r\n  constructor(private customerservice: CustomerService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.customerservice.customer\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.sales_area = data?.sales_areas;\r\n      });\r\n  }\r\n\r\n  expandAll() {\r\n    if (!this.isExpanded) {\r\n      this.sales_area.forEach((area: any) =>\r\n        area?.id ? (this.expandedRows[area.id] = true) : ''\r\n      );\r\n    } else {\r\n      this.expandedRows = {};\r\n    }\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12\">\r\n    <div class=\"card\">\r\n      <p-table\r\n        [value]=\"sales_area\"\r\n        dataKey=\"id\"\r\n        [expandedRowKeys]=\"expandedRows\"\r\n        responsiveLayout=\"scroll\"\r\n      >\r\n        <ng-template pTemplate=\"caption\">\r\n          <ng-container *ngIf=\"sales_area?.length > 0\">\r\n            <button\r\n            pButton\r\n            icon=\"pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}\"\r\n            label=\"{{ isExpanded ? 'Collapse All' : 'Expand All' }}\"\r\n            (click)=\"expandAll()\"\r\n          ></button>\r\n          <div class=\"flex table-header\"></div>\r\n          </ng-container>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"header\">\r\n          <tr *ngIf=\"sales_area?.length > 0\">\r\n            <th style=\"width: 3rem\"></th>\r\n            <th pSortableColumn=\"name\">\r\n             Organization\r\n            </th>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"body\" let-sale_area let-expanded=\"expanded\">\r\n          <tr *ngIf=\"sales_area?.length > 0\">\r\n            <td>\r\n              <button\r\n                type=\"button\"\r\n                pButton\r\n                pRipple\r\n                [pRowToggler]=\"sale_area\"\r\n                class=\"p-button-text p-button-rounded p-button-plain\"\r\n                [icon]=\"\r\n                  expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\r\n                \"\r\n              ></button>\r\n            </td>\r\n            <td>\r\n              {{ sale_area?.sales_organization }}\r\n            </td>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"rowexpansion\" let-sale_area>\r\n          <tr>\r\n            <td style=\"width: 3rem\"></td>\r\n            <td colspan=\"2\">\r\n              <div class=\"grid mx-0\">\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Organization</span\r\n                  >\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.sales_organization || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Distribution Channel</span\r\n                  >\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.distribution_channel || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Division</span\r\n                  >\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.division || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Currency</span\r\n                  >\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.currency || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Account Assignment Group</span\r\n                  >\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.customer_account_assignment_group || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Customer Group</span\r\n                  >\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.customer_group || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Customer Price Group\r\n                  </span>\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.customer_price_group || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                  <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                    >Customer Pricing Procedure\r\n                  </span>\r\n                  <span class=\"block font-medium mb-3 text-600\">\r\n                    {{ sale_area?.customer_pricing_procedure || \"-\" }}\r\n                  </span>\r\n                </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Delivery Blocked For Customer\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.delivery_is_blocked_for_customer || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n                <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Deletion Indicator\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.deletion_indicator || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Sales Group\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.sales_group || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Sales Office\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.sales_office || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Shipping Condition\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.shipping_condition || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Supplying Plant\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.supplying_plant || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"col-12 lg:col-4\">\r\n                    <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n                      >Account Group\r\n                    </span>\r\n                    <span class=\"block font-medium mb-3 text-600\">\r\n                      {{ sale_area?.customer_account_group || \"-\" }}\r\n                    </span>\r\n                  </div>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </ng-template>\r\n        <ng-template pTemplate=\"emptymessage\">\r\n          <tr>\r\n            <td colspan=\"6\">There are no Sales Area Available for this record.</td>\r\n          </tr>\r\n      </ng-template>\r\n      </p-table>\r\n    </div>\r\n  </div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICQ/BC,EAAA,CAAAC,uBAAA,GAA6C;IAC3CD,EAAA,CAAAE,cAAA,gBAKD;IADCF,EAAA,CAAAG,UAAA,mBAAAC,yFAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACtBV,EAAA,CAAAW,YAAA,EAAS;IACVX,EAAA,CAAAY,SAAA,cAAqC;;;;;IAJnCZ,EAAA,CAAAa,SAAA,EAAyD;IAAzDb,EAAA,CAAAc,sBAAA,sBAAAP,MAAA,CAAAQ,UAAA,8BAAyD;IACzDf,EAAA,CAAAgB,qBAAA,UAAAT,MAAA,CAAAQ,UAAA,iCAAwD;;;;;IAJ1Df,EAAA,CAAAiB,UAAA,IAAAC,gEAAA,0BAA6C;;;;IAA9BlB,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAa,UAAA,kBAAAb,MAAA,CAAAa,UAAA,CAAAC,MAAA,MAA4B;;;;;IAW3CrB,EAAA,CAAAE,cAAA,SAAmC;IACjCF,EAAA,CAAAY,SAAA,aAA6B;IAC7BZ,EAAA,CAAAE,cAAA,aAA2B;IAC1BF,EAAA,CAAAsB,MAAA,qBACD;IACFtB,EADE,CAAAW,YAAA,EAAK,EACF;;;;;IALLX,EAAA,CAAAiB,UAAA,IAAAM,sDAAA,gBAAmC;;;;IAA9BvB,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAa,UAAA,kBAAAb,MAAA,CAAAa,UAAA,CAAAC,MAAA,MAA4B;;;;;IAS/BrB,EADF,CAAAE,cAAA,SAAmC,SAC7B;IACFF,EAAA,CAAAY,SAAA,iBASU;IACZZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAE,cAAA,SAAI;IACFF,EAAA,CAAAsB,MAAA,GACF;IACFtB,EADE,CAAAW,YAAA,EAAK,EACF;;;;;;IAVCX,EAAA,CAAAa,SAAA,GAAyB;IAEzBb,EAFA,CAAAmB,UAAA,gBAAAK,YAAA,CAAyB,SAAAC,WAAA,gDAIxB;IAIHzB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,MAAAF,YAAA,kBAAAA,YAAA,CAAAG,kBAAA,MACF;;;;;IAfF3B,EAAA,CAAAiB,UAAA,IAAAW,sDAAA,gBAAmC;;;;IAA9B5B,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAa,UAAA,kBAAAb,MAAA,CAAAa,UAAA,CAAAC,MAAA,MAA4B;;;;;IAmBjCrB,EAAA,CAAAE,cAAA,SAAI;IACFF,EAAA,CAAAY,SAAA,aAA6B;IAIvBZ,EAHN,CAAAE,cAAA,aAAgB,cACS,cACQ,eAExB;IAAAF,EAAA,CAAAsB,MAAA,mBAAY;IAAAtB,EAAA,CAAAW,YAAA,EACd;IACDX,EAAA,CAAAE,cAAA,eAA8C;IAC5CF,EAAA,CAAAsB,MAAA,GACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,cAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,4BAAoB;IAAAtB,EAAA,CAAAW,YAAA,EACtB;IACDX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,gBAAQ;IAAAtB,EAAA,CAAAW,YAAA,EACV;IACDX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,gBAAQ;IAAAtB,EAAA,CAAAW,YAAA,EACV;IACDX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,gCAAwB;IAAAtB,EAAA,CAAAW,YAAA,EAC1B;IACDX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,sBAAc;IAAAtB,EAAA,CAAAW,YAAA,EAChB;IACDX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,6BACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,mCACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEFX,EADJ,CAAAE,cAAA,eAA6B,gBAEtB;IAAAF,EAAA,CAAAsB,MAAA,sCACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADJ,CAAAE,cAAA,eAA6B,gBAEtB;IAAAF,EAAA,CAAAsB,MAAA,2BACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,oBACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,qBACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,2BACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,wBACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IACFtB,EADE,CAAAW,YAAA,EAAO,EACH;IAEJX,EADF,CAAAE,cAAA,eAA6B,gBAExB;IAAAF,EAAA,CAAAsB,MAAA,sBACH;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAE,cAAA,gBAA8C;IAC5CF,EAAA,CAAAsB,MAAA,IACF;IAIVtB,EAJU,CAAAW,YAAA,EAAO,EACH,EACJ,EACH,EACF;;;;IArHKX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAF,kBAAA,cACF;IAOE3B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAC,oBAAA,cACF;IAOE9B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAE,QAAA,cACF;IAOE/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAG,QAAA,cACF;IAOEhC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAI,iCAAA,cACF;IAOEjC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAK,cAAA,cACF;IAOElC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAM,oBAAA,cACF;IAOEnC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAO,0BAAA,cACF;IAOIpC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAQ,gCAAA,cACF;IAOErC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAS,kBAAA,cACF;IAOEtC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAU,WAAA,cACF;IAOEvC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAW,YAAA,cACF;IAOExC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAY,kBAAA,cACF;IAOEzC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAa,eAAA,cACF;IAOE1C,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA0B,kBAAA,OAAAG,YAAA,kBAAAA,YAAA,CAAAc,sBAAA,cACF;;;;;IAQR3C,EADF,CAAAE,cAAA,SAAI,aACc;IAAAF,EAAA,CAAAsB,MAAA,yDAAkD;IACpEtB,EADoE,CAAAW,YAAA,EAAK,EACpE;;;ADrKf,OAAM,MAAOiC,0BAA0B;EAMrCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAL3B,KAAAC,YAAY,GAAG,IAAIjD,OAAO,EAAQ;IACnC,KAAAsB,UAAU,GAAQ,IAAI;IACtB,KAAAL,UAAU,GAAY,KAAK;IAC3B,KAAAiC,YAAY,GAAiB,EAAE;EAEiB;EAEvDC,QAAQA,CAAA;IACN,IAAI,CAACH,eAAe,CAACI,QAAQ,CAC1BC,IAAI,CAACpD,SAAS,CAAC,IAAI,CAACgD,YAAY,CAAC,CAAC,CAClCK,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACjC,UAAU,GAAGiC,IAAI,EAAEC,WAAW;IACrC,CAAC,CAAC;EACN;EAEA5C,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACK,UAAU,EAAE;MACpB,IAAI,CAACK,UAAU,CAACmC,OAAO,CAAEC,IAAS,IAChCA,IAAI,EAAEC,EAAE,GAAI,IAAI,CAACT,YAAY,CAACQ,IAAI,CAACC,EAAE,CAAC,GAAG,IAAI,GAAI,EAAE,CACpD;IACH,CAAC,MAAM;MACL,IAAI,CAACT,YAAY,GAAG,EAAE;IACxB;IACA,IAAI,CAACjC,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEA2C,WAAWA,CAAA;IACT,IAAI,CAACX,YAAY,CAACY,IAAI,EAAE;IACxB,IAAI,CAACZ,YAAY,CAACa,QAAQ,EAAE;EAC9B;;;uBA9BWhB,0BAA0B,EAAA5C,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA1BnB,0BAA0B;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXjCtE,EAFN,CAAAE,cAAA,aAAoB,aACE,iBAMf;UAwKCF,EAvKA,CAAAiB,UAAA,IAAAuD,iDAAA,yBAAiC,IAAAC,iDAAA,yBAWD,IAAAC,iDAAA,yBAQoC,IAAAC,iDAAA,2BAmBhB,IAAAC,iDAAA,yBAiId;UAO5C5E,EAFI,CAAAW,YAAA,EAAU,EACN,EACF;;;UAnLAX,EAAA,CAAAa,SAAA,GAAoB;UAEpBb,EAFA,CAAAmB,UAAA,UAAAoD,GAAA,CAAAnD,UAAA,CAAoB,oBAAAmD,GAAA,CAAAvB,YAAA,CAEY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}