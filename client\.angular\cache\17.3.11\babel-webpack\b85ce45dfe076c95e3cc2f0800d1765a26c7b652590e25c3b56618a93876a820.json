{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../product.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/table\";\nfunction DescriptionComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Description\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DescriptionComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const desc_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((desc_r1 == null ? null : desc_r1.language) || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((desc_r1 == null ? null : desc_r1.description) || \"-\");\n  }\n}\nexport class DescriptionComponent {\n  constructor(productservice) {\n    this.productservice = productservice;\n    this.unsubscribe$ = new Subject();\n    this.description = null;\n  }\n  ngOnInit() {\n    this.productservice.product.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.description = data?.descriptions;\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function DescriptionComponent_Factory(t) {\n      return new (t || DescriptionComponent)(i0.ɵɵdirectiveInject(i1.ProductService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DescriptionComponent,\n      selectors: [[\"app-description\"]],\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"p-datatable-sm\", \"p-datatable-gridlines\", 3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"]],\n      template: function DescriptionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-table\", 0);\n          i0.ɵɵtemplate(1, DescriptionComponent_ng_template_1_Template, 5, 0, \"ng-template\", 1)(2, DescriptionComponent_ng_template_2_Template, 5, 2, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"value\", ctx.description);\n        }\n      },\n      dependencies: [i2.PrimeTemplate, i3.Table],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "desc_r1", "language", "description", "DescriptionComponent", "constructor", "productservice", "unsubscribe$", "ngOnInit", "product", "pipe", "subscribe", "data", "descriptions", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "ProductService", "selectors", "decls", "vars", "consts", "template", "DescriptionComponent_Template", "rf", "ctx", "ɵɵtemplate", "DescriptionComponent_ng_template_1_Template", "DescriptionComponent_ng_template_2_Template", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\description\\description.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\description\\description.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { ProductService } from '../../product.service';\r\n\r\n@Component({\r\n  selector: 'app-description',\r\n  templateUrl: './description.component.html',\r\n  styleUrl: './description.component.scss',\r\n})\r\nexport class DescriptionComponent {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public description: any = null;\r\n\r\n  constructor(private productservice: ProductService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.productservice.product\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.description = data?.descriptions;\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-table [value]=\"description\" class=\"p-datatable-sm p-datatable-gridlines\">\r\n    <ng-template pTemplate=\"header\">\r\n        <tr>\r\n            <th>Language</th>\r\n            <th>Description</th>\r\n        </tr>\r\n    </ng-template>\r\n    <ng-template pTemplate=\"body\" let-desc>\r\n        <tr>\r\n            <td>{{ desc?.language || '-'}}</td>\r\n            <td>{{ desc?.description || '-'}}</td>\r\n        </tr>\r\n    </ng-template>\r\n</p-table>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;ICE7BC,EADJ,CAAAC,cAAA,SAAI,SACI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IACnBF,EADmB,CAAAG,YAAA,EAAK,EACnB;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,SACI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IACrCF,EADqC,CAAAG,YAAA,EAAK,EACrC;;;;IAFGH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAC,QAAA,SAA0B;IAC1BP,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAE,WAAA,SAA6B;;;ADD7C,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAH1B,KAAAC,YAAY,GAAG,IAAId,OAAO,EAAQ;IACnC,KAAAU,WAAW,GAAQ,IAAI;EAEuB;EAErDK,QAAQA,CAAA;IACN,IAAI,CAACF,cAAc,CAACG,OAAO,CACxBC,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACa,YAAY,CAAC,CAAC,CAClCI,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACT,WAAW,GAAGS,IAAI,EAAEC,YAAY;IACvC,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,YAAY,CAACQ,IAAI,EAAE;IACxB,IAAI,CAACR,YAAY,CAACS,QAAQ,EAAE;EAC9B;;;uBAjBWZ,oBAAoB,EAAAT,EAAA,CAAAsB,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAApBf,oBAAoB;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTjC/B,EAAA,CAAAC,cAAA,iBAA4E;UAOxED,EANA,CAAAiC,UAAA,IAAAC,2CAAA,yBAAgC,IAAAC,2CAAA,yBAMO;UAM3CnC,EAAA,CAAAG,YAAA,EAAU;;;UAbDH,EAAA,CAAAoC,UAAA,UAAAJ,GAAA,CAAAxB,WAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}