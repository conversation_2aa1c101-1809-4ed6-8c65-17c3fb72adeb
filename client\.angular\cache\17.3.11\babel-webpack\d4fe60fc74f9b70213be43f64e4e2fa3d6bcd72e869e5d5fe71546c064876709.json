{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ToastModule } from 'primeng/toast';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { ListboxModule } from 'primeng/listbox';\nimport { DialogModule } from 'primeng/dialog';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { CalendarModule } from 'primeng/calendar';\nimport { NgSelectModule } from '@ng-select/ng-select';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { CrmComponent } from './crm.component';\nimport { CrmRoutingModule } from './crm-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class CrmModule {\n  static {\n    this.ɵfac = function CrmModule_Factory(t) {\n      return new (t || CrmModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CrmModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, CrmRoutingModule, ToastModule, FormsModule, TableModule, ButtonModule, ListboxModule, DialogModule, InputTextModule, InputSwitchModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule, RadioButtonModule, TabMenuModule, CalendarModule, NgSelectModule, ConfirmDialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CrmModule, {\n    declarations: [CrmComponent],\n    imports: [CommonModule, CrmRoutingModule, ToastModule, FormsModule, TableModule, ButtonModule, ListboxModule, DialogModule, InputTextModule, InputSwitchModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule, RadioButtonModule, TabMenuModule, CalendarModule, NgSelectModule, ConfirmDialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ToastModule", "FormsModule", "ReactiveFormsModule", "TableModule", "ButtonModule", "InputTextModule", "InputTextareaModule", "InputSwitchModule", "DropdownModule", "CheckboxModule", "ListboxModule", "DialogModule", "RadioButtonModule", "TabMenuModule", "CalendarModule", "NgSelectModule", "MessageService", "ConfirmationService", "ConfirmDialogModule", "CrmComponent", "CrmRoutingModule", "CrmModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\crm\\crm.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { ListboxModule } from 'primeng/listbox';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { TabMenuModule } from 'primeng/tabmenu';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { CrmComponent } from './crm.component';\r\nimport { CrmRoutingModule } from './crm-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [CrmComponent],\r\n  imports: [\r\n    CommonModule,\r\n    CrmRoutingModule,\r\n    ToastModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    ListboxModule,\r\n    DialogModule,\r\n    InputTextModule,\r\n    InputSwitchModule,\r\n    InputTextareaModule,\r\n    DropdownModule,\r\n    ReactiveFormsModule,\r\n    CheckboxModule,\r\n    RadioButtonModule,\r\n    TabMenuModule,\r\n    CalendarModule,\r\n    NgSelectModule,\r\n    ConfirmDialogModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class CrmModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;;AA2BvD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA;IAAS;EAAA;;;iBAFT,CAACL,cAAc,EAAEC,mBAAmB,CAAC;MAAAK,OAAA,GApB9CvB,YAAY,EACZqB,gBAAgB,EAChBpB,WAAW,EACXC,WAAW,EACXE,WAAW,EACXC,YAAY,EACZM,aAAa,EACbC,YAAY,EACZN,eAAe,EACfE,iBAAiB,EACjBD,mBAAmB,EACnBE,cAAc,EACdN,mBAAmB,EACnBO,cAAc,EACdG,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdG,mBAAmB;IAAA;EAAA;;;2EAIVG,SAAS;IAAAE,YAAA,GAxBLJ,YAAY;IAAAG,OAAA,GAEzBvB,YAAY,EACZqB,gBAAgB,EAChBpB,WAAW,EACXC,WAAW,EACXE,WAAW,EACXC,YAAY,EACZM,aAAa,EACbC,YAAY,EACZN,eAAe,EACfE,iBAAiB,EACjBD,mBAAmB,EACnBE,cAAc,EACdN,mBAAmB,EACnBO,cAAc,EACdG,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdG,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}