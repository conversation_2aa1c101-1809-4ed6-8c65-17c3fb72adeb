{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./app.menu.component\";\nconst _c0 = [\"menuContainer\"];\nconst _c1 = () => [\"/\"];\nexport class AppSidebarComponent {\n  constructor(layoutService, el) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.timeout = null;\n  }\n  onMouseEnter() {\n    if (!this.layoutService.state.anchored) {\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = null;\n      }\n      this.layoutService.state.sidebarActive = true;\n    }\n  }\n  onMouseLeave() {\n    if (!this.layoutService.state.anchored) {\n      if (!this.timeout) {\n        this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\n      }\n    }\n  }\n  anchor() {\n    this.layoutService.state.anchored = !this.layoutService.state.anchored;\n  }\n  static {\n    this.ɵfac = function AppSidebarComponent_Factory(t) {\n      return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      viewQuery: function AppSidebarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuContainer = _t.first);\n        }\n      },\n      decls: 11,\n      vars: 4,\n      consts: [[\"menuContainer\", \"\"], [1, \"layout-sidebar\", 3, \"mouseenter\", \"mouseleave\"], [1, \"sidebar-header\"], [1, \"app-logo\", 3, \"routerLink\"], [1, \"app-logo-small\", \"h-2rem\"], [3, \"src\"], [1, \"flex\", \"w-full\"], [1, \"w-full\", 3, \"src\"], [\"type\", \"button\", 1, \"layout-sidebar-anchor\", \"p-link\", \"z-2\", 3, \"click\"], [1, \"layout-menu-container\"]],\n      template: function AppSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"mouseenter\", function AppSidebarComponent_Template_div_mouseenter_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function AppSidebarComponent_Template_div_mouseleave_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"a\", 3)(3, \"div\", 4);\n          i0.ɵɵelement(4, \"img\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 6);\n          i0.ɵɵelement(6, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AppSidebarComponent_Template_button_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.anchor());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 9, 0);\n          i0.ɵɵelement(10, \"app-menu\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", \"assets/layout/images/chs-logo-\" + (ctx.layoutService.config().colorScheme === \"light\" ? \"dark\" : \"light\") + \".svg\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", \"assets/layout/images/chs-logo-\" + (ctx.layoutService.config().colorScheme === \"light\" ? \"dark\" : \"light\") + \".svg\", i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [i2.RouterLink, i3.AppMenuComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "constructor", "layoutService", "el", "timeout", "onMouseEnter", "state", "anchored", "clearTimeout", "sidebarActive", "onMouseLeave", "setTimeout", "anchor", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "selectors", "viewQuery", "AppSidebarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppSidebarComponent_Template_div_mouseenter_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppSidebarComponent_Template_div_mouseleave_0_listener", "ɵɵelement", "ɵɵelementEnd", "AppSidebarComponent_Template_button_click_7_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "config", "colorScheme", "ɵɵsanitizeUrl"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\layout\\app.sidebar.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\layout\\app.sidebar.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { LayoutService } from './service/app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-sidebar',\r\n    templateUrl: './app.sidebar.component.html'\r\n})\r\nexport class AppSidebarComponent {\r\n    timeout: any = null;\r\n  \r\n    @ViewChild('menuContainer') menuContainer!: ElementRef;\r\n    constructor(public layoutService: LayoutService, public el: ElementRef) {}\r\n    \r\n\r\n    onMouseEnter() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (this.timeout) {\r\n                clearTimeout(this.timeout);\r\n                this.timeout = null;\r\n            }\r\n            this.layoutService.state.sidebarActive = true;\r\n           \r\n    \r\n        }\r\n    }\r\n\r\n    onMouseLeave() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (!this.timeout) {\r\n                this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\r\n            }\r\n        }\r\n    }\r\n\r\n    anchor() {\r\n        this.layoutService.state.anchored = !this.layoutService.state.anchored;\r\n    }\r\n\r\n}\r\n", "<div class=\"layout-sidebar\" (mouseenter)=\"onMouseEnter()\" (mouseleave)=\"onMouseLeave()\">\r\n    <div class=\"sidebar-header\">\r\n        <a [routerLink]=\"['/']\" class=\"app-logo\">\r\n            <div class=\"app-logo-small h-2rem\" >\r\n                <img  [src]=\"'assets/layout/images/chs-logo-'+ (layoutService.config().colorScheme === 'light' ? 'dark' : 'light') + '.svg'\">\r\n            </div>\r\n            <div class=\"flex w-full\">\r\n                <img class=\"w-full\" [src]=\"'assets/layout/images/chs-logo-'+ (layoutService.config().colorScheme === 'light' ? 'dark' : 'light') + '.svg'\">\r\n                <!-- <img  class=\"h-2rem ml-3\" [src]=\"'assets/layout/images/appname-'+ (layoutService.config().colorScheme === 'light' ? 'dark' : 'light') + '.png'\"/> -->\r\n            </div>\r\n        </a>\r\n        <button class=\"layout-sidebar-anchor p-link z-2 \" type=\"button\" (click)=\"anchor()\"></button>\r\n    </div>\r\n \r\n\r\n    <div #menuContainer class=\"layout-menu-container\">\r\n        <app-menu></app-menu>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;AAOA,OAAM,MAAOA,mBAAmB;EAI5BC,YAAmBC,aAA4B,EAASC,EAAc;IAAnD,KAAAD,aAAa,GAAbA,aAAa;IAAwB,KAAAC,EAAE,GAAFA,EAAE;IAH1D,KAAAC,OAAO,GAAQ,IAAI;EAGsD;EAGzEC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACH,aAAa,CAACI,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,IAAI,CAACH,OAAO,EAAE;QACdI,YAAY,CAAC,IAAI,CAACJ,OAAO,CAAC;QAC1B,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;MACA,IAAI,CAACF,aAAa,CAACI,KAAK,CAACG,aAAa,GAAG,IAAI;IAGjD;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACR,aAAa,CAACI,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAGO,UAAU,CAAC,MAAM,IAAI,CAACT,aAAa,CAACI,KAAK,CAACG,aAAa,GAAG,KAAK,EAAE,GAAG,CAAC;MACxF;IACJ;EACJ;EAEAG,MAAMA,CAAA;IACF,IAAI,CAACV,aAAa,CAACI,KAAK,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACL,aAAa,CAACI,KAAK,CAACC,QAAQ;EAC1E;;;uBA7BSP,mBAAmB,EAAAa,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA;IAAA;EAAA;;;YAAnBjB,mBAAmB;MAAAkB,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCPhCR,EAAA,CAAAU,cAAA,aAAwF;UAA9BV,EAA9B,CAAAW,UAAA,wBAAAC,uDAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;YAAA,OAAAd,EAAA,CAAAe,WAAA,CAAcN,GAAA,CAAAjB,YAAA,EAAc;UAAA,EAAC,wBAAAwB,uDAAA;YAAAhB,EAAA,CAAAa,aAAA,CAAAC,GAAA;YAAA,OAAAd,EAAA,CAAAe,WAAA,CAAeN,GAAA,CAAAZ,YAAA,EAAc;UAAA,EAAC;UAG3EG,EAFR,CAAAU,cAAA,aAA4B,WACiB,aACD;UAChCV,EAAA,CAAAiB,SAAA,aAA6H;UACjIjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAU,cAAA,aAAyB;UACrBV,EAAA,CAAAiB,SAAA,aAA2I;UAGnJjB,EADI,CAAAkB,YAAA,EAAM,EACN;UACJlB,EAAA,CAAAU,cAAA,gBAAmF;UAAnBV,EAAA,CAAAW,UAAA,mBAAAQ,qDAAA;YAAAnB,EAAA,CAAAa,aAAA,CAAAC,GAAA;YAAA,OAAAd,EAAA,CAAAe,WAAA,CAASN,GAAA,CAAAV,MAAA,EAAQ;UAAA,EAAC;UACtFC,EADuF,CAAAkB,YAAA,EAAS,EAC1F;UAGNlB,EAAA,CAAAU,cAAA,gBAAkD;UAC9CV,EAAA,CAAAiB,SAAA,gBAAqB;UAE7BjB,EADI,CAAAkB,YAAA,EAAM,EACJ;;;UAhBKlB,EAAA,CAAAoB,SAAA,GAAoB;UAApBpB,EAAA,CAAAqB,UAAA,eAAArB,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAoB;UAETvB,EAAA,CAAAoB,SAAA,GAAsH;UAAtHpB,EAAA,CAAAqB,UAAA,4CAAAZ,GAAA,CAAApB,aAAA,CAAAmC,MAAA,GAAAC,WAAA,2CAAAzB,EAAA,CAAA0B,aAAA,CAAsH;UAGxG1B,EAAA,CAAAoB,SAAA,GAAsH;UAAtHpB,EAAA,CAAAqB,UAAA,4CAAAZ,GAAA,CAAApB,aAAA,CAAAmC,MAAA,GAAAC,WAAA,2CAAAzB,EAAA,CAAA0B,aAAA,CAAsH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}