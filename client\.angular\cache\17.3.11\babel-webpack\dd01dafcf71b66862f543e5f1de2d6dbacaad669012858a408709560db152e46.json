{"ast": null, "code": "import { SelectCustomerComponent } from \"./select-customer.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport class SelectCustomerAuthGuard {\n  constructor(dialog, auth) {\n    this.dialog = dialog;\n    this.auth = auth;\n    this.isDialogOpen = false;\n  }\n  canActivate(route, state) {\n    return this.authenticate(state.url);\n  }\n  canActivateChild(childRoute, state) {\n    return this.authenticate(state.url);\n  }\n  authenticate(url) {\n    if (this.auth.isLoggedIn && !this.auth.isCustomerSelected) {\n      if (!this.isDialogOpen) {\n        this.isDialogOpen = true;\n        this.openDialog();\n      }\n    }\n    return true;\n  }\n  openDialog() {\n    const dialogRef = this.dialog.open(SelectCustomerComponent, {\n      header: \"Select Vendor\",\n      width: '80vw'\n    });\n  }\n  static {\n    this.ɵfac = function SelectCustomerAuthGuard_Factory(t) {\n      return new (t || SelectCustomerAuthGuard)(i0.ɵɵinject(i1.DialogService), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SelectCustomerAuthGuard,\n      factory: SelectCustomerAuthGuard.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["SelectCustomerComponent", "SelectCustomerAuthGuard", "constructor", "dialog", "auth", "isDialogOpen", "canActivate", "route", "state", "authenticate", "url", "canActivateChild", "childRoute", "isLoggedIn", "isCustomerSelected", "openDialog", "dialogRef", "open", "header", "width", "i0", "ɵɵinject", "i1", "DialogService", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\select-customer\\select.customer.auth.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport {\r\n  ActivatedRouteSnapshot,\r\n  CanActivate,\r\n  CanActivateChild,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from \"@angular/router\";\r\nimport { DialogService } from \"primeng/dynamicdialog\";\r\nimport { SelectCustomerComponent } from \"./select-customer.component\";\r\nimport { AuthService } from \"src/app/core/authentication/auth.service\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class SelectCustomerAuthGuard implements CanActivate, CanActivateChild {\r\n  private isDialogOpen: boolean = false;\r\n  constructor(\r\n    private dialog: DialogService,\r\n    private auth: AuthService\r\n  ) { }\r\n\r\n  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {\r\n    return this.authenticate(state.url);\r\n  }\r\n\r\n  canActivateChild(\r\n    childRoute: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): boolean | UrlTree {\r\n    return this.authenticate(state.url);\r\n  }\r\n\r\n  private authenticate(url: string): boolean | UrlTree {\r\n    if (this.auth.isLoggedIn && !this.auth.isCustomerSelected) {\r\n      if (!this.isDialogOpen) {\r\n        this.isDialogOpen = true;\r\n        this.openDialog();\r\n      }\r\n    }\r\n    return true;\r\n  }\r\n\r\n  openDialog() {\r\n    const dialogRef = this.dialog.open(\r\n      SelectCustomerComponent,\r\n      {\r\n        header: \"Select Vendor\",\r\n        width: '80vw'\r\n      }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AASA,SAASA,uBAAuB,QAAQ,6BAA6B;;;;AAMrE,OAAM,MAAOC,uBAAuB;EAElCC,YACUC,MAAqB,EACrBC,IAAiB;IADjB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IAHN,KAAAC,YAAY,GAAY,KAAK;EAIjC;EAEJC,WAAWA,CAACC,KAA6B,EAAEC,KAA0B;IACnE,OAAO,IAAI,CAACC,YAAY,CAACD,KAAK,CAACE,GAAG,CAAC;EACrC;EAEAC,gBAAgBA,CACdC,UAAkC,EAClCJ,KAA0B;IAE1B,OAAO,IAAI,CAACC,YAAY,CAACD,KAAK,CAACE,GAAG,CAAC;EACrC;EAEQD,YAAYA,CAACC,GAAW;IAC9B,IAAI,IAAI,CAACN,IAAI,CAACS,UAAU,IAAI,CAAC,IAAI,CAACT,IAAI,CAACU,kBAAkB,EAAE;MACzD,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE;QACtB,IAAI,CAACA,YAAY,GAAG,IAAI;QACxB,IAAI,CAACU,UAAU,EAAE;MACnB;IACF;IACA,OAAO,IAAI;EACb;EAEAA,UAAUA,CAAA;IACR,MAAMC,SAAS,GAAG,IAAI,CAACb,MAAM,CAACc,IAAI,CAChCjB,uBAAuB,EACvB;MACEkB,MAAM,EAAE,eAAe;MACvBC,KAAK,EAAE;KACR,CACF;EACH;;;uBApCWlB,uBAAuB,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAvBxB,uBAAuB;MAAAyB,OAAA,EAAvBzB,uBAAuB,CAAA0B,IAAA;MAAAC,UAAA,EAFtB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}