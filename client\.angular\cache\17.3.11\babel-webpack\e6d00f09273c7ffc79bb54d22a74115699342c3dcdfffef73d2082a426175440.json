{"ast": null, "code": "import { AppSidebarComponent } from './app.sidebar.component';\nimport { environment } from 'src/environments/environment';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { SelectCustomerComponent } from './select-customer/select-customer.component';\nimport { Permission } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/layout/service/app.layout.service\";\nimport * as i2 from \"primeng/dynamicdialog\";\nimport * as i3 from \"src/app/core/authentication/auth.service\";\nimport * as i4 from \"./service/manage-user.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/ripple\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"primeng/styleclass\";\nimport * as i10 from \"./app.breadcrumb.component\";\nimport * as i11 from \"./app.sidebar.component\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"searchinput\"];\nconst _c2 = () => [\"/store/profile\"];\nconst _c3 = () => [\"/backoffice\"];\nfunction AppTopbarComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AppTopbarComponent_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDialog());\n    });\n    i0.ɵɵelementStart(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28)(4, \"small\");\n    i0.ɵɵtext(5, \"Switch Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.total_customer);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", (ctx_r2.loggedInUser == null ? null : ctx_r2.loggedInUser.customer == null ? null : ctx_r2.loggedInUser.customer.bp_id) + \" - \" + (ctx_r2.loggedInUser == null ? null : ctx_r2.loggedInUser.customer == null ? null : ctx_r2.loggedInUser.customer.customer_name));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.loggedInUser == null ? null : ctx_r2.loggedInUser.customer == null ? null : ctx_r2.loggedInUser.customer.bp_id) + \" - \" + (ctx_r2.loggedInUser == null ? null : ctx_r2.loggedInUser.customer == null ? null : ctx_r2.loggedInUser.customer.customer_name), \"\");\n  }\n}\nfunction AppTopbarComponent_li_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17)(1, \"a\", 18);\n    i0.ɵɵelement(2, \"i\", 30);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Backoffice\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction AppTopbarComponent_li_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 17)(1, \"a\", 31);\n    i0.ɵɵlistener(\"click\", function AppTopbarComponent_li_25_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.adaptUI());\n    });\n    i0.ɵɵelement(2, \"i\", 32);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Adapt UI\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class AppTopbarComponent {\n  constructor(layoutService, el, dialog, authService, manageUserService) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.manageUserService = manageUserService;\n    this.searchActive = false;\n    this.loggedInUser = null;\n    this.hasAdminAccess = false;\n    this.total_customer = 0;\n  }\n  ngOnInit() {\n    let permissions = this.authService.getPermissions;\n    if (permissions.includes(Permission.Vendor_Portal_Backoffice)) {\n      this.hasAdminAccess = true;\n    }\n    this.loggedInUser = this.authService.userDetail;\n    this.manageUserService.getLoggedInUserCustomersCount().subscribe({\n      next: res => {\n        this.total_customer = res || 0;\n      }\n    });\n  }\n  openDialog() {\n    this.dialog.open(SelectCustomerComponent, {\n      header: 'Select Customer',\n      width: '80vw'\n    });\n  }\n  activateSearch() {\n    this.searchActive = true;\n    setTimeout(() => {\n      this.searchInput.nativeElement.focus();\n    }, 100);\n  }\n  deactivateSearch() {\n    this.searchActive = false;\n  }\n  onMenuButtonClick() {\n    this.layoutService.onMenuToggle();\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  onSidebarButtonClick() {\n    this.layoutService.showSidebar();\n  }\n  logout() {\n    this.authService.doLogout();\n  }\n  adaptUI() {\n    window.open(environment.cmsApiEndpoint + '/admin', '_blank');\n  }\n  static {\n    this.ɵfac = function AppTopbarComponent_Factory(t) {\n      return new (t || AppTopbarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.DialogService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ManageUserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppTopbarComponent,\n      selectors: [[\"app-topbar\"]],\n      viewQuery: function AppTopbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(AppSidebarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService])],\n      decls: 36,\n      vars: 6,\n      consts: [[\"menubutton\", \"\"], [\"profile\", \"\"], [1, \"layout-topbar\"], [1, \"topbar-start\"], [\"type\", \"button\", 1, \"topbar-menubutton\", \"p-link\", \"p-trigger\", 3, \"click\"], [1, \"pi\", \"pi-bars\"], [1, \"topbar-breadcrumb\"], [1, \"layout-topbar-menu-section\"], [1, \"topbar-end\"], [1, \"topbar-menu\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-1 p-button-rounded flex gap-2 ml-auto\", 3, \"click\", 4, \"ngIf\"], [1, \"ml-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-cog\", 1, \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"profile-item\", \"topbar-item\", \"relative\"], [\"pStyleClass\", \"@next\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", \"pRipple\", \"\", 1, \"cursor-pointer\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [1, \"topbar-menu\", \"active-topbar-menu\", \"p-4\", \"w-15rem\", \"z-5\", \"ng-hidden\", \"border-round\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mb-3\"], [\"href\", \"javascript: void(0)\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"routerLink\"], [1, \"pi\", \"pi-fw\", \"pi-user\", \"mr-2\"], [\"role\", \"menuitem\", \"class\", \"m-0 mb-3\", 4, \"ngIf\"], [\"href\", \"#\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\"], [1, \"pi\", \"pi-fw\", \"pi-cog\", \"mr-2\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mt-3\", \"pt-3\", \"border-top-1\", \"logout-btn\"], [\"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", \"font-medium\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sign-out\", \"mr-2\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"p-1\", \"p-button-rounded\", \"flex\", \"gap-2\", \"ml-auto\", 3, \"click\"], [1, \"ml-1\", \"text-lg\", \"border-primary\", \"border-circle\", \"border-1\", \"w-2rem\", \"h-2rem\", \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-white\", \"text-primary\"], [1, \"flex\", \"flex-column\", \"w-15rem\", \"align-items-start\"], [1, \"m-0\", \"p-0\", \"relative\", \"w-full\", \"max-w-17rem\", \"overflow-hidden\", \"text-overflow-ellipsis\", \"white-space-nowrap\", 3, \"title\"], [1, \"pi\", \"pi-fw\", \"pi-desktop\", \"mr-2\"], [\"href\", \"javascript: void(0)\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sliders-h\", \"mr-2\"]],\n      template: function AppTopbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"button\", 4, 0);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMenuButtonClick());\n          });\n          i0.ɵɵelement(4, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"app-breadcrumb\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7);\n          i0.ɵɵelement(7, \"app-sidebar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\");\n          i0.ɵɵtemplate(11, AppTopbarComponent_button_11_Template, 8, 3, \"button\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\", 11)(13, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onConfigButtonClick());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"li\", 13, 1)(16, \"a\", 14);\n          i0.ɵɵelement(17, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"ul\", 16)(19, \"li\", 17)(20, \"a\", 18);\n          i0.ɵɵelement(21, \"i\", 19);\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23, \"Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(24, AppTopbarComponent_li_24_Template, 5, 2, \"li\", 20)(25, AppTopbarComponent_li_25_Template, 5, 0, \"li\", 20);\n          i0.ɵɵelementStart(26, \"li\", 17)(27, \"a\", 21);\n          i0.ɵɵelement(28, \"i\", 22);\n          i0.ɵɵelementStart(29, \"span\");\n          i0.ɵɵtext(30, \"Settings\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"li\", 23)(32, \"a\", 24);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_a_click_32_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelement(33, \"i\", 25);\n          i0.ɵɵelementStart(34, \"span\");\n          i0.ɵɵtext(35, \"Logout\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.total_customer > 1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c2));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasAdminAccess);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasAdaptUIAccess);\n        }\n      },\n      dependencies: [i5.NgIf, i6.ButtonDirective, i7.Ripple, i8.RouterLink, i9.StyleClass, i10.AppBreadcrumbComponent, i11.AppSidebarComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "environment", "DialogService", "SelectCustomerComponent", "Permission", "i0", "ɵɵelementStart", "ɵɵlistener", "AppTopbarComponent_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openDialog", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "total_customer", "ɵɵproperty", "loggedInUser", "customer", "bp_id", "customer_name", "ɵɵtextInterpolate1", "ɵɵelement", "ɵɵpureFunction0", "_c3", "AppTopbarComponent_li_25_Template_a_click_1_listener", "_r4", "adaptUI", "AppTopbarComponent", "constructor", "layoutService", "el", "dialog", "authService", "manageUserService", "searchActive", "hasAdminAccess", "ngOnInit", "permissions", "getPermissions", "includes", "Vendor_Portal_Backoffice", "userDetail", "getLoggedInUserCustomersCount", "subscribe", "next", "res", "open", "header", "width", "activateSearch", "setTimeout", "searchInput", "nativeElement", "focus", "deactivateSearch", "onMenuButtonClick", "onMenuToggle", "onConfigButtonClick", "showConfigSidebar", "onSidebarButtonClick", "showSidebar", "logout", "doLogout", "window", "cmsApiEndpoint", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "i3", "AuthService", "i4", "ManageUserService", "selectors", "viewQuery", "AppTopbarComponent_Query", "rf", "ctx", "decls", "vars", "consts", "template", "AppTopbarComponent_Template", "AppTopbarComponent_Template_button_click_2_listener", "_r1", "ɵɵtemplate", "AppTopbarComponent_button_11_Template", "AppTopbarComponent_Template_button_click_13_listener", "AppTopbarComponent_li_24_Template", "AppTopbarComponent_li_25_Template", "AppTopbarComponent_Template_a_click_32_listener", "_c2", "hasAdaptUIAccess"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.topbar.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.topbar.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { LayoutService } from 'src/app/store/layout/service/app.layout.service';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { ManageUserService } from './service/manage-user.service';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { SelectCustomerComponent } from './select-customer/select-customer.component';\r\nimport { Permission } from 'src/app/constants/api.constants';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    templateUrl: './app.topbar.component.html',\r\n    providers: [DialogService]\r\n})\r\nexport class AppTopbarComponent implements OnInit {\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n    @ViewChild('searchinput') searchInput!: ElementRef;\r\n    @ViewChild(AppSidebarComponent) appSidebar!: AppSidebarComponent;\r\n    searchActive: boolean = false;\r\n    public loggedInUser: any = null;\r\n    hasAdminAccess: boolean = false;\r\n    total_customer = 0;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n        public dialog: DialogService,\r\n        private authService: AuthService,\r\n        private manageUserService: ManageUserService\r\n    ) { }\r\n\r\n    ngOnInit(): void {\r\n        let permissions = this.authService.getPermissions;\r\n        if (permissions.includes(Permission.Vendor_Portal_Backoffice)) {\r\n            this.hasAdminAccess = true;\r\n        }\r\n        this.loggedInUser = this.authService.userDetail;\r\n        this.manageUserService.getLoggedInUserCustomersCount().subscribe({\r\n            next: (res: any) => {\r\n                this.total_customer = res || 0;\r\n            }\r\n        });\r\n    }\r\n\r\n    openDialog() {\r\n        this.dialog.open(SelectCustomerComponent, {\r\n            header: 'Select Customer',\r\n            width: '80vw'\r\n        });\r\n    }\r\n\r\n    activateSearch() {\r\n        this.searchActive = true;\r\n        setTimeout(() => {\r\n            this.searchInput.nativeElement.focus();\r\n        }, 100);\r\n    }\r\n\r\n    deactivateSearch() {\r\n        this.searchActive = false;\r\n    }\r\n    onMenuButtonClick() {\r\n        this.layoutService.onMenuToggle();\r\n    }\r\n\r\n    onConfigButtonClick() {\r\n        this.layoutService.showConfigSidebar();\r\n    }\r\n\r\n    onSidebarButtonClick() {\r\n        this.layoutService.showSidebar();\r\n    }\r\n\r\n    logout() {\r\n        this.authService.doLogout();\r\n    }\r\n\r\n    adaptUI() {\r\n        window.open(environment.cmsApiEndpoint + '/admin', '_blank');\r\n    }\r\n}", "<div class=\"layout-topbar\">\r\n    <div class=\"topbar-start\">\r\n        <button #menubutton type=\"button\" class=\"topbar-menubutton p-link p-trigger\" (click)=\"onMenuButtonClick()\">\r\n            <i class=\"pi pi-bars\"></i>\r\n        </button>\r\n\r\n        <app-breadcrumb class=\"topbar-breadcrumb\"></app-breadcrumb>\r\n    </div>\r\n    <div class=\"layout-topbar-menu-section\">\r\n        <app-sidebar></app-sidebar>\r\n    </div>\r\n    <div class=\"topbar-end\">\r\n        <ul class=\"topbar-menu  \">\r\n            <!-- <li class=\"hidden lg:block\">\r\n                <div class=\"topbar-search\" [ngClass]=\"{'topbar-search-active': searchActive}\">\r\n                    <button pButton icon=\"pi pi-search\"\r\n                        class=\"topbar-searchbutton p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n                        type=\"button\" (click)=\"activateSearch()\"></button>\r\n                    <div class=\"search-input-wrapper\">\r\n                        <span class=\"p-input-icon-right\">\r\n                            <input #searchinput type=\"text\" pInputText placeholder=\"Search\" (blur)=\"deactivateSearch()\"\r\n                                (keydown.escape)=\"deactivateSearch()\" />\r\n                            <i class=\"pi pi-search\"></i>\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </li>\r\n\r\n            <li class=\"profile-item topbar-item \">\r\n                <button pButton type=\"button\" icon=\"pi pi-bell\"\r\n                    class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n            </li>\r\n\r\n            <li class=\"profile-item topbar-item \">\r\n                <button pButton type=\"button\" icon=\"pi pi-comment\"\r\n                    class=\"p-button-text p-button-secondary relative text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n            </li> -->\r\n\r\n            <li>\r\n                <button type=\"button\" pButton class=\"p-1 p-button-rounded flex gap-2 ml-auto\" (click)=\"openDialog()\"\r\n                    *ngIf=\"total_customer > 1\">\r\n                    <span\r\n                        class=\"ml-1 text-lg border-primary border-circle border-1 w-2rem h-2rem flex justify-content-center align-items-center bg-white text-primary\">{{\r\n                        total_customer }}</span>\r\n                    <div class=\"flex flex-column w-15rem align-items-start\">\r\n                        <small>Switch Account</small>\r\n                        <p class=\"m-0 p-0 relative w-full max-w-17rem overflow-hidden text-overflow-ellipsis white-space-nowrap\"\r\n                            [title]=\"loggedInUser?.customer?.bp_id + ' - ' + loggedInUser?.customer?.customer_name\">\r\n                            {{loggedInUser?.customer?.bp_id + ' - ' + loggedInUser?.customer?.customer_name}}</p>\r\n                    </div>\r\n                </button>\r\n            </li>\r\n\r\n            <li class=\"ml-3\">\r\n                <button pButton type=\"button\" icon=\"pi pi-cog\"\r\n                    class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n                    (click)=\"onConfigButtonClick()\"></button>\r\n            </li>\r\n\r\n            <li #profile class=\"profile-item topbar-item relative\">\r\n                <a pStyleClass=\"@next\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\" leaveToClass=\"ng-hidden\"\r\n                    leaveActiveClass=\"px-fadeout\" [hideOnOutsideClick]=\"true\" pRipple class=\"cursor-pointer\">\r\n                    <i class=\"pi pi-fw pi-user\"></i>\r\n                </a>\r\n\r\n                <ul class=\"topbar-menu active-topbar-menu p-4 w-15rem z-5 ng-hidden border-round\">\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"javascript: void(0)\" [routerLink]=\"['/store/profile']\"\r\n                            class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-user mr-2\"></i>\r\n                            <span>Profile</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\" *ngIf=\"hasAdminAccess\">\r\n                        <a href=\"javascript: void(0)\" [routerLink]=\"['/backoffice']\"\r\n                            class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-desktop mr-2\"></i>\r\n                            <span>Backoffice</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\" *ngIf=\"hasAdaptUIAccess\">\r\n                        <a href=\"javascript: void(0)\" (click)=\"adaptUI()\"\r\n                            class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-sliders-h mr-2\"></i>\r\n                            <span>Adapt UI</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"#\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-cog mr-2\"></i>\r\n                            <span>Settings</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mt-3 pt-3 border-top-1 logout-btn\">\r\n                        <a (click)=\"logout()\"\r\n                            class=\"flex align-items-center hover:text-primary-500 transition-duration-200 font-medium cursor-pointer\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-sign-out mr-2\"></i>\r\n                            <span>Logout</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n            </li>\r\n\r\n            <!-- <li class=\"right-panel-button relative hidden lg:block\">\r\n                <button pButton type=\"button\" label=\"Today\" style=\"width:5.7rem\" icon=\"pi pi-bookmark\"\r\n                    class=\"layout-rightmenu-button hidden md:block font-normal\" styleClass=\"font-normal\"\r\n                    (click)=\"onSidebarButtonClick()\"></button>\r\n                <button pButton type=\"button\" icon=\"pi pi-bookmark\" styleClass=\"font-normal\"\r\n                    class=\"layout-rightmenu-button block md:hidden\" (click)=\"onSidebarButtonClick()\"></button>\r\n            </li> -->\r\n        </ul>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yBAAyB;AAE7D,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,UAAU,QAAQ,iCAAiC;;;;;;;;;;;;;;;;;;;;IC+B5CC,EAAA,CAAAC,cAAA,iBAC+B;IAD+CD,EAAA,CAAAE,UAAA,mBAAAC,8DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAEhGT,EAAA,CAAAC,cAAA,eACkJ;IAAAD,EAAA,CAAAU,MAAA,GAC7H;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAExBX,EADJ,CAAAC,cAAA,cAAwD,YAC7C;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IAC7BX,EAAA,CAAAC,cAAA,YAC4F;IACxFD,EAAA,CAAAU,MAAA,GAAiF;IAE7FV,EAF6F,CAAAW,YAAA,EAAI,EACvF,EACD;;;;IAR6IX,EAAA,CAAAY,SAAA,GAC7H;IAD6HZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,cAAA,CAC7H;IAIbd,EAAA,CAAAY,SAAA,GAAuF;IAAvFZ,EAAA,CAAAe,UAAA,WAAAT,MAAA,CAAAU,YAAA,kBAAAV,MAAA,CAAAU,YAAA,CAAAC,QAAA,kBAAAX,MAAA,CAAAU,YAAA,CAAAC,QAAA,CAAAC,KAAA,aAAAZ,MAAA,CAAAU,YAAA,kBAAAV,MAAA,CAAAU,YAAA,CAAAC,QAAA,kBAAAX,MAAA,CAAAU,YAAA,CAAAC,QAAA,CAAAE,aAAA,EAAuF;IACvFnB,EAAA,CAAAY,SAAA,EAAiF;IAAjFZ,EAAA,CAAAoB,kBAAA,OAAAd,MAAA,CAAAU,YAAA,kBAAAV,MAAA,CAAAU,YAAA,CAAAC,QAAA,kBAAAX,MAAA,CAAAU,YAAA,CAAAC,QAAA,CAAAC,KAAA,aAAAZ,MAAA,CAAAU,YAAA,kBAAAV,MAAA,CAAAU,YAAA,CAAAC,QAAA,kBAAAX,MAAA,CAAAU,YAAA,CAAAC,QAAA,CAAAE,aAAA,MAAiF;;;;;IA4BrFnB,EADJ,CAAAC,cAAA,aAA4D,YAIG;IACvDD,EAAA,CAAAqB,SAAA,YAAwC;IACxCrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAExBV,EAFwB,CAAAW,YAAA,EAAO,EACvB,EACH;;;IAP6BX,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAA8B;;;;;;IAS5DvB,EADJ,CAAAC,cAAA,aAA8D,YAIC;IAH7BD,EAAA,CAAAE,UAAA,mBAAAsB,qDAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAqB,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoB,OAAA,EAAS;IAAA,EAAC;IAI7C1B,EAAA,CAAAqB,SAAA,YAA0C;IAC1CrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAEtBV,EAFsB,CAAAW,YAAA,EAAO,EACrB,EACH;;;AD7EzB,OAAM,MAAOgB,kBAAkB;EAU3BC,YACWC,aAA4B,EAC5BC,EAAc,EACdC,MAAqB,EACpBC,WAAwB,EACxBC,iBAAoC;IAJrC,KAAAJ,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAV7B,KAAAC,YAAY,GAAY,KAAK;IACtB,KAAAlB,YAAY,GAAQ,IAAI;IAC/B,KAAAmB,cAAc,GAAY,KAAK;IAC/B,KAAArB,cAAc,GAAG,CAAC;EAQd;EAEJsB,QAAQA,CAAA;IACJ,IAAIC,WAAW,GAAG,IAAI,CAACL,WAAW,CAACM,cAAc;IACjD,IAAID,WAAW,CAACE,QAAQ,CAACxC,UAAU,CAACyC,wBAAwB,CAAC,EAAE;MAC3D,IAAI,CAACL,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,CAACnB,YAAY,GAAG,IAAI,CAACgB,WAAW,CAACS,UAAU;IAC/C,IAAI,CAACR,iBAAiB,CAACS,6BAA6B,EAAE,CAACC,SAAS,CAAC;MAC7DC,IAAI,EAAGC,GAAQ,IAAI;QACf,IAAI,CAAC/B,cAAc,GAAG+B,GAAG,IAAI,CAAC;MAClC;KACH,CAAC;EACN;EAEApC,UAAUA,CAAA;IACN,IAAI,CAACsB,MAAM,CAACe,IAAI,CAAChD,uBAAuB,EAAE;MACtCiD,MAAM,EAAE,iBAAiB;MACzBC,KAAK,EAAE;KACV,CAAC;EACN;EAEAC,cAAcA,CAAA;IACV,IAAI,CAACf,YAAY,GAAG,IAAI;IACxBgB,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,KAAK,EAAE;IAC1C,CAAC,EAAE,GAAG,CAAC;EACX;EAEAC,gBAAgBA,CAAA;IACZ,IAAI,CAACpB,YAAY,GAAG,KAAK;EAC7B;EACAqB,iBAAiBA,CAAA;IACb,IAAI,CAAC1B,aAAa,CAAC2B,YAAY,EAAE;EACrC;EAEAC,mBAAmBA,CAAA;IACf,IAAI,CAAC5B,aAAa,CAAC6B,iBAAiB,EAAE;EAC1C;EAEAC,oBAAoBA,CAAA;IAChB,IAAI,CAAC9B,aAAa,CAAC+B,WAAW,EAAE;EACpC;EAEAC,MAAMA,CAAA;IACF,IAAI,CAAC7B,WAAW,CAAC8B,QAAQ,EAAE;EAC/B;EAEApC,OAAOA,CAAA;IACHqC,MAAM,CAACjB,IAAI,CAAClD,WAAW,CAACoE,cAAc,GAAG,QAAQ,EAAE,QAAQ,CAAC;EAChE;;;uBAlESrC,kBAAkB,EAAA3B,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAnE,EAAA,CAAAiE,iBAAA,CAAAjE,EAAA,CAAAoE,UAAA,GAAApE,EAAA,CAAAiE,iBAAA,CAAAI,EAAA,CAAAxE,aAAA,GAAAG,EAAA,CAAAiE,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAvE,EAAA,CAAAiE,iBAAA,CAAAO,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAlB9C,kBAAkB;MAAA+C,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;yBAIhBlF,mBAAmB;;;;;;;;;uCANnB,CAACE,aAAa,CAAC;MAAAkF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCXtB7E,EAFR,CAAAC,cAAA,aAA2B,aACG,mBACqF;UAA9BD,EAAA,CAAAE,UAAA,mBAAAkF,oDAAA;YAAApF,EAAA,CAAAI,aAAA,CAAAiF,GAAA;YAAA,OAAArF,EAAA,CAAAQ,WAAA,CAASsE,GAAA,CAAAvB,iBAAA,EAAmB;UAAA,EAAC;UACtGvD,EAAA,CAAAqB,SAAA,WAA0B;UAC9BrB,EAAA,CAAAW,YAAA,EAAS;UAETX,EAAA,CAAAqB,SAAA,wBAA2D;UAC/DrB,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAC,cAAA,aAAwC;UACpCD,EAAA,CAAAqB,SAAA,kBAA2B;UAC/BrB,EAAA,CAAAW,YAAA,EAAM;UA4BEX,EA3BR,CAAAC,cAAA,aAAwB,YACM,UA0BlB;UACAD,EAAA,CAAAsF,UAAA,KAAAC,qCAAA,qBAC+B;UAWnCvF,EAAA,CAAAW,YAAA,EAAK;UAGDX,EADJ,CAAAC,cAAA,cAAiB,kBAGuB;UAAhCD,EAAA,CAAAE,UAAA,mBAAAsF,qDAAA;YAAAxF,EAAA,CAAAI,aAAA,CAAAiF,GAAA;YAAA,OAAArF,EAAA,CAAAQ,WAAA,CAASsE,GAAA,CAAArB,mBAAA,EAAqB;UAAA,EAAC;UACvCzD,EADwC,CAAAW,YAAA,EAAS,EAC5C;UAGDX,EADJ,CAAAC,cAAA,iBAAuD,aAE0C;UACzFD,EAAA,CAAAqB,SAAA,aAAgC;UACpCrB,EAAA,CAAAW,YAAA,EAAI;UAIIX,EAFR,CAAAC,cAAA,cAAkF,cACzC,aAI0B;UACvDD,EAAA,CAAAqB,SAAA,aAAqC;UACrCrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,eAAO;UAErBV,EAFqB,CAAAW,YAAA,EAAO,EACpB,EACH;UAULX,EATA,CAAAsF,UAAA,KAAAG,iCAAA,iBAA4D,KAAAC,iCAAA,iBASE;UAU1D1F,EADJ,CAAAC,cAAA,cAAqC,aAG0B;UACvDD,EAAA,CAAAqB,SAAA,aAAoC;UACpCrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,gBAAQ;UAEtBV,EAFsB,CAAAW,YAAA,EAAO,EACrB,EACH;UAEDX,EADJ,CAAAC,cAAA,cAAkE,aAIH;UAHxDD,EAAA,CAAAE,UAAA,mBAAAyF,gDAAA;YAAA3F,EAAA,CAAAI,aAAA,CAAAiF,GAAA;YAAA,OAAArF,EAAA,CAAAQ,WAAA,CAASsE,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAIjB7D,EAAA,CAAAqB,SAAA,aAAyC;UACzCrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAU,MAAA,cAAM;UAexCV,EAfwC,CAAAW,YAAA,EAAO,EACnB,EACH,EACJ,EACJ,EASJ,EACH,EACJ;;;UAlFeX,EAAA,CAAAY,SAAA,IAAwB;UAAxBZ,EAAA,CAAAe,UAAA,SAAA+D,GAAA,CAAAhE,cAAA,KAAwB;UAqBKd,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAe,UAAA,4BAA2B;UAMvBf,EAAA,CAAAY,SAAA,GAAiC;UAAjCZ,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAsB,eAAA,IAAAsE,GAAA,EAAiC;UAQ7B5F,EAAA,CAAAY,SAAA,GAAoB;UAApBZ,EAAA,CAAAe,UAAA,SAAA+D,GAAA,CAAA3C,cAAA,CAAoB;UASpBnC,EAAA,CAAAY,SAAA,EAAsB;UAAtBZ,EAAA,CAAAe,UAAA,SAAA+D,GAAA,CAAAe,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}