{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../supplier.service\";\nexport class SupplierInfoComponent {\n  constructor(supplierservice) {\n    this.supplierservice = supplierservice;\n    this.unsubscribe$ = new Subject();\n    this.supplierDetails = null;\n  }\n  ngOnInit() {\n    this.supplierservice.supplier.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.supplierDetails = data?.[0];\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SupplierInfoComponent_Factory(t) {\n      return new (t || SupplierInfoComponent)(i0.ɵɵdirectiveInject(i1.SupplierService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierInfoComponent,\n      selectors: [[\"app-supplier-info\"]],\n      decls: 46,\n      vars: 9,\n      consts: [[1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"]],\n      template: function SupplierInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2);\n          i0.ɵɵtext(3, \"Partner Id\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 1)(7, \"span\", 2);\n          i0.ɵɵtext(8, \"Customer Id\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"span\", 3);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 1)(12, \"span\", 2);\n          i0.ɵɵtext(13, \"Supplier Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 3);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 1)(17, \"span\", 2);\n          i0.ɵɵtext(18, \"Authorization Group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 3);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 1)(22, \"span\", 2);\n          i0.ɵɵtext(23, \"Created By User\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 3);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 1)(27, \"span\", 2);\n          i0.ɵɵtext(28, \"Responsible Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 3);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 1)(32, \"span\", 2);\n          i0.ɵɵtext(33, \"Supplier Account Group \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 3);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 1)(37, \"span\", 2);\n          i0.ɵɵtext(38, \"Supplier Corporate Group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 3);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 1)(42, \"span\", 2);\n          i0.ɵɵtext(43, \"Vat Registration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 3);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.supplierDetails == null ? null : ctx.supplierDetails.bp_id) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.supplierDetails == null ? null : ctx.supplierDetails.customer_id) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.supplierDetails == null ? null : ctx.supplierDetails.supplier_name) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.supplierDetails == null ? null : ctx.supplierDetails.authorization_group) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.supplierDetails == null ? null : ctx.supplierDetails.created_by_user) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.supplierDetails == null ? null : ctx.supplierDetails.responsible_type) || \"-\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.supplierDetails == null ? null : ctx.supplierDetails.supplier_account_group) || \"-\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.supplierDetails == null ? null : ctx.supplierDetails.supplier_corporate_group) || \"-\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate((ctx.supplierDetails == null ? null : ctx.supplierDetails.vat_registration) || \"-\");\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "SupplierInfoComponent", "constructor", "supplierservice", "unsubscribe$", "supplierDetails", "ngOnInit", "supplier", "pipe", "subscribe", "data", "ngOnDestroy", "next", "complete", "i0", "ɵɵdirectiveInject", "i1", "SupplierService", "selectors", "decls", "vars", "consts", "template", "SupplierInfoComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "bp_id", "customer_id", "supplier_name", "authorization_group", "created_by_user", "responsible_type", "ɵɵtextInterpolate", "supplier_account_group", "supplier_corporate_group", "vat_registration"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\supplier\\supplier-details\\supplier-info\\supplier-info.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\supplier\\supplier-details\\supplier-info\\supplier-info.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { SupplierService } from '../../supplier.service';\r\n\r\n@Component({\r\n  selector: 'app-supplier-info',\r\n  templateUrl: './supplier-info.component.html',\r\n  styleUrl: './supplier-info.component.scss',\r\n})\r\nexport class SupplierInfoComponent {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public supplierDetails: any = null;\r\n\r\n  constructor(private supplierservice: SupplierService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.supplierservice.supplier\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.supplierDetails = data?.[0];\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"grid mx-0\">\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Partner Id</span>\r\n        <span class=\"block font-medium mb-3 text-600\">\r\n            {{ supplierDetails?.bp_id || \"-\" }}\r\n        </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Customer Id</span>\r\n        <span class=\"block font-medium mb-3 text-600\">\r\n            {{ supplierDetails?.customer_id || \"-\" }}\r\n        </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Supplier Name</span>\r\n        <span class=\"block font-medium mb-3 text-600\">\r\n            {{ supplierDetails?.supplier_name || \"-\" }}\r\n        </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Authorization Group</span>\r\n        <span class=\"block font-medium mb-3 text-600\">\r\n            {{ supplierDetails?.authorization_group || \"-\" }}\r\n        </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Created By User</span>\r\n        <span class=\"block font-medium mb-3 text-600\">\r\n            {{ supplierDetails?.created_by_user || \"-\" }}\r\n        </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Responsible Type</span>\r\n        <span class=\"block font-medium mb-3 text-600\">\r\n            {{ supplierDetails?.responsible_type || \"-\" }}\r\n        </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Supplier Account Group\r\n        </span>\r\n        <span class=\"block font-medium mb-3 text-600\">{{\r\n            supplierDetails?.supplier_account_group || \"-\"\r\n            }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Supplier Corporate Group</span>\r\n        <span class=\"block font-medium mb-3 text-600\">{{\r\n            supplierDetails?.supplier_corporate_group || \"-\"\r\n            }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n        <span class=\"text-900 block font-medium mb-3 font-bold\">Vat Registration</span>\r\n        <span class=\"block font-medium mb-3 text-600\">{{\r\n            supplierDetails?.vat_registration || \"-\"\r\n            }}</span>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;AAQzC,OAAM,MAAOC,qBAAqB;EAIhCC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAH3B,KAAAC,YAAY,GAAG,IAAIL,OAAO,EAAQ;IACnC,KAAAM,eAAe,GAAQ,IAAI;EAEqB;EAEvDC,QAAQA,CAAA;IACN,IAAI,CAACH,eAAe,CAACI,QAAQ,CAC1BC,IAAI,CAACR,SAAS,CAAC,IAAI,CAACI,YAAY,CAAC,CAAC,CAClCK,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACL,eAAe,GAAGK,IAAI,GAAG,CAAC,CAAC;IAClC,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,YAAY,CAACQ,IAAI,EAAE;IACxB,IAAI,CAACR,YAAY,CAACS,QAAQ,EAAE;EAC9B;;;uBAjBWZ,qBAAqB,EAAAa,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAArBhB,qBAAqB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BV,EAFR,CAAAY,cAAA,aAAuB,aACU,cAC+B;UAAAZ,EAAA,CAAAa,MAAA,iBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAO;UACzEd,EAAA,CAAAY,cAAA,cAA8C;UAC1CZ,EAAA,CAAAa,MAAA,GACJ;UACJb,EADI,CAAAc,YAAA,EAAO,EACL;UAEFd,EADJ,CAAAY,cAAA,aAA6B,cAC+B;UAAAZ,EAAA,CAAAa,MAAA,kBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAC1Ed,EAAA,CAAAY,cAAA,cAA8C;UAC1CZ,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAO,EACL;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAC5Ed,EAAA,CAAAY,cAAA,eAA8C;UAC1CZ,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAO,EACL;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAClFd,EAAA,CAAAY,cAAA,eAA8C;UAC1CZ,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAO,EACL;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAC9Ed,EAAA,CAAAY,cAAA,eAA8C;UAC1CZ,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAO,EACL;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAC/Ed,EAAA,CAAAY,cAAA,eAA8C;UAC1CZ,EAAA,CAAAa,MAAA,IACJ;UACJb,EADI,CAAAc,YAAA,EAAO,EACL;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,+BACxD;UAAAb,EAAA,CAAAc,YAAA,EAAO;UACPd,EAAA,CAAAY,cAAA,eAA8C;UAAAZ,EAAA,CAAAa,MAAA,IAExC;UACVb,EADU,CAAAc,YAAA,EAAO,EACX;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,gCAAwB;UAAAb,EAAA,CAAAc,YAAA,EAAO;UACvFd,EAAA,CAAAY,cAAA,eAA8C;UAAAZ,EAAA,CAAAa,MAAA,IAExC;UACVb,EADU,CAAAc,YAAA,EAAO,EACX;UAEFd,EADJ,CAAAY,cAAA,cAA6B,eAC+B;UAAAZ,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAC/Ed,EAAA,CAAAY,cAAA,eAA8C;UAAAZ,EAAA,CAAAa,MAAA,IAExC;UAEdb,EAFc,CAAAc,YAAA,EAAO,EACX,EACJ;;;UApDMd,EAAA,CAAAe,SAAA,GACJ;UADIf,EAAA,CAAAgB,kBAAA,OAAAL,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAA0B,KAAA,cACJ;UAKIjB,EAAA,CAAAe,SAAA,GACJ;UADIf,EAAA,CAAAgB,kBAAA,OAAAL,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAA2B,WAAA,cACJ;UAKIlB,EAAA,CAAAe,SAAA,GACJ;UADIf,EAAA,CAAAgB,kBAAA,OAAAL,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAA4B,aAAA,cACJ;UAKInB,EAAA,CAAAe,SAAA,GACJ;UADIf,EAAA,CAAAgB,kBAAA,OAAAL,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAA6B,mBAAA,cACJ;UAKIpB,EAAA,CAAAe,SAAA,GACJ;UADIf,EAAA,CAAAgB,kBAAA,OAAAL,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAA8B,eAAA,cACJ;UAKIrB,EAAA,CAAAe,SAAA,GACJ;UADIf,EAAA,CAAAgB,kBAAA,OAAAL,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAA+B,gBAAA,cACJ;UAK8CtB,EAAA,CAAAe,SAAA,GAExC;UAFwCf,EAAA,CAAAuB,iBAAA,EAAAZ,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAAiC,sBAAA,SAExC;UAIwCxB,EAAA,CAAAe,SAAA,GAExC;UAFwCf,EAAA,CAAAuB,iBAAA,EAAAZ,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAAkC,wBAAA,SAExC;UAIwCzB,EAAA,CAAAe,SAAA,GAExC;UAFwCf,EAAA,CAAAuB,iBAAA,EAAAZ,GAAA,CAAApB,eAAA,kBAAAoB,GAAA,CAAApB,eAAA,CAAAmC,gBAAA,SAExC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}