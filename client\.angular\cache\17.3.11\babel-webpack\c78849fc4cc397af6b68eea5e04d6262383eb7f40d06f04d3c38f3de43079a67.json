{"ast": null, "code": "'use strict';\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\nvar Format = {\n  RFC1738: 'RFC1738',\n  RFC3986: 'RFC3986'\n};\nmodule.exports = {\n  'default': Format.RFC3986,\n  formatters: {\n    RFC1738: function (value) {\n      return replace.call(value, percentTwenties, '+');\n    },\n    RFC3986: function (value) {\n      return String(value);\n    }\n  },\n  RFC1738: Format.RFC1738,\n  RFC3986: Format.RFC3986\n};", "map": {"version": 3, "names": ["replace", "String", "prototype", "percentTwenties", "Format", "RFC1738", "RFC3986", "module", "exports", "formatters", "value", "call"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/qs/lib/formats.js"], "sourcesContent": ["'use strict';\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\n\nvar Format = {\n    RFC1738: 'RFC1738',\n    RFC3986: 'RFC3986'\n};\n\nmodule.exports = {\n    'default': Format.RFC3986,\n    formatters: {\n        RFC1738: function (value) {\n            return replace.call(value, percentTwenties, '+');\n        },\n        RFC3986: function (value) {\n            return String(value);\n        }\n    },\n    RFC1738: Format.RFC1738,\n    RFC3986: Format.RFC3986\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,MAAM,CAACC,SAAS,CAACF,OAAO;AACtC,IAAIG,eAAe,GAAG,MAAM;AAE5B,IAAIC,MAAM,GAAG;EACTC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE;AACb,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG;EACb,SAAS,EAAEJ,MAAM,CAACE,OAAO;EACzBG,UAAU,EAAE;IACRJ,OAAO,EAAE,SAAAA,CAAUK,KAAK,EAAE;MACtB,OAAOV,OAAO,CAACW,IAAI,CAACD,KAAK,EAAEP,eAAe,EAAE,GAAG,CAAC;IACpD,CAAC;IACDG,OAAO,EAAE,SAAAA,CAAUI,KAAK,EAAE;MACtB,OAAOT,MAAM,CAACS,KAAK,CAAC;IACxB;EACJ,CAAC;EACDL,OAAO,EAAED,MAAM,CAACC,OAAO;EACvBC,OAAO,EAAEF,MAAM,CAACE;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}