{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/authentication/auth.guard';\nimport { contentResolver } from './core/content-resolver';\nimport { SelectCustomerAuthGuard } from './store/layout/select-customer/select.customer.auth.guard';\nimport { Permission } from './constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routerOptions = {\n  anchorScrolling: 'enabled',\n  useHash: true\n};\nconst routes = [{\n  path: 'store',\n  canActivate: [AuthGuard, SelectCustomerAuthGuard],\n  canActivateChild: [AuthGuard, SelectCustomerAuthGuard],\n  loadChildren: () => import('./store/store.module').then(m => m.StoreModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: 'backoffice',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./backoffice/backoffice.module').then(m => m.BackofficeModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common',\n    permission: Permission.Vendor_Portal_Backoffice\n  }\n}, {\n  path: 'auth',\n  loadChildren: () => import('./session/session.module').then(mod => mod.SessionModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: '',\n  redirectTo: 'store',\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: 'store'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, routerOptions), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "contentResolver", "SelectCustomerAuthGuard", "Permission", "routerOptions", "anchorScrolling", "useHash", "routes", "path", "canActivate", "canActivateChild", "loadChildren", "then", "m", "StoreModule", "resolve", "commonContent", "data", "slug", "BackofficeModule", "permission", "Vendor_Portal_Backoffice", "mod", "SessionModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { ExtraOptions, RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './core/authentication/auth.guard';\r\nimport { contentResolver } from './core/content-resolver';\r\nimport { SelectCustomerAuthGuard } from './store/layout/select-customer/select.customer.auth.guard';\r\nimport { Permission } from './constants/api.constants';\r\n\r\nconst routerOptions: ExtraOptions = {\r\n  anchorScrolling: 'enabled',\r\n  useHash: true,\r\n};\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'store',\r\n    canActivate: [AuthGuard, SelectCustomerAuthGuard],\r\n    canActivateChild: [AuthGuard, SelectCustomerAuthGuard],\r\n    loadChildren: () =>\r\n      import('./store/store.module').then((m) => m.StoreModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common'\r\n    },\r\n  },\r\n  {\r\n    path: 'backoffice',\r\n    canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./backoffice/backoffice.module').then((m) => m.BackofficeModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n      permission: Permission.Vendor_Portal_Backoffice\r\n    },\r\n  },\r\n  {\r\n    path: 'auth',\r\n    loadChildren: () =>\r\n      import('./session/session.module').then((mod) => mod.SessionModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n    },\r\n  },\r\n  { path: '', redirectTo: 'store', pathMatch: 'full' },\r\n  { path: '**', redirectTo: 'store' },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes, routerOptions)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AACA,SAAuBA,YAAY,QAAgB,iBAAiB;AACpE,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,uBAAuB,QAAQ,2DAA2D;AACnG,SAASC,UAAU,QAAQ,2BAA2B;;;AAEtD,MAAMC,aAAa,GAAiB;EAClCC,eAAe,EAAE,SAAS;EAC1BC,OAAO,EAAE;CACV;AAED,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,CAACT,SAAS,EAAEE,uBAAuB,CAAC;EACjDQ,gBAAgB,EAAE,CAACV,SAAS,EAAEE,uBAAuB,CAAC;EACtDS,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC;EAC3DC,OAAO,EAAE;IACPC,aAAa,EAAEf;GAChB;EACDgB,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EACEV,IAAI,EAAE,YAAY;EAClBC,WAAW,EAAE,CAACT,SAAS,CAAC;EACxBW,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,gBAAgB,CAAC;EAC1EJ,OAAO,EAAE;IACPC,aAAa,EAAEf;GAChB;EACDgB,IAAI,EAAE;IACJC,IAAI,EAAE,QAAQ;IACdE,UAAU,EAAEjB,UAAU,CAACkB;;CAE1B,EACD;EACEb,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEU,GAAG,IAAKA,GAAG,CAACC,aAAa,CAAC;EACrER,OAAO,EAAE;IACPC,aAAa,EAAEf;GAChB;EACDgB,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EAAEV,IAAI,EAAE,EAAE;EAAEgB,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEjB,IAAI,EAAE,IAAI;EAAEgB,UAAU,EAAE;AAAO,CAAE,CACpC;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjB3B,YAAY,CAAC4B,OAAO,CAACpB,MAAM,EAAEH,aAAa,CAAC,EAC3CL,YAAY;IAAA;EAAA;;;2EAEX2B,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA9B,YAAA;IAAA+B,OAAA,GAFjB/B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}