{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"./reset-password.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ResetPasswordComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Capital Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Small Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Special Character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_7_div_1_Template, 2, 0, \"div\", 11)(2, ResetPasswordComponent_div_7_div_2_Template, 2, 0, \"div\", 11)(3, ResetPasswordComponent_div_7_div_3_Template, 2, 0, \"div\", 11)(4, ResetPasswordComponent_div_7_div_4_Template, 2, 0, \"div\", 11)(5, ResetPasswordComponent_div_7_div_5_Template, 2, 0, \"div\", 11)(6, ResetPasswordComponent_div_7_div_6_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"minlength\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasNumber\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasCapitalCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasSmallCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"password\"].errors[\"hasSpecialCharacters\"]);\n  }\n}\nfunction ResetPasswordComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Passwords must match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, ResetPasswordComponent_div_12_div_1_Template, 2, 0, \"div\", 11)(2, ResetPasswordComponent_div_12_div_2_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"passwordConfirm\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.f[\"passwordConfirm\"].errors[\"confirmedValidator\"]);\n  }\n}\nfunction ConfirmedValidator(controlName, matchingControlName) {\n  return formGroup => {\n    const control = formGroup.controls[controlName];\n    const matchingControl = formGroup.controls[matchingControlName];\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\n      return;\n    }\n    if (control.value !== matchingControl.value) {\n      matchingControl.setErrors({\n        confirmedValidator: true\n      });\n    } else {\n      matchingControl.setErrors(null);\n    }\n  };\n}\nfunction patternValidator(regex, error) {\n  return control => {\n    if (!control.value) {\n      // if control is empty return no error\n      return null;\n    }\n    // test the value of the control against the regexp supplied\n    const valid = regex.test(control.value);\n    // if true, return no error (no error), else return error passed in the second parameter\n    return valid ? null : error;\n  };\n}\nexport class ResetPasswordComponent {\n  constructor(formBuilder, service, route, router) {\n    this.formBuilder = formBuilder;\n    this.service = service;\n    this.route = route;\n    this.router = router;\n    this.form = this.formBuilder.group({\n      password: ['', [Validators.required, Validators.minLength(8),\n      // check whether the entered password has a number\n      patternValidator(/\\d/, {\n        hasNumber: true\n      }),\n      // check whether the entered password has upper case letter\n      patternValidator(/[A-Z]/, {\n        hasCapitalCase: true\n      }),\n      // check whether the entered password has a lower case letter\n      patternValidator(/[a-z]/, {\n        hasSmallCase: true\n      }),\n      // check whether the entered password has a special character\n      patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\n        hasSpecialCharacters: true\n      })]],\n      passwordConfirm: ['', Validators.required]\n    }, {\n      validators: ConfirmedValidator('password', 'passwordConfirm')\n    });\n    this.submitted = false;\n    this.saving = false;\n    this.token = '';\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      this.token = params['code'];\n    });\n  }\n  get f() {\n    return this.form.controls;\n  }\n  onSubmit() {\n    this.submitted = true;\n    if (this.form.invalid) {\n      return;\n    }\n    if (!this.token) {\n      // this._snackBar.open('Invalid Url.', { type: 'Warning' });\n      return;\n    }\n    this.saving = true;\n    this.service.resetPassword({\n      passwordConfirmation: this.form.value.password,\n      password: this.form.value.password,\n      code: this.token\n    }).subscribe({\n      complete: () => {\n        this.onReset();\n        this.saving = false;\n        // this._snackBar.open('Password reset successfully!');\n      },\n      error: err => {\n        this.saving = false;\n        // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });\n      }\n    });\n  }\n  onReset() {\n    this.submitted = false;\n    this.form.reset();\n    setTimeout(() => {\n      this.router.navigate([\"store\"]);\n    }, 2000);\n  }\n  static {\n    this.ɵfac = function ResetPasswordComponent_Factory(t) {\n      return new (t || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ResetPasswordService), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordComponent,\n      selectors: [[\"app-reset-password\"]],\n      decls: 18,\n      vars: 10,\n      consts: [[1, \"mb-4\"], [3, \"formGroup\"], [1, \"form-group\", \"mb-4\", \"required\"], [1, \"mb-2\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"form-control\", \"mt-1\", \"mb-2\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"password\", \"formControlName\", \"passwordConfirm\", 1, \"form-control\", \"mt-1\", \"mb-2\", 3, \"ngClass\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", \"routerLink\", \"/store\", 1, \"btn\", \"btn-light\"], [1, \"invalid-feedback\"], [4, \"ngIf\"]],\n      template: function ResetPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h4\", 0);\n          i0.ɵɵtext(1, \"Reset Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"form\", 1)(3, \"div\", 2)(4, \"label\", 3);\n          i0.ɵɵtext(5, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 4);\n          i0.ɵɵtemplate(7, ResetPasswordComponent_div_7_Template, 7, 6, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"label\", 3);\n          i0.ɵɵtext(10, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"input\", 6);\n          i0.ɵɵtemplate(12, ResetPasswordComponent_div_12_Template, 3, 2, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_14_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(15, \"Reset Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵtext(17, \"Cancel\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c0, ctx.submitted && ctx.f[\"password\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"password\"].errors);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx.submitted && ctx.f[\"passwordConfirm\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"passwordConfirm\"].errors);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.saving);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\"[_nghost-%COMP%] {\\n  padding: 2rem;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\nh4[_ngcontent-%COMP%] {\\n  font-family: var(--snjy-font-family-secondary);\\n  font-weight: var(--snjy-font-weight-bold);\\n  font-size: var(--snjy-font-size-2);\\n  color: var(--snjy-color-secondary);\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  font-family: var(--snjy-font-family-ternary);\\n  font-weight: var(--snjy-font-weight-semi-bold);\\n  font-size: var(--snjy-font-size-0-9);\\n  color: var(--snjy-color-secondary);\\n}\\n\\nform[_ngcontent-%COMP%] {\\n  min-width: 450px;\\n}\\n\\ninput[_ngcontent-%COMP%] {\\n  font-family: var(--snjy-font-family-ternary);\\n  font-weight: var(--snjy-font-weight-normal);\\n  font-size: var(--snjy-font-size-1);\\n  color: var(--snjy-font-color-seconday);\\n  background: var(--snjy-color-main-background-ternary);\\n  border: 1px solid var(--snjy-border-color);\\n  padding: 0.5rem 1rem;\\n  outline: none;\\n}\\ninput[_ngcontent-%COMP%]:hover {\\n  border: 1px solid var(--snjy-border-color);\\n  outline: none;\\n  box-shadow: none;\\n}\\n\\n.hint[_ngcontent-%COMP%] {\\n  font-family: var(--snjy-font-family-ternary);\\n  font-weight: var(--snjy-font-weight-normal);\\n  font-size: var(--snjy-font-size-0-875);\\n  color: var(--snjy-color-dark-secondary);\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  width: 48%;\\n  font-family: var(--snjy-font-family-secondary);\\n  font-weight: var(--snjy-font-weight-bold);\\n  font-size: var(--snjy-font-size-1);\\n  padding: 10px;\\n  background-color: var(--snjy-color-secondary);\\n  color: var(--snjy-color-main-background);\\n}\\n\\n.btn-light[_ngcontent-%COMP%] {\\n  background-color: var(--snjy-color-primary);\\n  color: var(--snjy-color-secondary);\\n}\\n\\n@media (max-width: 500px) {\\n  form[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: var(--snjy-font-size-0-875);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ResetPasswordComponent_div_7_div_1_Template", "ResetPasswordComponent_div_7_div_2_Template", "ResetPasswordComponent_div_7_div_3_Template", "ResetPasswordComponent_div_7_div_4_Template", "ResetPasswordComponent_div_7_div_5_Template", "ResetPasswordComponent_div_7_div_6_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "f", "errors", "ResetPasswordComponent_div_12_div_1_Template", "ResetPasswordComponent_div_12_div_2_Template", "ConfirmedValidator", "controlName", "matchingControlName", "formGroup", "control", "controls", "matchingControl", "value", "setErrors", "confirmedValidator", "patternValidator", "regex", "error", "valid", "test", "ResetPasswordComponent", "constructor", "formBuilder", "service", "route", "router", "form", "group", "password", "required", "<PERSON><PERSON><PERSON><PERSON>", "hasNumber", "hasCapitalCase", "hasSmallCase", "hasSpecialCharacters", "passwordConfirm", "validators", "submitted", "saving", "token", "ngOnInit", "queryParams", "subscribe", "params", "onSubmit", "invalid", "resetPassword", "passwordConfirmation", "code", "complete", "onReset", "err", "reset", "setTimeout", "navigate", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ResetPasswordService", "i3", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "ResetPasswordComponent_Template", "rf", "ctx", "ɵɵelement", "ResetPasswordComponent_div_7_Template", "ResetPasswordComponent_div_12_Template", "ɵɵlistener", "ResetPasswordComponent_Template_button_click_14_listener", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\reset-password\\reset-password.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\reset-password\\reset-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { ResetPasswordService } from './reset-password.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\nfunction ConfirmedValidator(controlName: string, matchingControlName: string) {\r\n  return (formGroup: FormGroup) => {\r\n    const control = formGroup.controls[controlName];\r\n    const matchingControl = formGroup.controls[matchingControlName];\r\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\r\n      return;\r\n    }\r\n    if (control.value !== matchingControl.value) {\r\n      matchingControl.setErrors({ confirmedValidator: true });\r\n    } else {\r\n      matchingControl.setErrors(null);\r\n    }\r\n  }\r\n}\r\n\r\nfunction patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {\r\n  return (control: AbstractControl) => {\r\n    if (!control.value) {\r\n      // if control is empty return no error\r\n      return null;\r\n    }\r\n\r\n    // test the value of the control against the regexp supplied\r\n    const valid = regex.test(control.value);\r\n\r\n    // if true, return no error (no error), else return error passed in the second parameter\r\n    return valid ? null : error;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-reset-password',\r\n  templateUrl: './reset-password.component.html',\r\n  styleUrls: ['./reset-password.component.scss']\r\n})\r\nexport class ResetPasswordComponent {\r\n  form: FormGroup = this.formBuilder.group(\r\n    {\r\n      password: ['',\r\n        [\r\n          Validators.required, Validators.minLength(8),\r\n          // check whether the entered password has a number\r\n          patternValidator(/\\d/, {\r\n            hasNumber: true\r\n          }),\r\n          // check whether the entered password has upper case letter\r\n          patternValidator(/[A-Z]/, {\r\n            hasCapitalCase: true\r\n          }),\r\n          // check whether the entered password has a lower case letter\r\n          patternValidator(/[a-z]/, {\r\n            hasSmallCase: true\r\n          }),\r\n          // check whether the entered password has a special character\r\n          patternValidator(\r\n            /[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/,\r\n            {\r\n              hasSpecialCharacters: true\r\n            }\r\n          ),\r\n        ]\r\n      ],\r\n      passwordConfirm: ['', Validators.required],\r\n    },\r\n    {\r\n      validators: ConfirmedValidator('password', 'passwordConfirm'),\r\n    },\r\n  );\r\n  submitted = false;\r\n  saving = false;\r\n  token = '';\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private service: ResetPasswordService,\r\n    private route: ActivatedRoute,\r\n    public router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.route.queryParams\r\n      .subscribe(params => {\r\n        this.token = params['code'];\r\n      }\r\n      );\r\n  }\r\n\r\n  get f(): { [key: string]: AbstractControl } {\r\n    return this.form.controls;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    this.submitted = true;\r\n\r\n    if (this.form.invalid) {\r\n      return;\r\n    }\r\n\r\n    if (!this.token) {\r\n      // this._snackBar.open('Invalid Url.', { type: 'Warning' });\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    this.service.resetPassword({\r\n      passwordConfirmation: this.form.value.password,\r\n      password: this.form.value.password,\r\n      code: this.token\r\n    }).subscribe({\r\n      complete: () => {\r\n        this.onReset();\r\n        this.saving = false;\r\n        // this._snackBar.open('Password reset successfully!');\r\n      },\r\n      error: (err) => {\r\n        this.saving = false;\r\n        // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });\r\n      },\r\n    })\r\n  }\r\n\r\n  onReset(): void {\r\n    this.submitted = false;\r\n    this.form.reset();\r\n    setTimeout(() => {\r\n      this.router.navigate([\"store\"]);\r\n    }, 2000);\r\n  }\r\n}\r\n", "<h4 class=\"mb-4\">Reset Password</h4>\r\n<form [formGroup]=\"form\">\r\n    <div class=\"form-group mb-4 required\">\r\n        <label class=\"mb-2\">Password</label>\r\n        <input type=\"password\" formControlName=\"password\" class=\"form-control mt-1 mb-2\"\r\n            [ngClass]=\"{ 'is-invalid': submitted && f['password'].errors }\" />\r\n        <div *ngIf=\"submitted && f['password'].errors\" class=\"invalid-feedback\">\r\n            <div *ngIf=\"f['password'].errors['required']\">\r\n                This field is required</div>\r\n            <div *ngIf=\"f['password'].errors['minlength']\">\r\n                Must be at least 8 characters</div>\r\n            <div *ngIf=\"f['password'].errors['hasNumber']\">\r\n                Must contain at least one number</div>\r\n            <div *ngIf=\"f['password'].errors['hasCapitalCase']\">\r\n                Must contain at least one Letter in Capital Case</div>\r\n            <div *ngIf=\"f['password'].errors['hasSmallCase']\">\r\n                Must contain at least one Letter in Small Case</div>\r\n            <div *ngIf=\"f['password'].errors['hasSpecialCharacters']\">\r\n                Must contain at least one Special Character</div>\r\n        </div>\r\n    </div>\r\n    <div class=\"form-group mb-4 required\">\r\n        <label class=\"mb-2\">Confirm Password</label>\r\n        <input type=\"password\" formControlName=\"passwordConfirm\" class=\"form-control mt-1 mb-2\"\r\n            [ngClass]=\"{ 'is-invalid': submitted && f['passwordConfirm'].errors }\" />\r\n        <div *ngIf=\"submitted && f['passwordConfirm'].errors\" class=\"invalid-feedback\">\r\n            <div *ngIf=\"f['passwordConfirm'].errors['required']\">\r\n                This field is required</div>\r\n            <div *ngIf=\"f['passwordConfirm'].errors['confirmedValidator']\">\r\n                Passwords must match\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"d-flex justify-content-between\">\r\n        <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"saving\" (click)=\"onSubmit()\">Reset Password</button>\r\n        <button type=\"submit\" class=\"btn btn-light\" routerLink=\"/store\">Cancel</button>\r\n    </div>\r\n</form>"], "mappings": "AACA,SAAiFA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;ICMvGC,EAAA,CAAAC,cAAA,UAA8C;IAC1CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1CH,EAAA,CAAAC,cAAA,UAAoD;IAChDD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1DH,EAAA,CAAAC,cAAA,UAAkD;IAC9CD,EAAA,CAAAE,MAAA,sDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxDH,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAZzDH,EAAA,CAAAC,cAAA,cAAwE;IAWpED,EAVA,CAAAI,UAAA,IAAAC,2CAAA,kBAA8C,IAAAC,2CAAA,kBAEC,IAAAC,2CAAA,kBAEA,IAAAC,2CAAA,kBAEK,IAAAC,2CAAA,kBAEF,IAAAC,2CAAA,kBAEQ;IAE9DV,EAAA,CAAAG,YAAA,EAAM;;;;IAZIH,EAAA,CAAAW,SAAA,EAAsC;IAAtCX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,aAAsC;IAEtCf,EAAA,CAAAW,SAAA,EAAuC;IAAvCX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,cAAuC;IAEvCf,EAAA,CAAAW,SAAA,EAAuC;IAAvCX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,cAAuC;IAEvCf,EAAA,CAAAW,SAAA,EAA4C;IAA5CX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,mBAA4C;IAE5Cf,EAAA,CAAAW,SAAA,EAA0C;IAA1CX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,iBAA0C;IAE1Cf,EAAA,CAAAW,SAAA,EAAkD;IAAlDX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,yBAAkD;;;;;IASxDf,EAAA,CAAAC,cAAA,UAAqD;IACjDD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAA+D;IAC3DD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALVH,EAAA,CAAAC,cAAA,cAA+E;IAG3ED,EAFA,CAAAI,UAAA,IAAAY,4CAAA,kBAAqD,IAAAC,4CAAA,kBAEU;IAGnEjB,EAAA,CAAAG,YAAA,EAAM;;;;IALIH,EAAA,CAAAW,SAAA,EAA6C;IAA7CX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,oBAAAC,MAAA,aAA6C;IAE7Cf,EAAA,CAAAW,SAAA,EAAuD;IAAvDX,EAAA,CAAAY,UAAA,SAAAC,MAAA,CAAAC,CAAA,oBAAAC,MAAA,uBAAuD;;;ADtBzE,SAASG,kBAAkBA,CAACC,WAAmB,EAAEC,mBAA2B;EAC1E,OAAQC,SAAoB,IAAI;IAC9B,MAAMC,OAAO,GAAGD,SAAS,CAACE,QAAQ,CAACJ,WAAW,CAAC;IAC/C,MAAMK,eAAe,GAAGH,SAAS,CAACE,QAAQ,CAACH,mBAAmB,CAAC;IAC/D,IAAII,eAAe,CAACT,MAAM,IAAI,CAACS,eAAe,CAACT,MAAM,CAAC,oBAAoB,CAAC,EAAE;MAC3E;IACF;IACA,IAAIO,OAAO,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;MAC3CD,eAAe,CAACE,SAAS,CAAC;QAAEC,kBAAkB,EAAE;MAAI,CAAE,CAAC;IACzD,CAAC,MAAM;MACLH,eAAe,CAACE,SAAS,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;AACH;AAEA,SAASE,gBAAgBA,CAACC,KAAa,EAAEC,KAAuB;EAC9D,OAAQR,OAAwB,IAAI;IAClC,IAAI,CAACA,OAAO,CAACG,KAAK,EAAE;MAClB;MACA,OAAO,IAAI;IACb;IAEA;IACA,MAAMM,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACV,OAAO,CAACG,KAAK,CAAC;IAEvC;IACA,OAAOM,KAAK,GAAG,IAAI,GAAGD,KAAK;EAC7B,CAAC;AACH;AAOA,OAAM,MAAOG,sBAAsB;EAqCjCC,YACUC,WAAwB,EACxBC,OAA6B,EAC7BC,KAAqB,EACtBC,MAAc;IAHb,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACN,KAAAC,MAAM,GAANA,MAAM;IAxCf,KAAAC,IAAI,GAAc,IAAI,CAACJ,WAAW,CAACK,KAAK,CACtC;MACEC,QAAQ,EAAE,CAAC,EAAE,EACX,CACE1C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,CAAC,CAAC;MAC5C;MACAf,gBAAgB,CAAC,IAAI,EAAE;QACrBgB,SAAS,EAAE;OACZ,CAAC;MACF;MACAhB,gBAAgB,CAAC,OAAO,EAAE;QACxBiB,cAAc,EAAE;OACjB,CAAC;MACF;MACAjB,gBAAgB,CAAC,OAAO,EAAE;QACxBkB,YAAY,EAAE;OACf,CAAC;MACF;MACAlB,gBAAgB,CACd,wCAAwC,EACxC;QACEmB,oBAAoB,EAAE;OACvB,CACF,CACF,CACF;MACDC,eAAe,EAAE,CAAC,EAAE,EAAEjD,UAAU,CAAC2C,QAAQ;KAC1C,EACD;MACEO,UAAU,EAAE/B,kBAAkB,CAAC,UAAU,EAAE,iBAAiB;KAC7D,CACF;IACD,KAAAgC,SAAS,GAAG,KAAK;IACjB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,KAAK,GAAG,EAAE;EAON;EAEJC,QAAQA,CAAA;IACN,IAAI,CAAChB,KAAK,CAACiB,WAAW,CACnBC,SAAS,CAACC,MAAM,IAAG;MAClB,IAAI,CAACJ,KAAK,GAAGI,MAAM,CAAC,MAAM,CAAC;IAC7B,CAAC,CACA;EACL;EAEA,IAAI1C,CAACA,CAAA;IACH,OAAO,IAAI,CAACyB,IAAI,CAAChB,QAAQ;EAC3B;EAEAkC,QAAQA,CAAA;IACN,IAAI,CAACP,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACX,IAAI,CAACmB,OAAO,EAAE;MACrB;IACF;IAEA,IAAI,CAAC,IAAI,CAACN,KAAK,EAAE;MACf;MACA;IACF;IAEA,IAAI,CAACD,MAAM,GAAG,IAAI;IAClB,IAAI,CAACf,OAAO,CAACuB,aAAa,CAAC;MACzBC,oBAAoB,EAAE,IAAI,CAACrB,IAAI,CAACd,KAAK,CAACgB,QAAQ;MAC9CA,QAAQ,EAAE,IAAI,CAACF,IAAI,CAACd,KAAK,CAACgB,QAAQ;MAClCoB,IAAI,EAAE,IAAI,CAACT;KACZ,CAAC,CAACG,SAAS,CAAC;MACXO,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACC,OAAO,EAAE;QACd,IAAI,CAACZ,MAAM,GAAG,KAAK;QACnB;MACF,CAAC;MACDrB,KAAK,EAAGkC,GAAG,IAAI;QACb,IAAI,CAACb,MAAM,GAAG,KAAK;QACnB;MACF;KACD,CAAC;EACJ;EAEAY,OAAOA,CAAA;IACL,IAAI,CAACb,SAAS,GAAG,KAAK;IACtB,IAAI,CAACX,IAAI,CAAC0B,KAAK,EAAE;IACjBC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC,EAAE,IAAI,CAAC;EACV;;;uBA5FWlC,sBAAsB,EAAAjC,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAxE,EAAA,CAAAoE,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1E,EAAA,CAAAoE,iBAAA,CAAAK,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAAtB1C,sBAAsB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzCnClF,EAAA,CAAAC,cAAA,YAAiB;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5BH,EAFR,CAAAC,cAAA,cAAyB,aACiB,eACd;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpCH,EAAA,CAAAoF,SAAA,eACsE;UACtEpF,EAAA,CAAAI,UAAA,IAAAiF,qCAAA,iBAAwE;UAc5ErF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,aAAsC,eACd;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAoF,SAAA,gBAC6E;UAC7EpF,EAAA,CAAAI,UAAA,KAAAkF,sCAAA,iBAA+E;UAOnFtF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,cAA4C,iBAC+C;UAArBD,EAAA,CAAAuF,UAAA,mBAAAC,yDAAA;YAAA,OAASL,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UAACzD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9GH,EAAA,CAAAC,cAAA,iBAAgE;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAE9EF,EAF8E,CAAAG,YAAA,EAAS,EAC7E,EACH;;;UApCDH,EAAA,CAAAW,SAAA,GAAkB;UAAlBX,EAAA,CAAAY,UAAA,cAAAuE,GAAA,CAAA5C,IAAA,CAAkB;UAIZvC,EAAA,CAAAW,SAAA,GAA+D;UAA/DX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,aAAAC,MAAA,EAA+D;UAC7Df,EAAA,CAAAW,SAAA,EAAuC;UAAvCX,EAAA,CAAAY,UAAA,SAAAuE,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,aAAAC,MAAA,CAAuC;UAkBzCf,EAAA,CAAAW,SAAA,GAAsE;UAAtEX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,oBAAAC,MAAA,EAAsE;UACpEf,EAAA,CAAAW,SAAA,EAA8C;UAA9CX,EAAA,CAAAY,UAAA,SAAAuE,GAAA,CAAAjC,SAAA,IAAAiC,GAAA,CAAArE,CAAA,oBAAAC,MAAA,CAA8C;UASNf,EAAA,CAAAW,SAAA,GAAmB;UAAnBX,EAAA,CAAAY,UAAA,aAAAuE,GAAA,CAAAhC,MAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}