{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class MainComponent {\n  static {\n    this.ɵfac = function MainComponent_Factory(t) {\n      return new (t || MainComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MainComponent,\n      selectors: [[\"app-main\"]],\n      decls: 1,\n      vars: 0,\n      template: function MainComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MainComponent", "selectors", "decls", "vars", "template", "MainComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\configuration\\main\\main.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\configuration\\main\\main.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-main',\r\n  templateUrl: './main.component.html',\r\n  styleUrl: './main.component.scss'\r\n})\r\nexport class MainComponent {\r\n\r\n}\r\n", "<router-outlet></router-outlet>\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}