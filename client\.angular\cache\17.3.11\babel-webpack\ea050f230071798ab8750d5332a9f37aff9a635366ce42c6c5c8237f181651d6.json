{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SessionRoutingModule } from './session-routing.module';\nimport { SessionComponent } from './session.component';\nimport { LoginComponent } from './login/login.component';\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SignupComponent } from './signup/signup.component';\nimport { DialogModule } from 'primeng/dialog';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { MenubarModule } from 'primeng/menubar';\nimport { GalleriaModule } from 'primeng/galleria';\nimport { CarouselModule } from 'primeng/carousel';\nimport { TagModule } from 'primeng/tag';\nimport * as i0 from \"@angular/core\";\nexport class SessionModule {\n  static {\n    this.ɵfac = function SessionModule_Factory(t) {\n      return new (t || SessionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SessionModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, SessionRoutingModule, DialogModule, InputTextModule, ButtonModule, DropdownModule, MenubarModule, GalleriaModule, CarouselModule, TagModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SessionModule, {\n    declarations: [SessionComponent, LoginComponent, ForgotPasswordComponent, SignupComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, SessionRoutingModule, DialogModule, InputTextModule, ButtonModule, DropdownModule, MenubarModule, GalleriaModule, CarouselModule, TagModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SessionRoutingModule", "SessionComponent", "LoginComponent", "ForgotPasswordComponent", "FormsModule", "ReactiveFormsModule", "SignupComponent", "DialogModule", "ButtonModule", "InputTextModule", "DropdownModule", "MenubarModule", "GalleriaModule", "CarouselModule", "TagModule", "SessionModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\session.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SessionRoutingModule } from './session-routing.module';\r\nimport { SessionComponent } from './session.component';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { SignupComponent } from './signup/signup.component';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { MenubarModule } from 'primeng/menubar';\r\nimport { GalleriaModule } from 'primeng/galleria';\r\nimport { CarouselModule } from 'primeng/carousel';\r\nimport { TagModule } from 'primeng/tag';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SessionComponent,\r\n    LoginComponent,\r\n    ForgotPasswordComponent,\r\n    SignupComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    SessionRoutingModule,\r\n    DialogModule,\r\n    InputTextModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    MenubarModule,\r\n    GalleriaModule,\r\n    CarouselModule,\r\n    TagModule,\r\n    \r\n  ]\r\n})\r\nexport class SessionModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,SAAS,QAAQ,aAAa;;AA0BvC,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAftBhB,YAAY,EACZK,WAAW,EACXC,mBAAmB,EACnBL,oBAAoB,EACpBO,YAAY,EACZE,eAAe,EACfD,YAAY,EACZE,cAAc,EACdC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,SAAS;IAAA;EAAA;;;2EAIAC,aAAa;IAAAC,YAAA,GArBtBf,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EACvBG,eAAe;IAAAW,OAAA,GAGflB,YAAY,EACZK,WAAW,EACXC,mBAAmB,EACnBL,oBAAoB,EACpBO,YAAY,EACZE,eAAe,EACfD,YAAY,EACZE,cAAc,EACdC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,SAAS;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}