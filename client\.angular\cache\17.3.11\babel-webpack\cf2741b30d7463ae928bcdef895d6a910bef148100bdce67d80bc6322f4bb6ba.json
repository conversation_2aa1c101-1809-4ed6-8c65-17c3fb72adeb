{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class VendorService {\n  constructor(http) {\n    this.http = http;\n    this.vendorSubject = new BehaviorSubject(null);\n    this.vendor = this.vendorSubject.asObservable();\n  }\n  get(type, moduleurl) {\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`);\n  }\n  update(data, id, moduleurl) {\n    return this.http.put(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`, {\n      data\n    });\n  }\n  getUserByCustomer(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('populate[business_partner][populate]', 'addresses').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][customer_id][$containsi]', searchTerm).set('filters[$or][1][customer_name][$containsi]', searchTerm).set('filters[$or][2][business_partner][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}/customers`, {\n      params\n    });\n  }\n  getUserBySupplier(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('fields', 'supplier_id,supplier_name').set('populate[business_partner][fields][0]', 'bp_uuid').set('populate[business_partner][populate][addresses][fields][0]', 'house_number').set('populate[business_partner][populate][addresses][fields][1]', 'street_name').set('populate[business_partner][populate][addresses][fields][2]', 'city_name').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][3]', 'region').set('populate[business_partner][populate][addresses][fields][5]', 'postal_code').set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][supplier_id][$containsi]', searchTerm).set('filters[$or][1][supplier_name][$containsi]', searchTerm).set('filters[$or][2][business_partner][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}/suppliers`, {\n      params\n    });\n  }\n  getUserRoles() {\n    return this.http.get(`${CMS_APIContstant.USER_ROLES}`);\n  }\n  getUsers(page, pageSize, sortField, sortOrder, searchTerm, vendor_user_role_id) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][email][$containsi]', searchTerm).set('filters[$or][1][username][$containsi]', searchTerm).set('filters[$or][2][firstname][$containsi]', searchTerm).set('filters[$or][3][lastname][$containsi]', searchTerm).set('filters[$or][4][blocked][$containsi]', searchTerm);\n    }\n    if (vendor_user_role_id) {\n      params = params.set('filters[role][id][$eq]', vendor_user_role_id);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list?populate=role`, {\n      params\n    });\n  }\n  getUserByIDName(data) {\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`);\n  }\n  getUserByID(id) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate[vendor]=*`);\n  }\n  getCustomers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  getSuppliers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.SUPPLIERS}`, {\n      params\n    });\n  }\n  updateUser(userId, updatedData) {\n    return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n  }\n  getSettings() {\n    return this.http.get(`${CMS_APIContstant.SETTINGS}?fields[0]=id&populate[vendor_user_role][fields]=id`);\n  }\n  static {\n    this.ɵfac = function VendorService_Factory(t) {\n      return new (t || VendorService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VendorService,\n      factory: VendorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "VendorService", "constructor", "http", "vendorSubject", "vendor", "asObservable", "get", "type", "<PERSON><PERSON><PERSON>", "CMSAPI_END_POINT", "update", "data", "id", "put", "getUserByCustomer", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "USER_DETAILS", "getUserBySupplier", "getUserRoles", "USER_ROLES", "getUsers", "vendor_user_role_id", "getUserByIDName", "CUSTOMERS", "getUserByID", "getCustomers", "appendAll", "getSuppliers", "SUPPLIERS", "updateUser", "userId", "updatedData", "getSettings", "SETTINGS", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\vendor\\vendor.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class VendorService {\r\n  public vendorSubject = new BehaviorSubject<any>(null);\r\n  public vendor = this.vendorSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  get(type: string, moduleurl: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`\r\n    );\r\n  }\r\n\r\n  update(data: any, id: string, moduleurl: string) {\r\n    return this.http.put<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`,\r\n      { data }\r\n    );\r\n  }\r\n\r\n  getUserByCustomer(\r\n    id: any,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('populate[business_partner][populate]', 'addresses')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][customer_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][customer_name][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][2][business_partner][phone][$containsi]',\r\n          searchTerm\r\n        );\r\n    }\r\n\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}/customers`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserBySupplier(\r\n    id: any,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('fields', 'supplier_id,supplier_name')\r\n      .set('populate[business_partner][fields][0]', 'bp_uuid')\r\n      .set('populate[business_partner][populate][addresses][fields][0]', 'house_number')\r\n      .set('populate[business_partner][populate][addresses][fields][1]', 'street_name')\r\n      .set('populate[business_partner][populate][addresses][fields][2]', 'city_name')\r\n      .set('populate[business_partner][populate][addresses][fields][3]', 'region')\r\n      .set('populate[business_partner][populate][addresses][fields][3]', 'region')\r\n      .set('populate[business_partner][populate][addresses][fields][5]', 'postal_code')\r\n      .set('populate[business_partner][populate][addresses][populate][phone_numbers][fields][0]', 'phone_number')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][supplier_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][supplier_name][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][2][business_partner][phone][$containsi]',\r\n          searchTerm\r\n        );\r\n    }\r\n\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}/suppliers`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserRoles() {\r\n    return this.http.get<any[]>(`${CMS_APIContstant.USER_ROLES}`);\r\n  }\r\n\r\n  getUsers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string,\r\n    vendor_user_role_id?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][email][$containsi]', searchTerm)\r\n        .set('filters[$or][1][username][$containsi]', searchTerm)\r\n        .set('filters[$or][2][firstname][$containsi]', searchTerm)\r\n        .set('filters[$or][3][lastname][$containsi]', searchTerm)\r\n        .set('filters[$or][4][blocked][$containsi]', searchTerm);\r\n    }\r\n\r\n    if (vendor_user_role_id) {\r\n      params = params.set('filters[role][id][$eq]', vendor_user_role_id);\r\n    }\r\n\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/list?populate=role`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserByIDName(data: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`\r\n    );\r\n  }\r\n\r\n  getUserByID(id: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}?populate[vendor]=*`\r\n    );\r\n  }\r\n\r\n  getCustomers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getSuppliers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.SUPPLIERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  updateUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}`,\r\n      updatedData\r\n    );\r\n  }\r\n\r\n  getSettings() {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.SETTINGS}?fields[0]=id&populate[vendor_user_role][fields]=id`\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,aAAa;EAIxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,aAAa,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC9C,KAAAM,MAAM,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;EAEV;EAEvCC,GAAGA,CAACC,IAAY,EAAEC,SAAiB;IACjC,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAACU,gBAAgB,IAAID,SAAS,EAAE,CACpD;EACH;EAEAE,MAAMA,CAACC,IAAS,EAAEC,EAAU,EAAEJ,SAAiB;IAC7C,OAAO,IAAI,CAACN,IAAI,CAACW,GAAG,CAClB,GAAGd,gBAAgB,CAACU,gBAAgB,IAAID,SAAS,IAAII,EAAE,EAAE,EACzD;MAAED;IAAI,CAAE,CACT;EACH;EAEAG,iBAAiBA,CACfF,EAAO,EACPG,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAC1BwB,GAAG,CAAC,sCAAsC,EAAE,WAAW,CAAC,CACxDA,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC,CAC3DE,GAAG,CAAC,4CAA4C,EAAEF,UAAU,CAAC,CAC7DE,GAAG,CACF,sDAAsD,EACtDF,UAAU,CACX;IACL;IAEA,OAAO,IAAI,CAACjB,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAAC0B,YAAY,IAAIb,EAAE,YAAY,EAClD;MACEQ;KACD,CACF;EACH;EAEAM,iBAAiBA,CACfd,EAAO,EACPG,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAC1BwB,GAAG,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAC1CA,GAAG,CAAC,uCAAuC,EAAE,SAAS,CAAC,CACvDA,GAAG,CAAC,4DAA4D,EAAE,cAAc,CAAC,CACjFA,GAAG,CAAC,4DAA4D,EAAE,aAAa,CAAC,CAChFA,GAAG,CAAC,4DAA4D,EAAE,WAAW,CAAC,CAC9EA,GAAG,CAAC,4DAA4D,EAAE,QAAQ,CAAC,CAC3EA,GAAG,CAAC,4DAA4D,EAAE,QAAQ,CAAC,CAC3EA,GAAG,CAAC,4DAA4D,EAAE,aAAa,CAAC,CAChFA,GAAG,CAAC,qFAAqF,EAAE,cAAc,CAAC,CAC1GA,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC,CAC3DE,GAAG,CAAC,4CAA4C,EAAEF,UAAU,CAAC,CAC7DE,GAAG,CACF,sDAAsD,EACtDF,UAAU,CACX;IACL;IAEA,OAAO,IAAI,CAACjB,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAAC0B,YAAY,IAAIb,EAAE,YAAY,EAClD;MACEQ;KACD,CACF;EACH;EAEAO,YAAYA,CAAA;IACV,OAAO,IAAI,CAACzB,IAAI,CAACI,GAAG,CAAQ,GAAGP,gBAAgB,CAAC6B,UAAU,EAAE,CAAC;EAC/D;EAEAC,QAAQA,CACNd,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB,EACnBW,mBAA4B;IAE5B,IAAIV,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAC1BwB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC,CACxDE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC,CACzDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC,CACxDE,GAAG,CAAC,sCAAsC,EAAEF,UAAU,CAAC;IAC5D;IAEA,IAAIW,mBAAmB,EAAE;MACvBV,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,wBAAwB,EAAES,mBAAmB,CAAC;IACpE;IAEA,OAAO,IAAI,CAAC5B,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAAC0B,YAAY,qBAAqB,EACrD;MACEL;KACD,CACF;EACH;EAEAW,eAAeA,CAACpB,IAAS;IACvB,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAACiC,SAAS,wBAAwBrB,IAAI,EAAE,CAC5D;EACH;EAEAsB,WAAWA,CAACrB,EAAO;IACjB,OAAO,IAAI,CAACV,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAAC0B,YAAY,IAAIb,EAAE,qBAAqB,CAC5D;EACH;EAEAsB,YAAYA,CAACvB,IAAS;IACpB,MAAMS,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAACsC,SAAS,CAAC;MAAE,GAAGxB;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAAQ,GAAGP,gBAAgB,CAACiC,SAAS,EAAE,EAAE;MAC3DZ;KACD,CAAC;EACJ;EAEAgB,YAAYA,CAACzB,IAAS;IACpB,MAAMS,MAAM,GAAG,IAAIvB,UAAU,EAAE,CAACsC,SAAS,CAAC;MAAE,GAAGxB;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAAQ,GAAGP,gBAAgB,CAACsC,SAAS,EAAE,EAAE;MAC3DjB;KACD,CAAC;EACJ;EAEAkB,UAAUA,CAACC,MAAc,EAAEC,WAAgB;IACzC,OAAO,IAAI,CAACtC,IAAI,CAACW,GAAG,CAClB,GAAGd,gBAAgB,CAAC0B,YAAY,IAAIc,MAAM,EAAE,EAC5CC,WAAW,CACZ;EACH;EAEAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACvC,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAAC2C,QAAQ,qDAAqD,CAClF;EACH;;;uBA5KW1C,aAAa,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb9C,aAAa;MAAA+C,OAAA,EAAb/C,aAAa,CAAAgD,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}