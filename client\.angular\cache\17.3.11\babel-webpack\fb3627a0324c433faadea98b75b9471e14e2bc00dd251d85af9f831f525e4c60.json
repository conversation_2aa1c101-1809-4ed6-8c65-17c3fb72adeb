{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../configuration.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/toast\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/button\";\nfunction NotificationComponent_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 10);\n    i0.ɵɵtext(3, \"No records found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotificationComponent_ng_container_20_tr_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\", 16)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 16)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 16)(8, \"input\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NotificationComponent_ng_container_20_tr_1_ng_container_1_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editNotificationTypes.is_active, $event) || (ctx_r1.editNotificationTypes.is_active = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((item_r3 == null ? null : item_r3.code) || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((item_r3 == null ? null : item_r3.description) || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editNotificationTypes.is_active);\n  }\n}\nfunction NotificationComponent_ng_container_20_tr_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\", 16)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 16)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 16);\n    i0.ɵɵelement(8, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((item_r3 == null ? null : item_r3.code) || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((item_r3 == null ? null : item_r3.description) || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"checked\", item_r3.is_active ? \"checked\" : \"\");\n  }\n}\nfunction NotificationComponent_ng_container_20_tr_1_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NotificationComponent_ng_container_20_tr_1_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editNotification(item_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationComponent_ng_container_20_tr_1_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function NotificationComponent_ng_container_20_tr_1_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.updateNotification(item_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationComponent_ng_container_20_tr_1_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function NotificationComponent_ng_container_20_tr_1_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(item_r3.editing = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationComponent_ng_container_20_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, NotificationComponent_ng_container_20_tr_1_ng_container_1_Template, 9, 3, \"ng-container\", 9)(2, NotificationComponent_ng_container_20_tr_1_ng_container_2_Template, 9, 3, \"ng-container\", 9);\n    i0.ɵɵelementStart(3, \"td\", 12);\n    i0.ɵɵtemplate(4, NotificationComponent_ng_container_20_tr_1_button_4_Template, 1, 0, \"button\", 13)(5, NotificationComponent_ng_container_20_tr_1_button_5_Template, 1, 0, \"button\", 14)(6, NotificationComponent_ng_container_20_tr_1_button_6_Template, 1, 0, \"button\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.editing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r3.editing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r3.editing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.editing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.editing);\n  }\n}\nfunction NotificationComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotificationComponent_ng_container_20_tr_1_Template, 7, 5, \"tr\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.NotificationType);\n  }\n}\nfunction NotificationComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class NotificationComponent {\n  constructor(service, messageservice, route) {\n    this.service = service;\n    this.messageservice = messageservice;\n    this.route = route;\n    this.unsubscribe$ = new Subject();\n    this.NotificationType = [];\n    this.notificationType = '';\n    this.notificationTitle = '';\n    this.loading = false;\n    this.isChecked = false;\n    this.moduleurl = 'configurations';\n    this.editNotificationTypes = {\n      code: '',\n      description: '',\n      is_active: '',\n      type: ''\n    };\n  }\n  ngOnInit() {\n    const routeData = this.route.snapshot.data;\n    this.notificationType = routeData['type'];\n    this.notificationTitle = routeData['title'];\n    this.service.configuration.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.getNotificationData();\n    });\n  }\n  editNotification(item) {\n    this.editNotificationTypes = {\n      code: item.code,\n      description: item.description,\n      is_active: item.is_active,\n      type: item.type\n    };\n    item.editing = true;\n  }\n  updateNotification(item) {\n    const obj = {\n      ...this.editNotificationTypes\n    };\n    this.service.update(obj, item.documentId, this.moduleurl).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: res => {\n        item.editing = false;\n        item.description = this.editNotificationTypes.description;\n        item.code = this.editNotificationTypes.code;\n        item.is_active = this.editNotificationTypes.is_active;\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Updated Successfully!'\n        });\n      },\n      error: err => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  getNotificationData() {\n    if (this.notificationType) {\n      this.loading = true;\n      this.service.get(this.notificationType, `${this.moduleurl}?filters[type][$eq]=${this.notificationType}`).pipe(takeUntil(this.unsubscribe$)).subscribe({\n        next: value => {\n          this.loading = false;\n          if (value.data?.length) {\n            for (let i = 0; i < value.data.length; i++) {\n              const element = value.data[i];\n            }\n            this.NotificationType = value.data;\n          } else {\n            this.NotificationType = [];\n          }\n        },\n        error: err => {\n          this.loading = false;\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function NotificationComponent_Factory(t) {\n      return new (t || NotificationComponent)(i0.ɵɵdirectiveInject(i1.ConfigurationService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationComponent,\n      selectors: [[\"app-notification\"]],\n      decls: 22,\n      vars: 5,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"card\"], [1, \"grid\", \"mx-0\"], [1, \"p-2\", \"fw-bold\"], [\"&ngIf\", \"!loading\"], [1, \"table-responsive\"], [1, \"p-datatable\"], [1, \"p-datatable-thead\"], [1, \"p-datatable-tbody\"], [4, \"ngIf\"], [\"colspan\", \"3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"p-datatable-row\", \"p-custom-action\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-check\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"icon\", \"pi pi-times\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"p-datatable-row\"], [\"type\", \"checkbox\", \"id\", \"flexCheckChecked\", \"checked\", \"\", 1, \"form-check-input\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"checkbox\", \"id\", \"flexCheckChecked\", \"disabled\", \"disabled\", 1, \"form-check-input\", 3, \"checked\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-pencil\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-check\", 3, \"click\"], [\"pButton\", \"\", \"icon\", \"pi pi-times\", \"type\", \"button\", 3, \"click\"]],\n      template: function NotificationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementContainerStart(5, 4);\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"table\", 6)(8, \"thead\", 7)(9, \"tr\")(10, \"th\");\n          i0.ɵɵtext(11, \"Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"th\");\n          i0.ɵɵtext(13, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"th\");\n          i0.ɵɵtext(15, \"Allow Notification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\");\n          i0.ɵɵtext(17, \"Action\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"tbody\", 8);\n          i0.ɵɵtemplate(19, NotificationComponent_ng_container_19_Template, 4, 0, \"ng-container\", 9)(20, NotificationComponent_ng_container_20_Template, 2, 1, \"ng-container\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, NotificationComponent_div_21_Template, 2, 0, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.notificationTitle);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", !ctx.NotificationType.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.NotificationType.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.Toast, i6.CheckboxControlValueAccessor, i6.NgControlStatus, i6.NgModel, i7.ButtonDirective],\n      styles: [\".p-datatable[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.p-datatable[_ngcontent-%COMP%]   .p-datatable-thead[_ngcontent-%COMP%]    > tr[_ngcontent-%COMP%]    > th[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n}\\n\\n.custom-input[_ngcontent-%COMP%] {\\n  width: 75%;\\n}\\n\\n.p-custom-action[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%] {\\n  transform: scale(1.5);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmFja29mZmljZS9jb25maWd1cmF0aW9uL25vdGlmaWNhdGlvbi9ub3RpZmljYXRpb24uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0FBQ0Y7O0FBQ0E7RUFDRSxjQUFBO0FBRUY7O0FBQUE7RUFDRSxVQUFBO0FBR0Y7O0FBREE7RUFDRSxhQUFBO0VBQ0EsUUFBQTtBQUlGOztBQUZBO0VBQ0UscUJBQUE7QUFLRiIsInNvdXJjZXNDb250ZW50IjpbIi5wLWRhdGF0YWJsZSB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuLnAtZGF0YXRhYmxlIC5wLWRhdGF0YWJsZS10aGVhZCA+IHRyID4gdGgge1xyXG4gIGNvbG9yOiAjZmZmZmZmO1xyXG59XHJcbi5jdXN0b20taW5wdXQge1xyXG4gIHdpZHRoOiA3NSU7XHJcbn1cclxuLnAtY3VzdG9tLWFjdGlvbiB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBnYXA6IDVweDtcclxufVxyXG4uZm9ybS1jaGVjay1pbnB1dCB7XHJcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjUpO1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "NotificationComponent_ng_container_20_tr_1_ng_container_1_Template_input_ngModelChange_8_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "editNotificationTypes", "is_active", "ɵɵresetView", "ɵɵadvance", "ɵɵtextInterpolate", "item_r3", "code", "description", "ɵɵtwoWayProperty", "ɵɵelement", "ɵɵpropertyInterpolate", "ɵɵlistener", "NotificationComponent_ng_container_20_tr_1_button_4_Template_button_click_0_listener", "_r4", "$implicit", "editNotification", "NotificationComponent_ng_container_20_tr_1_button_5_Template_button_click_0_listener", "_r5", "updateNotification", "NotificationComponent_ng_container_20_tr_1_button_6_Template_button_click_0_listener", "_r6", "editing", "ɵɵtemplate", "NotificationComponent_ng_container_20_tr_1_ng_container_1_Template", "NotificationComponent_ng_container_20_tr_1_ng_container_2_Template", "NotificationComponent_ng_container_20_tr_1_button_4_Template", "NotificationComponent_ng_container_20_tr_1_button_5_Template", "NotificationComponent_ng_container_20_tr_1_button_6_Template", "ɵɵproperty", "NotificationComponent_ng_container_20_tr_1_Template", "NotificationType", "NotificationComponent", "constructor", "service", "messageservice", "route", "unsubscribe$", "notificationType", "notificationTitle", "loading", "isChecked", "<PERSON><PERSON><PERSON>", "type", "ngOnInit", "routeData", "snapshot", "data", "configuration", "pipe", "subscribe", "getNotificationData", "item", "obj", "update", "documentId", "next", "res", "add", "severity", "detail", "error", "err", "get", "value", "length", "i", "element", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ConfigurationService", "i2", "MessageService", "i3", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "NotificationComponent_Template", "rf", "ctx", "NotificationComponent_ng_container_19_Template", "NotificationComponent_ng_container_20_Template", "NotificationComponent_div_21_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\configuration\\notification\\notification.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\configuration\\notification\\notification.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ConfigurationService } from '../configuration.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-notification',\r\n  templateUrl: './notification.component.html',\r\n  styleUrl: './notification.component.scss',\r\n})\r\nexport class NotificationComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  NotificationType: any = [];\r\n  notificationType: string = '';\r\n  notificationTitle: string = '';\r\n  loading = false;\r\n  isChecked: boolean = false;\r\n  moduleurl = 'configurations';\r\n  editNotificationTypes = {\r\n    code: '',\r\n    description: '',\r\n    is_active: '',\r\n    type: '',\r\n  };\r\n\r\n  constructor(\r\n    private service: ConfigurationService,\r\n    private messageservice: MessageService,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const routeData = this.route.snapshot.data;\r\n    this.notificationType = routeData['type'];\r\n    this.notificationTitle = routeData['title'];\r\n    this.service.configuration\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.getNotificationData();\r\n      });\r\n  }\r\n\r\n  editNotification(item: any) {\r\n    this.editNotificationTypes = {\r\n      code: item.code,\r\n      description: item.description,\r\n      is_active: item.is_active,\r\n      type: item.type,\r\n    };\r\n    item.editing = true;\r\n  }\r\n\r\n  updateNotification(item: any) {\r\n    const obj: any = {\r\n      ...this.editNotificationTypes,\r\n    };\r\n    this.service\r\n      .update(obj, item.documentId, this.moduleurl)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          item.editing = false;\r\n          item.description = this.editNotificationTypes.description;\r\n          item.code = this.editNotificationTypes.code;\r\n          item.is_active = this.editNotificationTypes.is_active;\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Updated Successfully!',\r\n          });\r\n        },\r\n        error: (err) => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  getNotificationData() {\r\n    if (this.notificationType) {\r\n      this.loading = true;\r\n      this.service\r\n        .get(\r\n          this.notificationType,\r\n          `${this.moduleurl}?filters[type][$eq]=${this.notificationType}`\r\n        )\r\n        .pipe(takeUntil(this.unsubscribe$))\r\n        .subscribe({\r\n          next: (value) => {\r\n            this.loading = false;\r\n            if (value.data?.length) {\r\n              for (let i = 0; i < value.data.length; i++) {\r\n                const element = value.data[i];\r\n              }\r\n              this.NotificationType = value.data;\r\n            } else {\r\n              this.NotificationType = [];\r\n            }\r\n          },\r\n          error: (err) => {\r\n            this.loading = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n<div class=\"card\">\r\n  <div class=\"grid mx-0\">\r\n    <h5 class=\"p-2 fw-bold\">{{ notificationTitle }}</h5>\r\n  </div>\r\n  <ng-container &ngIf=\"!loading\">\r\n    <div class=\"table-responsive\">\r\n      <table class=\"p-datatable\">\r\n        <thead class=\"p-datatable-thead\">\r\n          <tr>\r\n            <th>Code</th>\r\n            <th>Description</th>\r\n            <th>Allow Notification</th>\r\n            <th>Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"p-datatable-tbody\">\r\n          <ng-container *ngIf=\"!NotificationType.length\">\r\n            <tr>\r\n              <td colspan=\"3\">No records found.</td>\r\n            </tr>\r\n          </ng-container>\r\n          <ng-container *ngIf=\"NotificationType.length\">\r\n            <tr *ngFor=\"let item of NotificationType; let i = index\">\r\n              <ng-container *ngIf=\"item.editing\">\r\n                <td class=\"p-datatable-row\">\r\n                  <span>{{ item?.code || '-'}}</span>\r\n                </td>\r\n                <td class=\"p-datatable-row\">\r\n                  <span>{{ item?.description || '-'}}</span>\r\n                </td>\r\n                <td class=\"p-datatable-row\">\r\n                  <input\r\n                    class=\"form-check-input\"\r\n                    type=\"checkbox\"\r\n                    id=\"flexCheckChecked\"\r\n                    checked\r\n                    [(ngModel)]=\"editNotificationTypes.is_active\"\r\n                  />\r\n                </td>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"!item.editing\">\r\n                <td class=\"p-datatable-row\">\r\n                  <span>{{ item?.code || '-'}}</span>\r\n                </td>\r\n                <td class=\"p-datatable-row\">\r\n                  <span>{{ item?.description || '-'}}</span>\r\n                </td>\r\n                <td class=\"p-datatable-row\">\r\n                  <input\r\n                    class=\"form-check-input\"\r\n                    type=\"checkbox\"\r\n                    id=\"flexCheckChecked\"\r\n                    disabled=\"disabled\"\r\n                    checked=\"{{ item.is_active ? 'checked' : '' }}\"\r\n                  />\r\n                </td>\r\n              </ng-container>\r\n              <td class=\"p-datatable-row p-custom-action\">\r\n                <button\r\n                  pButton\r\n                  type=\"button\"\r\n                  icon=\"pi pi-pencil\"\r\n                  (click)=\"editNotification(item)\"\r\n                  *ngIf=\"!item.editing\"\r\n                ></button>\r\n                <button\r\n                  pButton\r\n                  type=\"button\"\r\n                  icon=\"pi pi-check\"\r\n                  (click)=\"updateNotification(item)\"\r\n                  *ngIf=\"item.editing\"\r\n                ></button>\r\n                <button\r\n                  pButton\r\n                  icon=\"pi pi-times\"\r\n                  type=\"button\"\r\n                  (click)=\"item.editing = false\"\r\n                  *ngIf=\"item.editing\"\r\n                ></button>\r\n              </td>\r\n            </tr>\r\n          </ng-container>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </ng-container>\r\n</div>\r\n\r\n<div *ngIf=\"loading\">Loading...</div>\r\n"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICa/BC,EAAA,CAAAC,uBAAA,GAA+C;IAE3CD,EADF,CAAAE,cAAA,SAAI,aACc;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACnCH,EADmC,CAAAI,YAAA,EAAK,EACnC;;;;;;;IAIHJ,EAAA,CAAAC,uBAAA,GAAmC;IAE/BD,EADF,CAAAE,cAAA,aAA4B,WACpB;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAC9BH,EAD8B,CAAAI,YAAA,EAAO,EAChC;IAEHJ,EADF,CAAAE,cAAA,aAA4B,WACpB;IAAAF,EAAA,CAAAG,MAAA,GAA6B;IACrCH,EADqC,CAAAI,YAAA,EAAO,EACvC;IAEHJ,EADF,CAAAE,cAAA,aAA4B,gBAOxB;IADAF,EAAA,CAAAK,gBAAA,2BAAAC,kGAAAC,MAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAAY,kBAAA,CAAAF,MAAA,CAAAG,qBAAA,CAAAC,SAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAG,qBAAA,CAAAC,SAAA,GAAAP,MAAA;MAAA,OAAAP,EAAA,CAAAe,WAAA,CAAAR,MAAA;IAAA,EAA6C;IAEjDP,EAPE,CAAAI,YAAA,EAME,EACC;;;;;;IAbGJ,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAC,IAAA,SAAsB;IAGtBnB,EAAA,CAAAgB,SAAA,GAA6B;IAA7BhB,EAAA,CAAAiB,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAE,WAAA,SAA6B;IAQjCpB,EAAA,CAAAgB,SAAA,GAA6C;IAA7ChB,EAAA,CAAAqB,gBAAA,YAAAX,MAAA,CAAAG,qBAAA,CAAAC,SAAA,CAA6C;;;;;IAInDd,EAAA,CAAAC,uBAAA,GAAoC;IAEhCD,EADF,CAAAE,cAAA,aAA4B,WACpB;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAC9BH,EAD8B,CAAAI,YAAA,EAAO,EAChC;IAEHJ,EADF,CAAAE,cAAA,aAA4B,WACpB;IAAAF,EAAA,CAAAG,MAAA,GAA6B;IACrCH,EADqC,CAAAI,YAAA,EAAO,EACvC;IACLJ,EAAA,CAAAE,cAAA,aAA4B;IAC1BF,EAAA,CAAAsB,SAAA,gBAME;IACJtB,EAAA,CAAAI,YAAA,EAAK;;;;;IAbGJ,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAC,IAAA,SAAsB;IAGtBnB,EAAA,CAAAgB,SAAA,GAA6B;IAA7BhB,EAAA,CAAAiB,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAE,WAAA,SAA6B;IAQjCpB,EAAA,CAAAgB,SAAA,GAA+C;IAA/ChB,EAAA,CAAAuB,qBAAA,YAAAL,OAAA,CAAAJ,SAAA,kBAA+C;;;;;;IAKnDd,EAAA,CAAAE,cAAA,iBAMC;IAFCF,EAAA,CAAAwB,UAAA,mBAAAC,qFAAA;MAAAzB,EAAA,CAAAQ,aAAA,CAAAkB,GAAA;MAAA,MAAAR,OAAA,GAAAlB,EAAA,CAAAW,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAe,WAAA,CAASL,MAAA,CAAAkB,gBAAA,CAAAV,OAAA,CAAsB;IAAA,EAAC;IAEjClB,EAAA,CAAAI,YAAA,EAAS;;;;;;IACVJ,EAAA,CAAAE,cAAA,iBAMC;IAFCF,EAAA,CAAAwB,UAAA,mBAAAK,qFAAA;MAAA7B,EAAA,CAAAQ,aAAA,CAAAsB,GAAA;MAAA,MAAAZ,OAAA,GAAAlB,EAAA,CAAAW,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAe,WAAA,CAASL,MAAA,CAAAqB,kBAAA,CAAAb,OAAA,CAAwB;IAAA,EAAC;IAEnClB,EAAA,CAAAI,YAAA,EAAS;;;;;;IACVJ,EAAA,CAAAE,cAAA,iBAMC;IAFCF,EAAA,CAAAwB,UAAA,mBAAAQ,qFAAA;MAAAhC,EAAA,CAAAQ,aAAA,CAAAyB,GAAA;MAAA,MAAAf,OAAA,GAAAlB,EAAA,CAAAW,aAAA,GAAAgB,SAAA;MAAA,OAAA3B,EAAA,CAAAe,WAAA,CAAAG,OAAA,CAAAgB,OAAA,GAAwB,KAAK;IAAA,EAAC;IAE/BlC,EAAA,CAAAI,YAAA,EAAS;;;;;IAxDdJ,EAAA,CAAAE,cAAA,SAAyD;IAkBvDF,EAjBA,CAAAmC,UAAA,IAAAC,kEAAA,0BAAmC,IAAAC,kEAAA,0BAiBC;IAiBpCrC,EAAA,CAAAE,cAAA,aAA4C;IAe1CF,EAdA,CAAAmC,UAAA,IAAAG,4DAAA,qBAMC,IAAAC,4DAAA,qBAOA,IAAAC,4DAAA,qBAOA;IAELxC,EADE,CAAAI,YAAA,EAAK,EACF;;;;IAzDYJ,EAAA,CAAAgB,SAAA,EAAkB;IAAlBhB,EAAA,CAAAyC,UAAA,SAAAvB,OAAA,CAAAgB,OAAA,CAAkB;IAiBlBlC,EAAA,CAAAgB,SAAA,EAAmB;IAAnBhB,EAAA,CAAAyC,UAAA,UAAAvB,OAAA,CAAAgB,OAAA,CAAmB;IAuB7BlC,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAyC,UAAA,UAAAvB,OAAA,CAAAgB,OAAA,CAAmB;IAOnBlC,EAAA,CAAAgB,SAAA,EAAkB;IAAlBhB,EAAA,CAAAyC,UAAA,SAAAvB,OAAA,CAAAgB,OAAA,CAAkB;IAOlBlC,EAAA,CAAAgB,SAAA,EAAkB;IAAlBhB,EAAA,CAAAyC,UAAA,SAAAvB,OAAA,CAAAgB,OAAA,CAAkB;;;;;IAxD3BlC,EAAA,CAAAC,uBAAA,GAA8C;IAC5CD,EAAA,CAAAmC,UAAA,IAAAO,mDAAA,iBAAyD;;;;;IAApC1C,EAAA,CAAAgB,SAAA,EAAqB;IAArBhB,EAAA,CAAAyC,UAAA,YAAA/B,MAAA,CAAAiC,gBAAA,CAAqB;;;;;IAkEtD3C,EAAA,CAAAE,cAAA,UAAqB;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;AD9ErC,OAAM,MAAOwC,qBAAqB;EAehCC,YACUC,OAA6B,EAC7BC,cAA8B,EAC9BC,KAAqB;IAFrB,KAAAF,OAAO,GAAPA,OAAO;IACP,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IAjBP,KAAAC,YAAY,GAAG,IAAInD,OAAO,EAAQ;IAC1C,KAAA6C,gBAAgB,GAAQ,EAAE;IAC1B,KAAAO,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,SAAS,GAAG,gBAAgB;IAC5B,KAAAzC,qBAAqB,GAAG;MACtBM,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfN,SAAS,EAAE,EAAE;MACbyC,IAAI,EAAE;KACP;EAME;EAEHC,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,IAAI;IAC1C,IAAI,CAACT,gBAAgB,GAAGO,SAAS,CAAC,MAAM,CAAC;IACzC,IAAI,CAACN,iBAAiB,GAAGM,SAAS,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACX,OAAO,CAACc,aAAa,CACvBC,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAACkD,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAEH,IAAS,IAAI;MACvB,IAAI,CAACI,mBAAmB,EAAE;IAC5B,CAAC,CAAC;EACN;EAEAnC,gBAAgBA,CAACoC,IAAS;IACxB,IAAI,CAACnD,qBAAqB,GAAG;MAC3BM,IAAI,EAAE6C,IAAI,CAAC7C,IAAI;MACfC,WAAW,EAAE4C,IAAI,CAAC5C,WAAW;MAC7BN,SAAS,EAAEkD,IAAI,CAAClD,SAAS;MACzByC,IAAI,EAAES,IAAI,CAACT;KACZ;IACDS,IAAI,CAAC9B,OAAO,GAAG,IAAI;EACrB;EAEAH,kBAAkBA,CAACiC,IAAS;IAC1B,MAAMC,GAAG,GAAQ;MACf,GAAG,IAAI,CAACpD;KACT;IACD,IAAI,CAACiC,OAAO,CACToB,MAAM,CAACD,GAAG,EAAED,IAAI,CAACG,UAAU,EAAE,IAAI,CAACb,SAAS,CAAC,CAC5CO,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAACkD,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTM,IAAI,EAAGC,GAAG,IAAI;QACZL,IAAI,CAAC9B,OAAO,GAAG,KAAK;QACpB8B,IAAI,CAAC5C,WAAW,GAAG,IAAI,CAACP,qBAAqB,CAACO,WAAW;QACzD4C,IAAI,CAAC7C,IAAI,GAAG,IAAI,CAACN,qBAAqB,CAACM,IAAI;QAC3C6C,IAAI,CAAClD,SAAS,GAAG,IAAI,CAACD,qBAAqB,CAACC,SAAS;QACrD,IAAI,CAACiC,cAAc,CAACuB,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAAC3B,cAAc,CAACuB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAT,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACb,gBAAgB,EAAE;MACzB,IAAI,CAACE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACN,OAAO,CACT6B,GAAG,CACF,IAAI,CAACzB,gBAAgB,EACrB,GAAG,IAAI,CAACI,SAAS,uBAAuB,IAAI,CAACJ,gBAAgB,EAAE,CAChE,CACAW,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAACkD,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;QACTM,IAAI,EAAGQ,KAAK,IAAI;UACd,IAAI,CAACxB,OAAO,GAAG,KAAK;UACpB,IAAIwB,KAAK,CAACjB,IAAI,EAAEkB,MAAM,EAAE;YACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACjB,IAAI,CAACkB,MAAM,EAAEC,CAAC,EAAE,EAAE;cAC1C,MAAMC,OAAO,GAAGH,KAAK,CAACjB,IAAI,CAACmB,CAAC,CAAC;YAC/B;YACA,IAAI,CAACnC,gBAAgB,GAAGiC,KAAK,CAACjB,IAAI;UACpC,CAAC,MAAM;YACL,IAAI,CAAChB,gBAAgB,GAAG,EAAE;UAC5B;QACF,CAAC;QACD8B,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACtB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACL,cAAc,CAACuB,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IACN;EACF;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAAC/B,YAAY,CAACmB,IAAI,EAAE;IACxB,IAAI,CAACnB,YAAY,CAACgC,QAAQ,EAAE;EAC9B;;;uBAxGWrC,qBAAqB,EAAA5C,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtF,EAAA,CAAAkF,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAArB5C,qBAAqB;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlC/F,EAAA,CAAAsB,SAAA,iBAAsD;UAGlDtB,EAFJ,CAAAE,cAAA,aAAkB,aACO,YACG;UAAAF,EAAA,CAAAG,MAAA,GAAuB;UACjDH,EADiD,CAAAI,YAAA,EAAK,EAChD;UACNJ,EAAA,CAAAC,uBAAA,MAA+B;UAKrBD,EAJR,CAAAE,cAAA,aAA8B,eACD,eACQ,SAC3B,UACE;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3BJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAEdH,EAFc,CAAAI,YAAA,EAAK,EACZ,EACC;UACRJ,EAAA,CAAAE,cAAA,gBAAiC;UAM/BF,EALA,CAAAmC,UAAA,KAAA8D,8CAAA,0BAA+C,KAAAC,8CAAA,0BAKD;UA+DpDlG,EAFI,CAAAI,YAAA,EAAQ,EACF,EACJ;;UAEVJ,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAmC,UAAA,KAAAgE,qCAAA,iBAAqB;;;UAzFSnG,EAAA,CAAAyC,UAAA,cAAa;UAGfzC,EAAA,CAAAgB,SAAA,GAAuB;UAAvBhB,EAAA,CAAAiB,iBAAA,CAAA+E,GAAA,CAAA7C,iBAAA,CAAuB;UAc1BnD,EAAA,CAAAgB,SAAA,IAA8B;UAA9BhB,EAAA,CAAAyC,UAAA,UAAAuD,GAAA,CAAArD,gBAAA,CAAAkC,MAAA,CAA8B;UAK9B7E,EAAA,CAAAgB,SAAA,EAA6B;UAA7BhB,EAAA,CAAAyC,UAAA,SAAAuD,GAAA,CAAArD,gBAAA,CAAAkC,MAAA,CAA6B;UAmEhD7E,EAAA,CAAAgB,SAAA,EAAa;UAAbhB,EAAA,CAAAyC,UAAA,SAAAuD,GAAA,CAAA5C,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}