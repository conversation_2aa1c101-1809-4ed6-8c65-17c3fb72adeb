{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Lithuanian [lt]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mmozuras\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var units = {\n    ss: 'sekundė_sekundžių_sekundes',\n    m: 'minutė_minutės_minutę',\n    mm: 'minutės_minučių_minutes',\n    h: 'valanda_valandos_valandą',\n    hh: 'valandos_valandų_valandas',\n    d: 'diena_dienos_dieną',\n    dd: 'dienos_dienų_dienas',\n    M: 'mėnuo_mėnesio_mėnesį',\n    MM: 'mėnesiai_mėnesių_mėnesius',\n    y: 'metai_metų_metus',\n    yy: 'metai_metų_metus'\n  };\n  function translateSeconds(number, withoutSuffix, key, isFuture) {\n    if (withoutSuffix) {\n      return 'kelios sekundės';\n    } else {\n      return isFuture ? 'kelių sekundžių' : 'kelias sekundes';\n    }\n  }\n  function translateSingular(number, withoutSuffix, key, isFuture) {\n    return withoutSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n  }\n  function special(number) {\n    return number % 10 === 0 || number > 10 && number < 20;\n  }\n  function forms(key) {\n    return units[key].split('_');\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    if (number === 1) {\n      return result + translateSingular(number, withoutSuffix, key[0], isFuture);\n    } else if (withoutSuffix) {\n      return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n    } else {\n      if (isFuture) {\n        return result + forms(key)[1];\n      } else {\n        return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n      }\n    }\n  }\n  var lt = moment.defineLocale('lt', {\n    months: {\n      format: 'sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio'.split('_'),\n      standalone: 'sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis'.split('_'),\n      isFormat: /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?|MMMM?(\\[[^\\[\\]]*\\]|\\s)+D[oD]?/\n    },\n    monthsShort: 'sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd'.split('_'),\n    weekdays: {\n      format: 'sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį'.split('_'),\n      standalone: 'sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis'.split('_'),\n      isFormat: /dddd HH:mm/\n    },\n    weekdaysShort: 'Sek_Pir_Ant_Tre_Ket_Pen_Šeš'.split('_'),\n    weekdaysMin: 'S_P_A_T_K_Pn_Š'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY [m.] MMMM D [d.]',\n      LLL: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n      LLLL: 'YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]',\n      l: 'YYYY-MM-DD',\n      ll: 'YYYY [m.] MMMM D [d.]',\n      lll: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n      llll: 'YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]'\n    },\n    calendar: {\n      sameDay: '[Šiandien] LT',\n      nextDay: '[Rytoj] LT',\n      nextWeek: 'dddd LT',\n      lastDay: '[Vakar] LT',\n      lastWeek: '[Praėjusį] dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'po %s',\n      past: 'prieš %s',\n      s: translateSeconds,\n      ss: translate,\n      m: translateSingular,\n      mm: translate,\n      h: translateSingular,\n      hh: translate,\n      d: translateSingular,\n      dd: translate,\n      M: translateSingular,\n      MM: translate,\n      y: translateSingular,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-oji/,\n    ordinal: function (number) {\n      return number + '-oji';\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return lt;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "units", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "translateSeconds", "number", "withoutSuffix", "key", "isFuture", "translateSingular", "forms", "special", "split", "translate", "result", "lt", "defineLocale", "months", "format", "standalone", "isFormat", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/moment/locale/lt.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Lithuanian [lt]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/mmozuras\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var units = {\n        ss: 'sekundė_sekundžių_sekundes',\n        m: 'minutė_minutės_minutę',\n        mm: 'minutės_minučių_minutes',\n        h: 'valanda_valandos_valandą',\n        hh: 'valandos_valandų_valandas',\n        d: 'diena_dienos_dieną',\n        dd: 'dienos_dienų_dienas',\n        M: 'mėnuo_mėnesio_mėnesį',\n        MM: 'mėnesiai_mėnesių_mėnesius',\n        y: 'metai_metų_metus',\n        yy: 'metai_metų_metus',\n    };\n    function translateSeconds(number, withoutSuffix, key, isFuture) {\n        if (withoutSuffix) {\n            return 'kelios sekundės';\n        } else {\n            return isFuture ? 'kelių sekundžių' : 'kelias sekundes';\n        }\n    }\n    function translateSingular(number, withoutSuffix, key, isFuture) {\n        return withoutSuffix\n            ? forms(key)[0]\n            : isFuture\n              ? forms(key)[1]\n              : forms(key)[2];\n    }\n    function special(number) {\n        return number % 10 === 0 || (number > 10 && number < 20);\n    }\n    function forms(key) {\n        return units[key].split('_');\n    }\n    function translate(number, withoutSuffix, key, isFuture) {\n        var result = number + ' ';\n        if (number === 1) {\n            return (\n                result + translateSingular(number, withoutSuffix, key[0], isFuture)\n            );\n        } else if (withoutSuffix) {\n            return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n        } else {\n            if (isFuture) {\n                return result + forms(key)[1];\n            } else {\n                return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n            }\n        }\n    }\n    var lt = moment.defineLocale('lt', {\n        months: {\n            format: 'sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio'.split(\n                '_'\n            ),\n            standalone:\n                'sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis'.split(\n                    '_'\n                ),\n            isFormat: /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?|MMMM?(\\[[^\\[\\]]*\\]|\\s)+D[oD]?/,\n        },\n        monthsShort: 'sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd'.split('_'),\n        weekdays: {\n            format: 'sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį'.split(\n                '_'\n            ),\n            standalone:\n                'sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis'.split(\n                    '_'\n                ),\n            isFormat: /dddd HH:mm/,\n        },\n        weekdaysShort: 'Sek_Pir_Ant_Tre_Ket_Pen_Šeš'.split('_'),\n        weekdaysMin: 'S_P_A_T_K_Pn_Š'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY-MM-DD',\n            LL: 'YYYY [m.] MMMM D [d.]',\n            LLL: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n            LLLL: 'YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]',\n            l: 'YYYY-MM-DD',\n            ll: 'YYYY [m.] MMMM D [d.]',\n            lll: 'YYYY [m.] MMMM D [d.], HH:mm [val.]',\n            llll: 'YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]',\n        },\n        calendar: {\n            sameDay: '[Šiandien] LT',\n            nextDay: '[Rytoj] LT',\n            nextWeek: 'dddd LT',\n            lastDay: '[Vakar] LT',\n            lastWeek: '[Praėjusį] dddd LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'po %s',\n            past: 'prieš %s',\n            s: translateSeconds,\n            ss: translate,\n            m: translateSingular,\n            mm: translate,\n            h: translateSingular,\n            hh: translate,\n            d: translateSingular,\n            dd: translate,\n            M: translateSingular,\n            MM: translate,\n            y: translateSingular,\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-oji/,\n        ordinal: function (number) {\n            return number + '-oji';\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return lt;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,KAAK,GAAG;IACRC,EAAE,EAAE,4BAA4B;IAChCC,CAAC,EAAE,uBAAuB;IAC1BC,EAAE,EAAE,yBAAyB;IAC7BC,CAAC,EAAE,0BAA0B;IAC7BC,EAAE,EAAE,2BAA2B;IAC/BC,CAAC,EAAE,oBAAoB;IACvBC,EAAE,EAAE,qBAAqB;IACzBC,CAAC,EAAE,sBAAsB;IACzBC,EAAE,EAAE,2BAA2B;IAC/BC,CAAC,EAAE,kBAAkB;IACrBC,EAAE,EAAE;EACR,CAAC;EACD,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC5D,IAAIF,aAAa,EAAE;MACf,OAAO,iBAAiB;IAC5B,CAAC,MAAM;MACH,OAAOE,QAAQ,GAAG,iBAAiB,GAAG,iBAAiB;IAC3D;EACJ;EACA,SAASC,iBAAiBA,CAACJ,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC7D,OAAOF,aAAa,GACdI,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,GACbC,QAAQ,GACNE,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,GACbG,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC;EACzB;EACA,SAASI,OAAOA,CAACN,MAAM,EAAE;IACrB,OAAOA,MAAM,GAAG,EAAE,KAAK,CAAC,IAAKA,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAG;EAC5D;EACA,SAASK,KAAKA,CAACH,GAAG,EAAE;IAChB,OAAOf,KAAK,CAACe,GAAG,CAAC,CAACK,KAAK,CAAC,GAAG,CAAC;EAChC;EACA,SAASC,SAASA,CAACR,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACrD,IAAIM,MAAM,GAAGT,MAAM,GAAG,GAAG;IACzB,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OACIS,MAAM,GAAGL,iBAAiB,CAACJ,MAAM,EAAEC,aAAa,EAAEC,GAAG,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;IAE3E,CAAC,MAAM,IAAIF,aAAa,EAAE;MACtB,OAAOQ,MAAM,IAAIH,OAAO,CAACN,MAAM,CAAC,GAAGK,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGG,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MACH,IAAIC,QAAQ,EAAE;QACV,OAAOM,MAAM,GAAGJ,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM;QACH,OAAOO,MAAM,IAAIH,OAAO,CAACN,MAAM,CAAC,GAAGK,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGG,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE;IACJ;EACJ;EACA,IAAIQ,EAAE,GAAGxB,MAAM,CAACyB,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE;MACJC,MAAM,EAAE,mGAAmG,CAACN,KAAK,CAC7G,GACJ,CAAC;MACDO,UAAU,EACN,iGAAiG,CAACP,KAAK,CACnG,GACJ,CAAC;MACLQ,QAAQ,EAAE;IACd,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACT,KAAK,CAAC,GAAG,CAAC;IACzEU,QAAQ,EAAE;MACNJ,MAAM,EAAE,mFAAmF,CAACN,KAAK,CAC7F,GACJ,CAAC;MACDO,UAAU,EACN,0FAA0F,CAACP,KAAK,CAC5F,GACJ,CAAC;MACLQ,QAAQ,EAAE;IACd,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACX,KAAK,CAAC,GAAG,CAAC;IACvDY,WAAW,EAAE,gBAAgB,CAACZ,KAAK,CAAC,GAAG,CAAC;IACxCa,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE,qCAAqC;MAC1CC,IAAI,EAAE,2CAA2C;MACjDC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE,qCAAqC;MAC1CC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,oBAAoB;MAC9BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE3C,gBAAgB;MACnBX,EAAE,EAAEoB,SAAS;MACbnB,CAAC,EAAEe,iBAAiB;MACpBd,EAAE,EAAEkB,SAAS;MACbjB,CAAC,EAAEa,iBAAiB;MACpBZ,EAAE,EAAEgB,SAAS;MACbf,CAAC,EAAEW,iBAAiB;MACpBV,EAAE,EAAEc,SAAS;MACbb,CAAC,EAAES,iBAAiB;MACpBR,EAAE,EAAEY,SAAS;MACbX,CAAC,EAAEO,iBAAiB;MACpBN,EAAE,EAAEU;IACR,CAAC;IACDmC,sBAAsB,EAAE,aAAa;IACrCC,OAAO,EAAE,SAAAA,CAAU5C,MAAM,EAAE;MACvB,OAAOA,MAAM,GAAG,MAAM;IAC1B,CAAC;IACD6C,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOrC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}