{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CrmComponent } from './crm.component';\nimport { PartnerFunctionsComponent } from './partner-functions/partner-functions.component';\nimport { ConfigurationComponent } from './configuration/configuration.component';\nimport { MainComponent } from './main/main.component';\nimport { PartnerDeterminationComponent } from './partner-determination/partner-determination.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'admin-email',\n  component: CrmComponent,\n  data: {\n    type: 'CRM_ADMIN_EMAILS',\n    title: 'CRM Admin Email'\n  },\n  children: [{\n    path: '',\n    redirectTo: 'general-settings',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general-settings',\n    pathMatch: 'full'\n  }]\n}, {\n  path: 'configure-partner',\n  component: PartnerFunctionsComponent,\n  data: {\n    type: 'PARTNER_FUNCTIONS',\n    title: 'Partner Functions'\n  }\n}, {\n  path: 'configure-determination',\n  component: PartnerDeterminationComponent,\n  data: {\n    type: 'PARTNER_DETERMINATION',\n    title: 'Partner Determination'\n  }\n}, {\n  path: 'configurations',\n  component: MainComponent,\n  children: [{\n    path: 'payment-terms',\n    component: ConfigurationComponent,\n    data: {\n      type: 'PAYMENT_TERMS',\n      title: 'Payment Terms',\n      columns: ['Term', 'Sales Text']\n    }\n  }, {\n    path: 'inco-terms',\n    component: ConfigurationComponent,\n    data: {\n      type: 'INCOTERMS',\n      title: 'Inco Terms',\n      columns: ['Term', 'Description']\n    }\n  }, {\n    path: 'payment-methods',\n    component: ConfigurationComponent,\n    data: {\n      type: 'PAYMENT_METHOD',\n      title: 'Payment Methods',\n      columns: ['Payment Method', 'Account Symbol']\n    }\n  }, {\n    path: 'function-cp',\n    component: ConfigurationComponent,\n    data: {\n      type: 'FUNCTION_CP',\n      title: 'Function CP',\n      columns: ['Function', 'Description']\n    }\n  }, {\n    path: 'vip',\n    component: ConfigurationComponent,\n    data: {\n      type: 'VIP',\n      title: 'VIP',\n      columns: ['VIP', 'Description']\n    }\n  }, {\n    path: 'cp_authority',\n    component: ConfigurationComponent,\n    data: {\n      type: 'CP_AUTHORITY',\n      title: 'CP Authority',\n      columns: ['Authority', 'Description']\n    }\n  }, {\n    path: 'cp_departments',\n    component: ConfigurationComponent,\n    data: {\n      type: 'CP_DEPARTMENTS',\n      title: 'CP Departments',\n      columns: ['Department', 'Description']\n    }\n  }, {\n    path: '',\n    redirectTo: 'recommendation-types',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'recommendation-types',\n    pathMatch: 'full'\n  }]\n}];\nexport class CrmRoutingModule {\n  static {\n    this.ɵfac = function CrmRoutingModule_Factory(t) {\n      return new (t || CrmRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CrmRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CrmRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CrmComponent", "PartnerFunctionsComponent", "ConfigurationComponent", "MainComponent", "PartnerDeterminationComponent", "routes", "path", "component", "data", "type", "title", "children", "redirectTo", "pathMatch", "columns", "CrmRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\crm\\crm-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { CrmComponent } from './crm.component';\r\nimport { PartnerFunctionsComponent } from './partner-functions/partner-functions.component';\r\nimport { ConfigurationComponent } from './configuration/configuration.component';\r\nimport { MainComponent } from './main/main.component';\r\nimport { PartnerDeterminationComponent } from './partner-determination/partner-determination.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'admin-email',\r\n    component: CrmComponent,\r\n    data: { type: 'CRM_ADMIN_EMAILS', title: 'CRM Admin Email' },\r\n    children: [\r\n      { path: '', redirectTo: 'general-settings', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general-settings', pathMatch: 'full' },\r\n    ],\r\n  },\r\n  {\r\n    path: 'configure-partner',\r\n    component: PartnerFunctionsComponent,\r\n    data: {\r\n      type: 'PARTNER_FUNCTIONS',\r\n      title: 'Partner Functions',\r\n    },\r\n  },\r\n  {\r\n    path: 'configure-determination',\r\n    component: PartnerDeterminationComponent,\r\n    data: {\r\n      type: 'PARTNER_DETERMINATION',\r\n      title: 'Partner Determination',\r\n    },\r\n  },\r\n  {\r\n    path: 'configurations',\r\n    component: MainComponent,\r\n    children: [\r\n      {\r\n        path: 'payment-terms',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'PAYMENT_TERMS',\r\n          title: 'Payment Terms',\r\n          columns: ['Term', 'Sales Text'],\r\n        },\r\n      },\r\n      {\r\n        path: 'inco-terms',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'INCOTERMS',\r\n          title: 'Inco Terms',\r\n          columns: ['Term', 'Description'],\r\n        },\r\n      },\r\n      {\r\n        path: 'payment-methods',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'PAYMENT_METHOD',\r\n          title: 'Payment Methods',\r\n          columns: ['Payment Method', 'Account Symbol'],\r\n        },\r\n      },\r\n      {\r\n        path: 'function-cp',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'FUNCTION_CP',\r\n          title: 'Function CP',\r\n          columns: ['Function', 'Description'],\r\n        },\r\n      },\r\n      {\r\n        path: 'vip',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'VIP',\r\n          title: 'VIP',\r\n          columns: ['VIP', 'Description'],\r\n        },\r\n      },\r\n      {\r\n        path: 'cp_authority',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'CP_AUTHORITY',\r\n          title: 'CP Authority',\r\n          columns: ['Authority', 'Description'],\r\n        },\r\n      },\r\n      {\r\n        path: 'cp_departments',\r\n        component: ConfigurationComponent,\r\n        data: {\r\n          type: 'CP_DEPARTMENTS',\r\n          title: 'CP Departments',\r\n          columns: ['Department', 'Description'],\r\n        },\r\n      },\r\n      { path: '', redirectTo: 'recommendation-types', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'recommendation-types', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class CrmRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,6BAA6B,QAAQ,yDAAyD;;;AAEvG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEP,YAAY;EACvBQ,IAAI,EAAE;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAiB,CAAE;EAC5DC,QAAQ,EAAE,CACR;IAAEL,IAAI,EAAE,EAAE;IAAEM,UAAU,EAAE,kBAAkB;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC/D;IAAEP,IAAI,EAAE,IAAI;IAAEM,UAAU,EAAE,kBAAkB;IAAEC,SAAS,EAAE;EAAM,CAAE;CAEpE,EACD;EACEP,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEN,yBAAyB;EACpCO,IAAI,EAAE;IACJC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;;CAEV,EACD;EACEJ,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAEH,6BAA6B;EACxCI,IAAI,EAAE;IACJC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;;CAEV,EACD;EACEJ,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEJ,aAAa;EACxBQ,QAAQ,EAAE,CACR;IACEL,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,eAAe;MACtBI,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY;;GAEjC,EACD;IACER,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,YAAY;MACnBI,OAAO,EAAE,CAAC,MAAM,EAAE,aAAa;;GAElC,EACD;IACER,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,iBAAiB;MACxBI,OAAO,EAAE,CAAC,gBAAgB,EAAE,gBAAgB;;GAE/C,EACD;IACER,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,aAAa;MACpBI,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa;;GAEtC,EACD;IACER,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,KAAK;MACZI,OAAO,EAAE,CAAC,KAAK,EAAE,aAAa;;GAEjC,EACD;IACER,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,cAAc;MACrBI,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa;;GAEvC,EACD;IACER,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEL,sBAAsB;IACjCM,IAAI,EAAE;MACJC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,gBAAgB;MACvBI,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa;;GAExC,EACD;IAAER,IAAI,EAAE,EAAE;IAAEM,UAAU,EAAE,sBAAsB;IAAEC,SAAS,EAAE;EAAM,CAAE,EACnE;IAAEP,IAAI,EAAE,IAAI;IAAEM,UAAU,EAAE,sBAAsB;IAAEC,SAAS,EAAE;EAAM,CAAE;CAExE,CACF;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBhB,YAAY,CAACiB,QAAQ,CAACX,MAAM,CAAC,EAC7BN,YAAY;IAAA;EAAA;;;2EAEXgB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAnB,YAAA;IAAAoB,OAAA,GAFjBpB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}