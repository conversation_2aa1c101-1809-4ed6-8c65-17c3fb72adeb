{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from \"@angular/core\";\nimport { switchMap } from \"rxjs\";\nlet SelectCustomerComponent = class SelectCustomerComponent {\n  constructor(ref, _snackBar, manageUserService, auth) {\n    this.ref = ref;\n    this._snackBar = _snackBar;\n    this.manageUserService = manageUserService;\n    this.auth = auth;\n    this.selectedCustomerId = null;\n    this.customers = [];\n    this.searchBy = \"customer_name\";\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.saving = false;\n  }\n  ngOnInit() {\n    // this.loadCustomers({ first: 0, rows: 10 });\n  }\n  loadCustomers(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    this.manageUserService.getLoggedInUserCustomersSearch({\n      perPage: pageSize,\n      search: this.globalSearchTerm,\n      searchBy: this.searchBy,\n      sortBy: event.sortField ? `${event.sortField}:${event.sortOrder == 1 ? \"asc\" : 'desc'}` : '',\n      pageNo: page\n    }).subscribe({\n      next: ({\n        data,\n        total\n      }) => {\n        this.customers = data || [];\n        const selected = this.auth.userDetail.customer?.documentId;\n        if (selected) {\n          const selectedCustomer = this.customers.find(c => c.documentId === selected);\n          selectedCustomer && (this.selectedCustomerId = selectedCustomer);\n        }\n        this.totalRecords = total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching customers', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadCustomers({\n      first: 0,\n      rows: 10\n    });\n  }\n  clear(table) {\n    this.globalSearchTerm = '';\n    this.filter.nativeElement.value = '';\n    this.loadCustomers({\n      first: 0,\n      rows: 10\n    });\n  }\n  onSearchByChange(option) {\n    this.searchBy = option;\n    this.loadCustomers({\n      first: 0,\n      rows: 10\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    if (!this.selectedCustomerId) {\n      this._snackBar.open(\"Please select customer.\", {\n        type: \"Warning\"\n      });\n      return;\n    }\n    this.saving = true;\n    this.manageUserService.updateSelectedCustomer(this.selectedCustomerId, null).pipe(switchMap(res => {\n      const user = this.auth.userDetail;\n      return this.auth.getCartDetails(user.documentId);\n    })).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (cartres) {\n          _this.saving = false;\n          if (cartres?.cart) {\n            const cart = cartres.cart || null;\n            _this.auth.updateAuth({\n              cart,\n              customer: cart.customer\n            });\n          }\n          _this.ref.close();\n          location.reload();\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }(),\n      error: () => {\n        this.saving = false;\n        this._snackBar.open(\"Error while processing your request.\", {\n          type: \"Error\"\n        });\n      }\n    });\n  }\n  onReset() {\n    this.selectedCustomerId = null;\n  }\n};\n__decorate([ViewChild('filter')], SelectCustomerComponent.prototype, \"filter\", void 0);\nSelectCustomerComponent = __decorate([Component({\n  selector: \"app-select-customer\",\n  templateUrl: \"./select-customer.component.html\",\n  styleUrls: [\"./select-customer.component.scss\"]\n})], SelectCustomerComponent);\nexport { SelectCustomerComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "switchMap", "SelectCustomerComponent", "constructor", "ref", "_snackBar", "manageUserService", "auth", "selectedCustomerId", "customers", "searchBy", "totalRecords", "loading", "globalSearchTerm", "saving", "ngOnInit", "loadCustomers", "event", "page", "first", "rows", "pageSize", "getLoggedInUserCustomersSearch", "perPage", "search", "sortBy", "sortField", "sortOrder", "pageNo", "subscribe", "next", "data", "total", "selected", "userDetail", "customer", "documentId", "selectedCustomer", "find", "c", "error", "console", "onGlobalFilter", "table", "clear", "filter", "nativeElement", "value", "onSearchByChange", "option", "onSubmit", "_this", "open", "type", "updateSelectedCustomer", "pipe", "res", "user", "getCartDetails", "_ref", "_asyncToGenerator", "cartres", "cart", "updateAuth", "close", "location", "reload", "_x", "apply", "arguments", "onReset", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\select-customer\\select-customer.component.ts"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from \"@angular/core\";\r\nimport { DynamicDialogRef } from \"primeng/dynamicdialog\";\r\nimport { Table } from \"primeng/table\";\r\nimport { switchMap } from \"rxjs\";\r\nimport { AppToastService } from \"src/app/shared/services/toast.service\";\r\nimport { ManageUserService } from \"../service/manage-user.service\";\r\nimport { AuthService } from \"src/app/core/authentication/auth.service\";\r\n\r\n@Component({\r\n  selector: \"app-select-customer\",\r\n  templateUrl: \"./select-customer.component.html\",\r\n  styleUrls: [\"./select-customer.component.scss\"],\r\n})\r\nexport class SelectCustomerComponent {\r\n  public selectedCustomerId: any = null;\r\n  public customers: any[] = [];\r\n  public searchBy: string = \"customer_name\";\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public saving = false;\r\n  @ViewChild('filter') filter!: ElementRef;\r\n\r\n  constructor(\r\n    public ref: DynamicDialogRef,\r\n    private _snackBar: AppToastService,\r\n    private manageUserService: ManageUserService,\r\n    private auth: AuthService,\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n\r\n    // this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  loadCustomers(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    this.manageUserService\r\n      .getLoggedInUserCustomersSearch({\r\n        perPage: pageSize,\r\n        search: this.globalSearchTerm,\r\n        searchBy: this.searchBy,\r\n        sortBy: event.sortField ? `${event.sortField}:${event.sortOrder == 1 ? \"asc\" : 'desc'}` : '',\r\n        pageNo: page,\r\n      }).subscribe({\r\n        next: ({ data, total }: any) => {\r\n          this.customers = data || [];\r\n          const selected = this.auth.userDetail.customer?.documentId;\r\n          if (selected) {\r\n            const selectedCustomer = this.customers.find(c => c.documentId === selected);\r\n            selectedCustomer && (this.selectedCustomerId = selectedCustomer);\r\n          }\r\n          this.totalRecords = total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching customers', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  clear(table: Table) {\r\n    this.globalSearchTerm = '';\r\n    this.filter.nativeElement.value = '';\r\n    this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  onSearchByChange(option: string) {\r\n    this.searchBy = option;\r\n    this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (!this.selectedCustomerId) {\r\n      this._snackBar.open(\"Please select customer.\", {\r\n        type: \"Warning\",\r\n      });\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    this.manageUserService\r\n      .updateSelectedCustomer(this.selectedCustomerId, null)\r\n      .pipe(\r\n        switchMap((res) => {\r\n          const user = this.auth.userDetail;\r\n          return this.auth.getCartDetails(user.documentId);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: async (cartres: any) => {\r\n          this.saving = false;\r\n          if (cartres?.cart) {\r\n            const cart = cartres.cart || null;\r\n            this.auth.updateAuth({\r\n              cart,\r\n              customer: cart.customer\r\n            });\r\n          }\r\n          this.ref.close();\r\n          location.reload();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this._snackBar.open(\"Error while processing your request.\", {\r\n            type: \"Error\",\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  onReset() {\r\n    this.selectedCustomerId = null;\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAcC,SAAS,QAAQ,eAAe;AAGhE,SAASC,SAAS,QAAQ,MAAM;AAUzB,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAUlCC,YACSC,GAAqB,EACpBC,SAA0B,EAC1BC,iBAAoC,EACpCC,IAAiB;IAHlB,KAAAH,GAAG,GAAHA,GAAG;IACF,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,IAAI,GAAJA,IAAI;IAbP,KAAAC,kBAAkB,GAAQ,IAAI;IAC9B,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,QAAQ,GAAW,eAAe;IAClC,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,MAAM,GAAG,KAAK;EAQjB;EAEJC,QAAQA,CAAA;IAEN;EAAA;EAGFC,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,MAAMM,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,IAAI,CAACd,iBAAiB,CACnBgB,8BAA8B,CAAC;MAC9BC,OAAO,EAAEF,QAAQ;MACjBG,MAAM,EAAE,IAAI,CAACX,gBAAgB;MAC7BH,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBe,MAAM,EAAER,KAAK,CAACS,SAAS,GAAG,GAAGT,KAAK,CAACS,SAAS,IAAIT,KAAK,CAACU,SAAS,IAAI,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,GAAG,EAAE;MAC5FC,MAAM,EAAEV;KACT,CAAC,CAACW,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAK,CAAO,KAAI;QAC7B,IAAI,CAACvB,SAAS,GAAGsB,IAAI,IAAI,EAAE;QAC3B,MAAME,QAAQ,GAAG,IAAI,CAAC1B,IAAI,CAAC2B,UAAU,CAACC,QAAQ,EAAEC,UAAU;QAC1D,IAAIH,QAAQ,EAAE;UACZ,MAAMI,gBAAgB,GAAG,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,UAAU,KAAKH,QAAQ,CAAC;UAC5EI,gBAAgB,KAAK,IAAI,CAAC7B,kBAAkB,GAAG6B,gBAAgB,CAAC;QAClE;QACA,IAAI,CAAC1B,YAAY,GAAGqB,KAAK;QACzB,IAAI,CAACpB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD4B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC5B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA8B,cAAcA,CAACC,KAAY,EAAE1B,KAAY;IACvC,IAAI,CAACD,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEAwB,KAAKA,CAACD,KAAY;IAChB,IAAI,CAAC9B,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACgC,MAAM,CAACC,aAAa,CAACC,KAAK,GAAG,EAAE;IACpC,IAAI,CAAC/B,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEA4B,gBAAgBA,CAACC,MAAc;IAC7B,IAAI,CAACvC,QAAQ,GAAGuC,MAAM;IACtB,IAAI,CAACjC,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEA8B,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACN,IAAI,CAAC,IAAI,CAAC3C,kBAAkB,EAAE;MAC5B,IAAI,CAACH,SAAS,CAAC+C,IAAI,CAAC,yBAAyB,EAAE;QAC7CC,IAAI,EAAE;OACP,CAAC;MACF;IACF;IACA,IAAI,CAACvC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACR,iBAAiB,CACnBgD,sBAAsB,CAAC,IAAI,CAAC9C,kBAAkB,EAAE,IAAI,CAAC,CACrD+C,IAAI,CACHtD,SAAS,CAAEuD,GAAG,IAAI;MAChB,MAAMC,IAAI,GAAG,IAAI,CAAClD,IAAI,CAAC2B,UAAU;MACjC,OAAO,IAAI,CAAC3B,IAAI,CAACmD,cAAc,CAACD,IAAI,CAACrB,UAAU,CAAC;IAClD,CAAC,CAAC,CACH,CACAP,SAAS,CAAC;MACTC,IAAI;QAAA,IAAA6B,IAAA,GAAAC,iBAAA,CAAE,WAAOC,OAAY,EAAI;UAC3BV,KAAI,CAACrC,MAAM,GAAG,KAAK;UACnB,IAAI+C,OAAO,EAAEC,IAAI,EAAE;YACjB,MAAMA,IAAI,GAAGD,OAAO,CAACC,IAAI,IAAI,IAAI;YACjCX,KAAI,CAAC5C,IAAI,CAACwD,UAAU,CAAC;cACnBD,IAAI;cACJ3B,QAAQ,EAAE2B,IAAI,CAAC3B;aAChB,CAAC;UACJ;UACAgB,KAAI,CAAC/C,GAAG,CAAC4D,KAAK,EAAE;UAChBC,QAAQ,CAACC,MAAM,EAAE;QACnB,CAAC;QAAA,gBAXDpC,IAAIA,CAAAqC,EAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA,GAWH;MACD7B,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC1B,MAAM,GAAG,KAAK;QACnB,IAAI,CAACT,SAAS,CAAC+C,IAAI,CAAC,sCAAsC,EAAE;UAC1DC,IAAI,EAAE;SACP,CAAC;MACJ;KACD,CAAC;EACN;EAEAiB,OAAOA,CAAA;IACL,IAAI,CAAC9D,kBAAkB,GAAG,IAAI;EAChC;CACD;AAnGsB+D,UAAA,EAApBvE,SAAS,CAAC,QAAQ,CAAC,C,sDAAqB;AAR9BE,uBAAuB,GAAAqE,UAAA,EALnCxE,SAAS,CAAC;EACTyE,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACWxE,uBAAuB,CA2GnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}