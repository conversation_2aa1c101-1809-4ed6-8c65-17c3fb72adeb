{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/table\";\nimport * as i2 from \"primeng/api\";\nfunction VendorPurchaseOrderDetailsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 24);\n    i0.ɵɵelementStart(2, \"th\", 25);\n    i0.ɵɵtext(3, \"PO Line\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\", 26);\n    i0.ɵɵtext(5, \"Material Num\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 26);\n    i0.ɵɵtext(7, \"PO Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 26);\n    i0.ɵɵtext(9, \"Vendor Material\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 26);\n    i0.ɵɵtext(11, \"Material Desc\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 26);\n    i0.ɵɵtext(13, \"Quantity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 26);\n    i0.ɵɵtext(15, \"Weight\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction VendorPurchaseOrderDetailsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27);\n    i0.ɵɵelement(2, \"p-tableRadioButton\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", product_r1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r1.OrderLine);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r1.SKU);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r1.PODate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r1.VendorMaterial);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r1.MaterialDesc);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", product_r1.Quantity, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", product_r1.Weight, \"\");\n  }\n}\nexport class VendorPurchaseOrderDetailsComponent {\n  constructor() {\n    this.OrderLines = [{\n      OrderLine: 10,\n      SKU: '3RDPSUPPL',\n      PODate: '04/05/2025',\n      VendorMaterial: '15',\n      MaterialDesc: 'CountryInn & Suites Conditioner 1.18 oz.',\n      Quantity: 5,\n      Weight: '169.00'\n    }, {\n      OrderLine: 11,\n      SKU: '3RDPSUPPL',\n      PODate: '04/05/2025',\n      VendorMaterial: '15',\n      MaterialDesc: 'CountryInn & Suites Conditioner 1.18 oz.',\n      Quantity: 5,\n      Weight: '169.00'\n    }];\n    this.expandedRows = {};\n  }\n  toggleRow(product) {\n    this.expandedRows[product.OrderLine] = !this.expandedRows[product.OrderLine];\n  }\n  isRowExpanded(product) {\n    return this.expandedRows[product.OrderLine] || false;\n  }\n  static {\n    this.ɵfac = function VendorPurchaseOrderDetailsComponent_Factory(t) {\n      return new (t || VendorPurchaseOrderDetailsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VendorPurchaseOrderDetailsComponent,\n      selectors: [[\"app-vendor-purchase-order-details\"]],\n      decls: 123,\n      vars: 3,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"m-0\"], [1, \"grid\", \"mt-0\", \"relative\"], [1, \"col-12\", \"order-lines\", \"w-full\"], [1, \"p-4\", \"mb-0\", \"w-full\", \"bg-white\", \"border-round\", \"shadow-2\", \"overflow-hidden\", \"h-full\"], [1, \"mb-2\", \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\"], [1, \"block\", \"font-bold\", \"text-xl\", \"mb-2\", \"text-orange-600\"], [\"dataKey\", \"OrderLine\", \"styleClass\", \"p-datatable-gridlines\", 3, \"value\", \"responsiveLayout\", \"scrollable\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-12\", \"card-list\"], [1, \"card\", \"shadow-2\", \"p-4\", \"flex\", \"flex-column\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\"], [1, \"block\", \"font-bold\", \"text-xl\", \"mb-2\", \"text-primary\"], [1, \"form-group\", \"flex\", \"flex-column\", \"gap-2\", \"mb-3\"], [1, \"flex\", \"gap-4\"], [1, \"col-12\", \"lg:flex-1\", \"p-0\"], [1, \"form-group\", \"flex\", \"flex-column\", \"gap-2\"], [1, \"font-medium\"], [\"pinputtext\", \"\", \"id\", \"name1\", \"type\", \"text\", 1, \"p-inputtext\", \"p-component\", \"p-element\"], [1, \"col-12\", \"lg:flex-1\", \"p-0\", \"flex\", \"flex-column\", \"gap-2\"], [1, \"form-submit-sec\", \"mt-6\", \"flex\", \"align-items-center\", \"justify-content-end\", \"gap-3\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-9rem\", \"h-3rem\"], [2, \"width\", \"3rem\"], [1, \"font-semibold\", 2, \"min-width\", \"100px\"], [1, \"font-semibold\", 2, \"min-width\", \"150px\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [3, \"value\"]],\n      template: function VendorPurchaseOrderDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"Vendor Purchase Order Details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"h3\", 7);\n          i0.ɵɵtext(9, \"P.O # : 3RDPSUPPL-01\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"p-table\", 8);\n          i0.ɵɵtemplate(11, VendorPurchaseOrderDetailsComponent_ng_template_11_Template, 16, 0, \"ng-template\", 9)(12, VendorPurchaseOrderDetailsComponent_ng_template_12_Template, 17, 8, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"h3\", 14);\n          i0.ɵɵtext(17, \"Origin Address\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"div\", 16)(20, \"div\", 17)(21, \"div\", 18)(22, \"label\", 19);\n          i0.ɵɵtext(23, \"Company Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 21)(26, \"div\", 18)(27, \"label\", 19);\n          i0.ɵɵtext(28, \"Street Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 21)(31, \"div\", 18)(32, \"label\", 19);\n          i0.ɵɵtext(33, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"div\", 16)(37, \"div\", 17)(38, \"div\", 18)(39, \"label\", 19);\n          i0.ɵɵtext(40, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 21)(43, \"div\", 18)(44, \"label\", 19);\n          i0.ɵɵtext(45, \"Zip\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 21)(48, \"div\", 18)(49, \"label\", 19);\n          i0.ɵɵtext(50, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(52, \"hr\");\n          i0.ɵɵelementStart(53, \"div\", 13)(54, \"h3\", 14);\n          i0.ɵɵtext(55, \"Contact Details\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 15)(57, \"div\", 16)(58, \"div\", 17)(59, \"div\", 18)(60, \"label\", 19);\n          i0.ɵɵtext(61, \"Contact Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(62, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 21)(64, \"div\", 18)(65, \"label\", 19);\n          i0.ɵɵtext(66, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(67, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 21)(69, \"div\", 18)(70, \"label\", 19);\n          i0.ɵɵtext(71, \"Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"input\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(73, \"hr\");\n          i0.ɵɵelementStart(74, \"div\", 13)(75, \"h3\", 14);\n          i0.ɵɵtext(76, \"Shipment Dates\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 15)(78, \"div\", 16)(79, \"div\", 17)(80, \"div\", 18)(81, \"label\", 19);\n          i0.ɵɵtext(82, \"Earliest Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(83, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 21)(85, \"div\", 18)(86, \"label\", 19);\n          i0.ɵɵtext(87, \"Latest Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(88, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(89, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(90, \"hr\");\n          i0.ɵɵelementStart(91, \"div\", 13)(92, \"h3\", 14);\n          i0.ɵɵtext(93, \"Containers Totals\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 15)(95, \"div\", 16)(96, \"div\", 17)(97, \"div\", 18)(98, \"label\", 19);\n          i0.ɵɵtext(99, \"Carton vs Pallet \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(100, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"div\", 21)(102, \"div\", 18)(103, \"label\", 19);\n          i0.ɵɵtext(104, \"Carton/Pallet Qty \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(105, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(106, \"div\", 21)(107, \"div\", 18)(108, \"label\", 19);\n          i0.ɵɵtext(109, \"Total Weight \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(110, \"input\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(111, \"div\", 15)(112, \"div\", 16)(113, \"div\", 21)(114, \"div\", 18)(115, \"label\", 19);\n          i0.ɵɵtext(116, \"Freight Class\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(117, \"input\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(118, \"div\", 21)(119, \"div\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(120, \"div\", 22)(121, \"button\", 23);\n          i0.ɵɵtext(122, \"Save\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", ctx.OrderLines)(\"responsiveLayout\", \"scroll\")(\"scrollable\", true);\n        }\n      },\n      dependencies: [i1.Table, i2.PrimeTemplate, i1.TableRadioButton],\n      styles: [\".vendor-po-table table thead th {\\n  min-width: 150px;\\n}\\n  .vendor-po-table table tbody tr td:first-child {\\n  background: var(--surface-0);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvdmVuZG9yLXB1cmNoYXNlLW9yZGVyLWRldGFpbHMvdmVuZG9yLXB1cmNoYXNlLW9yZGVyLWRldGFpbHMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBS2dCO0VBQ0ksZ0JBQUE7QUFKcEI7QUFVb0I7RUFDSSw0QkFBQTtBQVJ4QiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCB7XHJcblxyXG4gICAgLnZlbmRvci1wby10YWJsZSB7XHJcbiAgICAgICAgdGFibGUge1xyXG4gICAgICAgICAgICB0aGVhZCB7XHJcbiAgICAgICAgICAgICAgICB0aCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWluLXdpZHRoOiAxNTBweDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgdGJvZHkge1xyXG4gICAgICAgICAgICAgICAgdHIge1xyXG4gICAgICAgICAgICAgICAgICAgIHRkOmZpcnN0LWNoaWxkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0wKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "product_r1", "ɵɵtextInterpolate", "OrderLine", "SKU", "PODate", "VendorMaterial", "MaterialDesc", "ɵɵtextInterpolate1", "Quantity", "Weight", "VendorPurchaseOrderDetailsComponent", "constructor", "OrderLines", "expandedRows", "toggleRow", "product", "isRowExpanded", "selectors", "decls", "vars", "consts", "template", "VendorPurchaseOrderDetailsComponent_Template", "rf", "ctx", "ɵɵtemplate", "VendorPurchaseOrderDetailsComponent_ng_template_11_Template", "VendorPurchaseOrderDetailsComponent_ng_template_12_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\vendor-purchase-order-details\\vendor-purchase-order-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\vendor-purchase-order-details\\vendor-purchase-order-details.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-vendor-purchase-order-details',\r\n  templateUrl: './vendor-purchase-order-details.component.html',\r\n  styleUrl: './vendor-purchase-order-details.component.scss'\r\n})\r\nexport class VendorPurchaseOrderDetailsComponent {\r\n  \r\n  calendarVal?: Date;\r\n\r\n  OrderLines: any = [\r\n    {\r\n      OrderLine: 10,\r\n      SKU: '3RDPSUPPL',\r\n      PODate: '04/05/2025',\r\n      VendorMaterial: '15',\r\n      MaterialDesc: 'CountryInn & Suites Conditioner 1.18 oz.',\r\n      Quantity: 5,\r\n      Weight: '169.00',\r\n    },\r\n    {\r\n      OrderLine: 11,\r\n      SKU: '3RDPSUPPL',\r\n      PODate: '04/05/2025',\r\n      VendorMaterial: '15',\r\n      MaterialDesc: 'CountryInn & Suites Conditioner 1.18 oz.',\r\n      Quantity: 5,\r\n      Weight: '169.00',\r\n    }\r\n  ];\r\n\r\n  expandedRows: { [key: string]: boolean } = {};\r\n\r\n  toggleRow(product: any) {\r\n    this.expandedRows[product.OrderLine] = !this.expandedRows[product.OrderLine];\r\n  }\r\n\r\n  isRowExpanded(product: any): boolean {\r\n    return this.expandedRows[product.OrderLine] || false;\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-4 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n        <h3 class=\"m-0\">Vendor Purchase Order Details</h3>\r\n        <!-- <h5 class=\"m-0\">Vendor Name : <span class=\"text-primary\">Volcom</span></h5> -->\r\n    </div>\r\n\r\n\r\n    <div class=\"grid mt-0 relative\">\r\n        <div class=\"col-12 order-lines w-full\">\r\n            <div class=\"p-4 mb-0 w-full bg-white border-round shadow-2 overflow-hidden h-full\">\r\n                <div class=\"mb-2 flex align-items-center justify-content-between cursor-pointer\">\r\n                    <h3 class=\"block font-bold text-xl mb-2 text-orange-600\">P.O # : 3RDPSUPPL-01</h3>\r\n                </div>\r\n\r\n                <p-table [value]=\"OrderLines\" dataKey=\"OrderLine\" [responsiveLayout]=\"'scroll'\"\r\n                    styleClass=\"p-datatable-gridlines\" [scrollable]=\"true\">\r\n\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th style=\"width: 3rem\"></th>\r\n                            <th style=\"min-width: 100px;\" class=\"font-semibold\">PO Line</th>\r\n                            <th style=\"min-width: 150px;\" class=\"font-semibold\">Material Num</th>\r\n                            <th style=\"min-width: 150px;\" class=\"font-semibold\">PO Date</th>\r\n                            <th style=\"min-width: 150px;\" class=\"font-semibold\">Vendor Material</th>\r\n                            <th style=\"min-width: 150px;\" class=\"font-semibold\">Material Desc</th>\r\n                            <th style=\"min-width: 150px;\" class=\"font-semibold\">Quantity</th>\r\n                            <th style=\"min-width: 150px;\" class=\"font-semibold\">Weight</th>\r\n                        </tr>\r\n                    </ng-template>\r\n\r\n                    <ng-template pTemplate=\"body\" let-product>\r\n                        <tr>\r\n                            <td class=\"flex align-items-center justify-content-center\">\r\n                                <p-tableRadioButton [value]=\"product\" />\r\n                            </td>\r\n                            <td>{{product.OrderLine}}</td>\r\n                            <td>{{product.SKU}}</td>\r\n                            <td>{{product.PODate}}</td>\r\n                            <td>{{product.VendorMaterial}}</td>\r\n                            <td>{{product.MaterialDesc}}</td>\r\n                            <td>${{product.Quantity}}</td>\r\n                            <td>${{product.Weight}}</td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"col-12 card-list\">\r\n            <div class=\"card shadow-2 p-4 flex flex-column gap-2\">\r\n                <div class=\"flex align-items-center justify-content-between cursor-pointer\">\r\n                    <h3 class=\"block font-bold text-xl mb-2 text-primary\">Origin Address</h3>\r\n                </div>\r\n                <div class=\"form-group flex flex-column gap-2 mb-3\">\r\n                    <div class=\"flex gap-4\">\r\n                        <div class=\"col-12 lg:flex-1 p-0\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Company Name</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Street Address</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">City</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"form-group flex flex-column gap-2 mb-3\">\r\n                    <div class=\"flex gap-4\">\r\n                        <div class=\"col-12 lg:flex-1 p-0\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">State</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Zip</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Country</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <hr />\r\n                <div class=\"flex align-items-center justify-content-between cursor-pointer\">\r\n                    <h3 class=\"block font-bold text-xl mb-2 text-primary\">Contact Details</h3>\r\n                </div>\r\n                <div class=\"form-group flex flex-column gap-2 mb-3\">\r\n                    <div class=\"flex gap-4\">\r\n                        <div class=\"col-12 lg:flex-1 p-0\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Contact Name</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Email Address</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Phone Number</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <hr />\r\n                <div class=\"flex align-items-center justify-content-between cursor-pointer\">\r\n                    <h3 class=\"block font-bold text-xl mb-2 text-primary\">Shipment Dates</h3>\r\n                </div>\r\n                <div class=\"form-group flex flex-column gap-2 mb-3\">\r\n                    <div class=\"flex gap-4\">\r\n                        <div class=\"col-12 lg:flex-1 p-0\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Earliest Date</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Latest Date</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\"></div>\r\n                    </div>\r\n                </div>\r\n                <hr />\r\n                <div class=\"flex align-items-center justify-content-between cursor-pointer\">\r\n                    <h3 class=\"block font-bold text-xl mb-2 text-primary\">Containers Totals</h3>\r\n                </div>\r\n                <div class=\"form-group flex flex-column gap-2 mb-3\">\r\n                    <div class=\"flex gap-4\">\r\n                        <div class=\"col-12 lg:flex-1 p-0\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Carton vs Pallet </label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Carton/Pallet Qty </label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Total Weight </label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"form-group flex flex-column gap-2 mb-3\">\r\n                    <div class=\"flex gap-4\">\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\">\r\n                            <div class=\"form-group flex flex-column gap-2\">\r\n                                <label class=\"font-medium\">Freight Class</label>\r\n                                <input pinputtext=\"\" id=\"name1\" type=\"text\" class=\"p-inputtext p-component p-element\">\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\"> </div>\r\n                        <div class=\"col-12 lg:flex-1 p-0 flex flex-column gap-2\"> </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"form-submit-sec mt-6 flex align-items-center justify-content-end gap-3\">\r\n                <button type=\"button\"\r\n                    class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-9rem h-3rem\">Save</button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n</div>"], "mappings": ";;;;;ICkBwBA,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,SAAA,aAA6B;IAC7BF,EAAA,CAAAC,cAAA,aAAoD;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChEJ,EAAA,CAAAC,cAAA,aAAoD;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrEJ,EAAA,CAAAC,cAAA,aAAoD;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChEJ,EAAA,CAAAC,cAAA,aAAoD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxEJ,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtEJ,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjEJ,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAC9DH,EAD8D,CAAAI,YAAA,EAAK,EAC9D;;;;;IAKDJ,EADJ,CAAAC,cAAA,SAAI,aAC2D;IACvDD,EAAA,CAAAE,SAAA,6BAAwC;IAC5CF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAK,EAC3B;;;;IATuBJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,UAAA,UAAAC,UAAA,CAAiB;IAErCP,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAQ,iBAAA,CAAAD,UAAA,CAAAE,SAAA,CAAqB;IACrBT,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAQ,iBAAA,CAAAD,UAAA,CAAAG,GAAA,CAAe;IACfV,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAQ,iBAAA,CAAAD,UAAA,CAAAI,MAAA,CAAkB;IAClBX,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAQ,iBAAA,CAAAD,UAAA,CAAAK,cAAA,CAA0B;IAC1BZ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAQ,iBAAA,CAAAD,UAAA,CAAAM,YAAA,CAAwB;IACxBb,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAc,kBAAA,MAAAP,UAAA,CAAAQ,QAAA,KAAqB;IACrBf,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAc,kBAAA,MAAAP,UAAA,CAAAS,MAAA,KAAmB;;;ADlCnD,OAAM,MAAOC,mCAAmC;EALhDC,YAAA;IASE,KAAAC,UAAU,GAAQ,CAChB;MACEV,SAAS,EAAE,EAAE;MACbC,GAAG,EAAE,WAAW;MAChBC,MAAM,EAAE,YAAY;MACpBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,0CAA0C;MACxDE,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;KACT,EACD;MACEP,SAAS,EAAE,EAAE;MACbC,GAAG,EAAE,WAAW;MAChBC,MAAM,EAAE,YAAY;MACpBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,0CAA0C;MACxDE,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;KACT,CACF;IAED,KAAAI,YAAY,GAA+B,EAAE;;EAE7CC,SAASA,CAACC,OAAY;IACpB,IAAI,CAACF,YAAY,CAACE,OAAO,CAACb,SAAS,CAAC,GAAG,CAAC,IAAI,CAACW,YAAY,CAACE,OAAO,CAACb,SAAS,CAAC;EAC9E;EAEAc,aAAaA,CAACD,OAAY;IACxB,OAAO,IAAI,CAACF,YAAY,CAACE,OAAO,CAACb,SAAS,CAAC,IAAI,KAAK;EACtD;;;uBAjCWQ,mCAAmC;IAAA;EAAA;;;YAAnCA,mCAAmC;MAAAO,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLxC9B,EAFR,CAAAC,cAAA,aAA2E,aACc,YACjE;UAAAD,EAAA,CAAAG,MAAA,oCAA6B;UAEjDH,EAFiD,CAAAI,YAAA,EAAK,EAEhD;UAOUJ,EAJhB,CAAAC,cAAA,aAAgC,aACW,aACgD,aACE,YACpB;UAAAD,EAAA,CAAAG,MAAA,2BAAoB;UACjFH,EADiF,CAAAI,YAAA,EAAK,EAChF;UAENJ,EAAA,CAAAC,cAAA,kBAC2D;UAevDD,EAbA,CAAAgC,UAAA,KAAAC,2DAAA,0BAAgC,KAAAC,2DAAA,2BAaU;UAgBtDlC,EAFQ,CAAAI,YAAA,EAAU,EACR,EACJ;UAKMJ,EAHZ,CAAAC,cAAA,eAA8B,eAC4B,eAC0B,cAClB;UAAAD,EAAA,CAAAG,MAAA,sBAAc;UACxEH,EADwE,CAAAI,YAAA,EAAK,EACvE;UAKUJ,EAJhB,CAAAC,cAAA,eAAoD,eACxB,eACc,eACiB,iBAChB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/CJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACjDJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACvCJ,EAAA,CAAAE,SAAA,iBAAsF;UAItGF,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;UAKUJ,EAJhB,CAAAC,cAAA,eAAoD,eACxB,eACc,eACiB,iBAChB;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACxCJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC1CJ,EAAA,CAAAE,SAAA,iBAAsF;UAItGF,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;UACNJ,EAAA,CAAAE,SAAA,UAAM;UAEFF,EADJ,CAAAC,cAAA,eAA4E,cAClB;UAAAD,EAAA,CAAAG,MAAA,uBAAe;UACzEH,EADyE,CAAAI,YAAA,EAAK,EACxE;UAKUJ,EAJhB,CAAAC,cAAA,eAAoD,eACxB,eACc,eACiB,iBAChB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/CJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAChDJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/CJ,EAAA,CAAAE,SAAA,iBAAsF;UAItGF,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;UACNJ,EAAA,CAAAE,SAAA,UAAM;UAEFF,EADJ,CAAAC,cAAA,eAA4E,cAClB;UAAAD,EAAA,CAAAG,MAAA,sBAAc;UACxEH,EADwE,CAAAI,YAAA,EAAK,EACvE;UAKUJ,EAJhB,CAAAC,cAAA,eAAoD,eACxB,eACc,eACiB,iBAChB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAChDJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyD,eACN,iBAChB;UAAAD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC9CJ,EAAA,CAAAE,SAAA,iBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UACNJ,EAAA,CAAAE,SAAA,eAA+D;UAEvEF,EADI,CAAAI,YAAA,EAAM,EACJ;UACNJ,EAAA,CAAAE,SAAA,UAAM;UAEFF,EADJ,CAAAC,cAAA,eAA4E,cAClB;UAAAD,EAAA,CAAAG,MAAA,yBAAiB;UAC3EH,EAD2E,CAAAI,YAAA,EAAK,EAC1E;UAKUJ,EAJhB,CAAAC,cAAA,eAAoD,eACxB,eACc,eACiB,iBAChB;UAAAD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACpDJ,EAAA,CAAAE,SAAA,kBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,gBAAyD,gBACN,kBAChB;UAAAD,EAAA,CAAAG,MAAA,2BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACrDJ,EAAA,CAAAE,SAAA,kBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAGEJ,EAFR,CAAAC,cAAA,gBAAyD,gBACN,kBAChB;UAAAD,EAAA,CAAAG,MAAA,sBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAChDJ,EAAA,CAAAE,SAAA,kBAAsF;UAItGF,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;UAKUJ,EAJhB,CAAAC,cAAA,gBAAoD,gBACxB,gBACqC,gBACN,kBAChB;UAAAD,EAAA,CAAAG,MAAA,sBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAChDJ,EAAA,CAAAE,SAAA,kBAAsF;UAE9FF,EADI,CAAAI,YAAA,EAAM,EACJ;UAENJ,EADA,CAAAE,SAAA,gBAAgE,gBACA;UAG5EF,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;UAEFJ,EADJ,CAAAC,cAAA,gBAAoF,mBAE0B;UAAAD,EAAA,CAAAG,MAAA,aAAI;UAK9HH,EAL8H,CAAAI,YAAA,EAAS,EACrH,EACJ,EACJ,EAEJ;;;UAhLmBJ,EAAA,CAAAK,SAAA,IAAoB;UACUL,EAD9B,CAAAM,UAAA,UAAAyB,GAAA,CAAAZ,UAAA,CAAoB,8BAAkD,oBACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}