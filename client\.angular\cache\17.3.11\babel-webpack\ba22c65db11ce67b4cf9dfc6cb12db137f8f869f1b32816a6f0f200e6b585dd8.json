{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ProfileComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"form\", 11)(2, \"div\", 12)(3, \"label\", 13)(4, \"span\", 8);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" User Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \": <PERSON> \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 12)(10, \"label\", 13)(11, \"span\", 8);\n    i0.ɵɵtext(12, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 14);\n    i0.ɵɵelement(15, \"input\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 12)(17, \"label\", 13)(18, \"span\", 8);\n    i0.ɵɵtext(19, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 14);\n    i0.ɵɵelement(22, \"input\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 12)(24, \"label\", 13)(25, \"span\", 8);\n    i0.ɵɵtext(26, \"phone_in_talk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 14);\n    i0.ɵɵelement(29, \"input\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 12)(31, \"label\", 13)(32, \"span\", 8);\n    i0.ɵɵtext(33, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" E-mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 14);\n    i0.ɵɵelement(36, \"input\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 19)(38, \"button\", 20);\n    i0.ɵɵtext(39, \"Submit\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ProfileComponent_div_20_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Capital Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Small Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_9_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Special Character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, ProfileComponent_div_20_div_9_div_1_Template, 2, 0, \"div\", 27)(2, ProfileComponent_div_20_div_9_div_2_Template, 2, 0, \"div\", 27)(3, ProfileComponent_div_20_div_9_div_3_Template, 2, 0, \"div\", 27)(4, ProfileComponent_div_20_div_9_div_4_Template, 2, 0, \"div\", 27)(5, ProfileComponent_div_20_div_9_div_5_Template, 2, 0, \"div\", 27)(6, ProfileComponent_div_20_div_9_div_6_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"minlength\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasNumber\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasCapitalCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasSmallCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasSpecialCharacters\"]);\n  }\n}\nfunction ProfileComponent_div_20_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Passwords must match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, ProfileComponent_div_20_div_17_div_1_Template, 2, 0, \"div\", 27)(2, ProfileComponent_div_20_div_17_div_2_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"confirmPassword\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"confirmPassword\"].errors[\"confirmedValidator\"]);\n  }\n}\nfunction ProfileComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"form\", 21);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_20_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changePassword());\n    });\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"label\", 13)(4, \"span\", 8);\n    i0.ɵɵtext(5, \"key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵelement(8, \"input\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ProfileComponent_div_20_div_9_Template, 7, 6, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"label\", 13)(12, \"span\", 8);\n    i0.ɵɵtext(13, \"key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" Retype Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 14);\n    i0.ɵɵelement(16, \"input\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ProfileComponent_div_20_div_17_Template, 3, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 19)(19, \"button\", 25);\n    i0.ɵɵtext(20, \"Submit\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.passwordsForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"currentPassword\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"currentPassword\"].errors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"confirmPassword\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"confirmPassword\"].errors);\n  }\n}\nfunction ConfirmedValidator(controlName, matchingControlName) {\n  return formGroup => {\n    const control = formGroup.controls[controlName];\n    const matchingControl = formGroup.controls[matchingControlName];\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\n      return;\n    }\n    if (control.value !== matchingControl.value) {\n      matchingControl.setErrors({\n        confirmedValidator: true\n      });\n    } else {\n      matchingControl.setErrors(null);\n    }\n  };\n}\nfunction patternValidator(regex, error) {\n  return control => {\n    if (!control.value) {\n      // if control is empty return no error\n      return null;\n    }\n    // test the value of the control against the regexp supplied\n    const valid = regex.test(control.value);\n    // if true, return no error (no error), else return error passed in the second parameter\n    return valid ? null : error;\n  };\n}\nexport class ProfileComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.toggle = {\n      profile: true,\n      password: false\n    };\n    this.isFormSubmitted = false;\n    this.isPasswordFormSubmitted = false;\n    this.loadingUserDetails = false;\n    this.userDetails = {};\n    this.saving = false;\n    this.changingPassword = false;\n  }\n  ngOnInit() {\n    this.passwordsForm = this.fb.group({\n      currentPassword: ['', [Validators.required, Validators.minLength(8),\n      // check whether the entered password has a number\n      patternValidator(/\\d/, {\n        hasNumber: true\n      }),\n      // check whether the entered password has upper case letter\n      patternValidator(/[A-Z]/, {\n        hasCapitalCase: true\n      }),\n      // check whether the entered password has a lower case letter\n      patternValidator(/[a-z]/, {\n        hasSmallCase: true\n      }),\n      // check whether the entered password has a special character\n      patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\n        hasSpecialCharacters: true\n      })]],\n      confirmPassword: ['']\n    }, {\n      validators: ConfirmedValidator('currentPassword', 'confirmPassword')\n    });\n    this.profileForm = this.fb.group({\n      userName: ['', Validators.required],\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n    this.fetchUserDetails();\n  }\n  get f() {\n    return this.profileForm.controls;\n  }\n  get pf() {\n    return this.passwordsForm.controls;\n  }\n  saveChanges() {\n    this.isFormSubmitted = true;\n    if (!this.profileForm.valid) {\n      return;\n    }\n    const value = this.profileForm.value;\n    this.saving = true;\n    // this.manageUserService.updateUser(this.userDetails.id, {\n    //   firstname: value.firstName,\n    //   lastname: value.lastName,\n    //   address: value.address,\n    //   email: value.email,\n    //   username: value.userName,\n    // }).subscribe((user) => {\n    //   this.saving = false;\n    //   this.userDetails = user;\n    //   this.authService.checkAdminUser().subscribe();\n    //   this._snackBar.open('Data updated successfully!', {\n    //     type: 'Success',\n    //   });\n    // }, (err) => {\n    //   this.saving = false;\n    //   this._snackBar.open(err?.error?.message || 'Error while processing your request.', {\n    //     type: 'Error',\n    //   });\n    // });\n  }\n  fetchUserDetails() {\n    this.loadingUserDetails = true;\n    // const user = this.authService.userDetail;\n    // this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\n    //   if (user) {\n    //     this.userDetails = user;\n    //     this.profileForm.patchValue({\n    //       firstName: user.firstname,\n    //       lastName: user.lastname,\n    //       address: user.address,\n    //       email: user.email,\n    //       userName: user.username,\n    //     });\n    //   }\n    //   this.loadingUserDetails = false;\n    // });\n  }\n  changePassword() {\n    this.isPasswordFormSubmitted = true;\n    if (!this.passwordsForm.valid) {\n      return;\n    }\n    this.changingPassword = true;\n    // this.manageUserService.updateUser(this.userDetails.id, {\n    //   password: this.passwordsForm.value.currentPassword,\n    // }).subscribe((user) => {\n    //   this.changingPassword = false;\n    //   this.passwordsForm.reset();\n    //   this._snackBar.open('Password changed successfully!', {\n    //     type: 'Success',\n    //   });\n    // }, (err) => {\n    //   this.changingPassword = false;\n    //   this._snackBar.open(err?.error?.message || 'Error while processing your request.', {\n    //     type: 'Error',\n    //   });\n    // });\n  }\n  toggleState(key) {\n    this.toggle[key] = !this.toggle[key];\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 21,\n      vars: 6,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"m-0\"], [1, \"card-list\", \"flex\", \"flex-column\", \"gap-2\", \"mb-3\"], [1, \"card\", \"shadow-2\", \"p-4\", \"h-full\", \"flex\", \"flex-column\", \"border-left-5\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\", 3, \"click\"], [1, \"block\", \"font-bold\", \"text-xl\", \"m-0\", \"text-primary\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\"], [1, \"material-symbols-rounded\"], [\"class\", \"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\", 4, \"ngIf\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-15rem\", \"gap-1\"], [1, \"form-input\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"John\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"Smith\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"<EMAIL>\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [1, \"form-submit-sec\", \"mt-5\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-12rem\", \"h-3rem\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"ngSubmit\", \"formGroup\"], [\"type\", \"password\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"new-password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\", 3, \"ngClass\"], [\"class\", \"text-red-600 mt-2\", 4, \"ngIf\"], [\"type\", \"password\", \"formControlName\", \"confirmPassword\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\", 3, \"ngClass\"], [\"type\", \"submit\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-12rem\", \"h-3rem\"], [1, \"text-red-600\", \"mt-2\"], [4, \"ngIf\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"My Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_div_click_6_listener() {\n            return ctx.toggleState(\"profile\");\n          });\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"User Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11, \"keyboard_arrow_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, ProfileComponent_div_12_Template, 40, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_div_click_14_listener() {\n            return ctx.toggleState(\"password\");\n          });\n          i0.ɵɵelementStart(15, \"h3\", 6);\n          i0.ɵɵtext(16, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 7)(18, \"span\", 8);\n          i0.ɵɵtext(19, \"keyboard_arrow_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(20, ProfileComponent_div_20_Template, 21, 9, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"active\", ctx.toggle[\"profile\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.toggle[\"profile\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.toggle[\"password\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.toggle[\"password\"]);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgForm, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "ProfileComponent_div_20_div_9_div_1_Template", "ProfileComponent_div_20_div_9_div_2_Template", "ProfileComponent_div_20_div_9_div_3_Template", "ProfileComponent_div_20_div_9_div_4_Template", "ProfileComponent_div_20_div_9_div_5_Template", "ProfileComponent_div_20_div_9_div_6_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "pf", "errors", "ProfileComponent_div_20_div_17_div_1_Template", "ProfileComponent_div_20_div_17_div_2_Template", "ɵɵlistener", "ProfileComponent_div_20_Template_form_ngSubmit_1_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "changePassword", "ProfileComponent_div_20_div_9_Template", "ProfileComponent_div_20_div_17_Template", "passwordsForm", "ɵɵpureFunction1", "_c0", "isPasswordFormSubmitted", "ConfirmedValidator", "controlName", "matchingControlName", "formGroup", "control", "controls", "matchingControl", "value", "setErrors", "confirmedValidator", "patternValidator", "regex", "error", "valid", "test", "ProfileComponent", "constructor", "fb", "toggle", "profile", "password", "isFormSubmitted", "loadingUserDetails", "userDetails", "saving", "changingPassword", "ngOnInit", "group", "currentPassword", "required", "<PERSON><PERSON><PERSON><PERSON>", "hasNumber", "hasCapitalCase", "hasSmallCase", "hasSpecialCharacters", "confirmPassword", "validators", "profileForm", "userName", "firstName", "lastName", "email", "phone", "fetchUserDetails", "f", "saveChanges", "toggleState", "key", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_Template_div_click_6_listener", "ProfileComponent_div_12_Template", "ProfileComponent_Template_div_click_14_listener", "ProfileComponent_div_20_Template", "ɵɵclassProp"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\profile\\profile.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, ValidationErrors, ValidatorFn, AbstractControl, FormBuilder, Validators } from '@angular/forms';\r\n\r\n\r\n\r\nfunction ConfirmedValidator(controlName: string, matchingControlName: string) {\r\n  return (formGroup: FormGroup) => {\r\n    const control = formGroup.controls[controlName];\r\n    const matchingControl = formGroup.controls[matchingControlName];\r\n    if (\r\n      matchingControl.errors &&\r\n      !matchingControl.errors['confirmedValidator']\r\n    ) {\r\n      return;\r\n    }\r\n    if (control.value !== matchingControl.value) {\r\n      matchingControl.setErrors({ confirmedValidator: true });\r\n    } else {\r\n      matchingControl.setErrors(null);\r\n    }\r\n  };\r\n}\r\n\r\n\r\nfunction patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {\r\n  return (control: AbstractControl) => {\r\n    if (!control.value) {\r\n      // if control is empty return no error\r\n      return null;\r\n    }\r\n\r\n    // test the value of the control against the regexp supplied\r\n    const valid = regex.test(control.value);\r\n\r\n    // if true, return no error (no error), else return error passed in the second parameter\r\n    return valid ? null : error;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrl: './profile.component.scss'\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  toggle: { [key: string]: boolean } = {\r\n    profile: true,\r\n    password: false\r\n  };\r\n  profileForm!: FormGroup;\r\n  passwordsForm!: FormGroup;\r\n  isFormSubmitted = false;\r\n  isPasswordFormSubmitted = false;\r\n  loadingUserDetails = false;\r\n  userDetails: any = {};\r\n  saving = false;\r\n  changingPassword = false;\r\n\r\n  constructor(private fb: FormBuilder) { }\r\n\r\n  ngOnInit(): void {\r\n    this.passwordsForm = this.fb.group({\r\n      currentPassword: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(8),\r\n          // check whether the entered password has a number\r\n          patternValidator(/\\d/, {\r\n            hasNumber: true,\r\n          }),\r\n          // check whether the entered password has upper case letter\r\n          patternValidator(/[A-Z]/, {\r\n            hasCapitalCase: true,\r\n          }),\r\n          // check whether the entered password has a lower case letter\r\n          patternValidator(/[a-z]/, {\r\n            hasSmallCase: true,\r\n          }),\r\n          // check whether the entered password has a special character\r\n          patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\r\n            hasSpecialCharacters: true,\r\n          }),\r\n        ],\r\n      ],\r\n      confirmPassword: [''],\r\n    }, {\r\n      validators: ConfirmedValidator('currentPassword', 'confirmPassword'),\r\n    });\r\n    this.profileForm = this.fb.group({\r\n      userName: ['', Validators.required],\r\n      firstName: ['', Validators.required],\r\n      lastName: ['', Validators.required],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phone: [''],\r\n    });\r\n    this.fetchUserDetails();\r\n  }\r\n\r\n  get f(): { [key: string]: AbstractControl } {\r\n    return this.profileForm.controls;\r\n  }\r\n\r\n  get pf(): { [key: string]: AbstractControl } {\r\n    return this.passwordsForm.controls;\r\n  }\r\n\r\n  saveChanges(): void {\r\n    this.isFormSubmitted = true;\r\n    if (!this.profileForm.valid) {\r\n      return;\r\n    }\r\n    const value = this.profileForm.value;\r\n    this.saving = true;\r\n\r\n    // this.manageUserService.updateUser(this.userDetails.id, {\r\n    //   firstname: value.firstName,\r\n    //   lastname: value.lastName,\r\n    //   address: value.address,\r\n    //   email: value.email,\r\n    //   username: value.userName,\r\n    // }).subscribe((user) => {\r\n    //   this.saving = false;\r\n    //   this.userDetails = user;\r\n    //   this.authService.checkAdminUser().subscribe();\r\n    //   this._snackBar.open('Data updated successfully!', {\r\n    //     type: 'Success',\r\n    //   });\r\n    // }, (err) => {\r\n    //   this.saving = false;\r\n    //   this._snackBar.open(err?.error?.message || 'Error while processing your request.', {\r\n    //     type: 'Error',\r\n    //   });\r\n    // });\r\n  }\r\n\r\n  fetchUserDetails() {\r\n    this.loadingUserDetails = true;\r\n    // const user = this.authService.userDetail;\r\n    // this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\r\n    //   if (user) {\r\n    //     this.userDetails = user;\r\n    //     this.profileForm.patchValue({\r\n    //       firstName: user.firstname,\r\n    //       lastName: user.lastname,\r\n    //       address: user.address,\r\n    //       email: user.email,\r\n    //       userName: user.username,\r\n    //     });\r\n    //   }\r\n    //   this.loadingUserDetails = false;\r\n    // });\r\n  }\r\n\r\n  changePassword() {\r\n    this.isPasswordFormSubmitted = true;\r\n    if (!this.passwordsForm.valid) {\r\n      return\r\n    }\r\n    this.changingPassword = true;\r\n    // this.manageUserService.updateUser(this.userDetails.id, {\r\n    //   password: this.passwordsForm.value.currentPassword,\r\n    // }).subscribe((user) => {\r\n    //   this.changingPassword = false;\r\n    //   this.passwordsForm.reset();\r\n    //   this._snackBar.open('Password changed successfully!', {\r\n    //     type: 'Success',\r\n    //   });\r\n    // }, (err) => {\r\n    //   this.changingPassword = false;\r\n    //   this._snackBar.open(err?.error?.message || 'Error while processing your request.', {\r\n    //     type: 'Error',\r\n    //   });\r\n    // });\r\n  }\r\n\r\n  toggleState(key: string) {\r\n    this.toggle[key] = !this.toggle[key];\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-4 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n        <h3 class=\"m-0\">My Account</h3>\r\n    </div>\r\n\r\n    <div class=\"card-list flex flex-column gap-2 mb-3\">\r\n        <div class=\"card shadow-2 p-4 h-full flex flex-column border-left-5 gap-2\">\r\n            <div class=\"flex align-items-center justify-content-between cursor-pointer\"\r\n                (click)=\"toggleState('profile')\">\r\n                <h3 class=\"block font-bold text-xl m-0 text-primary\">User Details</h3>\r\n                <button\r\n                    class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\"\r\n                    [class.active]=\"toggle['profile']\">\r\n                    <span class='material-symbols-rounded'>keyboard_arrow_down</span>\r\n                </button>\r\n            </div>\r\n            <div *ngIf=\"toggle['profile']\"\r\n                class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <form class=\"relative flex flex-column gap-1\">\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> User Name</label>\r\n                        <div class=\"form-input\">: John Smith </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> First Name</label>\r\n                        <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                placeholder=\"\" value=\"John\"> </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> Last Name</label>\r\n                        <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                placeholder=\"\" value=\"Smith\"> </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">phone_in_talk</span> Phone Number</label>\r\n                        <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                placeholder=\"\" value=\"\"> </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">mail</span> E-mail</label>\r\n                        <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                placeholder=\"\" value=\"<EMAIL>\"> </div>\r\n                    </div>\r\n                    <div class=\"form-submit-sec mt-5\">\r\n                        <button type=\"button\"\r\n                            class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-12rem h-3rem\">Submit</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"card shadow-2 p-4 h-full flex flex-column border-left-5 gap-2\">\r\n            <div class=\"flex align-items-center justify-content-between cursor-pointer\"\r\n                (click)=\"toggleState('password')\">\r\n                <h3 class=\"block font-bold text-xl m-0 text-primary\">Password</h3>\r\n                <button\r\n                    class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\"\r\n                    [class.active]=\"toggle['password']\">\r\n                    <span class='material-symbols-rounded'>keyboard_arrow_down</span>\r\n                </button>\r\n            </div>\r\n            <div *ngIf=\"toggle['password']\"\r\n                class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <form [formGroup]=\"passwordsForm\" (ngSubmit)=\"changePassword()\" class=\"relative flex flex-column gap-1\">\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">key</span> Password</label>\r\n                        <div class=\"form-input\">\r\n                            <input type=\"password\" formControlName=\"currentPassword\"\r\n                                class=\"p-inputtext p-component p-element w-30rem\" autocomplete=\"new-password\"\r\n                                [ngClass]=\"{ 'is-invalid': isPasswordFormSubmitted && pf['currentPassword'].errors }\" />\r\n                        </div>\r\n                        <div *ngIf=\"isPasswordFormSubmitted && pf['currentPassword'].errors\" class=\"text-red-600 mt-2\">\r\n                            <div *ngIf=\"pf['currentPassword'].errors['required']\">\r\n                                This field is required</div>\r\n                            <div *ngIf=\"pf['currentPassword'].errors['minlength']\">\r\n                                Must be at least 8 characters</div>\r\n                            <div *ngIf=\"pf['currentPassword'].errors['hasNumber']\">\r\n                                Must contain at least one number</div>\r\n                            <div *ngIf=\"pf['currentPassword'].errors['hasCapitalCase']\">\r\n                                Must contain at least one Letter in Capital Case</div>\r\n                            <div *ngIf=\"pf['currentPassword'].errors['hasSmallCase']\">\r\n                                Must contain at least one Letter in Small Case</div>\r\n                            <div *ngIf=\"pf['currentPassword'].errors['hasSpecialCharacters']\">\r\n                                Must contain at least one Special Character</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">key</span> Retype Password</label>\r\n                        <div class=\"form-input\">\r\n                            <input type=\"password\" formControlName=\"confirmPassword\"\r\n                                class=\"p-inputtext p-component p-element w-30rem\"\r\n                                [ngClass]=\"{ 'is-invalid': isPasswordFormSubmitted && pf['confirmPassword'].errors }\" />\r\n                        </div>\r\n                        <div *ngIf=\"isPasswordFormSubmitted && pf['confirmPassword'].errors\" class=\"text-red-600 mt-2\">\r\n                            <div *ngIf=\"pf['confirmPassword'].errors['required']\">\r\n                                This field is required</div>\r\n                            <div *ngIf=\"pf['confirmPassword'].errors['confirmedValidator']\">\r\n                                Passwords must match\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"form-submit-sec mt-5\">\r\n                        <button type=\"submit\"\r\n                            class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-12rem h-3rem\">Submit</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n</div>"], "mappings": "AACA,SAAiFA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICmBVC,EAJ7F,CAAAC,cAAA,cACgG,eAC9C,cACW,gBACgC,cACxC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IACzCF,EADyC,CAAAG,YAAA,EAAM,EACzC;IAE+EH,EADrF,CAAAC,cAAA,cAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAI,SAAA,iBACY;IACxCJ,EADyC,CAAAG,YAAA,EAAM,EACzC;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAI,SAAA,iBACa;IACzCJ,EAD0C,CAAAG,YAAA,EAAM,EAC1C;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClFH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAI,SAAA,iBACQ;IACpCJ,EADqC,CAAAG,YAAA,EAAM,EACrC;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAI,SAAA,iBACiC;IAC7DJ,EAD8D,CAAAG,YAAA,EAAM,EAC9D;IAEFH,EADJ,CAAAC,cAAA,eAAkC,kBAE6E;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAG7HF,EAH6H,CAAAG,YAAA,EAAS,EACxH,EACH,EACL;;;;;IAyBUH,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1CH,EAAA,CAAAC,cAAA,UAA4D;IACxDD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1DH,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,sDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxDH,EAAA,CAAAC,cAAA,UAAkE;IAC9DD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAZzDH,EAAA,CAAAC,cAAA,cAA+F;IAW3FD,EAVA,CAAAK,UAAA,IAAAC,4CAAA,kBAAsD,IAAAC,4CAAA,kBAEC,IAAAC,4CAAA,kBAEA,IAAAC,4CAAA,kBAEK,IAAAC,4CAAA,kBAEF,IAAAC,4CAAA,kBAEQ;IAEtEX,EAAA,CAAAG,YAAA,EAAM;;;;IAZIH,EAAA,CAAAY,SAAA,EAA8C;IAA9CZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,aAA8C;IAE9ChB,EAAA,CAAAY,SAAA,EAA+C;IAA/CZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,cAA+C;IAE/ChB,EAAA,CAAAY,SAAA,EAA+C;IAA/CZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,cAA+C;IAE/ChB,EAAA,CAAAY,SAAA,EAAoD;IAApDZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,mBAAoD;IAEpDhB,EAAA,CAAAY,SAAA,EAAkD;IAAlDZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,iBAAkD;IAElDhB,EAAA,CAAAY,SAAA,EAA0D;IAA1DZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,yBAA0D;;;;;IAahEhB,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAAgE;IAC5DD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALVH,EAAA,CAAAC,cAAA,cAA+F;IAG3FD,EAFA,CAAAK,UAAA,IAAAY,6CAAA,kBAAsD,IAAAC,6CAAA,kBAEU;IAGpElB,EAAA,CAAAG,YAAA,EAAM;;;;IALIH,EAAA,CAAAY,SAAA,EAA8C;IAA9CZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,aAA8C;IAE9ChB,EAAA,CAAAY,SAAA,EAAwD;IAAxDZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAC,EAAA,oBAAAC,MAAA,uBAAwD;;;;;;IAnC1EhB,EAFJ,CAAAC,cAAA,cACgG,eACY;IAAtED,EAAA,CAAAmB,UAAA,sBAAAC,0DAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAd,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAYV,MAAA,CAAAW,cAAA,EAAgB;IAAA,EAAC;IAE0BzB,EADrF,CAAAC,cAAA,cAAqD,gBACgC,cACxC;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAI,SAAA,gBAE4F;IAChGJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,UAAA,IAAAqB,sCAAA,kBAA+F;IAcnG1B,EAAA,CAAAG,YAAA,EAAM;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3EH,EAAA,CAAAC,cAAA,eAAwB;IACpBD,EAAA,CAAAI,SAAA,iBAE4F;IAChGJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,UAAA,KAAAsB,uCAAA,kBAA+F;IAOnG3B,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAkC,kBAE6E;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAG7HF,EAH6H,CAAAG,YAAA,EAAS,EACxH,EACH,EACL;;;;IA7CIH,EAAA,CAAAY,SAAA,EAA2B;IAA3BZ,EAAA,CAAAa,UAAA,cAAAC,MAAA,CAAAc,aAAA,CAA2B;IAOjB5B,EAAA,CAAAY,SAAA,GAAqF;IAArFZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA6B,eAAA,IAAAC,GAAA,EAAAhB,MAAA,CAAAiB,uBAAA,IAAAjB,MAAA,CAAAC,EAAA,oBAAAC,MAAA,EAAqF;IAEvFhB,EAAA,CAAAY,SAAA,EAA6D;IAA7DZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAiB,uBAAA,IAAAjB,MAAA,CAAAC,EAAA,oBAAAC,MAAA,CAA6D;IAqB3DhB,EAAA,CAAAY,SAAA,GAAqF;IAArFZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAA6B,eAAA,IAAAC,GAAA,EAAAhB,MAAA,CAAAiB,uBAAA,IAAAjB,MAAA,CAAAC,EAAA,oBAAAC,MAAA,EAAqF;IAEvFhB,EAAA,CAAAY,SAAA,EAA6D;IAA7DZ,EAAA,CAAAa,UAAA,SAAAC,MAAA,CAAAiB,uBAAA,IAAAjB,MAAA,CAAAC,EAAA,oBAAAC,MAAA,CAA6D;;;AD/F3F,SAASgB,kBAAkBA,CAACC,WAAmB,EAAEC,mBAA2B;EAC1E,OAAQC,SAAoB,IAAI;IAC9B,MAAMC,OAAO,GAAGD,SAAS,CAACE,QAAQ,CAACJ,WAAW,CAAC;IAC/C,MAAMK,eAAe,GAAGH,SAAS,CAACE,QAAQ,CAACH,mBAAmB,CAAC;IAC/D,IACEI,eAAe,CAACtB,MAAM,IACtB,CAACsB,eAAe,CAACtB,MAAM,CAAC,oBAAoB,CAAC,EAC7C;MACA;IACF;IACA,IAAIoB,OAAO,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;MAC3CD,eAAe,CAACE,SAAS,CAAC;QAAEC,kBAAkB,EAAE;MAAI,CAAE,CAAC;IACzD,CAAC,MAAM;MACLH,eAAe,CAACE,SAAS,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;AACH;AAGA,SAASE,gBAAgBA,CAACC,KAAa,EAAEC,KAAuB;EAC9D,OAAQR,OAAwB,IAAI;IAClC,IAAI,CAACA,OAAO,CAACG,KAAK,EAAE;MAClB;MACA,OAAO,IAAI;IACb;IAEA;IACA,MAAMM,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACV,OAAO,CAACG,KAAK,CAAC;IAEvC;IACA,OAAOM,KAAK,GAAG,IAAI,GAAGD,KAAK;EAC7B,CAAC;AACH;AAOA,OAAM,MAAOG,gBAAgB;EAc3BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAbtB,KAAAC,MAAM,GAA+B;MACnCC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX;IAGD,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAtB,uBAAuB,GAAG,KAAK;IAC/B,KAAAuB,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,KAAK;EAEe;EAEvCC,QAAQA,CAAA;IACN,IAAI,CAAC9B,aAAa,GAAG,IAAI,CAACqB,EAAE,CAACU,KAAK,CAAC;MACjCC,eAAe,EAAE,CACf,EAAE,EACF,CACE7D,UAAU,CAAC8D,QAAQ,EACnB9D,UAAU,CAAC+D,SAAS,CAAC,CAAC,CAAC;MACvB;MACApB,gBAAgB,CAAC,IAAI,EAAE;QACrBqB,SAAS,EAAE;OACZ,CAAC;MACF;MACArB,gBAAgB,CAAC,OAAO,EAAE;QACxBsB,cAAc,EAAE;OACjB,CAAC;MACF;MACAtB,gBAAgB,CAAC,OAAO,EAAE;QACxBuB,YAAY,EAAE;OACf,CAAC;MACF;MACAvB,gBAAgB,CAAC,wCAAwC,EAAE;QACzDwB,oBAAoB,EAAE;OACvB,CAAC,CACH,CACF;MACDC,eAAe,EAAE,CAAC,EAAE;KACrB,EAAE;MACDC,UAAU,EAAEpC,kBAAkB,CAAC,iBAAiB,EAAE,iBAAiB;KACpE,CAAC;IACF,IAAI,CAACqC,WAAW,GAAG,IAAI,CAACpB,EAAE,CAACU,KAAK,CAAC;MAC/BW,QAAQ,EAAE,CAAC,EAAE,EAAEvE,UAAU,CAAC8D,QAAQ,CAAC;MACnCU,SAAS,EAAE,CAAC,EAAE,EAAExE,UAAU,CAAC8D,QAAQ,CAAC;MACpCW,QAAQ,EAAE,CAAC,EAAE,EAAEzE,UAAU,CAAC8D,QAAQ,CAAC;MACnCY,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1E,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAAC0E,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;IACF,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA,IAAIC,CAACA,CAAA;IACH,OAAO,IAAI,CAACP,WAAW,CAAChC,QAAQ;EAClC;EAEA,IAAItB,EAAEA,CAAA;IACJ,OAAO,IAAI,CAACa,aAAa,CAACS,QAAQ;EACpC;EAEAwC,WAAWA,CAAA;IACT,IAAI,CAACxB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC,IAAI,CAACgB,WAAW,CAACxB,KAAK,EAAE;MAC3B;IACF;IACA,MAAMN,KAAK,GAAG,IAAI,CAAC8B,WAAW,CAAC9B,KAAK;IACpC,IAAI,CAACiB,MAAM,GAAG,IAAI;IAElB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAmB,gBAAgBA,CAAA;IACd,IAAI,CAACrB,kBAAkB,GAAG,IAAI;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA7B,cAAcA,CAAA;IACZ,IAAI,CAACM,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAC,IAAI,CAACH,aAAa,CAACiB,KAAK,EAAE;MAC7B;IACF;IACA,IAAI,CAACY,gBAAgB,GAAG,IAAI;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAqB,WAAWA,CAACC,GAAW;IACrB,IAAI,CAAC7B,MAAM,CAAC6B,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC7B,MAAM,CAAC6B,GAAG,CAAC;EACtC;;;uBAtIWhC,gBAAgB,EAAA/C,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBnC,gBAAgB;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1CrBzF,EAFR,CAAAC,cAAA,aAA2E,aACc,YACjE;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;UAIEH,EAFR,CAAAC,cAAA,aAAmD,aAC4B,aAElC;UAAjCD,EAAA,CAAAmB,UAAA,mBAAAwE,+CAAA;YAAA,OAASD,GAAA,CAAAZ,WAAA,CAAY,SAAS,CAAC;UAAA,EAAC;UAChC9E,EAAA,CAAAC,cAAA,YAAqD;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIlEH,EAHJ,CAAAC,cAAA,gBAEuC,eACI;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAElEF,EAFkE,CAAAG,YAAA,EAAO,EAC5D,EACP;UACNH,EAAA,CAAAK,UAAA,KAAAuF,gCAAA,kBACgG;UAqCpG5F,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,cAA2E,cAEjC;UAAlCD,EAAA,CAAAmB,UAAA,mBAAA0E,gDAAA;YAAA,OAASH,GAAA,CAAAZ,WAAA,CAAY,UAAU,CAAC;UAAA,EAAC;UACjC9E,EAAA,CAAAC,cAAA,aAAqD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI9DH,EAHJ,CAAAC,cAAA,iBAEwC,eACG;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAElEF,EAFkE,CAAAG,YAAA,EAAO,EAC5D,EACP;UACNH,EAAA,CAAAK,UAAA,KAAAyF,gCAAA,kBACgG;UAkD5G9F,EAHQ,CAAAG,YAAA,EAAM,EACJ,EAEJ;;;UAzGcH,EAAA,CAAAY,SAAA,GAAkC;UAAlCZ,EAAA,CAAA+F,WAAA,WAAAL,GAAA,CAAAxC,MAAA,YAAkC;UAIpClD,EAAA,CAAAY,SAAA,GAAuB;UAAvBZ,EAAA,CAAAa,UAAA,SAAA6E,GAAA,CAAAxC,MAAA,YAAuB;UA8CrBlD,EAAA,CAAAY,SAAA,GAAmC;UAAnCZ,EAAA,CAAA+F,WAAA,WAAAL,GAAA,CAAAxC,MAAA,aAAmC;UAIrClD,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,SAAA6E,GAAA,CAAAxC,MAAA,aAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}