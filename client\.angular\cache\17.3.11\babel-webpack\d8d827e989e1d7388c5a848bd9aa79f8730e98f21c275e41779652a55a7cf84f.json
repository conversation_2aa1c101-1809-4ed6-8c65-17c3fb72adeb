{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BackofficeComponent } from './backoffice.component';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BackofficeComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      data: {\n        breadcrumb: 'Dashboard'\n      },\n      loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule)\n    }, {\n      path: 'product',\n      data: {\n        breadcrumb: 'Product'\n      },\n      loadChildren: () => import('./product/product.module').then(m => m.ProductModule)\n    }, {\n      path: 'customer',\n      data: {\n        breadcrumb: 'Customer'\n      },\n      loadChildren: () => import('./customer/customer.module').then(m => m.CustomerModule)\n    }, {\n      path: 'contacts',\n      data: {\n        breadcrumb: 'Contacts'\n      },\n      loadChildren: () => import('./contacts/contact.module').then(m => m.ContactModule)\n    }, {\n      path: 'configuration',\n      data: {\n        breadcrumb: 'Configuration'\n      },\n      loadChildren: () => import('./configuration/configuration.module').then(m => m.ConfigurationModule)\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class BackofficeRoutingModule {\n  static {\n    this.ɵfac = function BackofficeRoutingModule_Factory(t) {\n      return new (t || BackofficeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BackofficeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BackofficeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "BackofficeComponent", "AppLayoutComponent", "routes", "path", "component", "children", "data", "breadcrumb", "loadChildren", "then", "m", "DashboardModule", "ProductModule", "CustomerModule", "ContactModule", "ConfigurationModule", "redirectTo", "BackofficeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\backoffice-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { BackofficeComponent } from './backoffice.component';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: BackofficeComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            data: { breadcrumb: 'Dashboard' },\r\n            loadChildren: () =>\r\n              import('./dashboard/dashboard.module').then(\r\n                (m) => m.DashboardModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'product',\r\n            data: { breadcrumb: 'Product' },\r\n            loadChildren: () =>\r\n              import('./product/product.module').then(\r\n                (m) => m.ProductModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'customer',\r\n            data: { breadcrumb: 'Customer' },\r\n            loadChildren: () =>\r\n              import('./customer/customer.module').then(\r\n                (m) => m.CustomerModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'contacts',\r\n            data: { breadcrumb: 'Contacts' },\r\n            loadChildren: () =>\r\n              import('./contacts/contact.module').then(\r\n                (m) => m.ContactModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'configuration',\r\n            data: { breadcrumb: 'Configuration' },\r\n            loadChildren: () =>\r\n              import('./configuration/configuration.module').then(\r\n                (m) => m.ConfigurationModule\r\n              ),\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class BackofficeRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,kBAAkB,QAAQ,+BAA+B;;;AAElE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,mBAAmB;EAC9BK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEH,kBAAkB;IAC7BI,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CACxCC,CAAC,IAAKA,CAAC,CAACC,eAAe;KAE7B,EACD;MACER,IAAI,EAAE,SAAS;MACfG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAC/BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CACpCC,CAAC,IAAKA,CAAC,CAACE,aAAa;KAE3B,EACD;MACET,IAAI,EAAE,UAAU;MAChBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CACtCC,CAAC,IAAKA,CAAC,CAACG,cAAc;KAE5B,EACD;MACEV,IAAI,EAAE,UAAU;MAChBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,2BAA2B,CAAC,CAACC,IAAI,CACrCC,CAAC,IAAKA,CAAC,CAACI,aAAa;KAE3B,EACD;MACEX,IAAI,EAAE,eAAe;MACrBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAe,CAAE;MACrCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACK,mBAAmB;KAEjC;GAEJ,EACD;IAAEZ,IAAI,EAAE,IAAI;IAAEa,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBlB,YAAY,CAACmB,QAAQ,CAAChB,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXkB,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAArB,YAAA;IAAAsB,OAAA,GAFxBtB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}