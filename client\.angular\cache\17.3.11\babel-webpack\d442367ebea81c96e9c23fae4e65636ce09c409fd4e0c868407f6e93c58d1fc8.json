{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"./layout/service/app.layout.service\";\nimport * as i3 from \"@angular/router\";\nexport class StoreComponent {\n  constructor(primengConfig, layoutService) {\n    this.primengConfig = primengConfig;\n    this.layoutService = layoutService;\n  }\n  ngOnInit() {\n    this.primengConfig.ripple = true; //enables core ripple functionality\n    //optional configuration with the default configuration\n    const config = {\n      ripple: false,\n      //toggles ripple on and off\n      menuMode: 'static',\n      //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\n      colorScheme: 'light',\n      //color scheme of the template, valid values are \"light\" and \"dark\"\n      theme: 'snjya',\n      //default component theme for PrimeNG\n      scale: 14 //size of the body font size to scale the whole application\n    };\n    this.layoutService.config.set(config);\n  }\n  static {\n    this.ɵfac = function StoreComponent_Factory(t) {\n      return new (t || StoreComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i2.LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoreComponent,\n      selectors: [[\"app-store\"]],\n      decls: 1,\n      vars: 0,\n      template: function StoreComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      styles: [\".all-overview-body {\\n  min-height: calc(100vh - 90px);\\n}\\n  .all-overview-body .card-list .v-details-list .v-details-box .text {\\n  min-width: 120px;\\n}\\n  .p-inputtext {\\n  height: 3.3rem;\\n  appearance: auto !important;\\n}\\n  .border-left-5 {\\n  border-left: 5px solid var(--orange-200);\\n}\\n  .p-calendar {\\n  display: flex;\\n}\\n  .p-calendar .p-button-icon-only {\\n  width: 3rem;\\n}\\n  .max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .text-shadow-l-blue {\\n  text-shadow: 0 2px 6px rgba(0, 63, 147, 0.8);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvc3RvcmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDSSw4QkFBQTtBQUFSO0FBS29CO0VBQ0ksZ0JBQUE7QUFIeEI7QUFVSTtFQUNJLGNBQUE7RUFDQSwyQkFBQTtBQVJSO0FBV0k7RUFDSSx3Q0FBQTtBQVRSO0FBWUk7RUFDSSxhQUFBO0FBVlI7QUFZUTtFQUNJLFdBQUE7QUFWWjtBQWNJO0VBQ0ksaUJBQUE7QUFaUjtBQWVJO0VBQ0ksNENBQUE7QUFiUiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCB7XHJcbiAgICAuYWxsLW92ZXJ2aWV3LWJvZHkge1xyXG4gICAgICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA5MHB4KTtcclxuXHJcbiAgICAgICAgLmNhcmQtbGlzdCB7XHJcbiAgICAgICAgICAgIC52LWRldGFpbHMtbGlzdCB7XHJcbiAgICAgICAgICAgICAgICAudi1kZXRhaWxzLWJveCB7XHJcbiAgICAgICAgICAgICAgICAgICAgLnRleHQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW4td2lkdGg6IDEyMHB4O1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAucC1pbnB1dHRleHQge1xyXG4gICAgICAgIGhlaWdodDogMy4zcmVtO1xyXG4gICAgICAgIGFwcGVhcmFuY2U6IGF1dG8gIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAuYm9yZGVyLWxlZnQtNSB7XHJcbiAgICAgICAgYm9yZGVyLWxlZnQ6IDVweCBzb2xpZCB2YXIoLS1vcmFuZ2UtMjAwKTtcclxuICAgIH1cclxuXHJcbiAgICAucC1jYWxlbmRhciB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuXHJcbiAgICAgICAgLnAtYnV0dG9uLWljb24tb25seSB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAzcmVtO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAubWF4LXctMTIwMCB7XHJcbiAgICAgICAgbWF4LXdpZHRoOiAxMjAwcHg7XHJcbiAgICB9XHJcblxyXG4gICAgLnRleHQtc2hhZG93LWwtYmx1ZSB7XHJcbiAgICAgICAgdGV4dC1zaGFkb3c6IDAgMnB4IDZweCByZ2JhKDAsIDYzLCAxNDcsIDAuOCk7XHJcbiAgICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["StoreComponent", "constructor", "primengConfig", "layoutService", "ngOnInit", "ripple", "config", "menuMode", "colorScheme", "theme", "scale", "set", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "i2", "LayoutService", "selectors", "decls", "vars", "template", "StoreComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\store.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\store.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { AppConfig, LayoutService } from './layout/service/app.layout.service';\r\n\r\n@Component({\r\n  selector: 'app-store',\r\n  templateUrl: './store.component.html',\r\n  styleUrl: './store.component.scss',\r\n})\r\nexport class StoreComponent {\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private layoutService: LayoutService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n    //optional configuration with the default configuration\r\n    const config: AppConfig = {\r\n      ripple: false, //toggles ripple on and off\r\n      menuMode: 'static', //layout mode of the menu, valid values are \"static\", \"overlay\", \"slim\", \"horizontal\", \"drawer\" and \"reveal\"\r\n      colorScheme: 'light', //color scheme of the template, valid values are \"light\" and \"dark\"\r\n      theme: 'snjya', //default component theme for PrimeNG\r\n      scale: 14, //size of the body font size to scale the whole application\r\n    };\r\n    this.layoutService.config.set(config);\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>"], "mappings": ";;;;AASA,OAAM,MAAOA,cAAc;EACzBC,YACUC,aAA4B,EAC5BC,aAA4B;IAD5B,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;EACpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACF,aAAa,CAACG,MAAM,GAAG,IAAI,CAAC,CAAC;IAClC;IACA,MAAMC,MAAM,GAAc;MACxBD,MAAM,EAAE,KAAK;MAAE;MACfE,QAAQ,EAAE,QAAQ;MAAE;MACpBC,WAAW,EAAE,OAAO;MAAE;MACtBC,KAAK,EAAE,OAAO;MAAE;MAChBC,KAAK,EAAE,EAAE,CAAE;KACZ;IACD,IAAI,CAACP,aAAa,CAACG,MAAM,CAACK,GAAG,CAACL,MAAM,CAAC;EACvC;;;uBAjBWN,cAAc,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAdjB,cAAc;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BX,EAAA,CAAAa,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}