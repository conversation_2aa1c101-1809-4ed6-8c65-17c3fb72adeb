{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class VendorContactService {\n  constructor(http) {\n    this.http = http;\n    this.userSubject = new BehaviorSubject(null);\n    this.user = this.userSubject.asObservable();\n  }\n  getUsers(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][email][$containsi]', searchTerm).set('filters[$or][1][username][$containsi]', searchTerm).set('filters[$or][2][firstname][$containsi]', searchTerm).set('filters[$or][3][lastname][$containsi]', searchTerm).set('filters[$or][4][blocked][$containsi]', searchTerm);\n    }\n    params = params.set('filters[role][id][$eq]', 3);\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list?populate=*`, {\n      params\n    });\n  }\n  getUserByID(id) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate[customers]=*&populate[vendor]=*`);\n  }\n  getCustomers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  updateUser(userId, updatedData) {\n    return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n  }\n  static {\n    this.ɵfac = function VendorContactService_Factory(t) {\n      return new (t || VendorContactService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VendorContactService,\n      factory: VendorContactService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "VendorContactService", "constructor", "http", "userSubject", "user", "asObservable", "getUsers", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "USER_DETAILS", "getUserByID", "id", "getCustomers", "data", "appendAll", "CUSTOMERS", "updateUser", "userId", "updatedData", "put", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\vendor-contact\\vendor-contact.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, map } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class VendorContactService {\r\n  public userSubject = new BehaviorSubject<any>(null);\r\n  public user = this.userSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getUsers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][email][$containsi]', searchTerm)\r\n        .set('filters[$or][1][username][$containsi]', searchTerm)\r\n        .set('filters[$or][2][firstname][$containsi]', searchTerm)\r\n        .set('filters[$or][3][lastname][$containsi]', searchTerm)\r\n        .set('filters[$or][4][blocked][$containsi]', searchTerm);\r\n    }\r\n    params = params.set('filters[role][id][$eq]', 3);\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/list?populate=*`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserByID(id: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}?populate[customers]=*&populate[vendor]=*`\r\n    );\r\n  }\r\n\r\n  getCustomers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  updateUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}`,\r\n      updatedData\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAyB,MAAM;AACvD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,WAAW,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC5C,KAAAM,IAAI,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;EAEN;EAEvCC,QAAQA,CACNC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC1BgB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC,CACxDE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC,CACzDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC,CACxDE,GAAG,CAAC,sCAAsC,EAAEF,UAAU,CAAC;IAC5D;IACAC,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAAC;IAChD,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAClB,GAAGlB,gBAAgB,CAACmB,YAAY,kBAAkB,EAClD;MACEN;KACD,CACF;EACH;EAEAO,WAAWA,CAACC,EAAO;IACjB,OAAO,IAAI,CAAClB,IAAI,CAACe,GAAG,CAClB,GAAGlB,gBAAgB,CAACmB,YAAY,IAAIE,EAAE,2CAA2C,CAClF;EACH;EAEAC,YAAYA,CAACC,IAAS;IACpB,MAAMV,MAAM,GAAG,IAAIf,UAAU,EAAE,CAAC0B,SAAS,CAAC;MAAE,GAAGD;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACpB,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACyB,SAAS,EAAE,EAAE;MAC3DZ;KACD,CAAC;EACJ;EAEAa,UAAUA,CAACC,MAAc,EAAEC,WAAgB;IACzC,OAAO,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAClB,GAAG7B,gBAAgB,CAACmB,YAAY,IAAIQ,MAAM,EAAE,EAC5CC,WAAW,CACZ;EACH;;;uBAvDW3B,oBAAoB,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBhC,oBAAoB;MAAAiC,OAAA,EAApBjC,oBAAoB,CAAAkC,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}