{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../product.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/editor\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/dropdown\";\nconst _c0 = () => ({\n  height: \"200px\"\n});\nfunction GeneralComponent_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"span\", 17);\n    i0.ɵɵtext(3, \"Product Storage\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 2)(5, \"span\", 18);\n    i0.ɵɵtext(6, \"Storage Conditions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 2)(10, \"span\", 18);\n    i0.ɵɵtext(11, \"Temperature Condition Ind\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 18);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 2)(15, \"span\", 18);\n    i0.ɵɵtext(16, \"Hazardous Material Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 18);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 2)(20, \"span\", 18);\n    i0.ɵɵtext(21, \"Nmbr Of Gr or Gi Slips To Print Qty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 18);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 2)(25, \"span\", 18);\n    i0.ɵɵtext(26, \"Label Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 18);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 2)(30, \"span\", 18);\n    i0.ɵɵtext(31, \"Label Form\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 18);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 2)(35, \"span\", 18);\n    i0.ɵɵtext(36, \"Min Remaining Shelf Life\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 18);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.storage_conditions) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.temperature_condition_ind) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.hazardous_material_number) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.nmbr_of_gr_or_gi_slips_to_print_qty) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.label_type) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.label_form) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.storage == null ? null : ctx_r0.products.storage.min_remaining_shelf_life) || \"-\");\n  }\n}\nfunction GeneralComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"span\", 17);\n    i0.ɵɵtext(3, \"Product Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 2)(5, \"span\", 18);\n    i0.ɵɵtext(6, \"Sales Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 2)(10, \"span\", 18);\n    i0.ɵɵtext(11, \"Tax Classification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 18);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 2)(15, \"span\", 18);\n    i0.ɵɵtext(16, \"Transportation Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 18);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.sale == null ? null : ctx_r0.products.sale.sales_status) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.sale == null ? null : ctx_r0.products.sale.tax_classification) || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r0.products == null ? null : ctx_r0.products.sale == null ? null : ctx_r0.products.sale.transportation_group) || \"-\");\n  }\n}\nexport class GeneralComponent {\n  constructor(fb, productservice, messageservice) {\n    this.fb = fb;\n    this.productservice = productservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.products = null;\n    this.status = ['ACTIVE', 'INACTIVE'];\n    this.GeneralForm = this.fb.group({\n      product_id: '',\n      name: [''],\n      product_summary: [''],\n      specification: [''],\n      product_status: ['']\n    });\n    this.isDisabled = true;\n  }\n  ngOnInit() {\n    this.productservice.product.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.products = data;\n      this.updateFormValues();\n    });\n  }\n  updateFormValues() {\n    this.GeneralForm.patchValue(this.products);\n  }\n  submitGereralForm() {\n    const {\n      name,\n      product_summary,\n      specification,\n      product_status\n    } = this.GeneralForm.value;\n    const payload = {\n      name,\n      product_summary,\n      specification,\n      product_status\n    };\n    this.productservice.updateProduct(this.products.documentId, payload).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: res => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Changes Saved Successfully!'\n        });\n      },\n      error: error => {\n        console.error('Error while processing your request.', error);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function GeneralComponent_Factory(t) {\n      return new (t || GeneralComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GeneralComponent,\n      selectors: [[\"app-general\"]],\n      decls: 32,\n      vars: 10,\n      consts: [[3, \"formGroup\"], [1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\", \"p-fluid\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"product_id\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"name\"], [\"formControlName\", \"product_status\", 3, \"options\"], [1, \"col-12\", \"lg:col-12\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"formControlName\", \"product_summary\", \"placeholder\", \"Enter text here...\"], [\"formControlName\", \"specification\", \"placeholder\", \"Enter text here...\"], [1, \"block\", \"font-large\", \"mb-3\", \"text-600\", \"p-custom-button\"], [\"type\", \"submit\", \"label\", \"SUBMIT\", 1, \"p-button-primary\", 3, \"click\"], [3, \"ngIf\"], [1, \"grid\", \"mx-0\", \"border-1\", \"m-5\"], [1, \"col-12\", \"lg:col-12\", \"border-1\"], [1, \"text-900\", \"block\", \"font-medium\", \"text-lg\", \"mb-3\", \"font-bold\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"text-base\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-base\", \"text-600\"]],\n      template: function GeneralComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n          i0.ɵɵtext(4, \"Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 4);\n          i0.ɵɵelement(6, \"input\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 2)(8, \"span\", 3);\n          i0.ɵɵtext(9, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 4);\n          i0.ɵɵelement(11, \"input\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"span\", 3);\n          i0.ɵɵtext(14, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 4);\n          i0.ɵɵelement(16, \"p-dropdown\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"span\", 3);\n          i0.ɵɵtext(19, \"Summary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\", 9);\n          i0.ɵɵelement(21, \"p-editor\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 8)(23, \"span\", 3);\n          i0.ɵɵtext(24, \"Specification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 9);\n          i0.ɵɵelement(26, \"p-editor\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 8)(28, \"span\", 12)(29, \"p-button\", 13);\n          i0.ɵɵlistener(\"click\", function GeneralComponent_Template_p_button_click_29_listener() {\n            return ctx.submitGereralForm();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(30, GeneralComponent_ng_template_30_Template, 39, 7, \"ng-template\", 14)(31, GeneralComponent_ng_template_31_Template, 19, 3, \"ng-template\", 14);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.GeneralForm);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"options\", ctx.status);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(8, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c0));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.products == null ? null : ctx.products.storage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.products == null ? null : ctx.products.sale);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i5.Editor, i6.Button, i7.InputText, i1.FormGroupDirective, i1.FormControlName, i8.Dropdown],\n      styles: [\".p-custom-button .p-button {\\n  font-size: 17px !important;\\n  float: right;\\n}\\n\\n.desc_input[_ngcontent-%COMP%] {\\n  opacity: 1.4;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmFja29mZmljZS9wcm9kdWN0L3Byb2R1Y3QtZGV0YWlscy9nZW5lcmFsL2dlbmVyYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSwwQkFBQTtFQUNBLFlBQUE7QUFDRjs7QUFDQTtFQUNFLFlBQUE7QUFFRiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAucC1jdXN0b20tYnV0dG9uIC5wLWJ1dHRvbiB7XHJcbiAgZm9udC1zaXplOiAxN3B4ICFpbXBvcnRhbnQ7XHJcbiAgZmxvYXQ6IHJpZ2h0O1xyXG59XHJcbi5kZXNjX2lucHV0IHtcclxuICBvcGFjaXR5OiAxLjQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "products", "storage", "storage_conditions", "temperature_condition_ind", "hazardous_material_number", "nmbr_of_gr_or_gi_slips_to_print_qty", "label_type", "label_form", "min_remaining_shelf_life", "sale", "sales_status", "tax_classification", "transportation_group", "GeneralComponent", "constructor", "fb", "productservice", "messageservice", "unsubscribe$", "status", "GeneralForm", "group", "product_id", "name", "product_summary", "specification", "product_status", "isDisabled", "ngOnInit", "product", "pipe", "subscribe", "data", "updateFormValues", "patchValue", "submitGereralForm", "value", "payload", "updateProduct", "documentId", "next", "res", "add", "severity", "detail", "error", "console", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "MessageService", "selectors", "decls", "vars", "consts", "template", "GeneralComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "GeneralComponent_Template_p_button_click_29_listener", "ɵɵtemplate", "GeneralComponent_ng_template_30_Template", "GeneralComponent_ng_template_31_Template", "ɵɵproperty", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\general\\general.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\general\\general.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder } from '@angular/forms';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ProductService } from '../../product.service';\r\n\r\n@Component({\r\n  selector: 'app-general',\r\n  templateUrl: './general.component.html',\r\n  styleUrl: './general.component.scss',\r\n})\r\nexport class GeneralComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public products: any = null;\r\n  public status: string[] = ['ACTIVE', 'INACTIVE'];\r\n  public GeneralForm: any = this.fb.group({\r\n    product_id: '',\r\n    name: [''],\r\n    product_summary: [''],\r\n    specification: [''],\r\n    product_status: [''],\r\n  });\r\n  public isDisabled: boolean = true;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private productservice: ProductService,\r\n    private messageservice: MessageService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.productservice.product\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.products = data;\r\n        this.updateFormValues();\r\n      });\r\n  }\r\n\r\n  updateFormValues(): void {\r\n    this.GeneralForm.patchValue(this.products);\r\n  }\r\n\r\n  submitGereralForm() {\r\n    const { name, product_summary, specification, product_status } =\r\n      this.GeneralForm.value;\r\n    const payload = {\r\n      name,\r\n      product_summary,\r\n      specification,\r\n      product_status,\r\n    };\r\n\r\n    this.productservice\r\n      .updateProduct(this.products.documentId, payload)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Changes Saved Successfully!',\r\n          });\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error while processing your request.', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<form [formGroup]=\"GeneralForm\">\r\n  <div class=\"grid mx-0\">\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Code</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input type=\"text\" pInputText formControlName=\"product_id\" />\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Name</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input type=\"text\" pInputText formControlName=\"name\" />\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Status</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <p-dropdown [options]=\"status\" formControlName=\"product_status\"></p-dropdown>\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Summary</span>\r\n      <span class=\"block font-medium mb-3 text-600\">\r\n        <p-editor formControlName=\"product_summary\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\" />\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Specification</span>\r\n      <span class=\"block font-medium mb-3 text-600\">\r\n        <p-editor formControlName=\"specification\" placeholder=\"Enter text here...\" [style]=\"{ height: '200px' }\" />\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12\">\r\n      <span class=\"block font-large mb-3 text-600 p-custom-button\">\r\n        <p-button type=\"submit\" class=\"p-button-primary\" label=\"SUBMIT\" (click)=\"submitGereralForm()\"></p-button>\r\n      </span>\r\n    </div>\r\n  </div>\r\n</form>\r\n\r\n<ng-template [ngIf]=\"products?.storage\">\r\n  <div class=\"grid mx-0 border-1 m-5\">\r\n    <div class=\"col-12 lg:col-12 border-1\">\r\n      <span class=\"text-900 block font-medium text-lg mb-3 font-bold\">Product Storage</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Storage Conditions</span>\r\n      <span class=\"block font-medium mb-3 text-base text-600\">{{\r\n        products?.storage?.storage_conditions || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Temperature Condition Ind</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.storage?.temperature_condition_ind || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Hazardous Material Number</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.storage?.hazardous_material_number || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Nmbr Of Gr or Gi Slips To Print Qty</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.storage?.nmbr_of_gr_or_gi_slips_to_print_qty || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Label Type</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.storage?.label_type || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Label Form</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.storage?.label_form || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Min Remaining Shelf Life</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.storage?.min_remaining_shelf_life || \"-\"\r\n        }}</span>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n<ng-template [ngIf]=\"products?.sale\">\r\n  <div class=\"grid mx-0 border-1 m-5\">\r\n    <div class=\"col-12 lg:col-12 border-1\">\r\n      <span class=\"text-900 block font-medium text-lg mb-3 font-bold\">Product Sale</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Sales Status</span>\r\n      <span class=\"block font-medium mb-3 text-base text-600\">{{\r\n        products?.sale?.sales_status || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Tax Classification</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.sale?.tax_classification || \"-\"\r\n        }}</span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">Transportation Group</span>\r\n      <span class=\"text-900 block font-medium mb-3 text-base font-bold\">{{\r\n        products?.sale?.transportation_group || \"-\"\r\n        }}</span>\r\n    </div>\r\n  </div>\r\n</ng-template>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICyCnCC,EAFJ,CAAAC,cAAA,cAAoC,cACK,eAC2B;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACjFF,EADiF,CAAAG,YAAA,EAAO,EAClF;IAEJH,EADF,CAAAC,cAAA,aAA6B,eACuC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3FH,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAE,MAAA,GAEpD;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,aAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClGH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,cAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClGH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,cAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5GH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,cAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnFH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,cAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnFH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,cAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjGH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IAERF,EAFQ,CAAAG,YAAA,EAAO,EACP,EACF;;;;IAxCsDH,EAAA,CAAAI,SAAA,GAEpD;IAFoDJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,kBAAA,SAEpD;IAI8DT,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAE,yBAAA,SAE9D;IAI8DV,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAG,yBAAA,SAE9D;IAI8DX,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAI,mCAAA,SAE9D;IAI8DZ,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAK,UAAA,SAE9D;IAI8Db,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAM,UAAA,SAE9D;IAI8Dd,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAO,wBAAA,SAE9D;;;;;IAOJf,EAFJ,CAAAC,cAAA,cAAoC,cACK,eAC2B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC/E;IAEJH,EADF,CAAAC,cAAA,aAA6B,eACuC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrFH,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAE,MAAA,GAEpD;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,aAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3FH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,cAA6B,gBACuC;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7FH,EAAA,CAAAC,cAAA,gBAAkE;IAAAD,EAAA,CAAAE,MAAA,IAE9D;IAERF,EAFQ,CAAAG,YAAA,EAAO,EACP,EACF;;;;IAhBsDH,EAAA,CAAAI,SAAA,GAEpD;IAFoDJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAS,IAAA,kBAAAV,MAAA,CAAAC,QAAA,CAAAS,IAAA,CAAAC,YAAA,SAEpD;IAI8DjB,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAS,IAAA,kBAAAV,MAAA,CAAAC,QAAA,CAAAS,IAAA,CAAAE,kBAAA,SAE9D;IAI8DlB,EAAA,CAAAI,SAAA,GAE9D;IAF8DJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,QAAA,kBAAAD,MAAA,CAAAC,QAAA,CAAAS,IAAA,kBAAAV,MAAA,CAAAC,QAAA,CAAAS,IAAA,CAAAG,oBAAA,SAE9D;;;ADnGV,OAAM,MAAOC,gBAAgB;EAa3BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAfhB,KAAAC,YAAY,GAAG,IAAI3B,OAAO,EAAQ;IACnC,KAAAS,QAAQ,GAAQ,IAAI;IACpB,KAAAmB,MAAM,GAAa,CAAC,QAAQ,EAAE,UAAU,CAAC;IACzC,KAAAC,WAAW,GAAQ,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MACtCC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,cAAc,EAAE,CAAC,EAAE;KACpB,CAAC;IACK,KAAAC,UAAU,GAAY,IAAI;EAM7B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACZ,cAAc,CAACa,OAAO,CACxBC,IAAI,CAACtC,SAAS,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAAChC,QAAQ,GAAGgC,IAAI;MACpB,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACN;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACb,WAAW,CAACc,UAAU,CAAC,IAAI,CAAClC,QAAQ,CAAC;EAC5C;EAEAmC,iBAAiBA,CAAA;IACf,MAAM;MAAEZ,IAAI;MAAEC,eAAe;MAAEC,aAAa;MAAEC;IAAc,CAAE,GAC5D,IAAI,CAACN,WAAW,CAACgB,KAAK;IACxB,MAAMC,OAAO,GAAG;MACdd,IAAI;MACJC,eAAe;MACfC,aAAa;MACbC;KACD;IAED,IAAI,CAACV,cAAc,CAChBsB,aAAa,CAAC,IAAI,CAACtC,QAAQ,CAACuC,UAAU,EAAEF,OAAO,CAAC,CAChDP,IAAI,CAACtC,SAAS,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTS,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACxB,cAAc,CAACyB,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC;EACN;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAC7B,YAAY,CAACsB,IAAI,EAAE;IACxB,IAAI,CAACtB,YAAY,CAAC8B,QAAQ,EAAE;EAC9B;;;uBA7DWnC,gBAAgB,EAAApB,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1D,EAAA,CAAAwD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5D,EAAA,CAAAwD,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhB1C,gBAAgB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRvBrE,EAHN,CAAAC,cAAA,cAAgC,aACP,aACQ,cAC6B;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnEH,EAAA,CAAAC,cAAA,cAAsD;UACpDD,EAAA,CAAAuE,SAAA,eAA6D;UAEjEvE,EADE,CAAAG,YAAA,EAAO,EACH;UAEJH,EADF,CAAAC,cAAA,aAA6B,cAC6B;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnEH,EAAA,CAAAC,cAAA,eAAsD;UACpDD,EAAA,CAAAuE,SAAA,gBAAuD;UAE3DvE,EADE,CAAAG,YAAA,EAAO,EACH;UAEJH,EADF,CAAAC,cAAA,cAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrEH,EAAA,CAAAC,cAAA,eAAsD;UACpDD,EAAA,CAAAuE,SAAA,qBAA6E;UAEjFvE,EADE,CAAAG,YAAA,EAAO,EACH;UAEJH,EADF,CAAAC,cAAA,cAA8B,eAC4B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtEH,EAAA,CAAAC,cAAA,eAA8C;UAC5CD,EAAA,CAAAuE,SAAA,oBAA6G;UAEjHvE,EADE,CAAAG,YAAA,EAAO,EACH;UAEJH,EADF,CAAAC,cAAA,cAA8B,eAC4B;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5EH,EAAA,CAAAC,cAAA,eAA8C;UAC5CD,EAAA,CAAAuE,SAAA,oBAA2G;UAE/GvE,EADE,CAAAG,YAAA,EAAO,EACH;UAGFH,EAFJ,CAAAC,cAAA,cAA8B,gBACiC,oBACmC;UAA9BD,EAAA,CAAAwE,UAAA,mBAAAC,qDAAA;YAAA,OAASH,GAAA,CAAA5B,iBAAA,EAAmB;UAAA,EAAC;UAIrG1C,EAJsG,CAAAG,YAAA,EAAW,EACpG,EACH,EACF,EACD;UAmDPH,EAjDA,CAAA0E,UAAA,KAAAC,wCAAA,2BAAwC,KAAAC,wCAAA,2BAiDH;;;UAzF/B5E,EAAA,CAAA6E,UAAA,cAAAP,GAAA,CAAA3C,WAAA,CAAyB;UAiBX3B,EAAA,CAAAI,SAAA,IAAkB;UAAlBJ,EAAA,CAAA6E,UAAA,YAAAP,GAAA,CAAA5C,MAAA,CAAkB;UAM+C1B,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAA8E,UAAA,CAAA9E,EAAA,CAAA+E,eAAA,IAAAC,GAAA,EAA6B;UAM/BhF,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAA8E,UAAA,CAAA9E,EAAA,CAAA+E,eAAA,IAAAC,GAAA,EAA6B;UAWnGhF,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAA6E,UAAA,SAAAP,GAAA,CAAA/D,QAAA,kBAAA+D,GAAA,CAAA/D,QAAA,CAAAC,OAAA,CAA0B;UAiD1BR,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAA6E,UAAA,SAAAP,GAAA,CAAA/D,QAAA,kBAAA+D,GAAA,CAAA/D,QAAA,CAAAS,IAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}