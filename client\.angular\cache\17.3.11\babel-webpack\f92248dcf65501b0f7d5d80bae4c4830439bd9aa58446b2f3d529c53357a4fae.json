{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../administrator.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/toast\";\nimport * as i5 from \"primeng/tabmenu\";\nimport * as i6 from \"primeng/button\";\nfunction AdminDetailsComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.userDetails == null ? null : ctx_r0.userDetails.username, \"\");\n  }\n}\nexport class AdminDetailsComponent {\n  constructor(administratorservice, route, router) {\n    this.administratorservice = administratorservice;\n    this.route = route;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.items = [];\n    this.activeItem = {};\n    this.userDetails = null;\n    this.filteredUsers = [];\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.id = '';\n  }\n  ngOnInit() {\n    this.id = this.route.snapshot.paramMap.get('id') || '';\n    this.makeMenuItems(this.id);\n    this.activeItem = this.items[0];\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const userId = params.get('id');\n      if (userId) {\n        this.loadUserData(userId);\n      }\n    });\n  }\n  makeMenuItems(id) {\n    this.items = [{\n      label: 'General',\n      icon: 'pi pi-list',\n      routerLink: `/backoffice/administrators/${id}/general`\n    }, {\n      label: 'Customer Details',\n      icon: 'pi pi-user',\n      routerLink: `/backoffice/administrators/${id}/customer-detail`\n    }, {\n      label: 'Supplier Details',\n      icon: 'pi pi-box',\n      routerLink: `/backoffice/administrators/${id}/supplier-detail`\n    }, {\n      label: 'Password',\n      icon: 'pi pi-key',\n      routerLink: `/backoffice/administrators/${id}/password`\n    }];\n  }\n  loadUserData(userId) {\n    this.administratorservice.getUserByID(userId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.userDetails = response || null;\n        this.administratorservice.adminSubject.next(this.userDetails);\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  searchUsers(event) {\n    const query = event.query.toLowerCase();\n    this.administratorservice.getUserByIDName(query).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      const users = response || [];\n      if (users.length) {\n        this.filteredUsers = response.map(user => ({\n          documentId: user.documentId,\n          id: user.id,\n          username: user.username\n        }));\n      } else {\n        this.filteredUsers = [{\n          id: 'The requested data does not exist.'\n        }];\n      }\n    });\n  }\n  onUserSelect(user) {\n    const userId = user.value.id;\n    if (userId) {\n      const tabName = this.activeItem.routerLink.split('/').pop();\n      this.makeMenuItems(userId);\n      this.router.navigate([`/backoffice/administrators/${userId}/${tabName}`]);\n    } else {\n      console.error('User ID is undefined or null');\n    }\n  }\n  goToBack() {\n    this.router.navigate(['/backoffice/administrators']);\n  }\n  static {\n    this.ɵfac = function AdminDetailsComponent_Factory(t) {\n      return new (t || AdminDetailsComponent)(i0.ɵɵdirectiveInject(i1.AdministratorService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDetailsComponent,\n      selectors: [[\"app-admin-details\"]],\n      decls: 12,\n      vars: 4,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"grid\"], [1, \"col-12\", \"sm:col-4\"], [1, \"mb-0\"], [4, \"ngIf\"], [1, \"col-12\", \"sm:col-4\", \"flex\", \"sm:justify-content-end\"], [\"icon\", \"pi pi-arrow-left\", \"label\", \"Back\", 1, \"p-button-primary\", \"p-back-button\", 3, \"onClick\"], [3, \"activeItemChange\", \"model\", \"activeItem\"], [1, \"grid\", \"py-4\"], [1, \"col-12\"]],\n      template: function AdminDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtemplate(4, AdminDetailsComponent_span_4_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(5, \"div\", 2);\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"p-button\", 6);\n          i0.ɵɵlistener(\"onClick\", function AdminDetailsComponent_Template_p_button_onClick_7_listener() {\n            return ctx.goToBack();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"p-tabMenu\", 7);\n          i0.ɵɵtwoWayListener(\"activeItemChange\", function AdminDetailsComponent_Template_p_tabMenu_activeItemChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeItem, $event) || (ctx.activeItem = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9);\n          i0.ɵɵelement(11, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.userDetails == null ? null : ctx.userDetails.username);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"model\", ctx.items);\n          i0.ɵɵtwoWayProperty(\"activeItem\", ctx.activeItem);\n        }\n      },\n      dependencies: [i3.NgIf, i4.Toast, i2.RouterOutlet, i5.TabMenu, i6.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "userDetails", "username", "AdminDetailsComponent", "constructor", "administratorservice", "route", "router", "unsubscribe$", "items", "activeItem", "filteredUsers", "isExpanded", "expandedRows", "id", "ngOnInit", "snapshot", "paramMap", "get", "makeMenuItems", "pipe", "subscribe", "params", "userId", "loadUserData", "label", "icon", "routerLink", "getUserByID", "next", "response", "adminSubject", "error", "console", "searchUsers", "event", "query", "toLowerCase", "getUserByIDName", "users", "length", "map", "user", "documentId", "onUserSelect", "value", "tabName", "split", "pop", "navigate", "goToBack", "ɵɵdirectiveInject", "i1", "AdministratorService", "i2", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "AdminDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "AdminDetailsComponent_span_4_Template", "ɵɵlistener", "AdminDetailsComponent_Template_p_button_onClick_7_listener", "ɵɵtwoWayListener", "AdminDetailsComponent_Template_p_tabMenu_activeItemChange_8_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵproperty", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\administrator\\admin-details\\admin-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\administrator\\admin-details\\admin-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AdministratorService } from '../administrator.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-admin-details',\r\n  templateUrl: './admin-details.component.html',\r\n  styleUrl: './admin-details.component.scss',\r\n})\r\nexport class AdminDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public items: MenuItem[] = [];\r\n  public activeItem: MenuItem = {};\r\n  public userDetails: any = null;\r\n  public filteredUsers: any[] = [];\r\n  public selectedUser: any;\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public id: string = '';\r\n\r\n  constructor(\r\n    private administratorservice: AdministratorService,\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.makeMenuItems(this.id);\r\n    this.activeItem = this.items[0];\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const userId = params.get('id');\r\n        if (userId) {\r\n          this.loadUserData(userId);\r\n        }\r\n      });\r\n  }\r\n\r\n  makeMenuItems(id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'General',\r\n        icon: 'pi pi-list',\r\n        routerLink: `/backoffice/administrators/${id}/general`,\r\n      },\r\n      {\r\n        label: 'Customer Details',\r\n        icon: 'pi pi-user',\r\n        routerLink: `/backoffice/administrators/${id}/customer-detail`,\r\n      },\r\n      {\r\n        label: 'Supplier Details',\r\n        icon: 'pi pi-box',\r\n        routerLink: `/backoffice/administrators/${id}/supplier-detail`,\r\n      },\r\n      {\r\n        label: 'Password',\r\n        icon: 'pi pi-key',\r\n        routerLink: `/backoffice/administrators/${id}/password`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  private loadUserData(userId: string): void {\r\n    this.administratorservice\r\n      .getUserByID(userId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.userDetails = response || null;\r\n          this.administratorservice.adminSubject.next(this.userDetails);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  searchUsers(event: any): void {\r\n    const query = event.query.toLowerCase();\r\n    this.administratorservice\r\n      .getUserByIDName(query)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        const users = response || [];\r\n        if (users.length) {\r\n          this.filteredUsers = response.map((user: any) => ({\r\n            documentId: user.documentId,\r\n            id: user.id,\r\n            username: user.username,\r\n          }));\r\n        } else {\r\n          this.filteredUsers = [\r\n            {\r\n              id: 'The requested data does not exist.',\r\n            },\r\n          ];\r\n        }\r\n      });\r\n  }\r\n\r\n  onUserSelect(user: any): void {\r\n    const userId = user.value.id;\r\n    if (userId) {\r\n      const tabName = this.activeItem.routerLink.split('/').pop();\r\n      this.makeMenuItems(userId);\r\n      this.router.navigate([`/backoffice/administrators/${userId}/${tabName}`]);\r\n    } else {\r\n      console.error('User ID is undefined or null');\r\n    }\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/backoffice/administrators']);\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n\r\n<div class=\"grid\">\r\n    <div class=\"col-12 sm:col-4\">\r\n        <h5 class=\"mb-0\">\r\n            <span *ngIf=\"userDetails?.username\">\r\n                {{ userDetails?.username }}</span>\r\n        </h5>\r\n    </div>\r\n\r\n    <div class=\"col-12 sm:col-4\">\r\n\r\n    </div>\r\n\r\n    <div class=\"col-12 sm:col-4 flex sm:justify-content-end\">\r\n        <p-button icon=\"pi pi-arrow-left\" class=\"p-button-primary p-back-button\" label=\"Back\"\r\n            (onClick)=\"goToBack()\"></p-button>\r\n    </div>\r\n</div>\r\n\r\n<p-tabMenu [model]=\"items\" [(activeItem)]=\"activeItem\"></p-tabMenu>\r\n\r\n<div class=\"grid py-4\">\r\n    <div class=\"col-12\">\r\n        <router-outlet></router-outlet>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICC7BC,EAAA,CAAAC,cAAA,WAAoC;IAChCD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlCH,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,QAAA,KAA2B;;;ADS3C,OAAM,MAAOC,qBAAqB;EAWhCC,YACUC,oBAA0C,EAC1CC,KAAqB,EACrBC,MAAc;IAFd,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbR,KAAAC,YAAY,GAAG,IAAIhB,OAAO,EAAQ;IACnC,KAAAiB,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAa,EAAE;IACzB,KAAAT,WAAW,GAAQ,IAAI;IACvB,KAAAU,aAAa,GAAU,EAAE;IAEzB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,EAAE,GAAW,EAAE;EAMnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,EAAE,GAAG,IAAI,CAACR,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACtD,IAAI,CAACC,aAAa,CAAC,IAAI,CAACL,EAAE,CAAC;IAC3B,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACH,KAAK,CAACW,QAAQ,CAChBG,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACe,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,MAAM,GAAGD,MAAM,CAACJ,GAAG,CAAC,IAAI,CAAC;MAC/B,IAAIK,MAAM,EAAE;QACV,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;MAC3B;IACF,CAAC,CAAC;EACN;EAEAJ,aAAaA,CAACL,EAAU;IACtB,IAAI,CAACL,KAAK,GAAG,CACX;MACEgB,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,8BAA8Bb,EAAE;KAC7C,EACD;MACEW,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,8BAA8Bb,EAAE;KAC7C,EACD;MACEW,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE,WAAW;MACjBC,UAAU,EAAE,8BAA8Bb,EAAE;KAC7C,EACD;MACEW,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,WAAW;MACjBC,UAAU,EAAE,8BAA8Bb,EAAE;KAC7C,CACF;EACH;EAEQU,YAAYA,CAACD,MAAc;IACjC,IAAI,CAAClB,oBAAoB,CACtBuB,WAAW,CAACL,MAAM,CAAC,CACnBH,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACe,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTQ,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC7B,WAAW,GAAG6B,QAAQ,IAAI,IAAI;QACnC,IAAI,CAACzB,oBAAoB,CAAC0B,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC5B,WAAW,CAAC;MAC/D,CAAC;MACD+B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAE,WAAWA,CAACC,KAAU;IACpB,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK,CAACC,WAAW,EAAE;IACvC,IAAI,CAAChC,oBAAoB,CACtBiC,eAAe,CAACF,KAAK,CAAC,CACtBhB,IAAI,CAAC3B,SAAS,CAAC,IAAI,CAACe,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAES,QAAa,IAAI;MAC3B,MAAMS,KAAK,GAAGT,QAAQ,IAAI,EAAE;MAC5B,IAAIS,KAAK,CAACC,MAAM,EAAE;QAChB,IAAI,CAAC7B,aAAa,GAAGmB,QAAQ,CAACW,GAAG,CAAEC,IAAS,KAAM;UAChDC,UAAU,EAAED,IAAI,CAACC,UAAU;UAC3B7B,EAAE,EAAE4B,IAAI,CAAC5B,EAAE;UACXZ,QAAQ,EAAEwC,IAAI,CAACxC;SAChB,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACS,aAAa,GAAG,CACnB;UACEG,EAAE,EAAE;SACL,CACF;MACH;IACF,CAAC,CAAC;EACN;EAEA8B,YAAYA,CAACF,IAAS;IACpB,MAAMnB,MAAM,GAAGmB,IAAI,CAACG,KAAK,CAAC/B,EAAE;IAC5B,IAAIS,MAAM,EAAE;MACV,MAAMuB,OAAO,GAAG,IAAI,CAACpC,UAAU,CAACiB,UAAU,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;MAC3D,IAAI,CAAC7B,aAAa,CAACI,MAAM,CAAC;MAC1B,IAAI,CAAChB,MAAM,CAAC0C,QAAQ,CAAC,CAAC,8BAA8B1B,MAAM,IAAIuB,OAAO,EAAE,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLb,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAC;IAC/C;EACF;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAAC3C,MAAM,CAAC0C,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;EACtD;;;uBA3GW9C,qBAAqB,EAAAT,EAAA,CAAAyD,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA3D,EAAA,CAAAyD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA7D,EAAA,CAAAyD,iBAAA,CAAAG,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAArBrD,qBAAqB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCflCrE,EAAA,CAAAuE,SAAA,iBAAsD;UAI9CvE,EAFR,CAAAC,cAAA,aAAkB,aACe,YACR;UACbD,EAAA,CAAAwE,UAAA,IAAAC,qCAAA,kBAAoC;UAG5CzE,EADI,CAAAG,YAAA,EAAK,EACH;UAENH,EAAA,CAAAuE,SAAA,aAEM;UAGFvE,EADJ,CAAAC,cAAA,aAAyD,kBAE1B;UAAvBD,EAAA,CAAA0E,UAAA,qBAAAC,2DAAA;YAAA,OAAWL,GAAA,CAAAd,QAAA,EAAU;UAAA,EAAC;UAElCxD,EAFmC,CAAAG,YAAA,EAAW,EACpC,EACJ;UAENH,EAAA,CAAAC,cAAA,mBAAuD;UAA5BD,EAAA,CAAA4E,gBAAA,8BAAAC,qEAAAC,MAAA;YAAA9E,EAAA,CAAA+E,kBAAA,CAAAT,GAAA,CAAAtD,UAAA,EAAA8D,MAAA,MAAAR,GAAA,CAAAtD,UAAA,GAAA8D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAAC9E,EAAA,CAAAG,YAAA,EAAY;UAG/DH,EADJ,CAAAC,cAAA,aAAuB,cACC;UAChBD,EAAA,CAAAuE,SAAA,qBAA+B;UAEvCvE,EADI,CAAAG,YAAA,EAAM,EACJ;;;UA1BwBH,EAAA,CAAAgF,UAAA,cAAa;UAKxBhF,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAgF,UAAA,SAAAV,GAAA,CAAA/D,WAAA,kBAAA+D,GAAA,CAAA/D,WAAA,CAAAC,QAAA,CAA2B;UAenCR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAgF,UAAA,UAAAV,GAAA,CAAAvD,KAAA,CAAe;UAACf,EAAA,CAAAiF,gBAAA,eAAAX,GAAA,CAAAtD,UAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}