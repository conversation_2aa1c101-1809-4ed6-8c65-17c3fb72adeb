{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PaymentDetailsComponent } from './payment-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: ':id',\n  component: PaymentDetailsComponent\n}];\nexport class PaymentDetailsRoutingModule {\n  static {\n    this.ɵfac = function PaymentDetailsRoutingModule_Factory(t) {\n      return new (t || PaymentDetailsRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PaymentDetailsRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PaymentDetailsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PaymentDetailsComponent", "routes", "path", "component", "PaymentDetailsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\payment-details\\payment-details-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { PaymentDetailsComponent } from './payment-details.component';\r\n\r\nconst routes: Routes = [{ path: ':id', component: PaymentDetailsComponent }];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class PaymentDetailsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;;;AAErE,MAAMC,MAAM,GAAW,CAAC;EAAEC,IAAI,EAAE,KAAK;EAAEC,SAAS,EAAEH;AAAuB,CAAE,CAAC;AAM5E,OAAM,MAAOI,2BAA2B;;;uBAA3BA,2BAA2B;IAAA;EAAA;;;YAA3BA;IAA2B;EAAA;;;gBAH5BL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,2BAA2B;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAF5BT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}