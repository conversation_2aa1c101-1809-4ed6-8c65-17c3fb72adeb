{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { InvoiceRoutingModule } from './invoice-routing.module';\nimport { InvoiceComponent } from './invoice.component';\nimport { FormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { CalendarModule } from 'primeng/calendar';\nimport * as i0 from \"@angular/core\";\nexport class InvoiceModule {\n  static {\n    this.ɵfac = function InvoiceModule_Factory(t) {\n      return new (t || InvoiceModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: InvoiceModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, InvoiceRoutingModule, TableModule, FormsModule, CalendarModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(InvoiceModule, {\n    declarations: [InvoiceComponent],\n    imports: [CommonModule, InvoiceRoutingModule, TableModule, FormsModule, CalendarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "InvoiceRoutingModule", "InvoiceComponent", "FormsModule", "TableModule", "CalendarModule", "InvoiceModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\invoice\\invoice.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { InvoiceRoutingModule } from './invoice-routing.module';\r\nimport { InvoiceComponent } from './invoice.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { CalendarModule } from 'primeng/calendar';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    InvoiceComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    InvoiceRoutingModule,\r\n    TableModule,\r\n    FormsModule,\r\n    CalendarModule,\r\n  ]\r\n})\r\nexport class InvoiceModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;;AAejD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAPtBN,YAAY,EACZC,oBAAoB,EACpBG,WAAW,EACXD,WAAW,EACXE,cAAc;IAAA;EAAA;;;2EAGLC,aAAa;IAAAC,YAAA,GAVtBL,gBAAgB;IAAAM,OAAA,GAGhBR,YAAY,EACZC,oBAAoB,EACpBG,WAAW,EACXD,WAAW,EACXE,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}