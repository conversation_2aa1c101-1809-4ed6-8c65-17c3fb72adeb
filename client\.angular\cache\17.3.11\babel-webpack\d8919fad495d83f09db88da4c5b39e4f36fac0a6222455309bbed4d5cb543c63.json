{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VendorContactComponent } from './vendor-contact.component';\nimport { VendorContactDetailsComponent } from './vendor-contact-details/vendor-contact-details.component';\nimport { Permission } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: VendorContactComponent,\n  data: {\n    permission: Permission.Vendor_Portal_Backoffice\n  }\n}, {\n  path: ':id',\n  component: VendorContactDetailsComponent,\n  data: {\n    permission: Permission.Vendor_Portal\n  },\n  children: [{\n    path: '',\n    component: VendorContactDetailsComponent\n  }, {\n    path: '',\n    redirectTo: 'vendor-contact',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'vendor-contact',\n    pathMatch: 'full'\n  }]\n}];\nexport class VendorContactRoutingModule {\n  static {\n    this.ɵfac = function VendorContactRoutingModule_Factory(t) {\n      return new (t || VendorContactRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VendorContactRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VendorContactRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VendorContactComponent", "VendorContactDetailsComponent", "Permission", "routes", "path", "component", "data", "permission", "Vendor_Portal_Backoffice", "Vendor_Portal", "children", "redirectTo", "pathMatch", "VendorContactRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\vendor-contact\\vendor-contact-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VendorContactComponent } from './vendor-contact.component';\r\nimport { VendorContactDetailsComponent } from './vendor-contact-details/vendor-contact-details.component';\r\nimport { Permission } from 'src/app/constants/api.constants';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: VendorContactComponent, data: { permission: Permission.Vendor_Portal_Backoffice } },\r\n  {\r\n    path: ':id',\r\n    component: VendorContactDetailsComponent,\r\n    data: { permission: Permission.Vendor_Portal },\r\n    children: [\r\n      { path: '', component: VendorContactDetailsComponent },\r\n      { path: '', redirectTo: 'vendor-contact', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'vendor-contact', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class VendorContactRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,UAAU,QAAQ,iCAAiC;;;AAE5D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEL,sBAAsB;EAAEM,IAAI,EAAE;IAAEC,UAAU,EAAEL,UAAU,CAACM;EAAwB;AAAE,CAAE,EAC1G;EACEJ,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEJ,6BAA6B;EACxCK,IAAI,EAAE;IAAEC,UAAU,EAAEL,UAAU,CAACO;EAAa,CAAE;EAC9CC,QAAQ,EAAE,CACR;IAAEN,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEJ;EAA6B,CAAE,EACtD;IAAEG,IAAI,EAAE,EAAE;IAAEO,UAAU,EAAE,gBAAgB;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC7D;IAAER,IAAI,EAAE,IAAI;IAAEO,UAAU,EAAE,gBAAgB;IAAEC,SAAS,EAAE;EAAM,CAAE;CAElE,CACF;AAMD,OAAM,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3Bd,YAAY,CAACe,QAAQ,CAACX,MAAM,CAAC,EAC7BJ,YAAY;IAAA;EAAA;;;2EAEXc,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAAjB,YAAA;IAAAkB,OAAA,GAF3BlB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}