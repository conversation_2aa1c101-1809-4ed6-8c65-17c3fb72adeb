{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CustomerAddressDependentService {\n  constructor(http) {\n    this.http = http;\n  }\n  getAddressDependent(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][address_id][$containsi]', searchTerm).set('filters[$or][1][city_code][$containsi]', searchTerm).set('filters[$or][2][county][$containsi]', searchTerm).set('filters[$or][3][express_train_station_name][$containsi]', searchTerm).set('filters[$or][4][train_station_name][$containsi]', searchTerm).set('filters[$or][5][customer_id][$containsi]', searchTerm);\n    }\n    if (id) {\n      params = params.set('filters[$or][0][customer_id][$eq]', id);\n    }\n    return this.http.get(`${CMS_APIContstant.CUSTOMER_ADDRESS_DEPENDENT}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function CustomerAddressDependentService_Factory(t) {\n      return new (t || CustomerAddressDependentService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CustomerAddressDependentService,\n      factory: CustomerAddressDependentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "CMS_APIContstant", "CustomerAddressDependentService", "constructor", "http", "getAddressDependent", "id", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "CUSTOMER_ADDRESS_DEPENDENT", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer-details\\customer-address-dependent\\customer-address-dependent.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CustomerAddressDependentService {\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getAddressDependent(\r\n    id: string,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][address_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][city_code][$containsi]', searchTerm)\r\n        .set('filters[$or][2][county][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][3][express_train_station_name][$containsi]',\r\n          searchTerm\r\n        )\r\n        .set('filters[$or][4][train_station_name][$containsi]', searchTerm)\r\n        .set('filters[$or][5][customer_id][$containsi]', searchTerm);\r\n    }\r\n    if (id) {\r\n      params = params.set('filters[$or][0][customer_id][$eq]', id);\r\n    }\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.CUSTOMER_ADDRESS_DEPENDENT}`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAG7D,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,+BAA+B;EAC1CC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,mBAAmBA,CACjBC,EAAU,EACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIZ,UAAU,EAAE,CAC1Ba,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,yCAAyC,EAAEF,UAAU,CAAC,CAC1DE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC,CACzDE,GAAG,CAAC,qCAAqC,EAAEF,UAAU,CAAC,CACtDE,GAAG,CACF,yDAAyD,EACzDF,UAAU,CACX,CACAE,GAAG,CAAC,iDAAiD,EAAEF,UAAU,CAAC,CAClEE,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC;IAChE;IACA,IAAIL,EAAE,EAAE;MACNM,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,mCAAmC,EAAEP,EAAE,CAAC;IAC9D;IACA,OAAO,IAAI,CAACF,IAAI,CAACa,GAAG,CAClB,GAAGhB,gBAAgB,CAACiB,0BAA0B,EAAE,EAChD;MACEN;KACD,CACF;EACH;;;uBAvCWV,+BAA+B,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAA/BpB,+BAA+B;MAAAqB,OAAA,EAA/BrB,+BAA+B,CAAAsB,IAAA;MAAAC,UAAA,EAF9B;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}