{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../contact.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/inputswitch\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction GeneralComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, GeneralComponent_div_7_div_1_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"firstname\"].errors && ctx_r0.f[\"firstname\"].errors[\"required\"]);\n  }\n}\nfunction GeneralComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, GeneralComponent_div_13_div_1_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"lastname\"].errors && ctx_r0.f[\"lastname\"].errors[\"required\"]);\n  }\n}\nfunction GeneralComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, GeneralComponent_div_19_div_1_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"email\"].errors && ctx_r0.f[\"email\"].errors[\"required\"]);\n  }\n}\nfunction GeneralComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Address is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, GeneralComponent_div_25_div_1_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.submitted && ctx_r0.f[\"address\"].errors && ctx_r0.f[\"address\"].errors[\"required\"]);\n  }\n}\nfunction GeneralComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"option\", 20);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ur_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngValue\", ur_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ur_r2.name);\n  }\n}\nexport class GeneralComponent {\n  constructor(fb, contactservice, messageservice) {\n    this.fb = fb;\n    this.contactservice = contactservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.contacts = null;\n    this.roles = [];\n    this.GeneralForm = this.fb.group({\n      firstname: ['', [Validators.required]],\n      lastname: ['', [Validators.required]],\n      email: ['', [Validators.required]],\n      address: ['', [Validators.required]],\n      confirmed: [''],\n      blocked: [''],\n      role: ['']\n    });\n    this.submitted = false;\n    this.saving = false;\n  }\n  ngOnInit() {\n    this.getUserRoles();\n    this.contactservice.contact.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      if (data) {\n        this.contacts = data;\n        this.updateFormValues();\n      }\n    });\n  }\n  getUserRoles() {\n    this.contactservice.getUserRoles().pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: res => {\n        const {\n          roles\n        } = res;\n        this.roles = roles || [];\n      },\n      error: res => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  updateFormValues() {\n    this.GeneralForm.patchValue(this.contacts);\n    this.GeneralForm.patchValue({\n      role: this.contacts.role.id\n    });\n  }\n  get f() {\n    return this.GeneralForm.controls;\n  }\n  submitGereralForm() {\n    this.submitted = true;\n    if (this.GeneralForm.invalid) {\n      return;\n    }\n    this.saving = true;\n    const {\n      firstname,\n      lastname,\n      email,\n      address,\n      confirmed,\n      blocked,\n      role\n    } = this.GeneralForm.value;\n    const payload = {\n      firstname,\n      lastname,\n      email,\n      address,\n      confirmed,\n      blocked,\n      role\n    };\n    this.contactservice.updateUser(this.contacts.id, payload).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: res => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Changes Saved Successfully!'\n        });\n      },\n      error: error => {\n        console.error('Error while processing your request.', error);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function GeneralComponent_Factory(t) {\n      return new (t || GeneralComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ContactService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GeneralComponent,\n      selectors: [[\"app-general\"]],\n      decls: 43,\n      vars: 18,\n      consts: [[3, \"formGroup\"], [1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\", \"p-fluid\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"firstname\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"lastname\", 3, \"ngClass\"], [\"type\", \"email\", \"pInputText\", \"\", \"formControlName\", \"email\", 3, \"ngClass\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"address\", 3, \"ngClass\"], [\"pInputText\", \"\", \"formControlName\", \"role\", 1, \"form-select\", \"mt-1\", \"p-custom-dropdown\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"md:col-2\"], [\"formControlName\", \"confirmed\"], [\"formControlName\", \"blocked\"], [1, \"col-12\", \"lg:col-12\"], [1, \"block\", \"font-large\", \"mb-3\", \"text-600\", \"p-custom-button\"], [\"type\", \"submit\", \"label\", \"SUBMIT\", 1, \"p-button-primary\", 3, \"click\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [3, \"ngValue\"]],\n      template: function GeneralComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n          i0.ɵɵtext(4, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 4);\n          i0.ɵɵelement(6, \"input\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, GeneralComponent_div_7_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"span\", 3);\n          i0.ɵɵtext(10, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\", 4);\n          i0.ɵɵelement(12, \"input\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, GeneralComponent_div_13_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 2)(15, \"span\", 3);\n          i0.ɵɵtext(16, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"span\", 4);\n          i0.ɵɵelement(18, \"input\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, GeneralComponent_div_19_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 2)(21, \"span\", 3);\n          i0.ɵɵtext(22, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 4);\n          i0.ɵɵelement(24, \"input\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, GeneralComponent_div_25_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 2)(27, \"span\", 3);\n          i0.ɵɵtext(28, \"Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 4)(30, \"select\", 10);\n          i0.ɵɵtemplate(31, GeneralComponent_ng_container_31_Template, 3, 2, \"ng-container\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 12)(33, \"span\", 3);\n          i0.ɵɵtext(34, \"Confirmed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"p-inputSwitch\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 12)(37, \"span\", 3);\n          i0.ɵɵtext(38, \"Blocked\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"p-inputSwitch\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 15)(41, \"span\", 16)(42, \"p-button\", 17);\n          i0.ɵɵlistener(\"click\", function GeneralComponent_Template_p_button_click_42_listener() {\n            return ctx.submitGereralForm();\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.GeneralForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.submitted && ctx.f[\"firstname\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"firstname\"].errors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.submitted && ctx.f[\"lastname\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"lastname\"].errors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx.submitted && ctx.f[\"email\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email\"].errors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c0, ctx.submitted && ctx.f[\"address\"].errors));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"address\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i5.Button, i6.InputText, i1.FormGroupDirective, i1.FormControlName, i7.InputSwitch],\n      styles: [\".p-custom-button .p-button {\\n  font-size: 15px !important;\\n}\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n\\n  .p-inputswitch {\\n  width: 25%;\\n  height: 35%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmFja29mZmljZS9jb250YWN0cy9jb250YWN0LWRldGFpbHMvZ2VuZXJhbC9nZW5lcmFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsMEJBQUE7QUFDRjs7QUFDQTtFQUNFLGNBQUE7QUFFRjs7QUFBQTtFQUNFLFVBQUE7RUFDQSxXQUFBO0FBR0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLnAtY3VzdG9tLWJ1dHRvbiAucC1idXR0b24ge1xyXG4gIGZvbnQtc2l6ZTogMTVweCAhaW1wb3J0YW50O1xyXG59XHJcbi5pbnZhbGlkLWZlZWRiYWNrIHtcclxuICBjb2xvcjogI2U3NGMzYztcclxufVxyXG46Om5nLWRlZXAgLnAtaW5wdXRzd2l0Y2gge1xyXG4gIHdpZHRoOiAyNSU7XHJcbiAgaGVpZ2h0OiAzNSU7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "GeneralComponent_div_7_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "submitted", "f", "errors", "GeneralComponent_div_13_div_1_Template", "GeneralComponent_div_19_div_1_Template", "GeneralComponent_div_25_div_1_Template", "ɵɵelementContainerStart", "ur_r2", "id", "ɵɵtextInterpolate", "name", "GeneralComponent", "constructor", "fb", "contactservice", "messageservice", "unsubscribe$", "contacts", "roles", "GeneralForm", "group", "firstname", "required", "lastname", "email", "address", "confirmed", "blocked", "role", "saving", "ngOnInit", "getUserRoles", "contact", "pipe", "subscribe", "data", "updateFormValues", "next", "res", "error", "add", "severity", "detail", "patchValue", "controls", "submitGereralForm", "invalid", "value", "payload", "updateUser", "console", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ContactService", "i3", "MessageService", "selectors", "decls", "vars", "consts", "template", "GeneralComponent_Template", "rf", "ctx", "ɵɵelement", "GeneralComponent_div_7_Template", "GeneralComponent_div_13_Template", "GeneralComponent_div_19_Template", "GeneralComponent_div_25_Template", "GeneralComponent_ng_container_31_Template", "ɵɵlistener", "GeneralComponent_Template_p_button_click_42_listener", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\contacts\\contact-details\\general\\general.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\contacts\\contact-details\\general\\general.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, Validators } from '@angular/forms';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ContactService } from '../../contact.service';\r\n\r\n@Component({\r\n  selector: 'app-general',\r\n  templateUrl: './general.component.html',\r\n  styleUrl: './general.component.scss',\r\n})\r\nexport class GeneralComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public contacts: any = null;\r\n  public roles: Array<any> = [];\r\n  public GeneralForm: any = this.fb.group({\r\n    firstname: ['', [Validators.required]],\r\n    lastname: ['', [Validators.required]],\r\n    email: ['', [Validators.required]],\r\n    address: ['', [Validators.required]],\r\n    confirmed: [''],\r\n    blocked: [''],\r\n    role: [''],\r\n  });\r\n  public submitted = false;\r\n  public saving = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private contactservice: ContactService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.getUserRoles();\r\n    this.contactservice.contact\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        if (data) {\r\n          this.contacts = data;\r\n          this.updateFormValues();\r\n        }\r\n      });\r\n  }\r\n\r\n  getUserRoles() {\r\n    this.contactservice\r\n      .getUserRoles()\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          const { roles }: any = res;\r\n          this.roles = roles || [];\r\n        },\r\n        error: (res: any) => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  updateFormValues(): void {\r\n    this.GeneralForm.patchValue(this.contacts);\r\n    this.GeneralForm.patchValue({ role: this.contacts.role.id });\r\n  }\r\n\r\n  get f(): any {\r\n    return this.GeneralForm.controls;\r\n  }\r\n\r\n  submitGereralForm() {\r\n    this.submitted = true;\r\n\r\n    if (this.GeneralForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.saving = true;\r\n    const { firstname, lastname, email, address, confirmed, blocked, role } =\r\n      this.GeneralForm.value;\r\n    const payload = {\r\n      firstname,\r\n      lastname,\r\n      email,\r\n      address,\r\n      confirmed,\r\n      blocked,\r\n      role,\r\n    };\r\n    this.contactservice\r\n      .updateUser(this.contacts.id, payload)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Changes Saved Successfully!',\r\n          });\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error while processing your request.', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<form [formGroup]=\"GeneralForm\">\r\n  <div class=\"grid mx-0\">\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">First Name</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          type=\"text\"\r\n          pInputText\r\n          formControlName=\"firstname\"\r\n          [ngClass]=\"{ 'is-invalid': submitted && f['firstname'].errors }\"\r\n        />\r\n      </span>\r\n      <div *ngIf=\"submitted && f['firstname'].errors\" class=\"invalid-feedback\">\r\n        <div\r\n          *ngIf=\"\r\n            submitted &&\r\n            f['firstname'].errors &&\r\n            f['firstname'].errors['required']\r\n          \"\r\n        >\r\n          First Name is required.\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Last Name</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          type=\"text\"\r\n          pInputText\r\n          formControlName=\"lastname\"\r\n          [ngClass]=\"{ 'is-invalid': submitted && f['lastname'].errors }\"\r\n        />\r\n      </span>\r\n      <div *ngIf=\"submitted && f['lastname'].errors\" class=\"invalid-feedback\">\r\n        <div\r\n          *ngIf=\"\r\n            submitted &&\r\n            f['lastname'].errors &&\r\n            f['lastname'].errors['required']\r\n          \"\r\n        >\r\n          Last Name is required.\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Email</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          type=\"email\"\r\n          pInputText\r\n          formControlName=\"email\"\r\n          [ngClass]=\"{ 'is-invalid': submitted && f['email'].errors }\"\r\n        />\r\n      </span>\r\n      <div *ngIf=\"submitted && f['email'].errors\" class=\"invalid-feedback\">\r\n        <div\r\n          *ngIf=\"\r\n            submitted && f['email'].errors && f['email'].errors['required']\r\n          \"\r\n        >\r\n          Email is required.\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Address</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <input\r\n          type=\"text\"\r\n          pInputText\r\n          formControlName=\"address\"\r\n          [ngClass]=\"{ 'is-invalid': submitted && f['address'].errors }\"\r\n        />\r\n      </span>\r\n      <div *ngIf=\"submitted && f['address'].errors\" class=\"invalid-feedback\">\r\n        <div\r\n          *ngIf=\"\r\n            submitted && f['address'].errors && f['address'].errors['required']\r\n          \"\r\n        >\r\n          Address is required.\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-4\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Role</span>\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <select\r\n          pInputText\r\n          formControlName=\"role\"\r\n          class=\"form-select mt-1 p-custom-dropdown\"\r\n        >\r\n          <ng-container *ngFor=\"let ur of roles\">\r\n            <option [ngValue]=\"ur.id\">{{ ur.name }}</option>\r\n          </ng-container>\r\n        </select>\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 md:col-2\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Confirmed</span>\r\n      <p-inputSwitch formControlName=\"confirmed\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"col-12 md:col-2\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\">Blocked</span>\r\n      <p-inputSwitch formControlName=\"blocked\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12\">\r\n      <span class=\"block font-large mb-3 text-600 p-custom-button\">\r\n        <p-button\r\n          type=\"submit\"\r\n          class=\"p-button-primary\"\r\n          label=\"SUBMIT\"\r\n          (click)=\"submitGereralForm()\"\r\n        ></p-button>\r\n      </span>\r\n    </div>\r\n  </div>\r\n</form>\r\n"], "mappings": "AACA,SAAsBA,UAAU,QAAQ,gBAAgB;AACxD,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICWjCC,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATRH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAI,UAAA,IAAAC,qCAAA,kBAMC;IAGHL,EAAA,CAAAG,YAAA,EAAM;;;;IARDH,EAAA,CAAAM,SAAA,EAIF;IAJEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,cAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,cAAAC,MAAA,aAIF;;;;;IAiBDX,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATRH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAI,UAAA,IAAAQ,sCAAA,kBAMC;IAGHZ,EAAA,CAAAG,YAAA,EAAM;;;;IARDH,EAAA,CAAAM,SAAA,EAIF;IAJEN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,aAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,aAAAC,MAAA,aAIF;;;;;IAiBDX,EAAA,CAAAC,cAAA,UAIC;IACCD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPRH,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAI,UAAA,IAAAS,sCAAA,kBAIC;IAGHb,EAAA,CAAAG,YAAA,EAAM;;;;IANDH,EAAA,CAAAM,SAAA,EAEA;IAFAN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,UAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,UAAAC,MAAA,aAEA;;;;;IAiBHX,EAAA,CAAAC,cAAA,UAIC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAPRH,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAI,UAAA,IAAAU,sCAAA,kBAIC;IAGHd,EAAA,CAAAG,YAAA,EAAM;;;;IANDH,EAAA,CAAAM,SAAA,EAEA;IAFAN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAE,CAAA,YAAAC,MAAA,IAAAH,MAAA,CAAAE,CAAA,YAAAC,MAAA,aAEA;;;;;IAcDX,EAAA,CAAAe,uBAAA,GAAuC;IACrCf,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAS,KAAA,CAAAC,EAAA,CAAiB;IAACjB,EAAA,CAAAM,SAAA,EAAa;IAAbN,EAAA,CAAAkB,iBAAA,CAAAF,KAAA,CAAAG,IAAA,CAAa;;;ADpFnD,OAAM,MAAOC,gBAAgB;EAgB3BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAlBhB,KAAAC,YAAY,GAAG,IAAI3B,OAAO,EAAQ;IACnC,KAAA4B,QAAQ,GAAQ,IAAI;IACpB,KAAAC,KAAK,GAAe,EAAE;IACtB,KAAAC,WAAW,GAAQ,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MACtCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAACkC,QAAQ,CAAC,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACkC,QAAQ,CAAC,CAAC;MACrCE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAACkC,QAAQ,CAAC,CAAC;MAClCG,OAAO,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACkC,QAAQ,CAAC,CAAC;MACpCI,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;IACK,KAAA5B,SAAS,GAAG,KAAK;IACjB,KAAA6B,MAAM,GAAG,KAAK;EAMlB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACjB,cAAc,CAACkB,OAAO,CACxBC,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAClCkB,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAIA,IAAI,EAAE;QACR,IAAI,CAAClB,QAAQ,GAAGkB,IAAI;QACpB,IAAI,CAACC,gBAAgB,EAAE;MACzB;IACF,CAAC,CAAC;EACN;EAEAL,YAAYA,CAAA;IACV,IAAI,CAACjB,cAAc,CAChBiB,YAAY,EAAE,CACdE,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAClCkB,SAAS,CAAC;MACTG,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAM;UAAEpB;QAAK,CAAE,GAAQoB,GAAG;QAC1B,IAAI,CAACpB,KAAK,GAAGA,KAAK,IAAI,EAAE;MAC1B,CAAC;MACDqB,KAAK,EAAGD,GAAQ,IAAI;QAClB,IAAI,CAACvB,cAAc,CAACyB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAACjB,WAAW,CAACwB,UAAU,CAAC,IAAI,CAAC1B,QAAQ,CAAC;IAC1C,IAAI,CAACE,WAAW,CAACwB,UAAU,CAAC;MAAEf,IAAI,EAAE,IAAI,CAACX,QAAQ,CAACW,IAAI,CAACpB;IAAE,CAAE,CAAC;EAC9D;EAEA,IAAIP,CAACA,CAAA;IACH,OAAO,IAAI,CAACkB,WAAW,CAACyB,QAAQ;EAClC;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC7C,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACmB,WAAW,CAAC2B,OAAO,EAAE;MAC5B;IACF;IAEA,IAAI,CAACjB,MAAM,GAAG,IAAI;IAClB,MAAM;MAAER,SAAS;MAAEE,QAAQ;MAAEC,KAAK;MAAEC,OAAO;MAAEC,SAAS;MAAEC,OAAO;MAAEC;IAAI,CAAE,GACrE,IAAI,CAACT,WAAW,CAAC4B,KAAK;IACxB,MAAMC,OAAO,GAAG;MACd3B,SAAS;MACTE,QAAQ;MACRC,KAAK;MACLC,OAAO;MACPC,SAAS;MACTC,OAAO;MACPC;KACD;IACD,IAAI,CAACd,cAAc,CAChBmC,UAAU,CAAC,IAAI,CAAChC,QAAQ,CAACT,EAAE,EAAEwC,OAAO,CAAC,CACrCf,IAAI,CAAC3C,SAAS,CAAC,IAAI,CAAC0B,YAAY,CAAC,CAAC,CAClCkB,SAAS,CAAC;MACTG,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACvB,cAAc,CAACyB,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC;MACDH,KAAK,EAAGA,KAAU,IAAI;QACpBW,OAAO,CAACX,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D;KACD,CAAC;EACN;EAEAY,WAAWA,CAAA;IACT,IAAI,CAACnC,YAAY,CAACqB,IAAI,EAAE;IACxB,IAAI,CAACrB,YAAY,CAACoC,QAAQ,EAAE;EAC9B;;;uBAnGWzC,gBAAgB,EAAApB,EAAA,CAAA8D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhE,EAAA,CAAA8D,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlE,EAAA,CAAA8D,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhBhD,gBAAgB;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRvB3E,EAHN,CAAAC,cAAA,cAAgC,aACP,aACQ,cAC6B;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzEH,EAAA,CAAAC,cAAA,cAAsD;UACpDD,EAAA,CAAA6E,SAAA,eAKE;UACJ7E,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAI,UAAA,IAAA0E,+BAAA,iBAAyE;UAW3E9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,aAA6B,cAC6B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAAC,cAAA,eAAsD;UACpDD,EAAA,CAAA6E,SAAA,gBAKE;UACJ7E,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAI,UAAA,KAAA2E,gCAAA,iBAAwE;UAW1E/E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,eAAsD;UACpDD,EAAA,CAAA6E,SAAA,gBAKE;UACJ7E,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAI,UAAA,KAAA4E,gCAAA,iBAAqE;UASvEhF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtEH,EAAA,CAAAC,cAAA,eAAsD;UACpDD,EAAA,CAAA6E,SAAA,gBAKE;UACJ7E,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAI,UAAA,KAAA6E,gCAAA,iBAAuE;UASzEjF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEjEH,EADF,CAAAC,cAAA,eAAsD,kBAKnD;UACCD,EAAA,CAAAI,UAAA,KAAA8E,yCAAA,2BAAuC;UAK7ClF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;UAEJH,EADF,CAAAC,cAAA,eAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAA6E,SAAA,yBAA2D;UAC7D7E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA6B,eAC6B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtEH,EAAA,CAAA6E,SAAA,yBAAyD;UAC3D7E,EAAA,CAAAG,YAAA,EAAM;UAGFH,EAFJ,CAAAC,cAAA,eAA8B,gBACiC,oBAM1D;UADCD,EAAA,CAAAmF,UAAA,mBAAAC,qDAAA;YAAA,OAASR,GAAA,CAAAtB,iBAAA,EAAmB;UAAA,EAAC;UAKvCtD,EAJS,CAAAG,YAAA,EAAW,EACP,EACH,EACF,EACD;;;UAvHDH,EAAA,CAAAO,UAAA,cAAAqE,GAAA,CAAAhD,WAAA,CAAyB;UASrB5B,EAAA,CAAAM,SAAA,GAAgE;UAAhEN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAqF,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,cAAAC,MAAA,EAAgE;UAG9DX,EAAA,CAAAM,SAAA,EAAwC;UAAxCN,EAAA,CAAAO,UAAA,SAAAqE,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,cAAAC,MAAA,CAAwC;UAmB1CX,EAAA,CAAAM,SAAA,GAA+D;UAA/DN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAqF,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,aAAAC,MAAA,EAA+D;UAG7DX,EAAA,CAAAM,SAAA,EAAuC;UAAvCN,EAAA,CAAAO,UAAA,SAAAqE,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,aAAAC,MAAA,CAAuC;UAmBzCX,EAAA,CAAAM,SAAA,GAA4D;UAA5DN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAqF,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,UAAAC,MAAA,EAA4D;UAG1DX,EAAA,CAAAM,SAAA,EAAoC;UAApCN,EAAA,CAAAO,UAAA,SAAAqE,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,UAAAC,MAAA,CAAoC;UAiBtCX,EAAA,CAAAM,SAAA,GAA8D;UAA9DN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAqF,eAAA,KAAAC,GAAA,EAAAV,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,YAAAC,MAAA,EAA8D;UAG5DX,EAAA,CAAAM,SAAA,EAAsC;UAAtCN,EAAA,CAAAO,UAAA,SAAAqE,GAAA,CAAAnE,SAAA,IAAAmE,GAAA,CAAAlE,CAAA,YAAAC,MAAA,CAAsC;UAkBXX,EAAA,CAAAM,SAAA,GAAQ;UAARN,EAAA,CAAAO,UAAA,YAAAqE,GAAA,CAAAjD,KAAA,CAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}