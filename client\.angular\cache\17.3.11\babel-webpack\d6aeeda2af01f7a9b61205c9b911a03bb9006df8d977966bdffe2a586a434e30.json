{"ast": null, "code": "import { AppSidebarComponent } from './app.sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/backoffice/layout/service/app.layout.service\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/ripple\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/styleclass\";\nimport * as i7 from \"./app.breadcrumb.component\";\nimport * as i8 from \"./app.sidebar.component\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"searchinput\"];\nconst _c2 = () => [\"/store\"];\nexport class AppTopbarComponent {\n  constructor(layoutService, el, authService) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.authService = authService;\n    this.searchActive = false;\n  }\n  activateSearch() {\n    this.searchActive = true;\n    setTimeout(() => {\n      this.searchInput.nativeElement.focus();\n    }, 100);\n  }\n  deactivateSearch() {\n    this.searchActive = false;\n  }\n  onMenuButtonClick() {\n    this.layoutService.onMenuToggle();\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  onSidebarButtonClick() {\n    this.layoutService.showSidebar();\n  }\n  logout() {\n    this.authService.doLogout();\n  }\n  static {\n    this.ɵfac = function AppTopbarComponent_Factory(t) {\n      return new (t || AppTopbarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppTopbarComponent,\n      selectors: [[\"app-topbar\"]],\n      viewQuery: function AppTopbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(AppSidebarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n        }\n      },\n      decls: 25,\n      vars: 3,\n      consts: [[\"menubutton\", \"\"], [\"profile\", \"\"], [1, \"layout-topbar\"], [1, \"topbar-start\"], [\"type\", \"button\", 1, \"topbar-menubutton\", \"p-link\", \"p-trigger\", 3, \"click\"], [1, \"pi\", \"pi-bars\"], [1, \"topbar-breadcrumb\"], [1, \"layout-topbar-menu-section\"], [1, \"topbar-end\"], [1, \"topbar-menu\"], [1, \"profile-item\", \"topbar-item\"], [\"pButton\", \"\", \"type\", \"button\", \"title\", \"Store\", \"icon\", \"pi pi-home\", 1, \"p-button-text\", \"p-button-secondary\", \"relative\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"routerLink\"], [\"title\", \"Profile\", 1, \"profile-item\", \"topbar-item\"], [\"pStyleClass\", \"@next\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", \"pRipple\", \"\", 1, \"cursor-pointer\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [1, \"topbar-menu\", \"active-topbar-menu\", \"h-2rem\", \"w-8rem\", \"z-5\", \"ng-hidden\", \"border-round\", \"mb-8\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mb-6\"], [\"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sign-out\", \"mr-2\"], [1, \"right-panel-button\", \"relative\", \"hidden\", \"lg:block\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Today\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"hidden\", \"md:block\", \"font-normal\", 2, \"width\", \"5.7rem\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-bookmark\", \"styleClass\", \"font-normal\", 1, \"layout-rightmenu-button\", \"block\", \"md:hidden\", 3, \"click\"]],\n      template: function AppTopbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"button\", 4, 0);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMenuButtonClick());\n          });\n          i0.ɵɵelement(4, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"backoffice-app-breadcrumb\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7);\n          i0.ɵɵelement(7, \"app-sidebar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\", 10);\n          i0.ɵɵelement(11, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\", 12, 1)(14, \"a\", 13);\n          i0.ɵɵelement(15, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"ul\", 15)(17, \"li\", 16)(18, \"a\", 17);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_a_click_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelement(19, \"i\", 18);\n          i0.ɵɵelementStart(20, \"span\");\n          i0.ɵɵtext(21, \"Logout\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(22, \"li\", 19)(23, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSidebarButtonClick());\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c2));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n        }\n      },\n      dependencies: [i3.ButtonDirective, i4.Ripple, i5.RouterLink, i6.StyleClass, i7.AppBreadcrumbComponent, i8.AppSidebarComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "AppTopbarComponent", "constructor", "layoutService", "el", "authService", "searchActive", "activateSearch", "setTimeout", "searchInput", "nativeElement", "focus", "deactivateSearch", "onMenuButtonClick", "onMenuToggle", "onConfigButtonClick", "showConfigSidebar", "onSidebarButtonClick", "showSidebar", "logout", "doLogout", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "AuthService", "selectors", "viewQuery", "AppTopbarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppTopbarComponent_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "ɵɵelementEnd", "AppTopbarComponent_Template_a_click_18_listener", "ɵɵtext", "AppTopbarComponent_Template_button_click_23_listener", "AppTopbarComponent_Template_button_click_24_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c2"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\layout\\app.topbar.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\layout\\app.topbar.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { LayoutService } from 'src/app/backoffice/layout/service/app.layout.service';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-topbar',\r\n  templateUrl: './app.topbar.component.html',\r\n})\r\nexport class AppTopbarComponent {\r\n  @ViewChild('menubutton') menuButton!: ElementRef;\r\n  @ViewChild('searchinput') searchInput!: ElementRef;\r\n  @ViewChild(AppSidebarComponent) appSidebar!: AppSidebarComponent;\r\n  searchActive: boolean = false;\r\n  constructor(\r\n    public layoutService: LayoutService,\r\n    public el: ElementRef,\r\n    private authService: AuthService\r\n  ) {}\r\n  activateSearch() {\r\n    this.searchActive = true;\r\n    setTimeout(() => {\r\n      this.searchInput.nativeElement.focus();\r\n    }, 100);\r\n  }\r\n\r\n  deactivateSearch() {\r\n    this.searchActive = false;\r\n  }\r\n  onMenuButtonClick() {\r\n    this.layoutService.onMenuToggle();\r\n  }\r\n\r\n  onConfigButtonClick() {\r\n    this.layoutService.showConfigSidebar();\r\n  }\r\n\r\n  onSidebarButtonClick() {\r\n    this.layoutService.showSidebar();\r\n  }\r\n\r\n  logout() {\r\n    this.authService.doLogout();\r\n  }\r\n}\r\n", "<div class=\"layout-topbar\">\r\n  <div class=\"topbar-start\">\r\n    <button #menubutton type=\"button\" class=\"topbar-menubutton p-link p-trigger\" (click)=\"onMenuButtonClick()\">\r\n      <i class=\"pi pi-bars\"></i>\r\n    </button>\r\n\r\n    <backoffice-app-breadcrumb class=\"topbar-breadcrumb\"></backoffice-app-breadcrumb>\r\n  </div>\r\n  <div class=\"layout-topbar-menu-section\">\r\n    <app-sidebar></app-sidebar>\r\n  </div>\r\n  <div class=\"topbar-end\">\r\n    <ul class=\"topbar-menu\">\r\n      <!-- <li class=\"hidden lg:block\">\r\n        <div\r\n          class=\"topbar-search\"\r\n          [ngClass]=\"{ 'topbar-search-active': searchActive }\"\r\n        >\r\n          <button\r\n            pButton\r\n            icon=\"pi pi-search\"\r\n            title=\"Search\"\r\n            class=\"topbar-searchbutton p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n            type=\"button\"\r\n            (click)=\"activateSearch()\"\r\n          ></button>\r\n          <div class=\"search-input-wrapper\">\r\n            <span class=\"p-input-icon-right\">\r\n              <input\r\n                #searchinput\r\n                type=\"text\"\r\n                pInputText\r\n                placeholder=\"Search\"\r\n                (blur)=\"deactivateSearch()\"\r\n                (keydown.escape)=\"deactivateSearch()\"\r\n              />\r\n              <i class=\"pi pi-search\"></i>\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </li> -->\r\n\r\n      <!-- <li class=\"profile-item topbar-item\">\r\n        <button\r\n          pButton\r\n          type=\"button\"\r\n          icon=\"pi pi-comment\"\r\n          title=\"Chat\"\r\n          class=\"p-button-text p-button-secondary relative text-color-secondary p-button-rounded flex-shrink-0\"\r\n        ></button>\r\n      </li> -->\r\n\r\n      <li class=\"profile-item topbar-item\">\r\n        <button pButton type=\"button\" [routerLink]=\"['/store']\" title=\"Store\" icon=\"pi pi-home\"\r\n          class=\"p-button-text p-button-secondary relative text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n      </li>\r\n\r\n      <!-- <li class=\"profile-item topbar-item\">\r\n        <button\r\n          pButton\r\n          type=\"button\"\r\n          icon=\"pi pi-bell\"\r\n          title=\"Notification\"\r\n          class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n        ></button>\r\n      </li> -->\r\n\r\n      <!-- <li class=\"ml-3\">\r\n        <button\r\n          pButton\r\n          type=\"button\"\r\n          icon=\"pi pi-cog\"\r\n          title=\"Setting\"\r\n          class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n          (click)=\"onConfigButtonClick()\"\r\n        ></button>\r\n      </li> -->\r\n\r\n      <li #profile class=\"profile-item topbar-item\" title=\"Profile\">\r\n        <a pStyleClass=\"@next\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\" leaveToClass=\"ng-hidden\"\r\n          leaveActiveClass=\"px-fadeout\" [hideOnOutsideClick]=\"true\" pRipple class=\"cursor-pointer\">\r\n          <i class=\"pi pi-fw pi-user\"></i>\r\n        </a>\r\n\r\n        <ul class=\"topbar-menu active-topbar-menu h-2rem w-8rem z-5 ng-hidden border-round mb-8\">\r\n          <li role=\"menuitem\" class=\"m-0 mb-6\">\r\n            <a (click)=\"logout()\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n              pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n              leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n              <i class=\"pi pi-fw pi-sign-out mr-2\"></i>\r\n              <span>Logout</span>\r\n            </a>\r\n          </li>\r\n        </ul>\r\n      </li>\r\n\r\n      <li class=\"right-panel-button relative hidden lg:block\">\r\n        <button pButton type=\"button\" label=\"Today\" style=\"width: 5.7rem\" icon=\"pi pi-bookmark\"\r\n          class=\"layout-rightmenu-button hidden md:block font-normal\" styleClass=\"font-normal\"\r\n          (click)=\"onSidebarButtonClick()\"></button>\r\n        <button pButton type=\"button\" icon=\"pi pi-bookmark\" styleClass=\"font-normal\"\r\n          class=\"layout-rightmenu-button block md:hidden\" (click)=\"onSidebarButtonClick()\"></button>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n</div>"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yBAAyB;;;;;;;;;;;;;AAO7D,OAAM,MAAOC,kBAAkB;EAK7BC,YACSC,aAA4B,EAC5BC,EAAc,EACbC,WAAwB;IAFzB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACD,KAAAC,WAAW,GAAXA,WAAW;IAJrB,KAAAC,YAAY,GAAY,KAAK;EAK1B;EACHC,cAAcA,CAAA;IACZ,IAAI,CAACD,YAAY,GAAG,IAAI;IACxBE,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,KAAK,EAAE;IACxC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACN,YAAY,GAAG,KAAK;EAC3B;EACAO,iBAAiBA,CAAA;IACf,IAAI,CAACV,aAAa,CAACW,YAAY,EAAE;EACnC;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACZ,aAAa,CAACa,iBAAiB,EAAE;EACxC;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAACd,aAAa,CAACe,WAAW,EAAE;EAClC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACd,WAAW,CAACe,QAAQ,EAAE;EAC7B;;;uBAlCWnB,kBAAkB,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlB1B,kBAAkB;MAAA2B,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;yBAGlB/B,mBAAmB;;;;;;;;;;;;;;;UCV5BqB,EAFJ,CAAAY,cAAA,aAA2B,aACC,mBACmF;UAA9BZ,EAAA,CAAAa,UAAA,mBAAAC,oDAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;YAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASN,GAAA,CAAAnB,iBAAA,EAAmB;UAAA,EAAC;UACxGQ,EAAA,CAAAkB,SAAA,WAA0B;UAC5BlB,EAAA,CAAAmB,YAAA,EAAS;UAETnB,EAAA,CAAAkB,SAAA,mCAAiF;UACnFlB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAY,cAAA,aAAwC;UACtCZ,EAAA,CAAAkB,SAAA,kBAA2B;UAC7BlB,EAAA,CAAAmB,YAAA,EAAM;UA0CFnB,EAzCJ,CAAAY,cAAA,aAAwB,YACE,cAwCe;UACnCZ,EAAA,CAAAkB,SAAA,kBACiH;UACnHlB,EAAA,CAAAmB,YAAA,EAAK;UAwBHnB,EADF,CAAAY,cAAA,iBAA8D,aAE+B;UACzFZ,EAAA,CAAAkB,SAAA,aAAgC;UAClClB,EAAA,CAAAmB,YAAA,EAAI;UAIAnB,EAFJ,CAAAY,cAAA,cAAyF,cAClD,aAGsB;UAFtDZ,EAAA,CAAAa,UAAA,mBAAAO,gDAAA;YAAApB,EAAA,CAAAe,aAAA,CAAAC,GAAA;YAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASN,GAAA,CAAAb,MAAA,EAAQ;UAAA,EAAC;UAGnBE,EAAA,CAAAkB,SAAA,aAAyC;UACzClB,EAAA,CAAAY,cAAA,YAAM;UAAAZ,EAAA,CAAAqB,MAAA,cAAM;UAIpBrB,EAJoB,CAAAmB,YAAA,EAAO,EACjB,EACD,EACF,EACF;UAGHnB,EADF,CAAAY,cAAA,cAAwD,kBAGnB;UAAjCZ,EAAA,CAAAa,UAAA,mBAAAS,qDAAA;YAAAtB,EAAA,CAAAe,aAAA,CAAAC,GAAA;YAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASN,GAAA,CAAAf,oBAAA,EAAsB;UAAA,EAAC;UAACI,EAAA,CAAAmB,YAAA,EAAS;UAC5CnB,EAAA,CAAAY,cAAA,kBACmF;UAAjCZ,EAAA,CAAAa,UAAA,mBAAAU,qDAAA;YAAAvB,EAAA,CAAAe,aAAA,CAAAC,GAAA;YAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASN,GAAA,CAAAf,oBAAA,EAAsB;UAAA,EAAC;UAI1FI,EAJ2F,CAAAmB,YAAA,EAAS,EACzF,EACF,EACD,EACF;;;UApDgCnB,EAAA,CAAAwB,SAAA,IAAyB;UAAzBxB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAyB;UA2BvB3B,EAAA,CAAAwB,SAAA,GAA2B;UAA3BxB,EAAA,CAAAyB,UAAA,4BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}