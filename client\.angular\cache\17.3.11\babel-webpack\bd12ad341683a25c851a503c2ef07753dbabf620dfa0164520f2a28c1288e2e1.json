{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { PimComponent } from './pim.component';\nimport { PimRoutingModule } from './pim-routing.module';\nimport { ToastModule } from 'primeng/toast';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { ListboxModule } from 'primeng/listbox';\nimport { DialogModule } from 'primeng/dialog';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageService } from 'primeng/api';\nimport { ConfirmationService } from 'primeng/api';\nimport { ConfigurationComponent } from './configuration/configuration.component';\nimport * as i0 from \"@angular/core\";\nexport class PimModule {\n  static {\n    this.ɵfac = function PimModule_Factory(t) {\n      return new (t || PimModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PimModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, PimRoutingModule, ToastModule, FormsModule, TableModule, ButtonModule, ListboxModule, DialogModule, InputTextModule, ReactiveFormsModule, CheckboxModule, ConfirmDialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PimModule, {\n    declarations: [PimComponent, ConfigurationComponent],\n    imports: [CommonModule, PimRoutingModule, ToastModule, FormsModule, TableModule, ButtonModule, ListboxModule, DialogModule, InputTextModule, ReactiveFormsModule, CheckboxModule, ConfirmDialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "PimComponent", "PimRoutingModule", "ToastModule", "FormsModule", "ReactiveFormsModule", "TableModule", "ButtonModule", "ListboxModule", "DialogModule", "InputTextModule", "CheckboxModule", "ConfirmDialogModule", "MessageService", "ConfirmationService", "ConfigurationComponent", "PimModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\pim\\pim.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { PimComponent } from './pim.component';\r\nimport { PimRoutingModule } from './pim-routing.module';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { ListboxModule } from 'primeng/listbox';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { MessageService } from 'primeng/api';\r\nimport { ConfirmationService } from 'primeng/api';\r\nimport { ConfigurationComponent } from './configuration/configuration.component';\r\n\r\n@NgModule({\r\n  declarations: [PimComponent, ConfigurationComponent],\r\n  imports: [\r\n    CommonModule,\r\n    PimRoutingModule,\r\n    ToastModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    ListboxModule,\r\n    DialogModule,\r\n    InputTextModule,\r\n    ReactiveFormsModule,\r\n    CheckboxModule,\r\n    ConfirmDialogModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class PimModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,mBAAmB,QAAQ,aAAa;AACjD,SAASC,sBAAsB,QAAQ,yCAAyC;;AAoBhF,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA;IAAS;EAAA;;;iBAFT,CAACH,cAAc,EAAEC,mBAAmB,CAAC;MAAAG,OAAA,GAb9CjB,YAAY,EACZE,gBAAgB,EAChBC,WAAW,EACXC,WAAW,EACXE,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfL,mBAAmB,EACnBM,cAAc,EACdC,mBAAmB;IAAA;EAAA;;;2EAIVI,SAAS;IAAAE,YAAA,GAjBLjB,YAAY,EAAEc,sBAAsB;IAAAE,OAAA,GAEjDjB,YAAY,EACZE,gBAAgB,EAChBC,WAAW,EACXC,WAAW,EACXE,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfL,mBAAmB,EACnBM,cAAc,EACdC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}