{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\nvar GetIntrinsic = require('get-intrinsic');\nvar setFunctionLength = require('set-function-length');\nvar $TypeError = require('es-errors/type');\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\nvar $defineProperty = require('es-define-property');\nvar $max = GetIntrinsic('%Math.max%');\nmodule.exports = function callBind(originalFunction) {\n  if (typeof originalFunction !== 'function') {\n    throw new $TypeError('a function is required');\n  }\n  var func = $reflectApply(bind, $call, arguments);\n  return setFunctionLength(func, 1 + $max(0, originalFunction.length - (arguments.length - 1)), true);\n};\nvar applyBind = function applyBind() {\n  return $reflectApply(bind, $apply, arguments);\n};\nif ($defineProperty) {\n  $defineProperty(module.exports, 'apply', {\n    value: applyBind\n  });\n} else {\n  module.exports.apply = applyBind;\n}", "map": {"version": 3, "names": ["bind", "require", "GetIntrinsic", "setFunctionLength", "$TypeError", "$apply", "$call", "$reflectApply", "call", "$defineProperty", "$max", "module", "exports", "callBind", "originalFunction", "func", "arguments", "length", "applyBind", "value", "apply"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/call-bind/index.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\nvar GetIntrinsic = require('get-intrinsic');\nvar setFunctionLength = require('set-function-length');\n\nvar $TypeError = require('es-errors/type');\nvar $apply = GetIntrinsic('%Function.prototype.apply%');\nvar $call = GetIntrinsic('%Function.prototype.call%');\nvar $reflectApply = GetIntrinsic('%Reflect.apply%', true) || bind.call($call, $apply);\n\nvar $defineProperty = require('es-define-property');\nvar $max = GetIntrinsic('%Math.max%');\n\nmodule.exports = function callBind(originalFunction) {\n\tif (typeof originalFunction !== 'function') {\n\t\tthrow new $TypeError('a function is required');\n\t}\n\tvar func = $reflectApply(bind, $call, arguments);\n\treturn setFunctionLength(\n\t\tfunc,\n\t\t1 + $max(0, originalFunction.length - (arguments.length - 1)),\n\t\ttrue\n\t);\n};\n\nvar applyBind = function applyBind() {\n\treturn $reflectApply(bind, $apply, arguments);\n};\n\nif ($defineProperty) {\n\t$defineProperty(module.exports, 'apply', { value: applyBind });\n} else {\n\tmodule.exports.apply = applyBind;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;AACnC,IAAIC,YAAY,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIE,iBAAiB,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAEtD,IAAIG,UAAU,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAII,MAAM,GAAGH,YAAY,CAAC,4BAA4B,CAAC;AACvD,IAAII,KAAK,GAAGJ,YAAY,CAAC,2BAA2B,CAAC;AACrD,IAAIK,aAAa,GAAGL,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAIF,IAAI,CAACQ,IAAI,CAACF,KAAK,EAAED,MAAM,CAAC;AAErF,IAAII,eAAe,GAAGR,OAAO,CAAC,oBAAoB,CAAC;AACnD,IAAIS,IAAI,GAAGR,YAAY,CAAC,YAAY,CAAC;AAErCS,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,gBAAgB,EAAE;EACpD,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;IAC3C,MAAM,IAAIV,UAAU,CAAC,wBAAwB,CAAC;EAC/C;EACA,IAAIW,IAAI,GAAGR,aAAa,CAACP,IAAI,EAAEM,KAAK,EAAEU,SAAS,CAAC;EAChD,OAAOb,iBAAiB,CACvBY,IAAI,EACJ,CAAC,GAAGL,IAAI,CAAC,CAAC,EAAEI,gBAAgB,CAACG,MAAM,IAAID,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,EAC7D,IACD,CAAC;AACF,CAAC;AAED,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EACpC,OAAOX,aAAa,CAACP,IAAI,EAAEK,MAAM,EAAEW,SAAS,CAAC;AAC9C,CAAC;AAED,IAAIP,eAAe,EAAE;EACpBA,eAAe,CAACE,MAAM,CAACC,OAAO,EAAE,OAAO,EAAE;IAAEO,KAAK,EAAED;EAAU,CAAC,CAAC;AAC/D,CAAC,MAAM;EACNP,MAAM,CAACC,OAAO,CAACQ,KAAK,GAAGF,SAAS;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}