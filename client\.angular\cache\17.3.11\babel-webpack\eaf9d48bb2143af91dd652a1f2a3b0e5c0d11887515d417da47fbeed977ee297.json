{"ast": null, "code": "import { AppSidebarComponent } from './app.sidebar.component';\nimport { environment } from 'src/environments/environment';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { SelectCustomerComponent } from './select-customer/select-customer.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/layout/service/app.layout.service\";\nimport * as i2 from \"primeng/dynamicdialog\";\nimport * as i3 from \"src/app/core/authentication/auth.service\";\nimport * as i4 from \"./service/manage-user.service\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/ripple\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"primeng/styleclass\";\nimport * as i9 from \"./app.breadcrumb.component\";\nimport * as i10 from \"./app.sidebar.component\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"searchinput\"];\nconst _c2 = () => [\"/store/profile\"];\nconst _c3 = () => [\"/backoffice\"];\nexport class AppTopbarComponent {\n  constructor(layoutService, el, dialog, authService, manageUserService) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.dialog = dialog;\n    this.authService = authService;\n    this.manageUserService = manageUserService;\n    this.searchActive = false;\n    this.loggedInUser = null;\n    this.total_customer = 0;\n  }\n  ngOnInit() {\n    this.loggedInUser = this.authService.userDetail;\n    this.manageUserService.getLoggedInUserCustomersCount().subscribe({\n      next: res => {\n        this.total_customer = res || 0;\n      }\n    });\n  }\n  openDialog() {\n    this.dialog.open(SelectCustomerComponent, {\n      header: 'Select Customer',\n      width: '80vw'\n    });\n  }\n  activateSearch() {\n    this.searchActive = true;\n    setTimeout(() => {\n      this.searchInput.nativeElement.focus();\n    }, 100);\n  }\n  deactivateSearch() {\n    this.searchActive = false;\n  }\n  onMenuButtonClick() {\n    this.layoutService.onMenuToggle();\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  onSidebarButtonClick() {\n    this.layoutService.showSidebar();\n  }\n  logout() {\n    this.authService.doLogout();\n  }\n  adaptUI() {\n    window.open(environment.cmsApiEndpoint + '/admin', '_blank');\n  }\n  static {\n    this.ɵfac = function AppTopbarComponent_Factory(t) {\n      return new (t || AppTopbarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.DialogService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ManageUserService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppTopbarComponent,\n      selectors: [[\"app-topbar\"]],\n      viewQuery: function AppTopbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(AppSidebarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n        }\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService])],\n      decls: 51,\n      vars: 8,\n      consts: [[\"menubutton\", \"\"], [\"profile\", \"\"], [1, \"layout-topbar\"], [1, \"topbar-start\"], [\"type\", \"button\", 1, \"topbar-menubutton\", \"p-link\", \"p-trigger\", 3, \"click\"], [1, \"pi\", \"pi-bars\"], [1, \"topbar-breadcrumb\"], [1, \"layout-topbar-menu-section\"], [1, \"topbar-end\"], [1, \"topbar-menu\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"p-button-rounded\", \"flex\", \"gap-3\", 3, \"click\"], [1, \"ml-1\", \"text-lg\", \"border-primary\", \"border-circle\", \"border-1\", \"w-2rem\", \"h-2rem\", \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-white\", \"text-primary\"], [1, \"flex\", \"flex-column\", \"w-15rem\", \"align-items-start\"], [1, \"text-overflow-ellipsis\", \"white-space-nowrap\", \"overflow-hidden\", \"w-full\", \"text-left\", 3, \"title\"], [1, \"ml-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-cog\", 1, \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"profile-item\", \"topbar-item\", \"relative\"], [\"pStyleClass\", \"@next\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", \"pRipple\", \"\", 1, \"cursor-pointer\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [1, \"topbar-menu\", \"active-topbar-menu\", \"p-4\", \"w-15rem\", \"z-5\", \"ng-hidden\", \"border-round\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mb-3\"], [\"href\", \"javascript: void(0)\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"routerLink\"], [1, \"pi\", \"pi-fw\", \"pi-user\", \"mr-2\"], [1, \"pi\", \"pi-fw\", \"pi-desktop\", \"mr-2\"], [\"href\", \"javascript: void(0)\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sliders-h\", \"mr-2\"], [\"href\", \"#\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\"], [1, \"pi\", \"pi-fw\", \"pi-cog\", \"mr-2\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mt-3\", \"pt-3\", \"border-top-1\", \"logout-btn\"], [\"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", \"font-medium\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sign-out\", \"mr-2\"]],\n      template: function AppTopbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"button\", 4, 0);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMenuButtonClick());\n          });\n          i0.ɵɵelement(4, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"app-breadcrumb\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7);\n          i0.ɵɵelement(7, \"app-sidebar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ul\", 9)(10, \"li\")(11, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openDialog());\n          });\n          i0.ɵɵelementStart(12, \"span\", 11);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"small\");\n          i0.ɵɵtext(16, \"Switch Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 13);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"li\", 14)(20, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onConfigButtonClick());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"li\", 16, 1)(23, \"a\", 17);\n          i0.ɵɵelement(24, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"ul\", 19)(26, \"li\", 20)(27, \"a\", 21);\n          i0.ɵɵelement(28, \"i\", 22);\n          i0.ɵɵelementStart(29, \"span\");\n          i0.ɵɵtext(30, \"Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"li\", 20)(32, \"a\", 21);\n          i0.ɵɵelement(33, \"i\", 23);\n          i0.ɵɵelementStart(34, \"span\");\n          i0.ɵɵtext(35, \"Backoffice\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"li\", 20)(37, \"a\", 24);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_a_click_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.adaptUI());\n          });\n          i0.ɵɵelement(38, \"i\", 25);\n          i0.ɵɵelementStart(39, \"span\");\n          i0.ɵɵtext(40, \"Adapt UI\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"li\", 20)(42, \"a\", 26);\n          i0.ɵɵelement(43, \"i\", 27);\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \"Settings\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"li\", 28)(47, \"a\", 29);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_a_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelement(48, \"i\", 30);\n          i0.ɵɵelementStart(49, \"span\");\n          i0.ɵɵtext(50, \"Logout\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate(ctx.total_customer);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"title\", (ctx.loggedInUser == null ? null : ctx.loggedInUser.customer == null ? null : ctx.loggedInUser.customer.bp_id) + \" - \" + (ctx.loggedInUser == null ? null : ctx.loggedInUser.customer == null ? null : ctx.loggedInUser.customer.customer_name));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate((ctx.loggedInUser == null ? null : ctx.loggedInUser.customer == null ? null : ctx.loggedInUser.customer.bp_id) + \" - \" + (ctx.loggedInUser == null ? null : ctx.loggedInUser.customer == null ? null : ctx.loggedInUser.customer.customer_name));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(7, _c3));\n        }\n      },\n      dependencies: [i5.ButtonDirective, i6.Ripple, i7.RouterLink, i8.StyleClass, i9.AppBreadcrumbComponent, i10.AppSidebarComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "environment", "DialogService", "SelectCustomerComponent", "AppTopbarComponent", "constructor", "layoutService", "el", "dialog", "authService", "manageUserService", "searchActive", "loggedInUser", "total_customer", "ngOnInit", "userDetail", "getLoggedInUserCustomersCount", "subscribe", "next", "res", "openDialog", "open", "header", "width", "activateSearch", "setTimeout", "searchInput", "nativeElement", "focus", "deactivateSearch", "onMenuButtonClick", "onMenuToggle", "onConfigButtonClick", "showConfigSidebar", "onSidebarButtonClick", "showSidebar", "logout", "doLogout", "adaptUI", "window", "cmsApiEndpoint", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "i3", "AuthService", "i4", "ManageUserService", "selectors", "viewQuery", "AppTopbarComponent_Query", "rf", "ctx", "decls", "vars", "consts", "template", "AppTopbarComponent_Template", "ɵɵelementStart", "ɵɵlistener", "AppTopbarComponent_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "ɵɵelementEnd", "AppTopbarComponent_Template_button_click_11_listener", "ɵɵtext", "AppTopbarComponent_Template_button_click_20_listener", "AppTopbarComponent_Template_a_click_37_listener", "AppTopbarComponent_Template_a_click_47_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty", "customer", "bp_id", "customer_name", "ɵɵpureFunction0", "_c2", "_c3"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.topbar.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.topbar.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { LayoutService } from 'src/app/store/layout/service/app.layout.service';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { ManageUserService } from './service/manage-user.service';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { SelectCustomerComponent } from './select-customer/select-customer.component';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    templateUrl: './app.topbar.component.html',\r\n    providers: [DialogService]\r\n})\r\nexport class AppTopbarComponent implements OnInit {\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n    @ViewChild('searchinput') searchInput!: ElementRef;\r\n    @ViewChild(AppSidebarComponent) appSidebar!: AppSidebarComponent;\r\n    searchActive: boolean = false;\r\n    public loggedInUser: any = null;\r\n    total_customer = 0;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n        public dialog: DialogService,\r\n        private authService: AuthService,\r\n        private manageUserService: ManageUserService\r\n    ) { }\r\n\r\n    ngOnInit(): void {\r\n        this.loggedInUser = this.authService.userDetail;\r\n        this.manageUserService.getLoggedInUserCustomersCount().subscribe({\r\n            next: (res: any) => {\r\n                this.total_customer = res || 0;\r\n            }\r\n        });\r\n    }\r\n\r\n    openDialog() {\r\n        this.dialog.open(SelectCustomerComponent, {\r\n          header: 'Select Customer',\r\n          width: '80vw'\r\n        });\r\n      }\r\n\r\n    activateSearch() {\r\n        this.searchActive = true;\r\n        setTimeout(() => {\r\n            this.searchInput.nativeElement.focus();\r\n        }, 100);\r\n    }\r\n\r\n    deactivateSearch() {\r\n        this.searchActive = false;\r\n    }\r\n    onMenuButtonClick() {\r\n        this.layoutService.onMenuToggle();\r\n    }\r\n\r\n    onConfigButtonClick() {\r\n        this.layoutService.showConfigSidebar();\r\n    }\r\n\r\n    onSidebarButtonClick() {\r\n        this.layoutService.showSidebar();\r\n    }\r\n\r\n    logout() {\r\n        this.authService.doLogout();\r\n    }\r\n\r\n    adaptUI() {\r\n        window.open(environment.cmsApiEndpoint + '/admin', '_blank');\r\n    }\r\n}", "<div class=\"layout-topbar\">\r\n    <div class=\"topbar-start\">\r\n        <button #menubutton type=\"button\" class=\"topbar-menubutton p-link p-trigger\" (click)=\"onMenuButtonClick()\">\r\n            <i class=\"pi pi-bars\"></i>\r\n        </button>\r\n\r\n        <app-breadcrumb class=\"topbar-breadcrumb\"></app-breadcrumb>\r\n    </div>\r\n    <div class=\"layout-topbar-menu-section\">\r\n        <app-sidebar></app-sidebar>\r\n    </div>\r\n    <div class=\"topbar-end\">\r\n        <ul class=\"topbar-menu  \">\r\n            <!-- <li class=\"hidden lg:block\">\r\n                <div class=\"topbar-search\" [ngClass]=\"{'topbar-search-active': searchActive}\">\r\n                    <button pButton icon=\"pi pi-search\"\r\n                        class=\"topbar-searchbutton p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n                        type=\"button\" (click)=\"activateSearch()\"></button>\r\n                    <div class=\"search-input-wrapper\">\r\n                        <span class=\"p-input-icon-right\">\r\n                            <input #searchinput type=\"text\" pInputText placeholder=\"Search\" (blur)=\"deactivateSearch()\"\r\n                                (keydown.escape)=\"deactivateSearch()\" />\r\n                            <i class=\"pi pi-search\"></i>\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </li>\r\n\r\n            <li class=\"profile-item topbar-item \">\r\n                <button pButton type=\"button\" icon=\"pi pi-bell\"\r\n                    class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n            </li>\r\n\r\n            <li class=\"profile-item topbar-item \">\r\n                <button pButton type=\"button\" icon=\"pi pi-comment\"\r\n                    class=\"p-button-text p-button-secondary relative text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n            </li> -->\r\n\r\n            <li>\r\n                <button type=\"button\" pButton class=\"p-button-rounded flex gap-3\" (click)=\"openDialog()\"\r\n                >\r\n                <span\r\n                    class=\"ml-1 text-lg border-primary border-circle border-1 w-2rem h-2rem flex justify-content-center align-items-center bg-white text-primary\">{{\r\n                    total_customer }}</span>\r\n                <div class=\"flex flex-column w-15rem align-items-start\">\r\n                    <small>Switch Account</small>\r\n                    <p class=\"text-overflow-ellipsis white-space-nowrap overflow-hidden w-full text-left\" [title]=\"loggedInUser?.customer?.bp_id + ' - ' + loggedInUser?.customer?.customer_name\">{{loggedInUser?.customer?.bp_id + ' - ' + loggedInUser?.customer?.customer_name}}</p>\r\n                </div>\r\n            </button>\r\n            </li>\r\n\r\n            <li class=\"ml-3\">\r\n                <button pButton type=\"button\" icon=\"pi pi-cog\"\r\n                    class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n                    (click)=\"onConfigButtonClick()\"></button>\r\n            </li>\r\n\r\n            <li #profile class=\"profile-item topbar-item relative\">\r\n                <a pStyleClass=\"@next\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\" leaveToClass=\"ng-hidden\"\r\n                    leaveActiveClass=\"px-fadeout\" [hideOnOutsideClick]=\"true\" pRipple class=\"cursor-pointer\">\r\n                    <i class=\"pi pi-fw pi-user\"></i>\r\n                </a>\r\n\r\n                <ul class=\"topbar-menu active-topbar-menu p-4 w-15rem z-5 ng-hidden border-round\">\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"javascript: void(0)\" [routerLink]=\"['/store/profile']\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-user mr-2\"></i>\r\n                            <span>Profile</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"javascript: void(0)\" [routerLink]=\"['/backoffice']\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-desktop mr-2\"></i>\r\n                            <span>Backoffice</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"javascript: void(0)\" (click)=\"adaptUI()\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-sliders-h mr-2\"></i>\r\n                            <span>Adapt UI</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"#\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-cog mr-2\"></i>\r\n                            <span>Settings</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mt-3 pt-3 border-top-1 logout-btn\">\r\n                        <a (click)=\"logout()\" class=\"flex align-items-center hover:text-primary-500 transition-duration-200 font-medium cursor-pointer\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-sign-out mr-2\"></i>\r\n                            <span>Logout</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n            </li>\r\n\r\n            <!-- <li class=\"right-panel-button relative hidden lg:block\">\r\n                <button pButton type=\"button\" label=\"Today\" style=\"width:5.7rem\" icon=\"pi pi-bookmark\"\r\n                    class=\"layout-rightmenu-button hidden md:block font-normal\" styleClass=\"font-normal\"\r\n                    (click)=\"onSidebarButtonClick()\"></button>\r\n                <button pButton type=\"button\" icon=\"pi pi-bookmark\" styleClass=\"font-normal\"\r\n                    class=\"layout-rightmenu-button block md:hidden\" (click)=\"onSidebarButtonClick()\"></button>\r\n            </li> -->\r\n        </ul>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yBAAyB;AAE7D,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,uBAAuB,QAAQ,6CAA6C;;;;;;;;;;;;;;;;AAOrF,OAAM,MAAOC,kBAAkB;EAS3BC,YACWC,aAA4B,EAC5BC,EAAc,EACdC,MAAqB,EACpBC,WAAwB,EACxBC,iBAAoC;IAJrC,KAAAJ,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAT7B,KAAAC,YAAY,GAAY,KAAK;IACtB,KAAAC,YAAY,GAAQ,IAAI;IAC/B,KAAAC,cAAc,GAAG,CAAC;EAQd;EAEJC,QAAQA,CAAA;IACJ,IAAI,CAACF,YAAY,GAAG,IAAI,CAACH,WAAW,CAACM,UAAU;IAC/C,IAAI,CAACL,iBAAiB,CAACM,6BAA6B,EAAE,CAACC,SAAS,CAAC;MAC7DC,IAAI,EAAGC,GAAQ,IAAI;QACf,IAAI,CAACN,cAAc,GAAGM,GAAG,IAAI,CAAC;MAClC;KACH,CAAC;EACN;EAEAC,UAAUA,CAAA;IACN,IAAI,CAACZ,MAAM,CAACa,IAAI,CAAClB,uBAAuB,EAAE;MACxCmB,MAAM,EAAE,iBAAiB;MACzBC,KAAK,EAAE;KACR,CAAC;EACJ;EAEFC,cAAcA,CAAA;IACV,IAAI,CAACb,YAAY,GAAG,IAAI;IACxBc,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,KAAK,EAAE;IAC1C,CAAC,EAAE,GAAG,CAAC;EACX;EAEAC,gBAAgBA,CAAA;IACZ,IAAI,CAAClB,YAAY,GAAG,KAAK;EAC7B;EACAmB,iBAAiBA,CAAA;IACb,IAAI,CAACxB,aAAa,CAACyB,YAAY,EAAE;EACrC;EAEAC,mBAAmBA,CAAA;IACf,IAAI,CAAC1B,aAAa,CAAC2B,iBAAiB,EAAE;EAC1C;EAEAC,oBAAoBA,CAAA;IAChB,IAAI,CAAC5B,aAAa,CAAC6B,WAAW,EAAE;EACpC;EAEAC,MAAMA,CAAA;IACF,IAAI,CAAC3B,WAAW,CAAC4B,QAAQ,EAAE;EAC/B;EAEAC,OAAOA,CAAA;IACHC,MAAM,CAAClB,IAAI,CAACpB,WAAW,CAACuC,cAAc,GAAG,QAAQ,EAAE,QAAQ,CAAC;EAChE;;;uBA7DSpC,kBAAkB,EAAAqC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAA5C,aAAA,GAAAuC,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;YAAlB9C,kBAAkB;MAAA+C,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;yBAIhBtD,mBAAmB;;;;;;;;;uCANnB,CAACE,aAAa,CAAC;MAAAsD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCVtBb,EAFR,CAAAoB,cAAA,aAA2B,aACG,mBACqF;UAA9BpB,EAAA,CAAAqB,UAAA,mBAAAC,oDAAA;YAAAtB,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASX,GAAA,CAAAzB,iBAAA,EAAmB;UAAA,EAAC;UACtGW,EAAA,CAAA0B,SAAA,WAA0B;UAC9B1B,EAAA,CAAA2B,YAAA,EAAS;UAET3B,EAAA,CAAA0B,SAAA,wBAA2D;UAC/D1B,EAAA,CAAA2B,YAAA,EAAM;UACN3B,EAAA,CAAAoB,cAAA,aAAwC;UACpCpB,EAAA,CAAA0B,SAAA,kBAA2B;UAC/B1B,EAAA,CAAA2B,YAAA,EAAM;UA6BM3B,EA5BZ,CAAAoB,cAAA,aAAwB,YACM,UA0BlB,kBAEC;UADiEpB,EAAA,CAAAqB,UAAA,mBAAAO,qDAAA;YAAA5B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASX,GAAA,CAAAnC,UAAA,EAAY;UAAA,EAAC;UAExFqB,EAAA,CAAAoB,cAAA,gBACkJ;UAAApB,EAAA,CAAA6B,MAAA,IAC7H;UAAA7B,EAAA,CAAA2B,YAAA,EAAO;UAExB3B,EADJ,CAAAoB,cAAA,eAAwD,aAC7C;UAAApB,EAAA,CAAA6B,MAAA,sBAAc;UAAA7B,EAAA,CAAA2B,YAAA,EAAQ;UAC7B3B,EAAA,CAAAoB,cAAA,aAA8K;UAAApB,EAAA,CAAA6B,MAAA,IAAiF;UAGvQ7B,EAHuQ,CAAA2B,YAAA,EAAI,EACjQ,EACD,EACJ;UAGD3B,EADJ,CAAAoB,cAAA,cAAiB,kBAGuB;UAAhCpB,EAAA,CAAAqB,UAAA,mBAAAS,qDAAA;YAAA9B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASX,GAAA,CAAAvB,mBAAA,EAAqB;UAAA,EAAC;UACvCS,EADwC,CAAA2B,YAAA,EAAS,EAC5C;UAGD3B,EADJ,CAAAoB,cAAA,iBAAuD,aAE0C;UACzFpB,EAAA,CAAA0B,SAAA,aAAgC;UACpC1B,EAAA,CAAA2B,YAAA,EAAI;UAII3B,EAFR,CAAAoB,cAAA,cAAkF,cACzC,aAG0B;UACvDpB,EAAA,CAAA0B,SAAA,aAAqC;UACrC1B,EAAA,CAAAoB,cAAA,YAAM;UAAApB,EAAA,CAAA6B,MAAA,eAAO;UAErB7B,EAFqB,CAAA2B,YAAA,EAAO,EACpB,EACH;UAED3B,EADJ,CAAAoB,cAAA,cAAqC,aAG0B;UACvDpB,EAAA,CAAA0B,SAAA,aAAwC;UACxC1B,EAAA,CAAAoB,cAAA,YAAM;UAAApB,EAAA,CAAA6B,MAAA,kBAAU;UAExB7B,EAFwB,CAAA2B,YAAA,EAAO,EACvB,EACH;UAED3B,EADJ,CAAAoB,cAAA,cAAqC,aAG0B;UAF7BpB,EAAA,CAAAqB,UAAA,mBAAAU,gDAAA;YAAA/B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASX,GAAA,CAAAjB,OAAA,EAAS;UAAA,EAAC;UAG7CG,EAAA,CAAA0B,SAAA,aAA0C;UAC1C1B,EAAA,CAAAoB,cAAA,YAAM;UAAApB,EAAA,CAAA6B,MAAA,gBAAQ;UAEtB7B,EAFsB,CAAA2B,YAAA,EAAO,EACrB,EACH;UAED3B,EADJ,CAAAoB,cAAA,cAAqC,aAG0B;UACvDpB,EAAA,CAAA0B,SAAA,aAAoC;UACpC1B,EAAA,CAAAoB,cAAA,YAAM;UAAApB,EAAA,CAAA6B,MAAA,gBAAQ;UAEtB7B,EAFsB,CAAA2B,YAAA,EAAO,EACrB,EACH;UAED3B,EADJ,CAAAoB,cAAA,cAAkE,aAGH;UAFxDpB,EAAA,CAAAqB,UAAA,mBAAAW,gDAAA;YAAAhC,EAAA,CAAAuB,aAAA,CAAAC,GAAA;YAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASX,GAAA,CAAAnB,MAAA,EAAQ;UAAA,EAAC;UAGjBK,EAAA,CAAA0B,SAAA,aAAyC;UACzC1B,EAAA,CAAAoB,cAAA,YAAM;UAAApB,EAAA,CAAA6B,MAAA,cAAM;UAexC7B,EAfwC,CAAA2B,YAAA,EAAO,EACnB,EACH,EACJ,EACJ,EASJ,EACH,EACJ;;;UA1E4J3B,EAAA,CAAAiC,SAAA,IAC7H;UAD6HjC,EAAA,CAAAkC,iBAAA,CAAApB,GAAA,CAAA1C,cAAA,CAC7H;UAGqE4B,EAAA,CAAAiC,SAAA,GAAuF;UAAvFjC,EAAA,CAAAmC,UAAA,WAAArB,GAAA,CAAA3C,YAAA,kBAAA2C,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,kBAAAtB,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,CAAAC,KAAA,aAAAvB,GAAA,CAAA3C,YAAA,kBAAA2C,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,kBAAAtB,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,CAAAE,aAAA,EAAuF;UAACtC,EAAA,CAAAiC,SAAA,EAAiF;UAAjFjC,EAAA,CAAAkC,iBAAA,EAAApB,GAAA,CAAA3C,YAAA,kBAAA2C,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,kBAAAtB,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,CAAAC,KAAA,aAAAvB,GAAA,CAAA3C,YAAA,kBAAA2C,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,kBAAAtB,GAAA,CAAA3C,YAAA,CAAAiE,QAAA,CAAAE,aAAA,EAAiF;UAajOtC,EAAA,CAAAiC,SAAA,GAA2B;UAA3BjC,EAAA,CAAAmC,UAAA,4BAA2B;UAMvBnC,EAAA,CAAAiC,SAAA,GAAiC;UAAjCjC,EAAA,CAAAmC,UAAA,eAAAnC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAiC;UAQjCxC,EAAA,CAAAiC,SAAA,GAA8B;UAA9BjC,EAAA,CAAAmC,UAAA,eAAAnC,EAAA,CAAAuC,eAAA,IAAAE,GAAA,EAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}