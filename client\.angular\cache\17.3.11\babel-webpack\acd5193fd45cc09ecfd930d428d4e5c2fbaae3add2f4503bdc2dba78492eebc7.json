{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AppToastService {\n  constructor() {\n    this.toasts = [];\n  }\n  open(textOrTpl, options = {\n    type: 'Success'\n  }) {\n    let classname = 'bg-success text-light';\n    if (options.type === 'Error') {\n      classname = 'bg-danger text-light';\n    }\n    if (options.type === 'Warning') {\n      classname = 'bg-warning text-dark';\n    }\n    this.toasts.push({\n      textOrTpl,\n      ...options,\n      classname\n    });\n  }\n  remove(toast) {\n    this.toasts = this.toasts.filter(t => t !== toast);\n  }\n  clear() {\n    this.toasts.splice(0, this.toasts.length);\n  }\n  static {\n    this.ɵfac = function AppToastService_Factory(t) {\n      return new (t || AppToastService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AppToastService,\n      factory: AppToastService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AppToastService", "constructor", "toasts", "open", "textOrTpl", "options", "type", "classname", "push", "remove", "toast", "filter", "t", "clear", "splice", "length", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\shared\\services\\toast.service.ts"], "sourcesContent": ["import { Injectable, TemplateRef } from \"@angular/core\";\r\n\r\ntype Type = 'Success' | 'Error' | 'Warning';\r\n\r\ninterface Options {\r\n    type: Type;\r\n}\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AppToastService {\r\n    toasts: any[] = [];\r\n\r\n    open(textOrTpl: string | TemplateRef<any>, options: Options = { type: 'Success' }) {\r\n        let classname = 'bg-success text-light';\r\n        if (options.type === 'Error') {\r\n            classname = 'bg-danger text-light';\r\n        }\r\n        if (options.type === 'Warning') {\r\n            classname = 'bg-warning text-dark';\r\n        }\r\n        this.toasts.push({ textOrTpl, ...options, classname });\r\n    }\r\n\r\n    remove(toast: any) {\r\n        this.toasts = this.toasts.filter((t) => t !== toast);\r\n    }\r\n\r\n    clear() {\r\n        this.toasts.splice(0, this.toasts.length);\r\n    }\r\n}"], "mappings": ";AASA,OAAM,MAAOA,eAAe;EAD5BC,YAAA;IAEI,KAAAC,MAAM,GAAU,EAAE;;EAElBC,IAAIA,CAACC,SAAoC,EAAEC,OAAA,GAAmB;IAAEC,IAAI,EAAE;EAAS,CAAE;IAC7E,IAAIC,SAAS,GAAG,uBAAuB;IACvC,IAAIF,OAAO,CAACC,IAAI,KAAK,OAAO,EAAE;MAC1BC,SAAS,GAAG,sBAAsB;IACtC;IACA,IAAIF,OAAO,CAACC,IAAI,KAAK,SAAS,EAAE;MAC5BC,SAAS,GAAG,sBAAsB;IACtC;IACA,IAAI,CAACL,MAAM,CAACM,IAAI,CAAC;MAAEJ,SAAS;MAAE,GAAGC,OAAO;MAAEE;IAAS,CAAE,CAAC;EAC1D;EAEAE,MAAMA,CAACC,KAAU;IACb,IAAI,CAACR,MAAM,GAAG,IAAI,CAACA,MAAM,CAACS,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKF,KAAK,CAAC;EACxD;EAEAG,KAAKA,CAAA;IACD,IAAI,CAACX,MAAM,CAACY,MAAM,CAAC,CAAC,EAAE,IAAI,CAACZ,MAAM,CAACa,MAAM,CAAC;EAC7C;;;uBApBSf,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAgB,OAAA,EAAfhB,eAAe,CAAAiB,IAAA;MAAAC,UAAA,EADF;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}