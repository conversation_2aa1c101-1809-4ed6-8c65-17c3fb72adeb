{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { stringify } from 'qs';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nexport class VendorContactService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.userSubject = new BehaviorSubject(null);\n    this.user = this.userSubject.asObservable();\n  }\n  getUsers(page, pageSize, sortField, sortOrder, searchParams, vendor_user_role_id) {\n    const obj = {\n      populate: 'role',\n      pagination: {\n        page: page,\n        pageSize: pageSize\n      },\n      filters: {\n        $and: [{\n          role: {\n            id: {\n              $eq: vendor_user_role_id\n            }\n          }\n        }]\n      }\n    };\n    if (searchParams.user) {\n      obj.filters.$and.push({\n        $or: [{\n          email: {\n            $containsi: searchParams.user\n          }\n        }, {\n          username: {\n            $containsi: searchParams.user\n          }\n        }, {\n          firstname: {\n            $containsi: searchParams.user\n          }\n        }, {\n          lastname: {\n            $containsi: searchParams.user\n          }\n        }]\n      });\n    }\n    if (searchParams.status) {\n      obj.filters.$and.push({\n        blocked: {\n          $eq: searchParams.status\n        }\n      });\n    }\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      obj.sort = `${sortField}:${order}`;\n    }\n    const query = stringify(obj);\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list?${query}`);\n  }\n  getUserByID(id) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate[suppliers]=*&populate[vendor]=*`);\n  }\n  getCustomers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  getSuppliers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.SUPPLIERS}`, {\n      params\n    });\n  }\n  updateUser(userId, updatedData) {\n    return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n  }\n  getSettings() {\n    return this.http.get(`${CMS_APIContstant.SETTINGS}?fields[0]=id&populate[vendor_user_role][fields]=id`);\n  }\n  static {\n    this.ɵfac = function VendorContactService_Factory(t) {\n      return new (t || VendorContactService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VendorContactService,\n      factory: VendorContactService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "stringify", "BehaviorSubject", "CMS_APIContstant", "VendorContactService", "constructor", "http", "authService", "userSubject", "user", "asObservable", "getUsers", "page", "pageSize", "sortField", "sortOrder", "searchParams", "vendor_user_role_id", "obj", "populate", "pagination", "filters", "$and", "role", "id", "$eq", "push", "$or", "email", "$containsi", "username", "firstname", "lastname", "status", "blocked", "undefined", "order", "sort", "query", "get", "USER_DETAILS", "getUserByID", "getCustomers", "data", "params", "appendAll", "CUSTOMERS", "getSuppliers", "SUPPLIERS", "updateUser", "userId", "updatedData", "put", "getSettings", "SETTINGS", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\vendor-contact\\vendor-contact.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { stringify } from 'qs';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class VendorContactService {\r\n  public userSubject = new BehaviorSubject<any>(null);\r\n  public user = this.userSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient, private authService: AuthService) {}\r\n\r\n  getUsers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchParams?: any,\r\n    vendor_user_role_id?: number\r\n  ): Observable<any[]> {\r\n    const obj: any = {\r\n      populate: 'role',\r\n      pagination: {\r\n        page: page,\r\n        pageSize: pageSize,\r\n      },\r\n      filters: {\r\n        $and: [\r\n          {\r\n            role: {\r\n              id: {\r\n                $eq: vendor_user_role_id,\r\n              },\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    };\r\n    if (searchParams.user) {\r\n      obj.filters.$and.push({\r\n        $or: [\r\n          {\r\n            email: { $containsi: searchParams.user },\r\n          },\r\n          {\r\n            username: { $containsi: searchParams.user },\r\n          },\r\n          {\r\n            firstname: { $containsi: searchParams.user },\r\n          },\r\n          {\r\n            lastname: { $containsi: searchParams.user },\r\n          },\r\n        ],\r\n      });\r\n    }\r\n    if (searchParams.status) {\r\n      obj.filters.$and.push({\r\n        blocked: {\r\n          $eq: searchParams.status,\r\n        },\r\n      });\r\n    }\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      obj.sort = `${sortField}:${order}`;\r\n    }\r\n    const query = stringify(obj);\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/list?${query}`\r\n    );\r\n  }\r\n\r\n  getUserByID(id: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}?populate[suppliers]=*&populate[vendor]=*`\r\n    );\r\n  }\r\n\r\n  getCustomers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getSuppliers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.SUPPLIERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  updateUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}`,\r\n      updatedData\r\n    );\r\n  }\r\n\r\n  getSettings() {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.SETTINGS}?fields[0]=id&populate[vendor_user_role][fields]=id`\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,SAAS,QAAQ,IAAI;AAC9B,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;;;;AAMlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB,EAAUC,WAAwB;IAAlD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,WAAW,GAAXA,WAAW;IAHlD,KAAAC,WAAW,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC5C,KAAAO,IAAI,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;EAE4B;EAEzEC,QAAQA,CACNC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,YAAkB,EAClBC,mBAA4B;IAE5B,MAAMC,GAAG,GAAQ;MACfC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE;QACVR,IAAI,EAAEA,IAAI;QACVC,QAAQ,EAAEA;OACX;MACDQ,OAAO,EAAE;QACPC,IAAI,EAAE,CACJ;UACEC,IAAI,EAAE;YACJC,EAAE,EAAE;cACFC,GAAG,EAAER;;;SAGV;;KAGN;IACD,IAAID,YAAY,CAACP,IAAI,EAAE;MACrBS,GAAG,CAACG,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpBC,GAAG,EAAE,CACH;UACEC,KAAK,EAAE;YAAEC,UAAU,EAAEb,YAAY,CAACP;UAAI;SACvC,EACD;UACEqB,QAAQ,EAAE;YAAED,UAAU,EAAEb,YAAY,CAACP;UAAI;SAC1C,EACD;UACEsB,SAAS,EAAE;YAAEF,UAAU,EAAEb,YAAY,CAACP;UAAI;SAC3C,EACD;UACEuB,QAAQ,EAAE;YAAEH,UAAU,EAAEb,YAAY,CAACP;UAAI;SAC1C;OAEJ,CAAC;IACJ;IACA,IAAIO,YAAY,CAACiB,MAAM,EAAE;MACvBf,GAAG,CAACG,OAAO,CAACC,IAAI,CAACI,IAAI,CAAC;QACpBQ,OAAO,EAAE;UACPT,GAAG,EAAET,YAAY,CAACiB;;OAErB,CAAC;IACJ;IACA,IAAInB,SAAS,IAAIC,SAAS,KAAKoB,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGrB,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CG,GAAG,CAACmB,IAAI,GAAG,GAAGvB,SAAS,IAAIsB,KAAK,EAAE;IACpC;IACA,MAAME,KAAK,GAAGrC,SAAS,CAACiB,GAAG,CAAC;IAC5B,OAAO,IAAI,CAACZ,IAAI,CAACiC,GAAG,CAClB,GAAGpC,gBAAgB,CAACqC,YAAY,SAASF,KAAK,EAAE,CACjD;EACH;EAEAG,WAAWA,CAACjB,EAAO;IACjB,OAAO,IAAI,CAAClB,IAAI,CAACiC,GAAG,CAClB,GAAGpC,gBAAgB,CAACqC,YAAY,IAAIhB,EAAE,2CAA2C,CAClF;EACH;EAEAkB,YAAYA,CAACC,IAAS;IACpB,MAAMC,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAAC6C,SAAS,CAAC;MAAE,GAAGF;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACrC,IAAI,CAACiC,GAAG,CAAQ,GAAGpC,gBAAgB,CAAC2C,SAAS,EAAE,EAAE;MAC3DF;KACD,CAAC;EACJ;EAEAG,YAAYA,CAACJ,IAAS;IACpB,MAAMC,MAAM,GAAG,IAAI5C,UAAU,EAAE,CAAC6C,SAAS,CAAC;MAAE,GAAGF;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACrC,IAAI,CAACiC,GAAG,CAAQ,GAAGpC,gBAAgB,CAAC6C,SAAS,EAAE,EAAE;MAC3DJ;KACD,CAAC;EACJ;EAEAK,UAAUA,CAACC,MAAc,EAAEC,WAAgB;IACzC,OAAO,IAAI,CAAC7C,IAAI,CAAC8C,GAAG,CAClB,GAAGjD,gBAAgB,CAACqC,YAAY,IAAIU,MAAM,EAAE,EAC5CC,WAAW,CACZ;EACH;EAEAE,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC/C,IAAI,CAACiC,GAAG,CAClB,GAAGpC,gBAAgB,CAACmD,QAAQ,qDAAqD,CAClF;EACH;;;uBAlGWlD,oBAAoB,EAAAmD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAApBxD,oBAAoB;MAAAyD,OAAA,EAApBzD,oBAAoB,CAAA0D,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}