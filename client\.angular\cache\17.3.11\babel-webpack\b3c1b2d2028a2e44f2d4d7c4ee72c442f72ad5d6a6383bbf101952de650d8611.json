{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Frisian [fy]\n//! author : <PERSON> : https://github.com/robin0van0der0v\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var monthsShortWithDots = 'jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.'.split('_'),\n    monthsShortWithoutDots = 'jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des'.split('_');\n  var fy = moment.defineLocale('fy', {\n    months: 'janne<PERSON>s_febrew<PERSON>_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber'.split('_'),\n    monthsShort: function (m, format) {\n      if (!m) {\n        return monthsShortWithDots;\n      } else if (/-MMM-/.test(format)) {\n        return monthsShortWithoutDots[m.month()];\n      } else {\n        return monthsShortWithDots[m.month()];\n      }\n    },\n    monthsParseExact: true,\n    weekdays: 'snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon'.split('_'),\n    weekdaysShort: 'si._mo._ti._wo._to._fr._so.'.split('_'),\n    weekdaysMin: 'Si_Mo_Ti_Wo_To_Fr_So'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD-MM-YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[hjoed om] LT',\n      nextDay: '[moarn om] LT',\n      nextWeek: 'dddd [om] LT',\n      lastDay: '[juster om] LT',\n      lastWeek: '[ôfrûne] dddd [om] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'oer %s',\n      past: '%s lyn',\n      s: 'in pear sekonden',\n      ss: '%d sekonden',\n      m: 'ien minút',\n      mm: '%d minuten',\n      h: 'ien oere',\n      hh: '%d oeren',\n      d: 'ien dei',\n      dd: '%d dagen',\n      M: 'ien moanne',\n      MM: '%d moannen',\n      y: 'ien jier',\n      yy: '%d jierren'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n    ordinal: function (number) {\n      return number + (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fy;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "monthsShortWithDots", "split", "monthsShortWithoutDots", "fy", "defineLocale", "months", "monthsShort", "m", "format", "test", "month", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/moment/locale/fy.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Frisian [fy]\n//! author : <PERSON> : https://github.com/robin0van0der0v\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var monthsShortWithDots =\n            'jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.'.split('_'),\n        monthsShortWithoutDots =\n            'jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des'.split('_');\n\n    var fy = moment.defineLocale('fy', {\n        months: 'jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber'.split(\n            '_'\n        ),\n        monthsShort: function (m, format) {\n            if (!m) {\n                return monthsShortWithDots;\n            } else if (/-MMM-/.test(format)) {\n                return monthsShortWithoutDots[m.month()];\n            } else {\n                return monthsShortWithDots[m.month()];\n            }\n        },\n        monthsParseExact: true,\n        weekdays: 'snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon'.split(\n            '_'\n        ),\n        weekdaysShort: 'si._mo._ti._wo._to._fr._so.'.split('_'),\n        weekdaysMin: 'Si_Mo_Ti_Wo_To_Fr_So'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD-MM-YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[hjoed om] LT',\n            nextDay: '[moarn om] LT',\n            nextWeek: 'dddd [om] LT',\n            lastDay: '[juster om] LT',\n            lastWeek: '[ôfrûne] dddd [om] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'oer %s',\n            past: '%s lyn',\n            s: 'in pear sekonden',\n            ss: '%d sekonden',\n            m: 'ien minút',\n            mm: '%d minuten',\n            h: 'ien oere',\n            hh: '%d oeren',\n            d: 'ien dei',\n            dd: '%d dagen',\n            M: 'ien moanne',\n            MM: '%d moannen',\n            y: 'ien jier',\n            yy: '%d jierren',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n        ordinal: function (number) {\n            return (\n                number +\n                (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de')\n            );\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return fy;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,mBAAmB,GACf,4DAA4D,CAACC,KAAK,CAAC,GAAG,CAAC;IAC3EC,sBAAsB,GAClB,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;EAEpE,IAAIE,EAAE,GAAGJ,MAAM,CAACK,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,gGAAgG,CAACJ,KAAK,CAC1G,GACJ,CAAC;IACDK,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAEC,MAAM,EAAE;MAC9B,IAAI,CAACD,CAAC,EAAE;QACJ,OAAOP,mBAAmB;MAC9B,CAAC,MAAM,IAAI,OAAO,CAACS,IAAI,CAACD,MAAM,CAAC,EAAE;QAC7B,OAAON,sBAAsB,CAACK,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;MAC5C,CAAC,MAAM;QACH,OAAOV,mBAAmB,CAACO,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;MACzC;IACJ,CAAC;IACDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,uDAAuD,CAACX,KAAK,CACnE,GACJ,CAAC;IACDY,aAAa,EAAE,6BAA6B,CAACZ,KAAK,CAAC,GAAG,CAAC;IACvDa,WAAW,EAAE,sBAAsB,CAACb,KAAK,CAAC,GAAG,CAAC;IAC9Cc,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,aAAa;MACjB3B,CAAC,EAAE,WAAW;MACd4B,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,iBAAiB;IACzCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OACIA,MAAM,IACLA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,IAAI,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;IAErE,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO9C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}