{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ApiConstant, AppConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\nimport { BehaviorSubject, catchError, fromEvent, lastValueFrom, map, of, switchMap, tap } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http, ngZone) {\n    this.http = http;\n    this.ngZone = ngZone;\n    this.permissions = new BehaviorSubject([]);\n    this.cmsTokenVal = new BehaviorSubject(\"\");\n    this.sessionChannel = new BroadcastChannel(\"session\");\n    this.logoutTriggered = false;\n    this.TokenKey = 'jwtToken';\n    this.UserDetailsKey = 'userInfo';\n    const user = this.getAuth();\n    this.userSubject = new BehaviorSubject(Object.keys(user).length ? user : \"\");\n    this.bindUserActivityEvents();\n  }\n  checkAdminUser() {\n    const user = this.getAuth();\n    if (user && user[this.UserDetailsKey]?.documentId) {\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(tap(cartres => {\n        if (cartres?.cart) {\n          const cart = cartres.cart || null;\n          this.updateAuth({\n            cart,\n            customer: cart.customer\n          });\n        }\n      }));\n    } else {\n      return of(null);\n    }\n  }\n  bindUserActivityEvents() {\n    const events = [\"click\", \"keydown\"];\n    for (let i = 0; i < events.length; i++) {\n      const element = events[i];\n      fromEvent(document, element).subscribe(data => {\n        this.setInavtivityTimer();\n        this.sessionChannel.postMessage({\n          type: \"activityFound\"\n        });\n      });\n    }\n    this.sessionChannel.onmessage = event => {\n      if (event?.data?.type == \"activityFound\") {\n        this.ngZone.run(() => {\n          this.setInavtivityTimer();\n        });\n      }\n      if (event?.data?.type == \"logout\") {\n        this.logoutTriggered = true;\n        this.doLogout();\n      }\n    };\n    this.setInavtivityTimer();\n    this.sessionChannel.postMessage({\n      type: \"activityFound\"\n    });\n  }\n  setInavtivityTimer() {\n    clearTimeout(this.timer);\n    if (!this.isLoggedIn) {\n      return;\n    }\n    this.timer = setTimeout(() => {\n      this.doLogout();\n    }, AppConstant.SESSION_TIMEOUT);\n  }\n  login(username, password, rememberMe) {\n    return this.http.post(CMS_APIContstant.SINGIN, {\n      identifier: (username || \"\").toLowerCase(),\n      password\n    }).pipe(tap(res => {\n      if (res) {\n        this.setAuth(res.jwt, res.user, rememberMe);\n      }\n      return res;\n    }), switchMap(res => {\n      if (res?.user) {\n        return this.getCartDetails(res.user.documentId).pipe(map(data => {\n          if (data?.cart) {\n            res.user.cart = data.cart;\n            res.user.customer = data.cart.customer;\n          }\n          this.updateAuth(res.user);\n          return res;\n        }));\n      }\n      return of(null);\n    }));\n  }\n  getCartDetails(userId) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`).pipe(map(res => {\n      if (res?.cart?.cart_items?.length) {\n        res.cart.cart_items = res.cart.cart_items.map(item => ({\n          ...item,\n          requested_quantity: item.requested_quantity.toString()\n        }));\n      }\n      return res;\n    }));\n  }\n  getToken() {\n    const val = this.userSubject.value;\n    return val ? val[this.TokenKey] : null;\n  }\n  get partnerFunction() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\n      return user[this.UserDetailsKey].customer.partner_functions[0];\n    }\n    return {};\n  }\n  get cart() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.cart) {\n      return user[this.UserDetailsKey].cart;\n    }\n    return {};\n  }\n  get userDetail() {\n    const user = this.userSubject.value;\n    return user ? user[this.UserDetailsKey] : null;\n  }\n  get isLoggedIn() {\n    return !!this.userSubject.value;\n  }\n  get isCustomerSelected() {\n    const user = this.userSubject.value;\n    if (user && user[this.UserDetailsKey]?.customer) {\n      return true;\n    }\n    return false;\n  }\n  updateAuth(user) {\n    const auth = this.getAuth();\n    if (user?.cart) {\n      auth[this.UserDetailsKey].cart = user?.cart;\n    }\n    if (user?.customer) {\n      auth[this.UserDetailsKey].customer = user?.customer;\n    }\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\n  }\n  isRememberMeSelected() {\n    return !!localStorage.getItem(this.TokenKey);\n  }\n  doLogout() {\n    this.resetAuth();\n    window.location.href = \"#/auth/login\";\n    window.location.reload();\n  }\n  resetAuth() {\n    this.removeAuthToken();\n    !this.logoutTriggered && this.sessionChannel.postMessage({\n      type: \"logout\"\n    });\n    this.userSubject.next(null);\n  }\n  getAuth() {\n    const authtoken = this.getAuthToken();\n    const userDetails = this.getUserDetails();\n    if (authtoken && this.isJsonString(userDetails)) {\n      return {\n        [this.UserDetailsKey]: JSON.parse(userDetails),\n        [this.TokenKey]: JSON.parse(authtoken)\n      };\n    }\n    return {};\n  }\n  setAuth(token, user, rememberMe) {\n    if (rememberMe) {\n      localStorage.setItem(this.TokenKey, JSON.stringify(token));\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    } else {\n      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\n    }\n    this.userSubject.next({\n      [this.UserDetailsKey]: user,\n      [this.TokenKey]: token\n    });\n  }\n  getAuthToken() {\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\n  }\n  getUserDetails() {\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\n  }\n  removeAuthToken() {\n    localStorage.removeItem(this.TokenKey);\n    sessionStorage.removeItem(this.TokenKey);\n    localStorage.removeItem(this.UserDetailsKey);\n    sessionStorage.removeItem(this.UserDetailsKey);\n  }\n  isJsonString(str) {\n    try {\n      JSON.parse(str);\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n  getUserPermissions() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const userDetails = _this.userDetail;\n      return yield lastValueFrom(_this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions`).pipe(map(res => {\n        if (res?.data?.length) {\n          const data = res?.data || [];\n          _this.permissions.next(data);\n          return data;\n        }\n        return [];\n      })).pipe(catchError(error => {\n        _this.permissions.next([]);\n        return error;\n      })));\n    })();\n  }\n  get getPermissions() {\n    return this.permissions?.value || [];\n  }\n  getCMSToken() {\n    return this.http.get(ApiConstant.FETCH_TOKEN).pipe(map(response => {\n      this.cmsTokenVal.next(response.token);\n      return response.token;\n    }));\n  }\n  get cmsToken() {\n    return of(this.cmsTokenVal?.value || \"\");\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["ApiConstant", "AppConstant", "CMS_APIContstant", "BehaviorSubject", "catchError", "fromEvent", "lastValueFrom", "map", "of", "switchMap", "tap", "AuthService", "constructor", "http", "ngZone", "permissions", "cmsTokenVal", "sessionChannel", "BroadcastChannel", "logoutTriggered", "TokenKey", "UserDetailsKey", "user", "getAuth", "userSubject", "Object", "keys", "length", "bindUserActivityEvents", "checkAdminUser", "documentId", "getCartDetails", "pipe", "cartres", "cart", "updateAuth", "customer", "events", "i", "element", "document", "subscribe", "data", "setInavtivityTimer", "postMessage", "type", "onmessage", "event", "run", "doLogout", "clearTimeout", "timer", "isLoggedIn", "setTimeout", "SESSION_TIMEOUT", "login", "username", "password", "rememberMe", "post", "SINGIN", "identifier", "toLowerCase", "res", "setAuth", "jwt", "userId", "get", "USER_DETAILS", "cart_items", "item", "requested_quantity", "toString", "getToken", "val", "value", "partnerFunction", "partner_functions", "userDetail", "isCustomerSelected", "auth", "isRememberMeSelected", "localStorage", "getItem", "resetAuth", "window", "location", "href", "reload", "removeAuthToken", "next", "authtoken", "getAuthToken", "userDetails", "getUserDetails", "isJsonString", "JSON", "parse", "token", "setItem", "stringify", "sessionStorage", "removeItem", "str", "e", "getUserPermissions", "_this", "_asyncToGenerator", "error", "getPermissions", "getCMSToken", "FETCH_TOKEN", "response", "cmsToken", "i0", "ɵɵinject", "i1", "HttpClient", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\core\\authentication\\auth.service.ts"], "sourcesContent": ["import { Injectable, Ng<PERSON>one } from \"@angular/core\";\r\nimport { HttpClient } from \"@angular/common/http\";\r\nimport { ApiConstant, AppConstant, CMS_APIContstant } from \"src/app/constants/api.constants\";\r\nimport {\r\n  BehaviorSubject,\r\n  catchError,\r\n  fromEvent,\r\n  lastValueFrom,\r\n  map,\r\n  Observable,\r\n  of,\r\n  switchMap,\r\n  tap,\r\n} from \"rxjs\";\r\nimport { stringify } from \"qs\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class AuthService {\r\n  public userSubject: BehaviorSubject<any>;\r\n  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);\r\n  private cmsTokenVal: BehaviorSubject<any> = new BehaviorSubject<any>(\"\");\r\n  private sessionChannel = new BroadcastChannel(\"session\");\r\n  private timer: any;\r\n  private logoutTriggered = false;\r\n  public TokenKey = 'jwtToken';\r\n  public UserDetailsKey = 'userInfo';\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private ngZone: NgZone\r\n  ) {\r\n    const user: any = this.getAuth();\r\n    this.userSubject = new BehaviorSubject<any>(Object.keys(user).length ? user : \"\");\r\n    this.bindUserActivityEvents();\r\n  }\r\n\r\n  checkAdminUser() {\r\n    const user: any = this.getAuth();\r\n    if (user && user[this.UserDetailsKey]?.documentId) {\r\n      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(\r\n        tap((cartres: any) => {\r\n          if (cartres?.cart) {\r\n            const cart = cartres.cart || null;\r\n            this.updateAuth({\r\n              cart,\r\n              customer: cart.customer\r\n            });\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n  bindUserActivityEvents() {\r\n    const events = [\"click\", \"keydown\"];\r\n    for (let i = 0; i < events.length; i++) {\r\n      const element = events[i];\r\n      fromEvent(document, element).subscribe((data) => {\r\n        this.setInavtivityTimer();\r\n        this.sessionChannel.postMessage({\r\n          type: \"activityFound\",\r\n        });\r\n      });\r\n    }\r\n    this.sessionChannel.onmessage = (event) => {\r\n      if (event?.data?.type == \"activityFound\") {\r\n        this.ngZone.run(() => {\r\n          this.setInavtivityTimer();\r\n        });\r\n      }\r\n      if (event?.data?.type == \"logout\") {\r\n        this.logoutTriggered = true;\r\n        this.doLogout();\r\n      }\r\n    };\r\n    this.setInavtivityTimer();\r\n    this.sessionChannel.postMessage({\r\n      type: \"activityFound\",\r\n    });\r\n  }\r\n\r\n  setInavtivityTimer() {\r\n    clearTimeout(this.timer);\r\n    if (!this.isLoggedIn) {\r\n      return;\r\n    }\r\n    this.timer = setTimeout(() => {\r\n      this.doLogout();\r\n    }, AppConstant.SESSION_TIMEOUT);\r\n  }\r\n\r\n  login(username: string, password: string, rememberMe: boolean) {\r\n    return this.http\r\n      .post<any>(CMS_APIContstant.SINGIN, {\r\n        identifier: (username || \"\").toLowerCase(),\r\n        password,\r\n      })\r\n      .pipe(\r\n        tap((res) => {\r\n          if (res) {\r\n            this.setAuth(res.jwt, res.user, rememberMe);\r\n          }\r\n          return res;\r\n        }),\r\n        switchMap((res) => {\r\n          if (res?.user) {\r\n            return this.getCartDetails(res.user.documentId).pipe(\r\n              map((data: any) => {\r\n                if (data?.cart) {\r\n                  res.user.cart = data.cart;\r\n                  res.user.customer = data.cart.customer;\r\n                }\r\n                this.updateAuth(res.user);\r\n                return res;\r\n              })\r\n            )\r\n          }\r\n          return of(null);\r\n        }),\r\n      );\r\n  }\r\n  getCartDetails(userId: string): any {\r\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`).pipe(\r\n      map((res: any) => {\r\n        if (res?.cart?.cart_items?.length) {\r\n          res.cart.cart_items = res.cart.cart_items.map((item: any) => ({\r\n            ...item,\r\n            requested_quantity: item.requested_quantity.toString()\r\n          }))\r\n        }\r\n        return res;\r\n      })\r\n    );\r\n  }\r\n\r\n  getToken() {\r\n    const val = this.userSubject.value;\r\n    return val ? val[this.TokenKey] : null;\r\n  }\r\n\r\n  get partnerFunction() {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {\r\n      return user[this.UserDetailsKey].customer.partner_functions[0];\r\n    }\r\n    return {};\r\n  }\r\n\r\n  get cart() {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.cart) {\r\n      return user[this.UserDetailsKey].cart;\r\n    }\r\n    return {};\r\n  }\r\n\r\n  get userDetail() {\r\n    const user = this.userSubject.value;\r\n    return user ? user[this.UserDetailsKey] : null;\r\n  }\r\n\r\n  get isLoggedIn(): boolean {\r\n    return !!this.userSubject.value;\r\n  }\r\n\r\n  get isCustomerSelected(): boolean {\r\n    const user = this.userSubject.value;\r\n    if (user && user[this.UserDetailsKey]?.customer) {\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  updateAuth(user: any) {\r\n    const auth: any = this.getAuth();\r\n    if (user?.cart) {\r\n      auth[this.UserDetailsKey].cart = user?.cart;\r\n    }\r\n    if (user?.customer) {\r\n      auth[this.UserDetailsKey].customer = user?.customer;\r\n    }\r\n    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());\r\n  }\r\n\r\n  isRememberMeSelected(): boolean {\r\n    return !!localStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  doLogout() {\r\n    this.resetAuth();\r\n    window.location.href = \"#/auth/login\";\r\n    window.location.reload();\r\n  }\r\n\r\n  resetAuth() {\r\n    this.removeAuthToken();\r\n    !this.logoutTriggered &&\r\n      this.sessionChannel.postMessage({\r\n        type: \"logout\",\r\n      });\r\n    this.userSubject.next(null);\r\n  }\r\n\r\n  getAuth(): any {\r\n    const authtoken: any = this.getAuthToken();\r\n    const userDetails: any = this.getUserDetails();\r\n    if (authtoken && this.isJsonString(userDetails)) {\r\n      return {\r\n        [this.UserDetailsKey]: JSON.parse(userDetails),\r\n        [this.TokenKey]: JSON.parse(authtoken)\r\n      }\r\n    }\r\n    return {};\r\n  }\r\n\r\n  setAuth(token: string, user: any, rememberMe: boolean) {\r\n    if (rememberMe) {\r\n      localStorage.setItem(this.TokenKey, JSON.stringify(token));\r\n      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    } else {\r\n      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));\r\n      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));\r\n    }\r\n    this.userSubject.next({\r\n      [this.UserDetailsKey]: user,\r\n      [this.TokenKey]: token\r\n    });\r\n  }\r\n\r\n  getAuthToken() {\r\n    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);\r\n  }\r\n\r\n  getUserDetails() {\r\n    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);\r\n  }\r\n\r\n  removeAuthToken() {\r\n    localStorage.removeItem(this.TokenKey);\r\n    sessionStorage.removeItem(this.TokenKey);\r\n    localStorage.removeItem(this.UserDetailsKey);\r\n    sessionStorage.removeItem(this.UserDetailsKey);\r\n  }\r\n\r\n  isJsonString(str: any) {\r\n    try {\r\n      JSON.parse(str);\r\n    } catch (e) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  async getUserPermissions() {\r\n    const userDetails = this.userDetail;\r\n    return await lastValueFrom(\r\n      this.http\r\n        .get<any>(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions`)\r\n        .pipe(\r\n          map((res) => {\r\n            if (res?.data?.length) {\r\n              const data = (res?.data || []);\r\n              this.permissions.next(data);\r\n              return data;\r\n            }\r\n            return [];\r\n          })\r\n        )\r\n        .pipe(\r\n          catchError((error) => {\r\n            this.permissions.next([]);\r\n            return error;\r\n          })\r\n        )\r\n    );\r\n  }\r\n\r\n  get getPermissions(): any[] {\r\n    return this.permissions?.value || [];\r\n  }\r\n\r\n  getCMSToken(): Observable<string> {\r\n    return this.http.get<{ token: string }>(ApiConstant.FETCH_TOKEN).pipe(\r\n      map((response) => {\r\n        this.cmsTokenVal.next(response.token);\r\n        return response.token as string;\r\n      }),\r\n    )\r\n  }\r\n\r\n  get cmsToken(): Observable<string> {\r\n    return of(this.cmsTokenVal?.value || \"\");\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAASA,WAAW,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,iCAAiC;AAC5F,SACEC,eAAe,EACfC,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,GAAG,EAEHC,EAAE,EACFC,SAAS,EACTC,GAAG,QACE,MAAM;;;AAMb,OAAM,MAAOC,WAAW;EAUtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IAVT,KAAAC,WAAW,GAAyB,IAAIZ,eAAe,CAAM,EAAE,CAAC;IAC/D,KAAAa,WAAW,GAAyB,IAAIb,eAAe,CAAM,EAAE,CAAC;IAChE,KAAAc,cAAc,GAAG,IAAIC,gBAAgB,CAAC,SAAS,CAAC;IAEhD,KAAAC,eAAe,GAAG,KAAK;IACxB,KAAAC,QAAQ,GAAG,UAAU;IACrB,KAAAC,cAAc,GAAG,UAAU;IAMhC,MAAMC,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAI,CAACC,WAAW,GAAG,IAAIrB,eAAe,CAAMsB,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,MAAM,GAAGL,IAAI,GAAG,EAAE,CAAC;IACjF,IAAI,CAACM,sBAAsB,EAAE;EAC/B;EAEAC,cAAcA,CAAA;IACZ,MAAMP,IAAI,GAAQ,IAAI,CAACC,OAAO,EAAE;IAChC,IAAID,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAES,UAAU,EAAE;MACjD,OAAO,IAAI,CAACC,cAAc,CAACT,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACS,UAAU,CAAC,CAACE,IAAI,CACnEtB,GAAG,CAAEuB,OAAY,IAAI;QACnB,IAAIA,OAAO,EAAEC,IAAI,EAAE;UACjB,MAAMA,IAAI,GAAGD,OAAO,CAACC,IAAI,IAAI,IAAI;UACjC,IAAI,CAACC,UAAU,CAAC;YACdD,IAAI;YACJE,QAAQ,EAAEF,IAAI,CAACE;WAChB,CAAC;QACJ;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL,OAAO5B,EAAE,CAAC,IAAI,CAAC;IACjB;EACF;EAEAoB,sBAAsBA,CAAA;IACpB,MAAMS,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACV,MAAM,EAAEW,CAAC,EAAE,EAAE;MACtC,MAAMC,OAAO,GAAGF,MAAM,CAACC,CAAC,CAAC;MACzBjC,SAAS,CAACmC,QAAQ,EAAED,OAAO,CAAC,CAACE,SAAS,CAAEC,IAAI,IAAI;QAC9C,IAAI,CAACC,kBAAkB,EAAE;QACzB,IAAI,CAAC1B,cAAc,CAAC2B,WAAW,CAAC;UAC9BC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAAC5B,cAAc,CAAC6B,SAAS,GAAIC,KAAK,IAAI;MACxC,IAAIA,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,eAAe,EAAE;QACxC,IAAI,CAAC/B,MAAM,CAACkC,GAAG,CAAC,MAAK;UACnB,IAAI,CAACL,kBAAkB,EAAE;QAC3B,CAAC,CAAC;MACJ;MACA,IAAII,KAAK,EAAEL,IAAI,EAAEG,IAAI,IAAI,QAAQ,EAAE;QACjC,IAAI,CAAC1B,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC8B,QAAQ,EAAE;MACjB;IACF,CAAC;IACD,IAAI,CAACN,kBAAkB,EAAE;IACzB,IAAI,CAAC1B,cAAc,CAAC2B,WAAW,CAAC;MAC9BC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAF,kBAAkBA,CAAA;IAChBO,YAAY,CAAC,IAAI,CAACC,KAAK,CAAC;IACxB,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACpB;IACF;IACA,IAAI,CAACD,KAAK,GAAGE,UAAU,CAAC,MAAK;MAC3B,IAAI,CAACJ,QAAQ,EAAE;IACjB,CAAC,EAAEhD,WAAW,CAACqD,eAAe,CAAC;EACjC;EAEAC,KAAKA,CAACC,QAAgB,EAAEC,QAAgB,EAAEC,UAAmB;IAC3D,OAAO,IAAI,CAAC7C,IAAI,CACb8C,IAAI,CAAMzD,gBAAgB,CAAC0D,MAAM,EAAE;MAClCC,UAAU,EAAE,CAACL,QAAQ,IAAI,EAAE,EAAEM,WAAW,EAAE;MAC1CL;KACD,CAAC,CACDzB,IAAI,CACHtB,GAAG,CAAEqD,GAAG,IAAI;MACV,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,OAAO,CAACD,GAAG,CAACE,GAAG,EAAEF,GAAG,CAACzC,IAAI,EAAEoC,UAAU,CAAC;MAC7C;MACA,OAAOK,GAAG;IACZ,CAAC,CAAC,EACFtD,SAAS,CAAEsD,GAAG,IAAI;MAChB,IAAIA,GAAG,EAAEzC,IAAI,EAAE;QACb,OAAO,IAAI,CAACS,cAAc,CAACgC,GAAG,CAACzC,IAAI,CAACQ,UAAU,CAAC,CAACE,IAAI,CAClDzB,GAAG,CAAEmC,IAAS,IAAI;UAChB,IAAIA,IAAI,EAAER,IAAI,EAAE;YACd6B,GAAG,CAACzC,IAAI,CAACY,IAAI,GAAGQ,IAAI,CAACR,IAAI;YACzB6B,GAAG,CAACzC,IAAI,CAACc,QAAQ,GAAGM,IAAI,CAACR,IAAI,CAACE,QAAQ;UACxC;UACA,IAAI,CAACD,UAAU,CAAC4B,GAAG,CAACzC,IAAI,CAAC;UACzB,OAAOyC,GAAG;QACZ,CAAC,CAAC,CACH;MACH;MACA,OAAOvD,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EACAuB,cAAcA,CAACmC,MAAc;IAC3B,OAAO,IAAI,CAACrD,IAAI,CAACsD,GAAG,CAAC,GAAGjE,gBAAgB,CAACkE,YAAY,IAAIF,MAAM,KAAK,CAAC,CAAClC,IAAI,CACxEzB,GAAG,CAAEwD,GAAQ,IAAI;MACf,IAAIA,GAAG,EAAE7B,IAAI,EAAEmC,UAAU,EAAE1C,MAAM,EAAE;QACjCoC,GAAG,CAAC7B,IAAI,CAACmC,UAAU,GAAGN,GAAG,CAAC7B,IAAI,CAACmC,UAAU,CAAC9D,GAAG,CAAE+D,IAAS,KAAM;UAC5D,GAAGA,IAAI;UACPC,kBAAkB,EAAED,IAAI,CAACC,kBAAkB,CAACC,QAAQ;SACrD,CAAC,CAAC;MACL;MACA,OAAOT,GAAG;IACZ,CAAC,CAAC,CACH;EACH;EAEAU,QAAQA,CAAA;IACN,MAAMC,GAAG,GAAG,IAAI,CAAClD,WAAW,CAACmD,KAAK;IAClC,OAAOD,GAAG,GAAGA,GAAG,CAAC,IAAI,CAACtD,QAAQ,CAAC,GAAG,IAAI;EACxC;EAEA,IAAIwD,eAAeA,CAAA;IACjB,MAAMtD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACmD,KAAK;IACnC,IAAIrD,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEe,QAAQ,EAAEyC,iBAAiB,EAAElD,MAAM,EAAE;MAC1E,OAAOL,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACe,QAAQ,CAACyC,iBAAiB,CAAC,CAAC,CAAC;IAChE;IACA,OAAO,EAAE;EACX;EAEA,IAAI3C,IAAIA,CAAA;IACN,MAAMZ,IAAI,GAAG,IAAI,CAACE,WAAW,CAACmD,KAAK;IACnC,IAAIrD,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEa,IAAI,EAAE;MAC3C,OAAOZ,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,CAACa,IAAI;IACvC;IACA,OAAO,EAAE;EACX;EAEA,IAAI4C,UAAUA,CAAA;IACZ,MAAMxD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACmD,KAAK;IACnC,OAAOrD,IAAI,GAAGA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG,IAAI;EAChD;EAEA,IAAI+B,UAAUA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAAC5B,WAAW,CAACmD,KAAK;EACjC;EAEA,IAAII,kBAAkBA,CAAA;IACpB,MAAMzD,IAAI,GAAG,IAAI,CAACE,WAAW,CAACmD,KAAK;IACnC,IAAIrD,IAAI,IAAIA,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,EAAEe,QAAQ,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAD,UAAUA,CAACb,IAAS;IAClB,MAAM0D,IAAI,GAAQ,IAAI,CAACzD,OAAO,EAAE;IAChC,IAAID,IAAI,EAAEY,IAAI,EAAE;MACd8C,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,CAACa,IAAI,GAAGZ,IAAI,EAAEY,IAAI;IAC7C;IACA,IAAIZ,IAAI,EAAEc,QAAQ,EAAE;MAClB4C,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,CAACe,QAAQ,GAAGd,IAAI,EAAEc,QAAQ;IACrD;IACA,IAAI,CAAC4B,OAAO,CAACgB,IAAI,CAAC,IAAI,CAAC5D,QAAQ,CAAC,EAAE4D,IAAI,CAAC,IAAI,CAAC3D,cAAc,CAAC,EAAE,IAAI,CAAC4D,oBAAoB,EAAE,CAAC;EAC3F;EAEAA,oBAAoBA,CAAA;IAClB,OAAO,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC/D,QAAQ,CAAC;EAC9C;EAEA6B,QAAQA,CAAA;IACN,IAAI,CAACmC,SAAS,EAAE;IAChBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;IACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;EAC1B;EAEAJ,SAASA,CAAA;IACP,IAAI,CAACK,eAAe,EAAE;IACtB,CAAC,IAAI,CAACtE,eAAe,IACnB,IAAI,CAACF,cAAc,CAAC2B,WAAW,CAAC;MAC9BC,IAAI,EAAE;KACP,CAAC;IACJ,IAAI,CAACrB,WAAW,CAACkE,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEAnE,OAAOA,CAAA;IACL,MAAMoE,SAAS,GAAQ,IAAI,CAACC,YAAY,EAAE;IAC1C,MAAMC,WAAW,GAAQ,IAAI,CAACC,cAAc,EAAE;IAC9C,IAAIH,SAAS,IAAI,IAAI,CAACI,YAAY,CAACF,WAAW,CAAC,EAAE;MAC/C,OAAO;QACL,CAAC,IAAI,CAACxE,cAAc,GAAG2E,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;QAC9C,CAAC,IAAI,CAACzE,QAAQ,GAAG4E,IAAI,CAACC,KAAK,CAACN,SAAS;OACtC;IACH;IACA,OAAO,EAAE;EACX;EAEA3B,OAAOA,CAACkC,KAAa,EAAE5E,IAAS,EAAEoC,UAAmB;IACnD,IAAIA,UAAU,EAAE;MACdwB,YAAY,CAACiB,OAAO,CAAC,IAAI,CAAC/E,QAAQ,EAAE4E,IAAI,CAACI,SAAS,CAACF,KAAK,CAAC,CAAC;MAC1DhB,YAAY,CAACiB,OAAO,CAAC,IAAI,CAAC9E,cAAc,EAAE2E,IAAI,CAACI,SAAS,CAAC9E,IAAI,CAAC,CAAC;IACjE,CAAC,MAAM;MACL+E,cAAc,CAACF,OAAO,CAAC,IAAI,CAAC/E,QAAQ,EAAE4E,IAAI,CAACI,SAAS,CAACF,KAAK,CAAC,CAAC;MAC5DG,cAAc,CAACF,OAAO,CAAC,IAAI,CAAC9E,cAAc,EAAE2E,IAAI,CAACI,SAAS,CAAC9E,IAAI,CAAC,CAAC;IACnE;IACA,IAAI,CAACE,WAAW,CAACkE,IAAI,CAAC;MACpB,CAAC,IAAI,CAACrE,cAAc,GAAGC,IAAI;MAC3B,CAAC,IAAI,CAACF,QAAQ,GAAG8E;KAClB,CAAC;EACJ;EAEAN,YAAYA,CAAA;IACV,OAAOV,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC/D,QAAQ,CAAC,IAAIiF,cAAc,CAAClB,OAAO,CAAC,IAAI,CAAC/D,QAAQ,CAAC;EACrF;EAEA0E,cAAcA,CAAA;IACZ,OAAOZ,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC9D,cAAc,CAAC,IAAIgF,cAAc,CAAClB,OAAO,CAAC,IAAI,CAAC9D,cAAc,CAAC;EACjG;EAEAoE,eAAeA,CAAA;IACbP,YAAY,CAACoB,UAAU,CAAC,IAAI,CAAClF,QAAQ,CAAC;IACtCiF,cAAc,CAACC,UAAU,CAAC,IAAI,CAAClF,QAAQ,CAAC;IACxC8D,YAAY,CAACoB,UAAU,CAAC,IAAI,CAACjF,cAAc,CAAC;IAC5CgF,cAAc,CAACC,UAAU,CAAC,IAAI,CAACjF,cAAc,CAAC;EAChD;EAEA0E,YAAYA,CAACQ,GAAQ;IACnB,IAAI;MACFP,IAAI,CAACC,KAAK,CAACM,GAAG,CAAC;IACjB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEMC,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMd,WAAW,GAAGa,KAAI,CAAC5B,UAAU;MACnC,aAAaxE,aAAa,CACxBoG,KAAI,CAAC7F,IAAI,CACNsD,GAAG,CAAM,GAAGjE,gBAAgB,CAACkE,YAAY,IAAIyB,WAAW,CAAC/D,UAAU,cAAc,CAAC,CAClFE,IAAI,CACHzB,GAAG,CAAEwD,GAAG,IAAI;QACV,IAAIA,GAAG,EAAErB,IAAI,EAAEf,MAAM,EAAE;UACrB,MAAMe,IAAI,GAAIqB,GAAG,EAAErB,IAAI,IAAI,EAAG;UAC9BgE,KAAI,CAAC3F,WAAW,CAAC2E,IAAI,CAAChD,IAAI,CAAC;UAC3B,OAAOA,IAAI;QACb;QACA,OAAO,EAAE;MACX,CAAC,CAAC,CACH,CACAV,IAAI,CACH5B,UAAU,CAAEwG,KAAK,IAAI;QACnBF,KAAI,CAAC3F,WAAW,CAAC2E,IAAI,CAAC,EAAE,CAAC;QACzB,OAAOkB,KAAK;MACd,CAAC,CAAC,CACH,CACJ;IAAC;EACJ;EAEA,IAAIC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC9F,WAAW,EAAE4D,KAAK,IAAI,EAAE;EACtC;EAEAmC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACjG,IAAI,CAACsD,GAAG,CAAoBnE,WAAW,CAAC+G,WAAW,CAAC,CAAC/E,IAAI,CACnEzB,GAAG,CAAEyG,QAAQ,IAAI;MACf,IAAI,CAAChG,WAAW,CAAC0E,IAAI,CAACsB,QAAQ,CAACd,KAAK,CAAC;MACrC,OAAOc,QAAQ,CAACd,KAAe;IACjC,CAAC,CAAC,CACH;EACH;EAEA,IAAIe,QAAQA,CAAA;IACV,OAAOzG,EAAE,CAAC,IAAI,CAACQ,WAAW,EAAE2D,KAAK,IAAI,EAAE,CAAC;EAC1C;;;uBArRWhE,WAAW,EAAAuG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAI,MAAA;IAAA;EAAA;;;aAAX3G,WAAW;MAAA4G,OAAA,EAAX5G,WAAW,CAAA6G,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}