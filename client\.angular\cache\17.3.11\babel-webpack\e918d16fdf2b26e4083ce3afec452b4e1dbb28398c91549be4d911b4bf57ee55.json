{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './login/login.component';\nimport { AuthGuard } from 'src/app/core/authentication/auth.guard';\nimport { SessionComponent } from './session.component';\nimport { contentResolver } from '../core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SessionComponent,\n  children: [{\n    path: 'login',\n    canActivate: [AuthGuard],\n    component: LoginComponent,\n    resolve: {\n      content: contentResolver\n    },\n    data: {\n      slug: 'login'\n    }\n  }, {\n    path: 'reset-password',\n    canActivate: [AuthGuard],\n    loadChildren: () => import('./reset-password/reset-password.module').then(m => m.ResetPasswordModule)\n  }, {\n    path: 'signup',\n    canActivate: [AuthGuard],\n    loadChildren: () => import('./signup/signup.module').then(m => m.SignupModule)\n  }, {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  }]\n}];\nexport class SessionRoutingModule {\n  static {\n    this.ɵfac = function SessionRoutingModule_Factory(t) {\n      return new (t || SessionRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SessionRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SessionRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "<PERSON><PERSON><PERSON><PERSON>", "SessionComponent", "contentResolver", "routes", "path", "component", "children", "canActivate", "resolve", "content", "data", "slug", "loadChildren", "then", "m", "ResetPasswordModule", "SignupModule", "redirectTo", "pathMatch", "SessionRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\session-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { LoginComponent } from './login/login.component';\r\nimport { AuthGuard } from 'src/app/core/authentication/auth.guard';\r\nimport { SessionComponent } from './session.component';\r\nimport { contentResolver } from '../core/content-resolver';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SessionComponent,\r\n    children: [\r\n      {\r\n        path: 'login',\r\n        canActivate: [AuthGuard],\r\n        component: LoginComponent,\r\n        resolve: {\r\n          content: contentResolver,\r\n        },\r\n        data: {\r\n          slug: 'login',\r\n        },\r\n      },\r\n      {\r\n        path: 'reset-password',\r\n        canActivate: [AuthGuard],\r\n        loadChildren: () =>\r\n          import('./reset-password/reset-password.module').then(\r\n            (m) => m.ResetPasswordModule\r\n          ),\r\n      },\r\n      {\r\n        path: 'signup',\r\n        canActivate: [AuthGuard],\r\n        loadChildren: () =>\r\n          import('./signup/signup.module').then((m) => m.SignupModule),\r\n      },\r\n      { path: '', redirectTo: 'login', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SessionRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,SAAS,QAAQ,wCAAwC;AAClE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,gBAAgB;EAC3BK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,OAAO;IACbG,WAAW,EAAE,CAACP,SAAS,CAAC;IACxBK,SAAS,EAAEN,cAAc;IACzBS,OAAO,EAAE;MACPC,OAAO,EAAEP;KACV;IACDQ,IAAI,EAAE;MACJC,IAAI,EAAE;;GAET,EACD;IACEP,IAAI,EAAE,gBAAgB;IACtBG,WAAW,EAAE,CAACP,SAAS,CAAC;IACxBY,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACC,mBAAmB;GAEjC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdG,WAAW,EAAE,CAACP,SAAS,CAAC;IACxBY,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,YAAY;GAC9D,EACD;IAAEZ,IAAI,EAAE,EAAE;IAAEa,UAAU,EAAE,OAAO;IAAEC,SAAS,EAAE;EAAM,CAAE;CAEvD,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBrB,YAAY,CAACsB,QAAQ,CAACjB,MAAM,CAAC,EAC7BL,YAAY;IAAA;EAAA;;;2EAEXqB,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAxB,YAAA;IAAAyB,OAAA,GAFrBzB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}