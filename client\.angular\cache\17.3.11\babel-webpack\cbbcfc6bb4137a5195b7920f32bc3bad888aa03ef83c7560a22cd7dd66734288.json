{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SchedulerComponent } from './scheduler.component';\nimport { AddSchedulerComponent } from './add-scheduler/add-scheduler.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SchedulerComponent\n}, {\n  path: 'add-scheduler',\n  component: AddSchedulerComponent\n}\n// {\n//   path: ':id',\n//   component: UserDetailsComponent,\n//   children: [\n//     { path: 'general', component: GeneralComponent },\n//     { path: 'customer-detail', component: CustomerDetailsComponent },\n//     { path: 'password', component: PasswordComponent },\n//     { path: '', redirectTo: 'general', pathMatch: 'full' },\n//     { path: '**', redirectTo: 'general', pathMatch: 'full' },\n//   ],\n// },\n];\nexport class SchedulerRoutingModule {\n  static {\n    this.ɵfac = function SchedulerRoutingModule_Factory(t) {\n      return new (t || SchedulerRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SchedulerRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SchedulerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SchedulerComponent", "AddSchedulerComponent", "routes", "path", "component", "SchedulerRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\scheduler\\scheduler-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { SchedulerComponent } from './scheduler.component';\r\nimport { AddSchedulerComponent } from './add-scheduler/add-scheduler.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: SchedulerComponent },\r\n  { path: 'add-scheduler', component: AddSchedulerComponent },\r\n  // {\r\n  //   path: ':id',\r\n  //   component: UserDetailsComponent,\r\n  //   children: [\r\n  //     { path: 'general', component: GeneralComponent },\r\n  //     { path: 'customer-detail', component: CustomerDetailsComponent },\r\n  //     { path: 'password', component: PasswordComponent },\r\n  //     { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n  //     { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n  //   ],\r\n  // },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SchedulerRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,qBAAqB,QAAQ,yCAAyC;;;AAE/E,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ;AAAkB,CAAE,EAC3C;EAAEG,IAAI,EAAE,eAAe;EAAEC,SAAS,EAAEH;AAAqB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD;AAMD,OAAM,MAAOI,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAHvBN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXM,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAFvBV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}