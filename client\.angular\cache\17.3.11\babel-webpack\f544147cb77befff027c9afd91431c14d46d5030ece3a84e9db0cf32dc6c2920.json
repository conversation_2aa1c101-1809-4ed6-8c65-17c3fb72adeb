{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/content-vendor.service\";\nimport * as i4 from \"./app.menu.component\";\nconst _c0 = [\"menuContainer\"];\nconst _c1 = () => [\"/\"];\nexport class AppSidebarComponent {\n  constructor(layoutService, el, route, CMSservice) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.timeout = null;\n    this.logo = '';\n  }\n  ngOnInit() {\n    const commonContent = this.route.snapshot.parent?.data['commonContent'];\n    const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"vendor.logo\");\n    this.logo = logoComponent?.logo?.[0]?.url || '';\n  }\n  onMouseEnter() {\n    if (!this.layoutService.state.anchored) {\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = null;\n      }\n      this.layoutService.state.sidebarActive = true;\n    }\n  }\n  onMouseLeave() {\n    if (!this.layoutService.state.anchored) {\n      if (!this.timeout) {\n        this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\n      }\n    }\n  }\n  anchor() {\n    this.layoutService.state.anchored = !this.layoutService.state.anchored;\n  }\n  static {\n    this.ɵfac = function AppSidebarComponent_Factory(t) {\n      return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentVendorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      viewQuery: function AppSidebarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuContainer = _t.first);\n        }\n      },\n      decls: 14,\n      vars: 4,\n      consts: [[\"menuContainer\", \"\"], [1, \"layout-sidebar\", 3, \"mouseenter\", \"mouseleave\"], [1, \"sidebar-header\"], [1, \"app-logo\", 3, \"routerLink\"], [1, \"app-logo-small\", \"h-2rem\"], [3, \"src\"], [1, \"flex\", \"w-full\"], [1, \"w-full\", 3, \"src\"], [\"type\", \"button\", 1, \"layout-sidebar-anchor\", \"p-link\", \"z-2\", 3, \"click\"], [1, \"layout-menu-container\"], [1, \"p-3\"], [1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-outlined\", \"cursor-auto\", \"justify-content-center\", \"m-0\", \"text-base\", \"w-full\", \"h-3rem\"]],\n      template: function AppSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"mouseenter\", function AppSidebarComponent_Template_div_mouseenter_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function AppSidebarComponent_Template_div_mouseleave_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"a\", 3)(3, \"div\", 4);\n          i0.ɵɵelement(4, \"img\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 6);\n          i0.ɵɵelement(6, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AppSidebarComponent_Template_button_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.anchor());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 9, 0);\n          i0.ɵɵelement(10, \"app-menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"h5\", 11);\n          i0.ɵɵtext(13, \"CHS Vendor Portal\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", \"assets/layout/images/chs-logo-\" + (ctx.layoutService.config().colorScheme === \"dark\" ? \"light\" : \"dark\") + \".svg\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", \"assets/layout/images/chs-logo-\" + (ctx.layoutService.config().colorScheme === \"dark\" ? \"light\" : \"dark\") + \".svg\", i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [i2.RouterLink, i4.AppMenuComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "constructor", "layoutService", "el", "route", "CMSservice", "timeout", "logo", "ngOnInit", "commonContent", "snapshot", "parent", "data", "logoComponent", "getDataByComponentName", "body", "url", "onMouseEnter", "state", "anchored", "clearTimeout", "sidebarActive", "onMouseLeave", "setTimeout", "anchor", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "ActivatedRoute", "i3", "ContentVendorService", "selectors", "viewQuery", "AppSidebarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppSidebarComponent_Template_div_mouseenter_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppSidebarComponent_Template_div_mouseleave_0_listener", "ɵɵelement", "ɵɵelementEnd", "AppSidebarComponent_Template_button_click_7_listener", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "config", "colorScheme", "ɵɵsanitizeUrl"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.sidebar.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.sidebar.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { LayoutService } from './service/app.layout.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { ContentVendorService } from 'src/app/shared/services/content-vendor.service';\r\n\r\n@Component({\r\n    selector: 'app-sidebar',\r\n    templateUrl: './app.sidebar.component.html'\r\n})\r\nexport class AppSidebarComponent implements OnInit {\r\n    timeout: any = null;\r\n    public logo = '';\r\n\r\n    @ViewChild('menuContainer') menuContainer!: ElementRef;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n        private route: ActivatedRoute,\r\n        private CMSservice: ContentVendorService\r\n    ) { }\r\n\r\n    ngOnInit(): void {\r\n        const commonContent = this.route.snapshot.parent?.data['commonContent'];\r\n        const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"vendor.logo\");\r\n        this.logo = logoComponent?.logo?.[0]?.url || '';\r\n    }\r\n\r\n\r\n    onMouseEnter() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (this.timeout) {\r\n                clearTimeout(this.timeout);\r\n                this.timeout = null;\r\n            }\r\n            this.layoutService.state.sidebarActive = true;\r\n\r\n\r\n        }\r\n    }\r\n\r\n    onMouseLeave() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (!this.timeout) {\r\n                this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\r\n            }\r\n        }\r\n    }\r\n\r\n    anchor() {\r\n        this.layoutService.state.anchored = !this.layoutService.state.anchored;\r\n    }\r\n\r\n}\r\n", "<div class=\"layout-sidebar\" (mouseenter)=\"onMouseEnter()\" (mouseleave)=\"onMouseLeave()\">\r\n    <div class=\"sidebar-header\">\r\n        <a [routerLink]=\"['/']\" class=\"app-logo\">\r\n            <div class=\"app-logo-small h-2rem\" >\r\n                <img  [src]=\"'assets/layout/images/chs-logo-'+ (layoutService.config().colorScheme === 'dark' ? 'light' : 'dark') + '.svg'\">\r\n            </div>\r\n            <div class=\"flex w-full\">\r\n                <img class=\"w-full\" [src]=\"'assets/layout/images/chs-logo-'+ (layoutService.config().colorScheme === 'dark' ? 'light' : 'dark') + '.svg'\">\r\n                <!-- <img  class=\"h-2rem ml-3\" [src]=\"'assets/layout/images/appname-'+ (layoutService.config().colorScheme === 'light' ? 'dark' : 'light') + '.png'\"/> -->\r\n            </div>\r\n        </a>\r\n        <button class=\"layout-sidebar-anchor p-link z-2 \" type=\"button\" (click)=\"anchor()\"></button>\r\n    </div>\r\n \r\n\r\n    <div #menuContainer class=\"layout-menu-container\">\r\n        <app-menu></app-menu>\r\n    </div>\r\n    <div class=\"p-3\">\r\n        <h5 class=\"p-element p-ripple p-button p-component p-button-outlined cursor-auto justify-content-center m-0 text-base w-full h-3rem\">CHS Vendor Portal</h5>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;AASA,OAAM,MAAOA,mBAAmB;EAM5BC,YACWC,aAA4B,EAC5BC,EAAc,EACbC,KAAqB,EACrBC,UAAgC;IAHjC,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACD,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IATtB,KAAAC,OAAO,GAAQ,IAAI;IACZ,KAAAC,IAAI,GAAG,EAAE;EASZ;EAEJC,QAAQA,CAAA;IACJ,MAAMC,aAAa,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,MAAM,EAAEC,IAAI,CAAC,eAAe,CAAC;IACvE,MAAMC,aAAa,GAAG,IAAI,CAACR,UAAU,CAACS,sBAAsB,CAACL,aAAa,CAACM,IAAI,EAAE,aAAa,CAAC;IAC/F,IAAI,CAACR,IAAI,GAAGM,aAAa,EAAEN,IAAI,GAAG,CAAC,CAAC,EAAES,GAAG,IAAI,EAAE;EACnD;EAGAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACf,aAAa,CAACgB,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,IAAI,CAACb,OAAO,EAAE;QACdc,YAAY,CAAC,IAAI,CAACd,OAAO,CAAC;QAC1B,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;MACA,IAAI,CAACJ,aAAa,CAACgB,KAAK,CAACG,aAAa,GAAG,IAAI;IAGjD;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACpB,aAAa,CAACgB,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,CAAC,IAAI,CAACb,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAGiB,UAAU,CAAC,MAAM,IAAI,CAACrB,aAAa,CAACgB,KAAK,CAACG,aAAa,GAAG,KAAK,EAAE,GAAG,CAAC;MACxF;IACJ;EACJ;EAEAG,MAAMA,CAAA;IACF,IAAI,CAACtB,aAAa,CAACgB,KAAK,CAACC,QAAQ,GAAG,CAAC,IAAI,CAACjB,aAAa,CAACgB,KAAK,CAACC,QAAQ;EAC1E;;;uBA1CSnB,mBAAmB,EAAAyB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAnBjC,mBAAmB;MAAAkC,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCThCZ,EAAA,CAAAc,cAAA,aAAwF;UAA9Bd,EAA9B,CAAAe,UAAA,wBAAAC,uDAAA;YAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAcN,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC,wBAAA4B,uDAAA;YAAApB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAeN,GAAA,CAAAhB,YAAA,EAAc;UAAA,EAAC;UAG3EG,EAFR,CAAAc,cAAA,aAA4B,WACiB,aACD;UAChCd,EAAA,CAAAqB,SAAA,aAA4H;UAChIrB,EAAA,CAAAsB,YAAA,EAAM;UACNtB,EAAA,CAAAc,cAAA,aAAyB;UACrBd,EAAA,CAAAqB,SAAA,aAA0I;UAGlJrB,EADI,CAAAsB,YAAA,EAAM,EACN;UACJtB,EAAA,CAAAc,cAAA,gBAAmF;UAAnBd,EAAA,CAAAe,UAAA,mBAAAQ,qDAAA;YAAAvB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASN,GAAA,CAAAd,MAAA,EAAQ;UAAA,EAAC;UACtFC,EADuF,CAAAsB,YAAA,EAAS,EAC1F;UAGNtB,EAAA,CAAAc,cAAA,gBAAkD;UAC9Cd,EAAA,CAAAqB,SAAA,gBAAqB;UACzBrB,EAAA,CAAAsB,YAAA,EAAM;UAEFtB,EADJ,CAAAc,cAAA,eAAiB,cACwH;UAAAd,EAAA,CAAAwB,MAAA,yBAAiB;UAE9JxB,EAF8J,CAAAsB,YAAA,EAAK,EACzJ,EACJ;;;UAnBKtB,EAAA,CAAAyB,SAAA,GAAoB;UAApBzB,EAAA,CAAA0B,UAAA,eAAA1B,EAAA,CAAA2B,eAAA,IAAAC,GAAA,EAAoB;UAET5B,EAAA,CAAAyB,SAAA,GAAqH;UAArHzB,EAAA,CAAA0B,UAAA,4CAAAb,GAAA,CAAApC,aAAA,CAAAoD,MAAA,GAAAC,WAAA,0CAAA9B,EAAA,CAAA+B,aAAA,CAAqH;UAGvG/B,EAAA,CAAAyB,SAAA,GAAqH;UAArHzB,EAAA,CAAA0B,UAAA,4CAAAb,GAAA,CAAApC,aAAA,CAAAoD,MAAA,GAAAC,WAAA,0CAAA9B,EAAA,CAAA+B,aAAA,CAAqH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}