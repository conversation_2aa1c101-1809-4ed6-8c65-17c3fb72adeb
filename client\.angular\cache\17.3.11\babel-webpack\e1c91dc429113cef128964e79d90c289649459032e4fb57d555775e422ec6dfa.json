{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { catchError, of } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../shared/components/lang-selector/lang-selector.component\";\nimport * as i5 from \"../forgot-password/forgot-password.component\";\nconst _c0 = () => [\"/auth/signup\"];\nexport class LoginComponent {\n  constructor(fb, auth, router, route) {\n    this.fb = fb;\n    this.auth = auth;\n    this.router = router;\n    this.route = route;\n    this.API_ENDPOINT = environment.apiEndpoint;\n    this.isSubmitting = false;\n    this.loginForm = this.fb.nonNullable.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required]],\n      rememberMe: [false]\n    });\n    this.errMsg = null;\n    this.loading = false;\n    this.showPass = false;\n    this.logo = '';\n    // forgotPassword() {\n    //   // this.dialog.open(ForgotPasswordComponent);\n    //\n    this.isDialogVisible = false;\n  }\n  ngOnInit() {\n    this.content = this.route.snapshot.data['content'];\n    this.commonContent = this.route.snapshot.parent?.data['commonContent'];\n  }\n  get email() {\n    return this.loginForm.get('email');\n  }\n  get password() {\n    return this.loginForm.get('password');\n  }\n  get rememberMe() {\n    return this.loginForm.get('rememberMe');\n  }\n  login() {\n    this.isSubmitting = true;\n    this.auth.login(this.email.value, this.password.value, this.rememberMe.value).pipe(catchError(err => of(err.error))).subscribe(res => {\n      this.isSubmitting = false;\n      if (res.jwt) {\n        this.router.navigate(['store']);\n      } else {\n        this.errMsg = res?.error?.message || 'Error while login';\n      }\n    });\n  }\n  reset() {\n    this.loginForm.reset();\n    this.errMsg = null;\n  }\n  forgotPassword() {\n    this.isDialogVisible = true;\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 54,\n      vars: 14,\n      consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"lg:pt-6\", \"pb-6\", \"md:pt-4\", \"pb-4\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"xl:max-w-15rem\", \"lg:max-w-12rem\", \"max-w-12rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\", \"lg:p-6\", \"md:p-4\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"form-group\", \"relative\", \"mb-3\"], [\"type\", \"email\", \"id\", \"username\", \"formControlName\", \"email\", \"placeholder\", \"Enter Email Address\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [\"formControlName\", \"password\", \"placeholder\", \"Enter Password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"type\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\", 3, \"click\"], [1, \"form-group\", \"relative\", \"mb-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"type\", \"checkbox\", \"formControlName\", \"rememberMe\", \"id\", \"exampleCheck1\", 1, \"form-check-box\", \"m-0\", \"w-1rem\", \"h-1rem\"], [\"for\", \"exampleCheck1\", 1, \"text-m\", \"text-gray-700\"], [\"type\", \"button\", 1, \"p-component\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\", \"border-none\", \"bg-white-alpha-10\", \"underline\", \"cursor-pointer\", 3, \"click\"], [1, \"form-footer\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\", 3, \"click\", \"disabled\"], [1, \"mt-3\", \"mb-3\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\"], [1, \"material-symbols-rounded\"], [1, \"copyright-sec\", \"flex\", \"flex-column\", \"position-relative\"], [1, \"m-0\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-0\", \"flex\", \"position-relative\", \"align-items-center\", \"justify-content-center\", \"list-none\", \"gap-3\"], [\"target\", \"_blank\", 1, \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-primary\", \"underline\", 3, \"href\"], [3, \"visibleChange\", \"visible\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(6, \"app-lang-selector\");\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵtext(8, \" You don't have any account? \");\n          i0.ɵɵelementStart(9, \"button\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"form\", 10)(15, \"h1\", 11);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 12);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 13);\n          i0.ɵɵelement(20, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 13);\n          i0.ɵɵelement(22, \"input\", 15);\n          i0.ɵɵelementStart(23, \"button\", 16)(24, \"span\", 17);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_24_listener() {\n            return ctx.showPass = !ctx.showPass;\n          });\n          i0.ɵɵtext(25, \"visibility\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 18)(27, \"div\", 19);\n          i0.ɵɵelement(28, \"input\", 20);\n          i0.ɵɵelementStart(29, \"label\", 21);\n          i0.ɵɵtext(30, \"Remember Me\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_31_listener() {\n            return ctx.forgotPassword();\n          });\n          i0.ɵɵtext(32, \"Having Trouble in Login?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 23)(34, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_34_listener() {\n            return ctx.login();\n          });\n          i0.ɵɵtext(35, \" Login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"span\");\n          i0.ɵɵtext(38, \"Or Login With\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"a\", 26)(40, \"span\", 27);\n          i0.ɵɵtext(41, \"key\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Login with SSO\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 28)(44, \"p\", 29);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"ul\", 30)(47, \"li\")(48, \"a\", 31);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"li\")(51, \"a\", 31);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"app-forgot-password\", 32);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LoginComponent_Template_app_forgot_password_visibleChange_53_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isDialogVisible, $event) || (ctx.isDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", (ctx.content == null ? null : ctx.content.i18n == null ? null : ctx.content.i18n[\"label.title\"]) || \"\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.content == null ? null : ctx.content.i18n == null ? null : ctx.content.i18n[\"label.subtitle\"]) || \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.showPass ? \"text\" : \"password\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"disabled\", !!ctx.loginForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"href\", ctx.API_ENDPOINT + \"/auth/signin\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n.label == null ? null : ctx.commonContent.i18n.label.copyright) || \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"href\", (ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"link.tearmsAndConditions\"]) || \"\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate((ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"label.copyright\"]) || \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"href\", (ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n[\"link.privacyPolicy\"]) || \"\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate((ctx.commonContent == null ? null : ctx.commonContent.i18n == null ? null : ctx.commonContent.i18n.label == null ? null : ctx.commonContent.i18n.label.privacyPolicy) || \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.isDialogVisible);\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i4.LangSelectorComponent, i5.ForgotPasswordComponent],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 480px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n\\n@media only screen and (max-width: 996px) {\\n  .login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n    max-width: 380px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2Vzc2lvbi9sb2dpbi9sb2dpbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQztFQUNDLGlCQUFBO0FBQUY7QUFFRTtFQUNDLDJCQUFBO0FBQUg7QUFJSztFQUNDLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtBQUZOO0FBS0s7RUFDQyxpQ0FBQTtBQUhOOztBQVlBO0VBQ0MsWUFBQTtFQUNBLDJCQUFBO0FBVEQ7O0FBWUE7RUFDQyxjQUFBO0FBVEQ7O0FBWUE7RUFHRztJQUNDLDJCQUFBO0VBWEY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5sb2dpbi1zZWMge1xyXG5cdC5sb2dpbi1wYWdlLWJvZHkge1xyXG5cdFx0bWF4LXdpZHRoOiAxNDQwcHg7XHJcblxyXG5cdFx0LmxvZ2luLWZvcm0ge1xyXG5cdFx0XHRtYXgtd2lkdGg6IDQ4MHB4ICFpbXBvcnRhbnQ7XHJcblxyXG5cdFx0XHRmb3JtIHtcclxuXHRcdFx0XHQuZm9ybS1ncm91cCB7XHJcblx0XHRcdFx0XHQucGFzcy1zaG93LWJ0biB7XHJcblx0XHRcdFx0XHRcdHJpZ2h0OiAxMnB4O1xyXG5cdFx0XHRcdFx0XHRoZWlnaHQ6IDI0cHg7XHJcblx0XHRcdFx0XHRcdHdpZHRoOiAyNHB4O1xyXG5cdFx0XHRcdFx0fVxyXG5cclxuXHRcdFx0XHRcdC5mb3JtLWNoZWNrLWJveCB7XHJcblx0XHRcdFx0XHRcdGFjY2VudC1jb2xvcjogdmFyKC0tcHJpbWFyeWNvbG9yKTtcclxuXHRcdFx0XHRcdH1cclxuXHRcdFx0XHR9XHJcblx0XHRcdH1cclxuXHRcdH1cclxuXHR9XHJcbn1cclxuXHJcblxyXG4ucC1pbnB1dHRleHQge1xyXG5cdGhlaWdodDogM3JlbTtcclxuXHRhcHBlYXJhbmNlOiBhdXRvICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5oLTMtM3JlbSB7XHJcblx0aGVpZ2h0OiAzLjNyZW07XHJcbn1cclxuXHJcbkBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDo5OTZweCkge1xyXG5cdC5sb2dpbi1zZWMge1xyXG5cdFx0LmxvZ2luLXBhZ2UtYm9keSB7XHJcblx0XHRcdC5sb2dpbi1mb3JtIHtcclxuXHRcdFx0XHRtYXgtd2lkdGg6IDM4MHB4ICFpbXBvcnRhbnQ7XHJcblx0XHRcdH1cclxuXHRcdH1cclxuXHR9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "catchError", "of", "environment", "LoginComponent", "constructor", "fb", "auth", "router", "route", "API_ENDPOINT", "apiEndpoint", "isSubmitting", "loginForm", "nonNullable", "group", "email", "required", "password", "rememberMe", "errMsg", "loading", "showPass", "logo", "isDialogVisible", "ngOnInit", "content", "snapshot", "data", "commonContent", "parent", "get", "login", "value", "pipe", "err", "error", "subscribe", "res", "jwt", "navigate", "message", "reset", "forgotPassword", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_span_click_24_listener", "LoginComponent_Template_button_click_31_listener", "LoginComponent_Template_button_click_34_listener", "ɵɵtwoWayListener", "LoginComponent_Template_app_forgot_password_visibleChange_53_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "i18n", "ɵɵtextInterpolate", "invalid", "label", "copyright", "ɵɵsanitizeUrl", "privacyPolicy", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\login\\login.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { catchError, of } from 'rxjs';\r\nimport { ForgotPasswordComponent } from '../forgot-password/forgot-password.component';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  public API_ENDPOINT: any = environment.apiEndpoint;\r\n  public isSubmitting = false;\r\n  public loginForm = this.fb.nonNullable.group({\r\n    email: ['', [Validators.required, Validators.email]],\r\n    password: ['', [Validators.required]],\r\n    rememberMe: [false],\r\n  });\r\n  public errMsg: any = null;\r\n  public loading = false;\r\n  public loginPageDetails!: any;\r\n  public showPass = false;\r\n  public logo = '';\r\n  public content!: any;\r\n  public commonContent!: any;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private auth: AuthService,\r\n    public router: Router,\r\n    private route: ActivatedRoute\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.content = this.route.snapshot.data['content'];\r\n    this.commonContent = this.route.snapshot.parent?.data['commonContent'];\r\n  }\r\n\r\n  get email() {\r\n    return this.loginForm.get('email')!;\r\n  }\r\n\r\n  get password() {\r\n    return this.loginForm.get('password')!;\r\n  }\r\n\r\n  get rememberMe() {\r\n    return this.loginForm.get('rememberMe')!;\r\n  }\r\n\r\n  login() {\r\n    this.isSubmitting = true;\r\n    this.auth\r\n      .login(this.email.value, this.password.value, this.rememberMe.value)\r\n      .pipe(catchError((err) => of(err.error)))\r\n      .subscribe((res: any) => {\r\n        this.isSubmitting = false;\r\n        if (res.jwt) {\r\n          this.router.navigate(['store']);\r\n        } else {\r\n          this.errMsg = res?.error?.message || 'Error while login';\r\n        }\r\n      });\r\n  }\r\n\r\n  reset() {\r\n    this.loginForm.reset();\r\n    this.errMsg = null;\r\n  }\r\n\r\n  // forgotPassword() {\r\n  //   // this.dialog.open(ForgotPasswordComponent);\r\n  //\r\n\r\n  isDialogVisible: boolean = false;\r\n\r\n  forgotPassword() {\r\n    this.isDialogVisible = true;\r\n  }\r\n}\r\n", "<section class=\"login-sec bg-white h-screen lg:pt-6 pb-6 md:pt-4 pb-4\">\r\n  <div class=\"login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full px-5\">\r\n    <div class=\"login-header relative flex align-items-center justify-content-between\">\r\n      <div class=\"logo w-full xl:max-w-15rem lg:max-w-12rem max-w-12rem\">\r\n        <a href=\"#\" class=\"flex w-full\"><img src=\"/assets/layout/images/chs-logo.svg\" alt=\"Logo\" class=\"w-full\" /></a>\r\n      </div>\r\n      <app-lang-selector></app-lang-selector>\r\n      <div class=\"sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium\">\r\n        You don't have any account?\r\n        <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n          class=\"sign-up-btn p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1\">\r\n          <span class=\"material-symbols-rounded text-2xl\">person</span> Sign Up\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"login-form mx-auto w-full bg-white border-round-3xl shadow-2 lg:p-6 md:p-4\">\r\n      <form class=\"flex flex-column position-relative\" [formGroup]=\"loginForm\">\r\n        <h1 class=\"mb-2 flex justify-content-center text-4xl font-bold text-primary\">{{\r\n          content?.i18n?.['label.title']\r\n          || '' }}\r\n        </h1>\r\n        <p class=\"mb-5 flex justify-content-center text-base font-medium text-gray-900\">{{\r\n          content?.i18n?.['label.subtitle']\r\n          || '' }}</p>\r\n        <div class=\"form-group relative mb-3\">\r\n          <input type=\"email\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\"\r\n            formControlName=\"email\" placeholder=\"Enter Email Address\" />\r\n        </div>\r\n        <div class=\"form-group relative mb-3\">\r\n          <input [type]=\"showPass ? 'text' : 'password'\" class=\"p-inputtext p-component p-element w-full bg-gray-50\"\r\n            formControlName=\"password\" placeholder=\"Enter Password\" />\r\n          <button type=\"button\"\r\n            class=\"pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer\"><span\r\n              class=\"material-symbols-rounded\" (click)=\"showPass = !showPass\">visibility</span></button>\r\n        </div>\r\n        <div class=\"form-group relative mb-4 flex align-items-center justify-content-between\">\r\n          <div class=\"flex align-items-center gap-1\">\r\n            <input type=\"checkbox\" class=\"form-check-box m-0 w-1rem h-1rem\" formControlName=\"rememberMe\"\r\n              id=\"exampleCheck1\" />\r\n            <label class=\"text-m text-gray-700\" for=\"exampleCheck1\">Remember Me</label>\r\n          </div>\r\n          <button type=\"button\"\r\n            class=\"p-component flex justify-content-center text-base font-medium text-gray-900 border-none bg-white-alpha-10 underline cursor-pointer\"\r\n            (click)=\"forgotPassword()\">Having Trouble in Login?</button>\r\n        </div>\r\n        <div class=\"form-footer\">\r\n          <button type=\"button\"\r\n            class=\"p-element p-ripple p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold\"\r\n            [disabled]=\"!!loginForm.invalid || isSubmitting\" (click)=\"login()\"> Login</button>\r\n          <div class=\"mt-3 mb-3 flex justify-content-center text-base font-medium text-gray-900\"><span>Or Login\r\n              With</span></div>\r\n          <a type=\"button\"\r\n            class=\"p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20\"\r\n            [attr.href]=\"API_ENDPOINT + '/auth/signin'\"><span class=\"material-symbols-rounded\">key</span> Login with\r\n            SSO</a>\r\n        </div>\r\n\r\n      </form>\r\n    </div>\r\n    <div class=\"copyright-sec flex flex-column position-relative\">\r\n      <p class=\"m-0 flex justify-content-center text-base font-medium text-gray-900\">{{\r\n        commonContent?.i18n?.label?.copyright\r\n        || '' }}</p>\r\n      <ul class=\"p-0 flex position-relative align-items-center justify-content-center list-none gap-3\">\r\n        <li><a [href]=\"commonContent?.i18n?.['link.tearmsAndConditions'] || ''\" target=\"_blank\"\r\n            class=\"flex justify-content-center text-base font-medium text-primary underline\">{{\r\n              commonContent?.i18n?.['label.copyright']\r\n              || '' }}</a></li>\r\n        <li><a [href]=\"commonContent?.i18n?.['link.privacyPolicy'] || ''\" target=\"_blank\"\r\n            class=\"flex justify-content-center text-base font-medium text-primary underline\">{{\r\n              commonContent?.i18n?.label?.privacyPolicy\r\n\r\n              || '' }}</a></li>\r\n      </ul>\r\n\r\n      <app-forgot-password [(visible)]=\"isDialogVisible\"></app-forgot-password>\r\n\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAAsBA,UAAU,QAAQ,gBAAgB;AAExD,SAASC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAErC,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;AAQ1D,OAAM,MAAOC,cAAc;EAgBzBC,YACUC,EAAe,EACfC,IAAiB,EAClBC,MAAc,EACbC,KAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,IAAI,GAAJA,IAAI;IACL,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,KAAK,GAALA,KAAK;IAnBR,KAAAC,YAAY,GAAQP,WAAW,CAACQ,WAAW;IAC3C,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,SAAS,GAAG,IAAI,CAACP,EAAE,CAACQ,WAAW,CAACC,KAAK,CAAC;MAC3CC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACgB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACiB,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;IACK,KAAAC,MAAM,GAAQ,IAAI;IAClB,KAAAC,OAAO,GAAG,KAAK;IAEf,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,IAAI,GAAG,EAAE;IAgDhB;IACA;IACA;IAEA,KAAAC,eAAe,GAAY,KAAK;EA3C5B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,OAAO,GAAG,IAAI,CAACjB,KAAK,CAACkB,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACpB,KAAK,CAACkB,QAAQ,CAACG,MAAM,EAAEF,IAAI,CAAC,eAAe,CAAC;EACxE;EAEA,IAAIZ,KAAKA,CAAA;IACP,OAAO,IAAI,CAACH,SAAS,CAACkB,GAAG,CAAC,OAAO,CAAE;EACrC;EAEA,IAAIb,QAAQA,CAAA;IACV,OAAO,IAAI,CAACL,SAAS,CAACkB,GAAG,CAAC,UAAU,CAAE;EACxC;EAEA,IAAIZ,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACN,SAAS,CAACkB,GAAG,CAAC,YAAY,CAAE;EAC1C;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACpB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,IAAI,CACNyB,KAAK,CAAC,IAAI,CAAChB,KAAK,CAACiB,KAAK,EAAE,IAAI,CAACf,QAAQ,CAACe,KAAK,EAAE,IAAI,CAACd,UAAU,CAACc,KAAK,CAAC,CACnEC,IAAI,CAACjC,UAAU,CAAEkC,GAAG,IAAKjC,EAAE,CAACiC,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC,CACxCC,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAAC1B,YAAY,GAAG,KAAK;MACzB,IAAI0B,GAAG,CAACC,GAAG,EAAE;QACX,IAAI,CAAC/B,MAAM,CAACgC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,IAAI,CAACpB,MAAM,GAAGkB,GAAG,EAAEF,KAAK,EAAEK,OAAO,IAAI,mBAAmB;MAC1D;IACF,CAAC,CAAC;EACN;EAEAC,KAAKA,CAAA;IACH,IAAI,CAAC7B,SAAS,CAAC6B,KAAK,EAAE;IACtB,IAAI,CAACtB,MAAM,GAAG,IAAI;EACpB;EAQAuB,cAAcA,CAAA;IACZ,IAAI,CAACnB,eAAe,GAAG,IAAI;EAC7B;;;uBApEWpB,cAAc,EAAAwC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAAdhD,cAAc;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnBf,EAJR,CAAAiB,cAAA,iBAAuE,aAC+B,aACf,aACd,WACjC;UAAAjB,EAAA,CAAAkB,SAAA,aAA0E;UAC5GlB,EAD4G,CAAAmB,YAAA,EAAI,EAC1G;UACNnB,EAAA,CAAAkB,SAAA,wBAAuC;UACvClB,EAAA,CAAAiB,cAAA,aAA6G;UAC3GjB,EAAA,CAAAoB,MAAA,oCACA;UAEEpB,EAFF,CAAAiB,cAAA,gBACqI,eACnF;UAAAjB,EAAA,CAAAoB,MAAA,cAAM;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,iBAChE;UAEJpB,EAFI,CAAAmB,YAAA,EAAS,EACL,EACF;UAGFnB,EAFJ,CAAAiB,cAAA,cAAwF,gBACb,cACM;UAAAjB,EAAA,CAAAoB,MAAA,IAG7E;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACLnB,EAAA,CAAAiB,cAAA,aAAgF;UAAAjB,EAAA,CAAAoB,MAAA,IAEtE;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACdnB,EAAA,CAAAiB,cAAA,eAAsC;UACpCjB,EAAA,CAAAkB,SAAA,iBAC8D;UAChElB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAiB,cAAA,eAAsC;UACpCjB,EAAA,CAAAkB,SAAA,iBAC4D;UAE0DlB,EADtH,CAAAiB,cAAA,kBACsH,gBAClD;UAA/BjB,EAAA,CAAAqB,UAAA,mBAAAC,+CAAA;YAAA,OAAAN,GAAA,CAAAtC,QAAA,IAAAsC,GAAA,CAAAtC,QAAA;UAAA,EAA8B;UAACsB,EAAA,CAAAoB,MAAA,kBAAU;UAChFpB,EADgF,CAAAmB,YAAA,EAAO,EAAS,EAC1F;UAEJnB,EADF,CAAAiB,cAAA,eAAsF,eACzC;UACzCjB,EAAA,CAAAkB,SAAA,iBACuB;UACvBlB,EAAA,CAAAiB,cAAA,iBAAwD;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UACrEpB,EADqE,CAAAmB,YAAA,EAAQ,EACvE;UACNnB,EAAA,CAAAiB,cAAA,kBAE6B;UAA3BjB,EAAA,CAAAqB,UAAA,mBAAAE,iDAAA;YAAA,OAASP,GAAA,CAAAjB,cAAA,EAAgB;UAAA,EAAC;UAACC,EAAA,CAAAoB,MAAA,gCAAwB;UACvDpB,EADuD,CAAAmB,YAAA,EAAS,EAC1D;UAEJnB,EADF,CAAAiB,cAAA,eAAyB,kBAG8C;UAAlBjB,EAAA,CAAAqB,UAAA,mBAAAG,iDAAA;YAAA,OAASR,GAAA,CAAA5B,KAAA,EAAO;UAAA,EAAC;UAAEY,EAAA,CAAAoB,MAAA,cAAK;UAAApB,EAAA,CAAAmB,YAAA,EAAS;UACGnB,EAAvF,CAAAiB,cAAA,eAAuF,YAAM;UAAAjB,EAAA,CAAAoB,MAAA,qBACrF;UAAOpB,EAAP,CAAAmB,YAAA,EAAO,EAAM;UAGyBnB,EAF9C,CAAAiB,cAAA,aAE8C,gBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,WAAG;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,uBAC3F;UAIXpB,EAJW,CAAAmB,YAAA,EAAI,EACL,EAED,EACH;UAEJnB,EADF,CAAAiB,cAAA,eAA8D,aACmB;UAAAjB,EAAA,CAAAoB,MAAA,IAErE;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UAERnB,EADN,CAAAiB,cAAA,cAAiG,UAC3F,aACiF;UAAAjB,EAAA,CAAAoB,MAAA,IAEvE;UAAIpB,EAAJ,CAAAmB,YAAA,EAAI,EAAK;UACnBnB,EAAJ,CAAAiB,cAAA,UAAI,aACiF;UAAAjB,EAAA,CAAAoB,MAAA,IAGvE;UAChBpB,EADgB,CAAAmB,YAAA,EAAI,EAAK,EACpB;UAELnB,EAAA,CAAAiB,cAAA,+BAAmD;UAA9BjB,EAAA,CAAAyB,gBAAA,2BAAAC,sEAAAC,MAAA;YAAA3B,EAAA,CAAA4B,kBAAA,CAAAZ,GAAA,CAAApC,eAAA,EAAA+C,MAAA,MAAAX,GAAA,CAAApC,eAAA,GAAA+C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAIxD3B,EAJyD,CAAAmB,YAAA,EAAsB,EAErE,EACF,EACE;;;UAtEoBnB,EAAA,CAAA6B,SAAA,GAA+B;UAA/B7B,EAAA,CAAA8B,UAAA,eAAA9B,EAAA,CAAA+B,eAAA,KAAAC,GAAA,EAA+B;UAONhC,EAAA,CAAA6B,SAAA,GAAuB;UAAvB7B,EAAA,CAAA8B,UAAA,cAAAd,GAAA,CAAA/C,SAAA,CAAuB;UACO+B,EAAA,CAAA6B,SAAA,GAG7E;UAH6E7B,EAAA,CAAAiC,kBAAA,MAAAjB,GAAA,CAAAlC,OAAA,kBAAAkC,GAAA,CAAAlC,OAAA,CAAAoD,IAAA,kBAAAlB,GAAA,CAAAlC,OAAA,CAAAoD,IAAA,4BAG7E;UACgFlC,EAAA,CAAA6B,SAAA,GAEtE;UAFsE7B,EAAA,CAAAmC,iBAAA,EAAAnB,GAAA,CAAAlC,OAAA,kBAAAkC,GAAA,CAAAlC,OAAA,CAAAoD,IAAA,kBAAAlB,GAAA,CAAAlC,OAAA,CAAAoD,IAAA,0BAEtE;UAMDlC,EAAA,CAAA6B,SAAA,GAAuC;UAAvC7B,EAAA,CAAA8B,UAAA,SAAAd,GAAA,CAAAtC,QAAA,uBAAuC;UAmB5CsB,EAAA,CAAA6B,SAAA,IAAgD;UAAhD7B,EAAA,CAAA8B,UAAA,eAAAd,GAAA,CAAA/C,SAAA,CAAAmE,OAAA,IAAApB,GAAA,CAAAhD,YAAA,CAAgD;UAKhDgC,EAAA,CAAA6B,SAAA,GAA2C;;UAO8B7B,EAAA,CAAA6B,SAAA,GAErE;UAFqE7B,EAAA,CAAAmC,iBAAA,EAAAnB,GAAA,CAAA/B,aAAA,kBAAA+B,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,kBAAAlB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,CAAAG,KAAA,kBAAArB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,CAAAG,KAAA,CAAAC,SAAA,QAErE;UAEDtC,EAAA,CAAA6B,SAAA,GAAgE;UAAhE7B,EAAA,CAAA8B,UAAA,UAAAd,GAAA,CAAA/B,aAAA,kBAAA+B,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,kBAAAlB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,qCAAAlC,EAAA,CAAAuC,aAAA,CAAgE;UACcvC,EAAA,CAAA6B,SAAA,EAEvE;UAFuE7B,EAAA,CAAAmC,iBAAA,EAAAnB,GAAA,CAAA/B,aAAA,kBAAA+B,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,kBAAAlB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,2BAEvE;UACPlC,EAAA,CAAA6B,SAAA,GAA0D;UAA1D7B,EAAA,CAAA8B,UAAA,UAAAd,GAAA,CAAA/B,aAAA,kBAAA+B,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,kBAAAlB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,+BAAAlC,EAAA,CAAAuC,aAAA,CAA0D;UACoBvC,EAAA,CAAA6B,SAAA,EAGvE;UAHuE7B,EAAA,CAAAmC,iBAAA,EAAAnB,GAAA,CAAA/B,aAAA,kBAAA+B,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,kBAAAlB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,CAAAG,KAAA,kBAAArB,GAAA,CAAA/B,aAAA,CAAAiD,IAAA,CAAAG,KAAA,CAAAG,aAAA,QAGvE;UAGKxC,EAAA,CAAA6B,SAAA,EAA6B;UAA7B7B,EAAA,CAAAyC,gBAAA,YAAAzB,GAAA,CAAApC,eAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}