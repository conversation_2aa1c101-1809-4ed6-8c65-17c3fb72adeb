{"ast": null, "code": "import { NgSelectModule } from '@ng-select/ng-select';\nimport { CommonModule } from '@angular/common';\nimport { ToastModule } from 'primeng/toast';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { AdministratorComponent } from './administrator.component';\nimport { RegisterAdminComponent } from './register-admin/register-admin.component';\nimport { AdminDetailsComponent } from './admin-details/admin-details.component';\nimport { CustomerDetailsComponent } from './admin-details/customer-details/customer-details.component';\nimport { GeneralComponent } from './admin-details/general/general.component';\nimport { PasswordComponent } from './admin-details/password/password.component';\nimport { AdministratorRoutingModule } from './administrator-routing.module';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { SupplierDetailsComponent } from './admin-details/supplier-details/supplier-details.component';\nimport * as i0 from \"@angular/core\";\nexport class AdministratorModule {\n  static {\n    this.ɵfac = function AdministratorModule_Factory(t) {\n      return new (t || AdministratorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdministratorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, NgSelectModule, ToastModule, AdministratorRoutingModule, FormsModule, TableModule, TabMenuModule, ButtonModule, InputTextModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule, InputSwitchModule, ConfirmDialogModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdministratorModule, {\n    declarations: [AdministratorComponent, CustomerDetailsComponent, SupplierDetailsComponent, RegisterAdminComponent, AdminDetailsComponent, GeneralComponent, PasswordComponent],\n    imports: [CommonModule, NgSelectModule, ToastModule, AdministratorRoutingModule, FormsModule, TableModule, TabMenuModule, ButtonModule, InputTextModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule, InputSwitchModule, ConfirmDialogModule]\n  });\n})();", "map": {"version": 3, "names": ["NgSelectModule", "CommonModule", "ToastModule", "FormsModule", "ReactiveFormsModule", "TableModule", "TabMenuModule", "ButtonModule", "InputTextModule", "InputTextareaModule", "DropdownModule", "InputSwitchModule", "CheckboxModule", "MessageService", "ConfirmationService", "AdministratorComponent", "RegisterAdminComponent", "AdminDetailsComponent", "CustomerDetailsComponent", "GeneralComponent", "PasswordComponent", "AdministratorRoutingModule", "ConfirmDialogModule", "SupplierDetailsComponent", "AdministratorModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\administrator\\administrator.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { TabMenuModule } from 'primeng/tabmenu';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { AdministratorComponent } from './administrator.component';\r\nimport { RegisterAdminComponent } from './register-admin/register-admin.component';\r\nimport { AdminDetailsComponent } from './admin-details/admin-details.component';\r\nimport { CustomerDetailsComponent } from './admin-details/customer-details/customer-details.component';\r\nimport { GeneralComponent } from './admin-details/general/general.component';\r\nimport { PasswordComponent } from './admin-details/password/password.component';\r\nimport { AdministratorRoutingModule } from './administrator-routing.module';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { SupplierDetailsComponent } from './admin-details/supplier-details/supplier-details.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AdministratorComponent,\r\n    CustomerDetailsComponent,\r\n    SupplierDetailsComponent,\r\n    RegisterAdminComponent,\r\n    AdminDetailsComponent,\r\n    GeneralComponent,\r\n    PasswordComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    NgSelectModule,\r\n    ToastModule,\r\n    AdministratorRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    TabMenuModule,\r\n    ButtonModule,\r\n    InputTextModule,\r\n    InputTextareaModule,\r\n    DropdownModule,\r\n    ReactiveFormsModule,\r\n    CheckboxModule,\r\n    InputSwitchModule,\r\n    ConfirmDialogModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class AdministratorModule {}\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,wBAAwB,QAAQ,6DAA6D;AACtG,SAASC,gBAAgB,QAAQ,2CAA2C;AAC5E,SAASC,iBAAiB,QAAQ,6CAA6C;AAC/E,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,wBAAwB,QAAQ,6DAA6D;;AA+BtG,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAFnB,CAACX,cAAc,EAAEC,mBAAmB,CAAC;MAAAW,OAAA,GAhB9CxB,YAAY,EACZD,cAAc,EACdE,WAAW,EACXmB,0BAA0B,EAC1BlB,WAAW,EACXE,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdN,mBAAmB,EACnBQ,cAAc,EACdD,iBAAiB,EACjBW,mBAAmB;IAAA;EAAA;;;2EAIVE,mBAAmB;IAAAE,YAAA,GA3B5BX,sBAAsB,EACtBG,wBAAwB,EACxBK,wBAAwB,EACxBP,sBAAsB,EACtBC,qBAAqB,EACrBE,gBAAgB,EAChBC,iBAAiB;IAAAK,OAAA,GAGjBxB,YAAY,EACZD,cAAc,EACdE,WAAW,EACXmB,0BAA0B,EAC1BlB,WAAW,EACXE,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdN,mBAAmB,EACnBQ,cAAc,EACdD,iBAAiB,EACjBW,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}