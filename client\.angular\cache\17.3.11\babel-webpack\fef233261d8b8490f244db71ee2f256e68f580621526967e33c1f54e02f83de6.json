{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { fork<PERSON><PERSON>n, Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../product.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/toast\";\nfunction CategoryComponent_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const c_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", c_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(c_r1.name);\n  }\n}\nfunction CategoryComponent_div_9_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Categories is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CategoryComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, CategoryComponent_div_9_small_1_Template, 2, 0, \"small\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f.product_categories_ids.errors == null ? null : ctx_r1.f.product_categories_ids.errors[\"required\"]);\n  }\n}\nfunction CategoryComponent_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const c_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", c_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(c_r3.name);\n  }\n}\nfunction CategoryComponent_div_16_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const pch_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(pch_r4);\n  }\n}\nfunction CategoryComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 4);\n    i0.ɵɵtext(2, \"Category hierarchy:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 5)(4, \"ol\", 17);\n    i0.ɵɵtemplate(5, CategoryComponent_div_16_li_5_Template, 3, 1, \"li\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categoryForm.controls == null ? null : ctx_r1.categoryForm.controls.categoryHierarchy == null ? null : ctx_r1.categoryForm.controls.categoryHierarchy.value);\n  }\n}\nexport class CategoryComponent {\n  constructor(fb, productservice, messageservice) {\n    this.fb = fb;\n    this.productservice = productservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.category = null;\n    this.categories = [];\n    this.selectedCategory = null;\n    this.catalogs = [];\n    this.selectedCatalog = null;\n    this.categoryForm = this.fb.group({\n      product_id: '',\n      product_categories_ids: ['', Validators.required],\n      categoryHierarchy: [[]],\n      product_catalogs_ids: ['']\n    });\n  }\n  ngOnInit() {\n    this.productservice.product.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.category = data;\n      this.onCategoryChange();\n      this.updateFormValues();\n    });\n  }\n  updateFormValues() {\n    this.categoryForm.patchValue(this.category);\n    forkJoin({\n      catagories: this.productservice.getAllCategory(),\n      catalogs: this.productservice.getAllCatalog()\n    }).subscribe(({\n      catagories,\n      catalogs\n    }) => {\n      this.categories = this.addCategoryHierarchy(catagories);\n      const productCatagories = this.category?.categories[0];\n      this.setSelectedPCH(productCatagories?.id);\n      if (this.category?.categories && this.category.categories.length) {\n        const product_categories_ids = this.category.categories[0]?.id || '';\n        this.categoryForm.controls.product_categories_ids.patchValue(product_categories_ids);\n      }\n      this.catalogs = catalogs;\n      if (this.category?.catalogs && this.category.catalogs.length) {\n        const product_catalogs_ids = this.category.catalogs[0]?.id || '';\n        this.categoryForm.controls.product_catalogs_ids.patchValue(product_catalogs_ids);\n      }\n    });\n  }\n  onCategoryChange() {\n    this.categoryForm.get('product_categories_ids').valueChanges.subscribe(value => {\n      this.setSelectedPCH(value);\n    });\n  }\n  setSelectedPCH(category_id) {\n    const pch = this.categories.find(o => category_id == o.id);\n    if (pch) {\n      this.categoryForm.controls.categoryHierarchy.patchValue(pch.parent_category_hierarchy);\n    } else {\n      const arr = [];\n      this.categoryForm.controls.categoryHierarchy.patchValue(arr);\n    }\n  }\n  addCategoryHierarchy(categories) {\n    const categoryMap = new Map();\n    const resultArray = [];\n    // Step 1: Create a map of categories using their IDs as keys\n    categories.forEach(category => {\n      categoryMap.set(category.id, {\n        ...category,\n        parent_category_hierarchy: []\n      });\n    });\n    // Step 2: Traverse the categories and add their parent hierarchy\n    categories.forEach(category => {\n      let parentId = category.parent_category_id;\n      const currentCategory = categoryMap.get(category.id);\n      while (parentId !== null) {\n        const parentCategory = categoryMap.get(parentId);\n        if (parentCategory) {\n          currentCategory.parent_category_hierarchy.unshift(parentCategory.name);\n          parentId = parentCategory.parent_category_id;\n        } else {\n          break;\n        }\n      }\n      if (currentCategory.parent_category_hierarchy.indexOf(currentCategory.name) === -1) {\n        currentCategory.parent_category_hierarchy.push(currentCategory.name);\n      }\n      resultArray.push(currentCategory);\n    });\n    return resultArray;\n  }\n  submitCategoryForm() {\n    const catFormVal = this.categoryForm.value;\n    const categaoryPayload = {\n      categories: catFormVal.product_categories_ids\n    };\n    const catalogPayload = {\n      catalogs: catFormVal.product_catalogs_ids\n    };\n    forkJoin({\n      saveProductCategory: this.productservice.updateProduct(this.category.documentId, categaoryPayload),\n      saveProductCatalog: this.productservice.updateProduct(this.category.documentId, catalogPayload)\n    }).pipe(takeUntil(this.unsubscribe$)).subscribe(({\n      saveProductCategory,\n      saveProductCatalog\n    }) => {\n      this.messageservice.add({\n        severity: 'success',\n        detail: 'Changes Saved Successfully!'\n      });\n    });\n  }\n  get f() {\n    return this.categoryForm.controls;\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function CategoryComponent_Factory(t) {\n      return new (t || CategoryComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CategoryComponent,\n      selectors: [[\"app-category\"]],\n      decls: 20,\n      vars: 6,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [3, \"formGroup\"], [1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-6\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\", \"p-fluid\"], [\"pInputText\", \"\", \"formControlName\", \"product_categories_ids\", 1, \"p-custom-dropdown\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-error\", 4, \"ngIf\"], [\"pInputText\", \"\", \"formControlName\", \"product_catalogs_ids\", 1, \"p-custom-dropdown\"], [\"class\", \"col-12 lg:col-6\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-5\"], [1, \"block\", \"font-large\", \"mb-3\", \"text-600\", \"p-custom-button\"], [\"type\", \"submit\", \"label\", \"SUBMIT\", 1, \"p-button-primary\", 3, \"click\"], [3, \"ngValue\"], [1, \"text-error\"], [4, \"ngIf\"], [1, \"breadcrumb\"], [\"class\", \"breadcrumb-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"breadcrumb-item\"]],\n      template: function CategoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"form\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Category Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5)(7, \"select\", 6);\n          i0.ɵɵtemplate(8, CategoryComponent_option_8_Template, 2, 2, \"option\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, CategoryComponent_div_9_Template, 2, 1, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 3)(11, \"span\", 4);\n          i0.ɵɵtext(12, \"Catalog Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 5)(14, \"select\", 9);\n          i0.ɵɵtemplate(15, CategoryComponent_option_15_Template, 2, 2, \"option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, CategoryComponent_div_16_Template, 6, 1, \"div\", 10);\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"span\", 12)(19, \"p-button\", 13);\n          i0.ɵɵlistener(\"click\", function CategoryComponent_Template_p_button_click_19_listener() {\n            return ctx.submitCategoryForm();\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.categoryForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.f.product_categories_ids == null ? null : ctx.f.product_categories_ids.touched) && ctx.f.product_categories_ids.hasError(\"required\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.catalogs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categoryForm.controls == null ? null : ctx.categoryForm.controls.categoryHierarchy == null ? null : ctx.categoryForm.controls.categoryHierarchy.value == null ? null : ctx.categoryForm.controls.categoryHierarchy.value.length);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i5.Button, i6.InputText, i1.FormGroupDirective, i1.FormControlName, i7.Toast],\n      styles: [\".breadcrumb-item[_ngcontent-%COMP%]    + .breadcrumb-item[_ngcontent-%COMP%]::before {\\n  content: \\\"/\\\";\\n  color: #888;\\n  font-size: 14px;\\n  margin: 7px;\\n}\\n\\n.breadcrumb[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  padding: 0px;\\n}\\n\\n.p-custom-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.p-dropdown-item[_ngcontent-%COMP%] {\\n  background-color: #0a061a;\\n}\\n\\n.p-custom-button[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  justify-content: flex-end;\\n}\\n\\n.p-custom-dropdown[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background-color: #2E323F;\\n  color: #FFFFFF;\\n}\\n\\n.p-custom-dropdown[_ngcontent-%COMP%]   option[_ngcontent-%COMP%]:hover {\\n  background-color: #2E323F;\\n  color: #007bff;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmFja29mZmljZS9wcm9kdWN0L3Byb2R1Y3QtZGV0YWlscy9jYXRlZ29yeS9jYXRlZ29yeS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFlBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUFDRjs7QUFDQTtFQUNFLGFBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFFRjs7QUFBQTtFQUNJLFdBQUE7QUFHSjs7QUFEQTtFQUNJLHlCQUFBO0FBSUo7O0FBRkE7RUFDRSx3QkFBQTtFQUNBLHlCQUFBO0FBS0Y7O0FBSEE7RUFDRSx5QkFBQTtFQUNBLGNBQUE7QUFNRjs7QUFKQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQU9GIiwic291cmNlc0NvbnRlbnQiOlsiLmJyZWFkY3J1bWItaXRlbSArIC5icmVhZGNydW1iLWl0ZW06OmJlZm9yZSB7XHJcbiAgY29udGVudDogXCIvXCI7XHJcbiAgY29sb3I6ICM4ODg7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIG1hcmdpbjogN3B4O1xyXG59XHJcbi5icmVhZGNydW1iIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGxpc3Qtc3R5bGU6IG5vbmU7XHJcbiAgcGFkZGluZzowcHg7XHJcbn1cclxuLnAtY3VzdG9tLWRyb3Bkb3due1xyXG4gICAgd2lkdGg6MTAwJTtcclxufVxyXG4ucC1kcm9wZG93bi1pdGVtIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IzBhMDYxYTtcclxufVxyXG4ucC1jdXN0b20tYnV0dG9ue1xyXG4gIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xyXG59XHJcbi5wLWN1c3RvbS1kcm9wZG93biBvcHRpb24ge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMyRTMyM0Y7XHJcbiAgY29sb3I6ICNGRkZGRkY7XHJcbn1cclxuLnAtY3VzdG9tLWRyb3Bkb3duIG9wdGlvbjpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzJFMzIzRjtcclxuICBjb2xvcjogIzAwN2JmZjtcclxufVxyXG4gICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "fork<PERSON><PERSON>n", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "c_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtemplate", "CategoryComponent_div_9_small_1_Template", "ctx_r1", "f", "product_categories_ids", "errors", "c_r3", "pch_r4", "CategoryComponent_div_16_li_5_Template", "categoryForm", "controls", "categoryHierarchy", "value", "CategoryComponent", "constructor", "fb", "productservice", "messageservice", "unsubscribe$", "category", "categories", "selectedCate<PERSON><PERSON>", "catalogs", "selectedCatalog", "group", "product_id", "required", "product_catalogs_ids", "ngOnInit", "product", "pipe", "subscribe", "data", "onCategoryChange", "updateFormValues", "patchValue", "catagories", "getAllCategory", "getAllCatalog", "addCategoryHierarchy", "productCatagories", "setSelectedPCH", "length", "get", "valueChanges", "category_id", "pch", "find", "o", "parent_category_hierarchy", "arr", "categoryMap", "Map", "resultArray", "for<PERSON>ach", "set", "parentId", "parent_category_id", "currentCategory", "parentCategory", "unshift", "indexOf", "push", "submitCategoryForm", "catFormVal", "categaoryPayload", "catalogPayload", "saveProductCategory", "updateProduct", "documentId", "saveProductCatalog", "add", "severity", "detail", "ngOnDestroy", "next", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "MessageService", "selectors", "decls", "vars", "consts", "template", "CategoryComponent_Template", "rf", "ctx", "ɵɵelement", "CategoryComponent_option_8_Template", "CategoryComponent_div_9_Template", "CategoryComponent_option_15_Template", "CategoryComponent_div_16_Template", "ɵɵlistener", "CategoryComponent_Template_p_button_click_19_listener", "touched", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\category\\category.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\category\\category.component.html"], "sourcesContent": ["import { Component, Input, OnInit, SimpleChanges } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ProductService } from '../../product.service';\r\nimport { MessageService } from 'primeng/api';\r\nimport { forkJoin, Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-category',\r\n  templateUrl: './category.component.html',\r\n  styleUrl: './category.component.scss',\r\n})\r\nexport class CategoryComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public category: any = null;\r\n  categories: any[] = [];\r\n  selectedCategory: any = null;\r\n  catalogs: any[] = [];\r\n  selectedCatalog: any = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private productservice: ProductService,\r\n    private messageservice: MessageService\r\n  ) {}\r\n\r\n  public categoryForm: any = this.fb.group({\r\n    product_id: '',\r\n    product_categories_ids: ['', Validators.required],\r\n    categoryHierarchy: [[]],\r\n    product_catalogs_ids: [''],\r\n  });\r\n\r\n  ngOnInit() {\r\n    this.productservice.product\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.category = data;\r\n        this.onCategoryChange();\r\n        this.updateFormValues();\r\n      });\r\n  }\r\n\r\n  updateFormValues(): void {\r\n    this.categoryForm.patchValue(this.category);\r\n    forkJoin({\r\n      catagories: this.productservice.getAllCategory(),\r\n      catalogs: this.productservice.getAllCatalog(),\r\n    }).subscribe(({ catagories, catalogs }) => {\r\n      this.categories = this.addCategoryHierarchy(catagories);\r\n      const productCatagories: any = this.category?.categories[0];\r\n      this.setSelectedPCH(productCatagories?.id);\r\n      if (this.category?.categories && this.category.categories.length) {\r\n        const product_categories_ids = this.category.categories[0]?.id || '';\r\n        this.categoryForm.controls.product_categories_ids.patchValue(\r\n          product_categories_ids\r\n        );\r\n      }\r\n      this.catalogs = catalogs;\r\n      if (this.category?.catalogs && this.category.catalogs.length) {\r\n        const product_catalogs_ids = this.category.catalogs[0]?.id || '';\r\n        this.categoryForm.controls.product_catalogs_ids.patchValue(\r\n          product_catalogs_ids\r\n        );\r\n      }\r\n    });\r\n  }\r\n\r\n  onCategoryChange() {\r\n    this.categoryForm\r\n      .get('product_categories_ids')\r\n      .valueChanges.subscribe((value: any) => {\r\n        this.setSelectedPCH(value);\r\n      });\r\n  }\r\n\r\n  setSelectedPCH(category_id: any) {\r\n    const pch = this.categories.find((o: any) => category_id == o.id);\r\n    if (pch) {\r\n      this.categoryForm.controls.categoryHierarchy.patchValue(\r\n        pch.parent_category_hierarchy\r\n      );\r\n    } else {\r\n      const arr: any = [];\r\n      this.categoryForm.controls.categoryHierarchy.patchValue(arr);\r\n    }\r\n  }\r\n\r\n  addCategoryHierarchy(categories: any) {\r\n    const categoryMap = new Map();\r\n    const resultArray: any = [];\r\n\r\n    // Step 1: Create a map of categories using their IDs as keys\r\n    categories.forEach((category: any) => {\r\n      categoryMap.set(category.id, {\r\n        ...category,\r\n        parent_category_hierarchy: [],\r\n      });\r\n    });\r\n\r\n    // Step 2: Traverse the categories and add their parent hierarchy\r\n    categories.forEach((category: any) => {\r\n      let parentId = category.parent_category_id;\r\n      const currentCategory = categoryMap.get(category.id);\r\n\r\n      while (parentId !== null) {\r\n        const parentCategory = categoryMap.get(parentId);\r\n        if (parentCategory) {\r\n          currentCategory.parent_category_hierarchy.unshift(\r\n            parentCategory.name\r\n          );\r\n          parentId = parentCategory.parent_category_id;\r\n        } else {\r\n          break;\r\n        }\r\n      }\r\n\r\n      if (\r\n        currentCategory.parent_category_hierarchy.indexOf(\r\n          currentCategory.name\r\n        ) === -1\r\n      ) {\r\n        currentCategory.parent_category_hierarchy.push(currentCategory.name);\r\n      }\r\n\r\n      resultArray.push(currentCategory);\r\n    });\r\n\r\n    return resultArray;\r\n  }\r\n\r\n  submitCategoryForm() {\r\n    const catFormVal = this.categoryForm.value;\r\n    const categaoryPayload = {\r\n      categories: catFormVal.product_categories_ids,\r\n    };\r\n    const catalogPayload = {\r\n      catalogs: catFormVal.product_catalogs_ids,\r\n    };\r\n    forkJoin({\r\n      saveProductCategory: this.productservice.updateProduct(\r\n        this.category.documentId,\r\n        categaoryPayload\r\n      ),\r\n      saveProductCatalog: this.productservice.updateProduct(\r\n        this.category.documentId,\r\n        catalogPayload\r\n      ),\r\n    })\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe(({ saveProductCategory, saveProductCatalog }) => {\r\n        this.messageservice.add({\r\n          severity: 'success',\r\n          detail: 'Changes Saved Successfully!',\r\n        });\r\n      });\r\n  }\r\n\r\n  get f() {\r\n    return this.categoryForm.controls;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n<form [formGroup]=\"categoryForm\">\r\n  <div class=\"grid mx-0\">\r\n    <div class=\"col-12 lg:col-6\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n        >Category Code</span\r\n      >\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <!-- <p-dropdown\r\n          [options]=\"categories\"\r\n          formControlName=\"product_categories_ids\"\r\n        ></p-dropdown> -->\r\n        <select pInputText class=\"p-custom-dropdown\" formControlName=\"product_categories_ids\">\r\n          <option *ngFor=\"let c of categories\" [ngValue]=\"c.id\">{{ c.name }}</option>\r\n        </select>\r\n      </span>\r\n      <div\r\n        *ngIf=\"\r\n          f.product_categories_ids?.touched &&\r\n          f.product_categories_ids.hasError('required')\r\n        \"\r\n        class=\"text-error\"\r\n      >\r\n        <small *ngIf=\"f.product_categories_ids.errors?.['required']\"\r\n          >Categories is required.</small\r\n        >\r\n      </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n        >Catalog Code</span\r\n      >\r\n      <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n        <!-- <p-dropdown\r\n          [options]=\"catalogs\"\r\n          formControlName=\"product_catalogs_ids\"\r\n        ></p-dropdown> -->\r\n        <select pInputText class=\"p-custom-dropdown\" formControlName=\"product_catalogs_ids\">\r\n          <option  *ngFor=\"let c of catalogs\" [ngValue]=\"c.id\">{{ c.name }}</option>\r\n        </select>\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6\" *ngIf=\"categoryForm.controls?.categoryHierarchy?.value?.length\">\r\n      <span class=\"text-900 block font-medium mb-3 font-bold\"\r\n      >Category hierarchy:</span>\r\n        <span class=\"block font-medium mb-3 text-600 p-fluid\">\r\n          <ol class=\"breadcrumb\">\r\n            <li class=\"breadcrumb-item\" *ngFor=\"let pch of categoryForm.controls?.categoryHierarchy?.value\">\r\n              <span>{{pch}}</span>\r\n            </li>\r\n          </ol>\r\n      </span>\r\n    </div>\r\n    <div class=\"col-12 lg:col-5\">\r\n      <span class=\"block font-large mb-3 text-600 p-custom-button\">\r\n        <p-button\r\n          type=\"submit\"\r\n          class=\"p-button-primary\"\r\n          label=\"SUBMIT\"\r\n          (click)=\"submitCategoryForm()\"\r\n        ></p-button>\r\n      </span>\r\n    </div>\r\n  </div>\r\n</form>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICSzCC,EAAA,CAAAC,cAAA,iBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAtCH,EAAA,CAAAI,UAAA,YAAAC,IAAA,CAAAC,EAAA,CAAgB;IAACN,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAQ,iBAAA,CAAAH,IAAA,CAAAI,IAAA,CAAY;;;;;IAUpET,EAAA,CAAAC,cAAA,YACG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EACzB;;;;;IATHH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAU,UAAA,IAAAC,wCAAA,oBACG;IAELX,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAO,SAAA,EAAmD;IAAnDP,EAAA,CAAAI,UAAA,SAAAQ,MAAA,CAAAC,CAAA,CAAAC,sBAAA,CAAAC,MAAA,kBAAAH,MAAA,CAAAC,CAAA,CAAAC,sBAAA,CAAAC,MAAA,aAAmD;;;;;IAezDf,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAtCH,EAAA,CAAAI,UAAA,YAAAY,IAAA,CAAAV,EAAA,CAAgB;IAACN,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAQ,iBAAA,CAAAQ,IAAA,CAAAP,IAAA,CAAY;;;;;IAU7DT,EADF,CAAAC,cAAA,aAAgG,WACxF;IAAAD,EAAA,CAAAE,MAAA,GAAO;IACfF,EADe,CAAAG,YAAA,EAAO,EACjB;;;;IADGH,EAAA,CAAAO,SAAA,GAAO;IAAPP,EAAA,CAAAQ,iBAAA,CAAAS,MAAA,CAAO;;;;;IALrBjB,EADF,CAAAC,cAAA,aAA6F,cAE1F;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvBH,EADF,CAAAC,cAAA,cAAsD,aAC7B;IACrBD,EAAA,CAAAU,UAAA,IAAAQ,sCAAA,iBAAgG;IAKxGlB,EAFM,CAAAG,YAAA,EAAK,EACF,EACH;;;;IAL8CH,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAI,UAAA,YAAAQ,MAAA,CAAAO,YAAA,CAAAC,QAAA,kBAAAR,MAAA,CAAAO,YAAA,CAAAC,QAAA,CAAAC,iBAAA,kBAAAT,MAAA,CAAAO,YAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAC,KAAA,CAAkD;;;ADpC1G,OAAM,MAAOC,iBAAiB;EAQ5BC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,cAA8B;IAF9B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAVhB,KAAAC,YAAY,GAAG,IAAI9B,OAAO,EAAQ;IACnC,KAAA+B,QAAQ,GAAQ,IAAI;IAC3B,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,gBAAgB,GAAQ,IAAI;IAC5B,KAAAC,QAAQ,GAAU,EAAE;IACpB,KAAAC,eAAe,GAAQ,IAAI;IAQpB,KAAAd,YAAY,GAAQ,IAAI,CAACM,EAAE,CAACS,KAAK,CAAC;MACvCC,UAAU,EAAE,EAAE;MACdrB,sBAAsB,EAAE,CAAC,EAAE,EAAElB,UAAU,CAACwC,QAAQ,CAAC;MACjDf,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBgB,oBAAoB,EAAE,CAAC,EAAE;KAC1B,CAAC;EAPC;EASHC,QAAQA,CAAA;IACN,IAAI,CAACZ,cAAc,CAACa,OAAO,CACxBC,IAAI,CAACzC,SAAS,CAAC,IAAI,CAAC6B,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACb,QAAQ,GAAGa,IAAI;MACpB,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACN;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACzB,YAAY,CAAC0B,UAAU,CAAC,IAAI,CAAChB,QAAQ,CAAC;IAC3ChC,QAAQ,CAAC;MACPiD,UAAU,EAAE,IAAI,CAACpB,cAAc,CAACqB,cAAc,EAAE;MAChDf,QAAQ,EAAE,IAAI,CAACN,cAAc,CAACsB,aAAa;KAC5C,CAAC,CAACP,SAAS,CAAC,CAAC;MAAEK,UAAU;MAAEd;IAAQ,CAAE,KAAI;MACxC,IAAI,CAACF,UAAU,GAAG,IAAI,CAACmB,oBAAoB,CAACH,UAAU,CAAC;MACvD,MAAMI,iBAAiB,GAAQ,IAAI,CAACrB,QAAQ,EAAEC,UAAU,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACqB,cAAc,CAACD,iBAAiB,EAAE5C,EAAE,CAAC;MAC1C,IAAI,IAAI,CAACuB,QAAQ,EAAEC,UAAU,IAAI,IAAI,CAACD,QAAQ,CAACC,UAAU,CAACsB,MAAM,EAAE;QAChE,MAAMtC,sBAAsB,GAAG,IAAI,CAACe,QAAQ,CAACC,UAAU,CAAC,CAAC,CAAC,EAAExB,EAAE,IAAI,EAAE;QACpE,IAAI,CAACa,YAAY,CAACC,QAAQ,CAACN,sBAAsB,CAAC+B,UAAU,CAC1D/B,sBAAsB,CACvB;MACH;MACA,IAAI,CAACkB,QAAQ,GAAGA,QAAQ;MACxB,IAAI,IAAI,CAACH,QAAQ,EAAEG,QAAQ,IAAI,IAAI,CAACH,QAAQ,CAACG,QAAQ,CAACoB,MAAM,EAAE;QAC5D,MAAMf,oBAAoB,GAAG,IAAI,CAACR,QAAQ,CAACG,QAAQ,CAAC,CAAC,CAAC,EAAE1B,EAAE,IAAI,EAAE;QAChE,IAAI,CAACa,YAAY,CAACC,QAAQ,CAACiB,oBAAoB,CAACQ,UAAU,CACxDR,oBAAoB,CACrB;MACH;IACF,CAAC,CAAC;EACJ;EAEAM,gBAAgBA,CAAA;IACd,IAAI,CAACxB,YAAY,CACdkC,GAAG,CAAC,wBAAwB,CAAC,CAC7BC,YAAY,CAACb,SAAS,CAAEnB,KAAU,IAAI;MACrC,IAAI,CAAC6B,cAAc,CAAC7B,KAAK,CAAC;IAC5B,CAAC,CAAC;EACN;EAEA6B,cAAcA,CAACI,WAAgB;IAC7B,MAAMC,GAAG,GAAG,IAAI,CAAC1B,UAAU,CAAC2B,IAAI,CAAEC,CAAM,IAAKH,WAAW,IAAIG,CAAC,CAACpD,EAAE,CAAC;IACjE,IAAIkD,GAAG,EAAE;MACP,IAAI,CAACrC,YAAY,CAACC,QAAQ,CAACC,iBAAiB,CAACwB,UAAU,CACrDW,GAAG,CAACG,yBAAyB,CAC9B;IACH,CAAC,MAAM;MACL,MAAMC,GAAG,GAAQ,EAAE;MACnB,IAAI,CAACzC,YAAY,CAACC,QAAQ,CAACC,iBAAiB,CAACwB,UAAU,CAACe,GAAG,CAAC;IAC9D;EACF;EAEAX,oBAAoBA,CAACnB,UAAe;IAClC,MAAM+B,WAAW,GAAG,IAAIC,GAAG,EAAE;IAC7B,MAAMC,WAAW,GAAQ,EAAE;IAE3B;IACAjC,UAAU,CAACkC,OAAO,CAAEnC,QAAa,IAAI;MACnCgC,WAAW,CAACI,GAAG,CAACpC,QAAQ,CAACvB,EAAE,EAAE;QAC3B,GAAGuB,QAAQ;QACX8B,yBAAyB,EAAE;OAC5B,CAAC;IACJ,CAAC,CAAC;IAEF;IACA7B,UAAU,CAACkC,OAAO,CAAEnC,QAAa,IAAI;MACnC,IAAIqC,QAAQ,GAAGrC,QAAQ,CAACsC,kBAAkB;MAC1C,MAAMC,eAAe,GAAGP,WAAW,CAACR,GAAG,CAACxB,QAAQ,CAACvB,EAAE,CAAC;MAEpD,OAAO4D,QAAQ,KAAK,IAAI,EAAE;QACxB,MAAMG,cAAc,GAAGR,WAAW,CAACR,GAAG,CAACa,QAAQ,CAAC;QAChD,IAAIG,cAAc,EAAE;UAClBD,eAAe,CAACT,yBAAyB,CAACW,OAAO,CAC/CD,cAAc,CAAC5D,IAAI,CACpB;UACDyD,QAAQ,GAAGG,cAAc,CAACF,kBAAkB;QAC9C,CAAC,MAAM;UACL;QACF;MACF;MAEA,IACEC,eAAe,CAACT,yBAAyB,CAACY,OAAO,CAC/CH,eAAe,CAAC3D,IAAI,CACrB,KAAK,CAAC,CAAC,EACR;QACA2D,eAAe,CAACT,yBAAyB,CAACa,IAAI,CAACJ,eAAe,CAAC3D,IAAI,CAAC;MACtE;MAEAsD,WAAW,CAACS,IAAI,CAACJ,eAAe,CAAC;IACnC,CAAC,CAAC;IAEF,OAAOL,WAAW;EACpB;EAEAU,kBAAkBA,CAAA;IAChB,MAAMC,UAAU,GAAG,IAAI,CAACvD,YAAY,CAACG,KAAK;IAC1C,MAAMqD,gBAAgB,GAAG;MACvB7C,UAAU,EAAE4C,UAAU,CAAC5D;KACxB;IACD,MAAM8D,cAAc,GAAG;MACrB5C,QAAQ,EAAE0C,UAAU,CAACrC;KACtB;IACDxC,QAAQ,CAAC;MACPgF,mBAAmB,EAAE,IAAI,CAACnD,cAAc,CAACoD,aAAa,CACpD,IAAI,CAACjD,QAAQ,CAACkD,UAAU,EACxBJ,gBAAgB,CACjB;MACDK,kBAAkB,EAAE,IAAI,CAACtD,cAAc,CAACoD,aAAa,CACnD,IAAI,CAACjD,QAAQ,CAACkD,UAAU,EACxBH,cAAc;KAEjB,CAAC,CACCpC,IAAI,CAACzC,SAAS,CAAC,IAAI,CAAC6B,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC,CAAC;MAAEoC,mBAAmB;MAAEG;IAAkB,CAAE,KAAI;MACzD,IAAI,CAACrD,cAAc,CAACsD,GAAG,CAAC;QACtBC,QAAQ,EAAE,SAAS;QACnBC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC;EACN;EAEA,IAAItE,CAACA,CAAA;IACH,OAAO,IAAI,CAACM,YAAY,CAACC,QAAQ;EACnC;EAEAgE,WAAWA,CAAA;IACT,IAAI,CAACxD,YAAY,CAACyD,IAAI,EAAE;IACxB,IAAI,CAACzD,YAAY,CAAC0D,QAAQ,EAAE;EAC9B;;;uBAzJW/D,iBAAiB,EAAAvB,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzF,EAAA,CAAAuF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3F,EAAA,CAAAuF,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAjBtE,iBAAiB;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX9BpG,EAAA,CAAAsG,SAAA,iBAAsD;UAIhDtG,EAHN,CAAAC,cAAA,cAAiC,aACR,aACQ,cAExB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EACf;UAMCH,EALF,CAAAC,cAAA,cAAsD,gBAKkC;UACpFD,EAAA,CAAAU,UAAA,IAAA6F,mCAAA,oBAAsD;UAE1DvG,EADE,CAAAG,YAAA,EAAS,EACJ;UACPH,EAAA,CAAAU,UAAA,IAAA8F,gCAAA,iBAMC;UAKHxG,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA6B,eAExB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EACd;UAMCH,EALF,CAAAC,cAAA,eAAsD,iBAKgC;UAClFD,EAAA,CAAAU,UAAA,KAAA+F,oCAAA,oBAAqD;UAG3DzG,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;UACNH,EAAA,CAAAU,UAAA,KAAAgG,iCAAA,kBAA6F;UAazF1G,EAFJ,CAAAC,cAAA,eAA6B,gBACkC,oBAM1D;UADCD,EAAA,CAAA2G,UAAA,mBAAAC,sDAAA;YAAA,OAASP,GAAA,CAAA5B,kBAAA,EAAoB;UAAA,EAAC;UAKxCzE,EAJS,CAAAG,YAAA,EAAW,EACP,EACH,EACF,EACD;;;UAhEuBH,EAAA,CAAAI,UAAA,cAAa;UACrCJ,EAAA,CAAAO,SAAA,EAA0B;UAA1BP,EAAA,CAAAI,UAAA,cAAAiG,GAAA,CAAAlF,YAAA,CAA0B;UAYAnB,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,YAAAiG,GAAA,CAAAvE,UAAA,CAAa;UAIpC9B,EAAA,CAAAO,SAAA,EAGD;UAHCP,EAAA,CAAAI,UAAA,UAAAiG,GAAA,CAAAxF,CAAA,CAAAC,sBAAA,kBAAAuF,GAAA,CAAAxF,CAAA,CAAAC,sBAAA,CAAA+F,OAAA,KAAAR,GAAA,CAAAxF,CAAA,CAAAC,sBAAA,CAAAgG,QAAA,aAGD;UAkByB9G,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAAiG,GAAA,CAAArE,QAAA,CAAW;UAIVhC,EAAA,CAAAO,SAAA,EAA6D;UAA7DP,EAAA,CAAAI,UAAA,SAAAiG,GAAA,CAAAlF,YAAA,CAAAC,QAAA,kBAAAiF,GAAA,CAAAlF,YAAA,CAAAC,QAAA,CAAAC,iBAAA,kBAAAgF,GAAA,CAAAlF,YAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAC,KAAA,kBAAA+E,GAAA,CAAAlF,YAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAC,KAAA,CAAA8B,MAAA,CAA6D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}