{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PimComponent } from './pim.component';\nimport { ConfigurationComponent } from './configuration/configuration.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PimComponent,\n  // Parent route\n  children: [{\n    path: 'configurations',\n    children: [{\n      path: 'flexible-group-type',\n      component: ConfigurationComponent,\n      data: {\n        type: 'FLEX_GROUP_TYPE',\n        title: 'Flexible Group Type',\n        columns: ['Code', 'Description', 'Active'] // Example\n      }\n    }]\n  }]\n}];\nexport class PimRoutingModule {\n  static {\n    this.ɵfac = function PimRoutingModule_Factory(t) {\n      return new (t || PimRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PimRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PimRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PimComponent", "ConfigurationComponent", "routes", "path", "component", "children", "data", "type", "title", "columns", "PimRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\pim\\pim-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { PimComponent } from './pim.component';\r\nimport { ConfigurationComponent } from './configuration/configuration.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: PimComponent, // Parent route\r\n    children: [\r\n      {\r\n        path: 'configurations',\r\n        children: [\r\n          {\r\n            path: 'flexible-group-type',\r\n            component: ConfigurationComponent,\r\n            data: {\r\n              type: 'FLEX_GROUP_TYPE',\r\n              title: 'Flexible Group Type',\r\n              columns: ['Code', 'Description', 'Active'], // Example\r\n            },\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class PimRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,yCAAyC;;;AAEhF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,YAAY;EAAE;EACzBK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,gBAAgB;IACtBE,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,qBAAqB;MAC3BC,SAAS,EAAEH,sBAAsB;MACjCK,IAAI,EAAE;QACJC,IAAI,EAAE,iBAAiB;QACvBC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAE;;KAE/C;GAEJ;CAEJ,CACF;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBX,YAAY,CAACY,QAAQ,CAACT,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXW,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFjBf,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}