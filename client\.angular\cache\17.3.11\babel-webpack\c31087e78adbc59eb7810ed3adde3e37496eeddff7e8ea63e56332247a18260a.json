{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\nif ($gOPD) {\n  try {\n    $gOPD([], 'length');\n  } catch (e) {\n    // IE 8 has a broken gOPD\n    $gOPD = null;\n  }\n}\nmodule.exports = $gOPD;", "map": {"version": 3, "names": ["GetIntrinsic", "require", "$gOPD", "e", "module", "exports"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/gopd/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar $gOPD = GetIntrinsic('%Object.getOwnPropertyDescriptor%', true);\n\nif ($gOPD) {\n\ttry {\n\t\t$gOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\t$gOPD = null;\n\t}\n}\n\nmodule.exports = $gOPD;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE3C,IAAIC,KAAK,GAAGF,YAAY,CAAC,mCAAmC,EAAE,IAAI,CAAC;AAEnE,IAAIE,KAAK,EAAE;EACV,IAAI;IACHA,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC;EACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACX;IACAD,KAAK,GAAG,IAAI;EACb;AACD;AAEAE,MAAM,CAACC,OAAO,GAAGH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}