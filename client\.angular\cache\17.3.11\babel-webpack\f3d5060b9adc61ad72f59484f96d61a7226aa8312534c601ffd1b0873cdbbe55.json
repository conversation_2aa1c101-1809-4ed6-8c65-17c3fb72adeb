{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil, concat, of, map, firstValueFrom } from 'rxjs';\nimport { catchError, distinctUntilChanged, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../users.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@ng-select/ng-select\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/confirmdialog\";\nconst _c0 = [\"filter\"];\nconst _c1 = () => [\"customer_id\", \"bp_uuid\", \"customer_name\", \"phone\"];\nfunction CustomerDetailsComponent_ng_template_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\": \", item_r2.customer_name, \"\");\n  }\n}\nfunction CustomerDetailsComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, CustomerDetailsComponent_ng_template_11_span_2_Template, 2, 1, \"span\", 20);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.item;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.customer_id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.customer_name);\n  }\n}\nfunction CustomerDetailsComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function CustomerDetailsComponent_ng_template_18_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dt1_r5 = i0.ɵɵreference(17);\n      return i0.ɵɵresetView(ctx_r3.clear(dt1_r5));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CustomerDetailsComponent_ng_template_18_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refresh());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 25);\n    i0.ɵɵelement(5, \"i\", 26);\n    i0.ɵɵelementStart(6, \"input\", 27, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerDetailsComponent_ng_template_18_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.globalSearchTerm, $event) || (ctx_r3.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function CustomerDetailsComponent_ng_template_18_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dt1_r5 = i0.ɵɵreference(17);\n      return i0.ɵɵresetView(ctx_r3.onGlobalFilter(dt1_r5, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.globalSearchTerm);\n  }\n}\nfunction CustomerDetailsComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 28);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 29)(4, \"div\", 30);\n    i0.ɵɵtext(5, \" Customer ID \");\n    i0.ɵɵelementStart(6, \"div\", 31);\n    i0.ɵɵelement(7, \"p-sortIcon\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"th\", 33)(9, \"div\", 30);\n    i0.ɵɵtext(10, \" GUID \");\n    i0.ɵɵelementStart(11, \"div\", 31);\n    i0.ɵɵelement(12, \"p-sortIcon\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"th\", 35)(14, \"div\", 30);\n    i0.ɵɵtext(15, \" Customer Name \");\n    i0.ɵɵelementStart(16, \"div\", 31);\n    i0.ɵɵelement(17, \"p-sortIcon\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"th\", 37)(19, \"div\", 30);\n    i0.ɵɵtext(20, \" Address \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 38)(22, \"div\", 30);\n    i0.ɵɵtext(23, \" Phone \");\n    i0.ɵɵelementStart(24, \"div\", 31);\n    i0.ɵɵelement(25, \"p-sortIcon\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"th\", 40)(27, \"div\", 30);\n    i0.ɵɵtext(28, \" Action \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerDetailsComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 41)(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function CustomerDetailsComponent_ng_template_20_Template_button_click_14_listener() {\n      const customer_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.deleteUserCustomer(customer_r7));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const customer_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", customer_r7);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r7 == null ? null : customer_r7.customer_id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.bp_uuid, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r7 == null ? null : customer_r7.customer_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate7(\" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].house_number) ? (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].house_number) + \",\" : \"\", \" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].street_name) ? (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].street_name) + \",\" : \"\", \" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].city_name) ? (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].city_name) + \",\" : \"\", \" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].district) ? (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].district) + \",\" : \"\", \" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].region) ? (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].region) + \",\" : \"\", \" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].country) ? (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].country) + \",\" : \"\", \" \", (customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].postal_code) ? customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.addresses[0] == null ? null : customer_r7.business_partner.addresses[0].postal_code : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r7 == null ? null : customer_r7.business_partner == null ? null : customer_r7.business_partner.phone, \" \");\n  }\n}\nfunction CustomerDetailsComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 44);\n    i0.ɵɵtext(2, \"No customers found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerDetailsComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 44);\n    i0.ɵɵtext(2, \"Loading customers data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CustomerDetailsComponent {\n  constructor(fb, usersservice, router, route, messageservice, confirmationservice) {\n    this.fb = fb;\n    this.usersservice = usersservice;\n    this.router = router;\n    this.route = route;\n    this.messageservice = messageservice;\n    this.confirmationservice = confirmationservice;\n    this.ngUnsubscribe$ = new Subject();\n    this.customer = [];\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.id = '';\n    this.documentID = '';\n    this.customerdata = [];\n    this.submitted = false;\n    this.saving = false;\n    this.defaultOptions = [];\n    this.selectedItems = [];\n    this.customerLoading = false;\n    this.customerInput$ = new Subject();\n    this.CustomerForm = this.fb.group({\n      customers: [null]\n    });\n  }\n  ngOnInit() {\n    this.defaultOptions = [{\n      customer_id: 'ALL'\n    }];\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.loadCustomers();\n    this.usersservice.user.pipe(takeUntil(this.ngUnsubscribe$)).subscribe(data => {\n      this.documentID = data?.documentId;\n      this.loadCustomerData({\n        first: 0,\n        rows: 10\n      });\n    });\n  }\n  loadCustomerData(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    const sortField = event.sortField;\n    const sortOrder = event.sortOrder;\n    this.usersservice.getUserByCustomer(this.documentID, page, pageSize, sortField, sortOrder, this.globalSearchTerm).pipe(takeUntil(this.ngUnsubscribe$)).subscribe({\n      next: response => {\n        this.customer = response?.data || [];\n        this.totalRecords = response?.pagination?.total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching customer', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadCustomerData({\n      first: 0,\n      rows: 10\n    });\n  }\n  loadCustomers() {\n    this.customers$ = concat(of(this.defaultOptions),\n    // default items\n    this.customerInput$.pipe(distinctUntilChanged(), tap(() => this.customerLoading = true), switchMap(term => {\n      if (term && term.length < 2) {\n        this.customerLoading = false;\n        return of(this.defaultOptions);\n      }\n      const params = {};\n      if (term) {\n        params[`filters[bp_id][$containsi]`] = term;\n        params[`fields[0]`] = 'customer_id';\n        params[`fields[1]`] = 'customer_name';\n      }\n      return this.usersservice.getCustomers(params).pipe(map(res => {\n        let data = res.data || [];\n        if (this.defaultOptions[0]) {\n          data.unshift(this.defaultOptions[0]);\n        }\n        return res.data;\n      }), catchError(() => of(this.defaultOptions)),\n      // empty list on error\n      tap(() => this.customerLoading = false));\n    })));\n  }\n  onAddCustOption($event) {\n    if ($event.customer_id === 'ALL') {\n      this.CustomerForm.patchValue({\n        customers: this.defaultOptions\n      });\n    } else {\n      const selectedCust = this.f.customers.value;\n      const index = selectedCust.findIndex(o => o.customer_id === 'ALL');\n      if (index > -1) {\n        selectedCust.splice(index, 1);\n        this.CustomerForm.patchValue({\n          customers: selectedCust\n        });\n      }\n    }\n  }\n  updateCustomer() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.submitted = true;\n      _this.saving = true;\n      if (_this.CustomerForm.invalid) {\n        return;\n      }\n      const reqPayload = {\n        ..._this.CustomerForm?.value\n      };\n      if (Array.isArray(reqPayload.customers) && reqPayload.customers.length) {\n        if (reqPayload.customers.find(c => c.customer_id === 'ALL')) {\n          const params = {\n            'fields[0]': 'documentId',\n            'pagination[pageSize]': '250'\n          };\n          const {\n            data\n          } = yield firstValueFrom(_this.usersservice.getCustomers(params));\n          reqPayload.customers = data;\n        }\n        reqPayload.customers = {\n          connect: reqPayload.customers.map(c => c.id)\n        };\n      }\n      const payload = {\n        ...reqPayload\n      };\n      delete payload.id;\n      delete payload.documentId;\n      _this.usersservice.updateUser(_this.id, payload).pipe(takeUntil(_this.ngUnsubscribe$)).subscribe({\n        next: () => {\n          _this.submitted = false;\n          _this.CustomerForm.patchValue({\n            customers: []\n          });\n          _this.messageservice.add({\n            severity: 'success',\n            detail: 'Changed Saved successfully!'\n          });\n          _this.refresh();\n        },\n        error: () => {\n          _this.submitted = false;\n          _this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    })();\n  }\n  deleteUserCustomer(e) {\n    this.usersservice.updateUser(this.id, {\n      customers: {\n        disconnect: e.id\n      }\n    }).pipe(takeUntil(this.ngUnsubscribe$)).subscribe({\n      next: () => {\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Changed Saved successfully'\n        });\n        this.refresh();\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  refresh() {\n    this.loadCustomerData({\n      first: 0,\n      rows: 10\n    });\n  }\n  clear(table) {\n    this.globalSearchTerm = '';\n    this.filter.nativeElement.value = '';\n    this.loadCustomerData({\n      first: 0,\n      rows: 10\n    });\n  }\n  bulkDelete() {\n    this.submitted = true;\n    this.saving = true;\n    if (!this.selectedItems || this.selectedItems.length === 0) {\n      return;\n    }\n    if (Array.isArray(this.selectedItems) && this.selectedItems.length) {\n      const deleteRequests = {\n        disconnect: this.selectedItems.map(c => c.id)\n      };\n      this.usersservice.updateUser(this.id, {\n        customers: deleteRequests\n      }).pipe(takeUntil(this.ngUnsubscribe$)).subscribe({\n        next: () => {\n          this.submitted = false;\n          this.selectedItems = [];\n          this.messageservice.add({\n            severity: 'success',\n            detail: 'Changed Saved successfully!'\n          });\n          this.refresh();\n        },\n        error: () => {\n          this.submitted = false;\n          this.messageservice.add({\n            severity: 'error',\n            detail: 'Error while processing your request.'\n          });\n        }\n      });\n    }\n  }\n  confirmDelete() {\n    this.confirmationservice.confirm({\n      message: 'Are you sure you want to delete the selected records?',\n      header: 'Confirm',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        this.bulkDelete();\n      }\n    });\n  }\n  get f() {\n    return this.CustomerForm.controls;\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe$.next();\n    this.ngUnsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function CustomerDetailsComponent_Factory(t) {\n      return new (t || CustomerDetailsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UsersService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i4.ConfirmationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerDetailsComponent,\n      selectors: [[\"app-customer-details\"]],\n      viewQuery: function CustomerDetailsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 21,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [2, \"display\", \"flex\", \"justify-content\", \"flex-start\"], [3, \"formGroup\"], [1, \"col-12\", \"md:col-9\"], [\"pInputText\", \"\", \"bindLabel\", \"customer_id\", \"formControlName\", \"customers\", \"typeToSearchText\", \"Select ALL or enter 2 or more chars to search customer\", 3, \"items\", \"multiple\", \"hideSelected\", \"loading\", \"minTermLength\", \"typeahead\", \"maxSelectedItems\", \"placeholder\"], [\"ng-option-tmp\", \"\"], [1, \"col-12\", \"md:col-3\"], [1, \"gap-2\", \"p-2\", \"h-full\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"DELETE\", \"pTooltip\", \"Delete\", \"tooltipPosition\", \"top\", 1, \"p-button-danger\", \"mr-3\", 3, \"click\", \"disabled\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"SUBMIT\", 1, \"p-button-primary\", \"mr-3\", 3, \"click\"], [\"dataKey\", \"id\", \"styleClass\", \"p-datatable-gridlines\", \"responsiveLayout\", \"scroll\", 3, \"selectionChange\", \"onLazyLoad\", \"value\", \"selection\", \"rows\", \"loading\", \"rowHover\", \"paginator\", \"globalFilterFields\", \"totalRecords\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [4, \"ngIf\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"gap-2\", \"justify-content-between\"], [\"pButton\", \"\", \"label\", \"Clear\", \"icon\", \"pi pi-filter-slash\", 1, \"p-button-outlined\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-refresh\", 1, \"p-button-primary\", \"p-refresh-button\", 3, \"click\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Keyword\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [2, \"width\", \"3em\"], [\"pSortableColumn\", \"customer_id\", 2, \"min-width\", \"12rem\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"flex\", \"align-items-center\"], [\"field\", \"Customer ID\"], [\"pSortableColumn\", \"business_partner.bp_uuid\", 2, \"min-width\", \"12rem\"], [\"field\", \"GUID\"], [\"pSortableColumn\", \"customer_name\", 2, \"min-width\", \"12rem\"], [\"field\", \"Customer Name\"], [2, \"min-width\", \"12rem\"], [\"pSortableColumn\", \"business_partner.phone\", 2, \"min-width\", \"12rem\"], [\"field\", \"Phone\"], [2, \"min-width\", \"5rem\"], [1, \"cursor-pointer\"], [3, \"value\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 1, \"p-button-primary\", \"mr-3\", \"p-button-sm\", 3, \"click\"], [\"colspan\", \"8\"]],\n      template: function CustomerDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\");\n          i0.ɵɵtext(5, \"Customers List\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"form\", 6)(7, \"div\", 2)(8, \"div\", 7)(9, \"ng-select\", 8);\n          i0.ɵɵpipe(10, \"async\");\n          i0.ɵɵtemplate(11, CustomerDetailsComponent_ng_template_11_Template, 3, 2, \"ng-template\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function CustomerDetailsComponent_Template_button_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.confirmDelete());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function CustomerDetailsComponent_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateCustomer());\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(16, \"p-table\", 14, 0);\n          i0.ɵɵtwoWayListener(\"selectionChange\", function CustomerDetailsComponent_Template_p_table_selectionChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedItems, $event) || (ctx.selectedItems = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onLazyLoad\", function CustomerDetailsComponent_Template_p_table_onLazyLoad_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadCustomerData($event));\n          });\n          i0.ɵɵtemplate(18, CustomerDetailsComponent_ng_template_18_Template, 8, 1, \"ng-template\", 15)(19, CustomerDetailsComponent_ng_template_19_Template, 29, 0, \"ng-template\", 16)(20, CustomerDetailsComponent_ng_template_20_Template, 15, 12, \"ng-template\", 17)(21, CustomerDetailsComponent_ng_template_21_Template, 3, 0, \"ng-template\", 18)(22, CustomerDetailsComponent_ng_template_22_Template, 3, 0, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(23, \"p-confirmDialog\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.CustomerForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"items\", i0.ɵɵpipeBind1(10, 18, ctx.customers$))(\"multiple\", true)(\"hideSelected\", true)(\"loading\", ctx.customerLoading)(\"minTermLength\", 0)(\"typeahead\", ctx.customerInput$)(\"maxSelectedItems\", 10)(\"placeholder\", \"Select 'ALL' or enter 2 or more chars to search customer\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.selectedItems || ctx.selectedItems.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.customer);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedItems);\n          i0.ɵɵproperty(\"rows\", 10)(\"loading\", ctx.loading)(\"rowHover\", true)(\"paginator\", true)(\"globalFilterFields\", i0.ɵɵpureFunction0(20, _c1))(\"totalRecords\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i5.NgIf, i6.NgSelectComponent, i6.NgOptionTemplateDirective, i4.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i7.Table, i7.SortableColumn, i7.SortIcon, i7.TableCheckbox, i7.TableHeaderCheckbox, i8.Tooltip, i9.ButtonDirective, i10.InputText, i1.FormGroupDirective, i1.FormControlName, i11.ConfirmDialog, i5.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "concat", "of", "map", "firstValueFrom", "catchError", "distinctUntilChanged", "switchMap", "tap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "item_r2", "customer_name", "ɵɵtemplate", "CustomerDetailsComponent_ng_template_11_span_2_Template", "ɵɵtextInterpolate", "customer_id", "ɵɵproperty", "ɵɵlistener", "CustomerDetailsComponent_ng_template_18_Template_button_click_2_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dt1_r5", "ɵɵreference", "ɵɵresetView", "clear", "CustomerDetailsComponent_ng_template_18_Template_button_click_3_listener", "refresh", "ɵɵelement", "ɵɵtwoWayListener", "CustomerDetailsComponent_ng_template_18_Template_input_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "globalSearchTerm", "CustomerDetailsComponent_ng_template_18_Template_input_input_6_listener", "onGlobalFilter", "ɵɵtwoWayProperty", "CustomerDetailsComponent_ng_template_20_Template_button_click_14_listener", "customer_r7", "_r6", "$implicit", "deleteUserCustomer", "business_partner", "bp_uuid", "ɵɵtextInterpolate7", "addresses", "house_number", "street_name", "city_name", "district", "region", "country", "postal_code", "phone", "CustomerDetailsComponent", "constructor", "fb", "usersservice", "router", "route", "messageservice", "confirmationservice", "ngUnsubscribe$", "customer", "totalRecords", "loading", "id", "documentID", "customerdata", "submitted", "saving", "defaultOptions", "selectedItems", "customerLoading", "customerInput$", "CustomerForm", "group", "customers", "ngOnInit", "parent", "snapshot", "paramMap", "get", "loadCustomers", "user", "pipe", "subscribe", "data", "documentId", "loadCustomerData", "first", "rows", "event", "page", "pageSize", "sortField", "sortOrder", "getUserByCustomer", "next", "response", "pagination", "total", "error", "console", "table", "customers$", "term", "length", "params", "getCustomers", "res", "unshift", "onAddCustOption", "patchValue", "selectedCust", "f", "value", "index", "findIndex", "o", "splice", "updateCustomer", "_this", "_asyncToGenerator", "invalid", "reqPayload", "Array", "isArray", "find", "c", "connect", "payload", "updateUser", "add", "severity", "detail", "e", "disconnect", "filter", "nativeElement", "bulkDelete", "deleteRequests", "confirmDelete", "confirm", "message", "header", "icon", "accept", "controls", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UsersService", "i3", "Router", "ActivatedRoute", "i4", "MessageService", "ConfirmationService", "selectors", "viewQuery", "CustomerDetailsComponent_Query", "rf", "ctx", "CustomerDetailsComponent_ng_template_11_Template", "CustomerDetailsComponent_Template_button_click_14_listener", "_r1", "CustomerDetailsComponent_Template_button_click_15_listener", "CustomerDetailsComponent_Template_p_table_selectionChange_16_listener", "CustomerDetailsComponent_Template_p_table_onLazyLoad_16_listener", "CustomerDetailsComponent_ng_template_18_Template", "CustomerDetailsComponent_ng_template_19_Template", "CustomerDetailsComponent_ng_template_20_Template", "CustomerDetailsComponent_ng_template_21_Template", "CustomerDetailsComponent_ng_template_22_Template", "ɵɵpipeBind1", "ɵɵpureFunction0", "_c1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\users\\user-details\\customer-details\\customer-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\users\\user-details\\customer-details\\customer-details.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';\r\nimport { Table } from 'primeng/table';\r\nimport {\r\n  Subject,\r\n  takeUntil,\r\n  Observable,\r\n  concat,\r\n  of,\r\n  map,\r\n  firstValueFrom,\r\n} from 'rxjs';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UsersService } from '../../users.service';\r\nimport { FormBuilder } from '@angular/forms';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport {\r\n  catchError,\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  tap,\r\n} from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-customer-details',\r\n  templateUrl: './customer-details.component.html',\r\n  styleUrl: './customer-details.component.scss',\r\n})\r\nexport class CustomerDetailsComponent implements OnInit {\r\n  private ngUnsubscribe$ = new Subject<void>();\r\n  @ViewChild('filter') filter!: ElementRef;\r\n  public customer: any[] = [];\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public id: string = '';\r\n  public documentID: string = '';\r\n  public customerdata: any[] = [];\r\n  public submitted: boolean = false;\r\n  public saving = false;\r\n  private defaultOptions: any = [];\r\n  selectedItems: any[] = [];\r\n  public customers$?: Observable<any[]>;\r\n  public customerLoading = false;\r\n  public customerInput$ = new Subject<string>();\r\n  public CustomerForm: any = this.fb.group({\r\n    customers: [null],\r\n  });\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private usersservice: UsersService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private messageservice: MessageService,\r\n    private confirmationservice: ConfirmationService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.defaultOptions = [{ customer_id: 'ALL' }];\r\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.loadCustomers();\r\n    this.usersservice.user\r\n      .pipe(takeUntil(this.ngUnsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.documentID = data?.documentId;\r\n        this.loadCustomerData({ first: 0, rows: 10 });\r\n      });\r\n  }\r\n\r\n  loadCustomerData(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n    this.usersservice\r\n      .getUserByCustomer(\r\n        this.documentID,\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .pipe(takeUntil(this.ngUnsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.customer = response?.data || [];\r\n          this.totalRecords = response?.pagination?.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching customer', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadCustomerData({ first: 0, rows: 10 });\r\n  }\r\n\r\n  private loadCustomers() {\r\n    this.customers$ = concat(\r\n      of(this.defaultOptions), // default items\r\n      this.customerInput$.pipe(\r\n        distinctUntilChanged(),\r\n        tap(() => (this.customerLoading = true)),\r\n        switchMap((term: any) => {\r\n          if (term && term.length < 2) {\r\n            this.customerLoading = false;\r\n            return of(this.defaultOptions);\r\n          }\r\n          const params: any = {};\r\n          if (term) {\r\n            params[`filters[bp_id][$containsi]`] = term;\r\n            params[`fields[0]`] = 'customer_id';\r\n            params[`fields[1]`] = 'customer_name';\r\n          }\r\n          return this.usersservice.getCustomers(params).pipe(\r\n            map((res: any) => {\r\n              let data = res.data || [];\r\n              if (this.defaultOptions[0]) {\r\n                data.unshift(this.defaultOptions[0]);\r\n              }\r\n              return res.data;\r\n            }),\r\n            catchError(() => of(this.defaultOptions)), // empty list on error\r\n            tap(() => (this.customerLoading = false))\r\n          );\r\n        })\r\n      )\r\n    );\r\n  }\r\n\r\n  onAddCustOption($event: any) {\r\n    if ($event.customer_id === 'ALL') {\r\n      this.CustomerForm.patchValue({ customers: this.defaultOptions });\r\n    } else {\r\n      const selectedCust = this.f.customers.value;\r\n      const index = selectedCust.findIndex((o: any) => o.customer_id === 'ALL');\r\n      if (index > -1) {\r\n        selectedCust.splice(index, 1);\r\n        this.CustomerForm.patchValue({ customers: selectedCust });\r\n      }\r\n    }\r\n  }\r\n\r\n  async updateCustomer() {\r\n    this.submitted = true;\r\n    this.saving = true;\r\n\r\n    if (this.CustomerForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    const reqPayload = { ...this.CustomerForm?.value };\r\n    if (Array.isArray(reqPayload.customers) && reqPayload.customers.length) {\r\n      if (reqPayload.customers.find((c: any) => c.customer_id === 'ALL')) {\r\n        const params: any = {\r\n          'fields[0]': 'documentId',\r\n          'pagination[pageSize]': '250',\r\n        };\r\n        const { data }: any = await firstValueFrom(\r\n          this.usersservice.getCustomers(params)\r\n        );\r\n        reqPayload.customers = data;\r\n      }\r\n      reqPayload.customers = {\r\n        connect: reqPayload.customers.map((c: any) => c.id),\r\n      };\r\n    }\r\n\r\n    const payload = { ...reqPayload };\r\n    delete payload.id;\r\n    delete payload.documentId;\r\n    this.usersservice\r\n      .updateUser(this.id, payload)\r\n      .pipe(takeUntil(this.ngUnsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.submitted = false;\r\n          this.CustomerForm.patchValue({ customers: <any>[] });\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Changed Saved successfully!',\r\n          });\r\n          this.refresh();\r\n        },\r\n        error: () => {\r\n          this.submitted = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  public deleteUserCustomer(e: any) {\r\n    this.usersservice\r\n      .updateUser(this.id, { customers: { disconnect: e.id } })\r\n      .pipe(takeUntil(this.ngUnsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Changed Saved successfully',\r\n          });\r\n          this.refresh();\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  refresh() {\r\n    this.loadCustomerData({ first: 0, rows: 10 });\r\n  }\r\n\r\n  clear(table: Table) {\r\n    this.globalSearchTerm = '';\r\n    this.filter.nativeElement.value = '';\r\n    this.loadCustomerData({ first: 0, rows: 10 });\r\n  }\r\n\r\n  bulkDelete() {\r\n    this.submitted = true;\r\n    this.saving = true;\r\n\r\n    if (!this.selectedItems || this.selectedItems.length === 0) {\r\n      return;\r\n    }\r\n\r\n    if (Array.isArray(this.selectedItems) && this.selectedItems.length) {\r\n      const deleteRequests = {\r\n        disconnect: this.selectedItems.map((c: any) => c.id),\r\n      };\r\n\r\n      this.usersservice\r\n        .updateUser(this.id, { customers: deleteRequests })\r\n        .pipe(takeUntil(this.ngUnsubscribe$))\r\n        .subscribe({\r\n          next: () => {\r\n            this.submitted = false;\r\n            this.selectedItems = [];\r\n            this.messageservice.add({\r\n              severity: 'success',\r\n              detail: 'Changed Saved successfully!',\r\n            });\r\n            this.refresh();\r\n          },\r\n          error: () => {\r\n            this.submitted = false;\r\n            this.messageservice.add({\r\n              severity: 'error',\r\n              detail: 'Error while processing your request.',\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  confirmDelete() {\r\n    this.confirmationservice.confirm({\r\n      message: 'Are you sure you want to delete the selected records?',\r\n      header: 'Confirm',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      accept: () => {\r\n        this.bulkDelete();\r\n      },\r\n    });\r\n  }\r\n\r\n  get f(): any {\r\n    return this.CustomerForm.controls;\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.ngUnsubscribe$.next();\r\n    this.ngUnsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12\">\r\n        <div class=\"card\">\r\n            <div style=\"display: flex; justify-content: flex-start\">\r\n                <h5>Customers List</h5>\r\n            </div>\r\n            <form [formGroup]=\"CustomerForm\">\r\n                <div class=\"grid\">\r\n                    <div class=\"col-12 md:col-9\">\r\n                        <ng-select pInputText [items]=\"customers$ | async\" bindLabel=\"customer_id\" [multiple]=\"true\"\r\n                            [hideSelected]=\"true\" [loading]=\"customerLoading\" [minTermLength]=\"0\"\r\n                            formControlName=\"customers\" [typeahead]=\"customerInput$\" [maxSelectedItems]=\"10\"\r\n                            [placeholder]=\"\r\n                'Select \\'ALL\\' or enter 2 or more chars to search customer'\r\n              \" typeToSearchText=\"Select ALL or enter 2 or more chars to search customer\">\r\n                            <ng-template ng-option-tmp let-item=\"item\">\r\n                                <span>{{ item.customer_id }}</span>\r\n                                <span *ngIf=\"item.customer_name\">: {{ item.customer_name }}</span>\r\n                            </ng-template>\r\n                        </ng-select>\r\n                    </div>\r\n                    <div class=\"col-12 md:col-3\">\r\n                        <div class=\"gap-2 p-2 h-full\">\r\n                            <button pButton type=\"button\" label=\"DELETE\" class=\"p-button-danger mr-3\"\r\n                                [disabled]=\"!selectedItems || selectedItems.length === 0\" (click)=\"confirmDelete()\"\r\n                                pTooltip=\"Delete\" tooltipPosition=\"top\"></button>\r\n                            <button pButton type=\"button\" label=\"SUBMIT\" class=\"p-button-primary mr-3\"\r\n                                (click)=\"updateCustomer()\"></button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </form>\r\n\r\n            <p-table #dt1 [value]=\"customer\" [(selection)]=\"selectedItems\" dataKey=\"id\" [rows]=\"10\"\r\n                (onLazyLoad)=\"loadCustomerData($event)\" [loading]=\"loading\" [rowHover]=\"true\"\r\n                styleClass=\"p-datatable-gridlines\" [paginator]=\"true\" [globalFilterFields]=\"[\r\n          'customer_id',\r\n          'bp_uuid',\r\n          'customer_name',\r\n          'phone'\r\n        ]\" [totalRecords]=\"totalRecords\" responsiveLayout=\"scroll\">\r\n                <ng-template pTemplate=\"caption\">\r\n                    <div class=\"flex justify-content-between flex-column sm:flex-row gap-2\">\r\n                        <div class=\"flex flex-row gap-2 justify-content-between\">\r\n                            <button pButton label=\"Clear\" class=\"p-button-outlined\" icon=\"pi pi-filter-slash\"\r\n                                (click)=\"clear(dt1)\"></button>\r\n                            <button pButton type=\"button\" class=\"p-button-primary p-refresh-button\" icon=\"pi pi-refresh\"\r\n                                (click)=\"refresh()\"></button>\r\n                        </div>\r\n                        <span class=\"p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                                (input)=\"onGlobalFilter(dt1, $event)\" placeholder=\"Search Keyword\" class=\"w-full\" />\r\n                        </span>\r\n                    </div>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"header\">\r\n                    <tr>\r\n                        <th style=\"width: 3em\">\r\n                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\r\n                        </th>\r\n                        <th style=\"min-width: 12rem\" pSortableColumn=\"customer_id\">\r\n                            <div class=\"flex justify-content-between align-items-center\">\r\n                                Customer ID\r\n                                <div class=\"flex align-items-center\">\r\n                                    <p-sortIcon field=\"Customer ID\"></p-sortIcon>\r\n                                </div>\r\n                            </div>\r\n                        </th>\r\n                        <th style=\"min-width: 12rem\" pSortableColumn=\"business_partner.bp_uuid\">\r\n                            <div class=\"flex justify-content-between align-items-center\">\r\n                                GUID\r\n                                <div class=\"flex align-items-center\">\r\n                                    <p-sortIcon field=\"GUID\"></p-sortIcon>\r\n                                </div>\r\n                            </div>\r\n                        </th>\r\n                        <th style=\"min-width: 12rem\" pSortableColumn=\"customer_name\">\r\n                            <div class=\"flex justify-content-between align-items-center\">\r\n                                Customer Name\r\n                                <div class=\"flex align-items-center\">\r\n                                    <p-sortIcon field=\"Customer Name\"></p-sortIcon>\r\n                                </div>\r\n                            </div>\r\n                        </th>\r\n                        <th style=\"min-width: 12rem\">\r\n                            <div class=\"flex justify-content-between align-items-center\">\r\n                                Address\r\n                            </div>\r\n                        </th>\r\n                        <th style=\"min-width: 12rem\" pSortableColumn=\"business_partner.phone\">\r\n                            <div class=\"flex justify-content-between align-items-center\">\r\n                                Phone\r\n                                <div class=\"flex align-items-center\">\r\n                                    <p-sortIcon field=\"Phone\"></p-sortIcon>\r\n                                </div>\r\n                            </div>\r\n                        </th>\r\n                        <th style=\"min-width: 5rem\">\r\n                            <div class=\"flex justify-content-between align-items-center\">\r\n                                Action\r\n                            </div>\r\n                        </th>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"body\" let-customer>\r\n                    <tr class=\"cursor-pointer\">\r\n                        <td>\r\n                            <p-tableCheckbox [value]=\"customer\"></p-tableCheckbox>\r\n                        </td>\r\n                        <td>\r\n                            {{ customer?.customer_id }}\r\n                        </td>\r\n                        <td>\r\n                            {{ customer?.business_partner?.bp_uuid }}\r\n                        </td>\r\n                        <td>\r\n                            {{ customer?.customer_name }}\r\n                        </td>\r\n                        <td>\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.house_number\r\n                            ? customer?.business_partner?.addresses[0]?.house_number + \",\"\r\n                            : \"\"\r\n                            }}\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.street_name\r\n                            ? customer?.business_partner?.addresses[0]?.street_name + \",\"\r\n                            : \"\"\r\n                            }}\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.city_name\r\n                            ? customer?.business_partner?.addresses[0]?.city_name + \",\"\r\n                            : \"\"\r\n                            }}\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.district\r\n                            ? customer?.business_partner?.addresses[0]?.district + \",\"\r\n                            : \"\"\r\n                            }}\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.region\r\n                            ? customer?.business_partner?.addresses[0]?.region + \",\"\r\n                            : \"\"\r\n                            }}\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.country\r\n                            ? customer?.business_partner?.addresses[0]?.country + \",\"\r\n                            : \"\"\r\n                            }}\r\n                            {{\r\n                            customer?.business_partner?.addresses[0]?.postal_code\r\n                            ? customer?.business_partner?.addresses[0]?.postal_code\r\n                            : \"\"\r\n                            }}\r\n                        </td>\r\n                        <td>\r\n                            {{ customer?.business_partner?.phone }}\r\n                        </td>\r\n                        <td>\r\n                            <button pButton type=\"button\" icon=\"pi pi-trash\" pTooltip=\"Delete\"\r\n                                class=\"p-button-primary mr-3 p-button-sm\"\r\n                                (click)=\"deleteUserCustomer(customer)\"></button>\r\n                        </td>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"emptymessage\">\r\n                    <tr>\r\n                        <td colspan=\"8\">No customers found.</td>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"loadingbody\">\r\n                    <tr>\r\n                        <td colspan=\"8\">Loading customers data. Please wait...</td>\r\n                    </tr>\r\n                </ng-template>\r\n            </p-table>\r\n        </div>\r\n    </div>\r\n</div>\r\n<p-confirmDialog></p-confirmDialog>"], "mappings": ";AAEA,SACEA,OAAO,EACPC,SAAS,EAETC,MAAM,EACNC,EAAE,EACFC,GAAG,EACHC,cAAc,QACT,MAAM;AAKb,SACEC,UAAU,EACVC,oBAAoB,EACpBC,SAAS,EACTC,GAAG,QACE,gBAAgB;;;;;;;;;;;;;;;;;ICHSC,EAAA,CAAAC,cAAA,WAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjCH,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,OAAAC,OAAA,CAAAC,aAAA,KAA0B;;;;;IAD3DP,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAQ,UAAA,IAAAC,uDAAA,mBAAiC;;;;IAD3BT,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAU,iBAAA,CAAAJ,OAAA,CAAAK,WAAA,CAAsB;IACrBX,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAY,UAAA,SAAAN,OAAA,CAAAC,aAAA,CAAwB;;;;;;IA2BnCP,EAFR,CAAAC,cAAA,cAAwE,cACX,iBAE5B;IAArBD,EAAA,CAAAa,UAAA,mBAAAC,yEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,KAAA,CAAAH,MAAA,CAAU;IAAA,EAAC;IAACnB,EAAA,CAAAG,YAAA,EAAS;IAClCH,EAAA,CAAAC,cAAA,iBACwB;IAApBD,EAAA,CAAAa,UAAA,mBAAAU,yEAAA;MAAAvB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC;IAC3BxB,EAD4B,CAAAG,YAAA,EAAS,EAC/B;IACNH,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAyB,SAAA,YAA4B;IAC5BzB,EAAA,CAAAC,cAAA,mBACwF;IADlDD,EAAA,CAAA0B,gBAAA,2BAAAC,gFAAAC,MAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAA6B,kBAAA,CAAAZ,MAAA,CAAAa,gBAAA,EAAAF,MAAA,MAAAX,MAAA,CAAAa,gBAAA,GAAAF,MAAA;MAAA,OAAA5B,EAAA,CAAAqB,WAAA,CAAAO,MAAA;IAAA,EAA8B;IAChE5B,EAAA,CAAAa,UAAA,mBAAAkB,wEAAAH,MAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAe,cAAA,CAAAb,MAAA,EAAAS,MAAA,CAA2B;IAAA,EAAC;IAEjD5B,EAHQ,CAAAG,YAAA,EACwF,EACrF,EACL;;;;IAHwCH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAiC,gBAAA,YAAAhB,MAAA,CAAAa,gBAAA,CAA8B;;;;;IAOxE9B,EADJ,CAAAC,cAAA,SAAI,aACuB;IACnBD,EAAA,CAAAyB,SAAA,4BAA+C;IACnDzB,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,aAA2D,cACM;IACzDD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAC,cAAA,cAAqC;IACjCD,EAAA,CAAAyB,SAAA,qBAA6C;IAGzDzB,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,aAAwE,cACP;IACzDD,EAAA,CAAAE,MAAA,cACA;IAAAF,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAyB,SAAA,sBAAsC;IAGlDzB,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA6D,eACI;IACzDD,EAAA,CAAAE,MAAA,uBACA;IAAAF,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAyB,SAAA,sBAA+C;IAG3DzB,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA6B,eACoC;IACzDD,EAAA,CAAAE,MAAA,iBACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACL;IAEDH,EADJ,CAAAC,cAAA,cAAsE,eACL;IACzDD,EAAA,CAAAE,MAAA,eACA;IAAAF,EAAA,CAAAC,cAAA,eAAqC;IACjCD,EAAA,CAAAyB,SAAA,sBAAuC;IAGnDzB,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACL;IAEDH,EADJ,CAAAC,cAAA,cAA4B,eACqC;IACzDD,EAAA,CAAAE,MAAA,gBACJ;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACL,EACJ;;;;;;IAIDH,EADJ,CAAAC,cAAA,aAA2B,SACnB;IACAD,EAAA,CAAAyB,SAAA,0BAAsD;IAC1DzB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,MAAA,IAmCJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEDH,EADJ,CAAAC,cAAA,UAAI,kBAG2C;IAAvCD,EAAA,CAAAa,UAAA,mBAAAqB,0EAAA;MAAA,MAAAC,WAAA,GAAAnC,EAAA,CAAAe,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAqB,kBAAA,CAAAH,WAAA,CAA4B;IAAA,EAAC;IAElDnC,EAFmD,CAAAG,YAAA,EAAS,EACnD,EACJ;;;;IAxDoBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAY,UAAA,UAAAuB,WAAA,CAAkB;IAGnCnC,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8B,WAAA,kBAAAA,WAAA,CAAAxB,WAAA,MACJ;IAEIX,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8B,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAC,OAAA,MACJ;IAEIxC,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8B,WAAA,kBAAAA,WAAA,CAAA5B,aAAA,MACJ;IAEIP,EAAA,CAAAI,SAAA,GAmCJ;IAnCIJ,EAAA,CAAAyC,kBAAA,OAAAN,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAC,YAAA,KAAAR,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAC,YAAA,oBAAAR,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAE,WAAA,KAAAT,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAE,WAAA,oBAAAT,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAG,SAAA,KAAAV,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAG,SAAA,oBAAAV,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAI,QAAA,KAAAX,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAI,QAAA,oBAAAX,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAK,MAAA,KAAAZ,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAK,MAAA,oBAAAZ,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAM,OAAA,KAAAb,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAM,OAAA,oBAAAb,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAO,WAAA,IAAAd,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAG,SAAA,qBAAAP,WAAA,CAAAI,gBAAA,CAAAG,SAAA,IAAAO,WAAA,WAmCJ;IAEIjD,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8B,WAAA,kBAAAA,WAAA,CAAAI,gBAAA,kBAAAJ,WAAA,CAAAI,gBAAA,CAAAW,KAAA,MACJ;;;;;IAUAlD,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IACvCF,EADuC,CAAAG,YAAA,EAAK,EACvC;;;;;IAIDH,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAC1DF,EAD0D,CAAAG,YAAA,EAAK,EAC1D;;;ADnJzB,OAAM,MAAOgD,wBAAwB;EAqBnCC,YACUC,EAAe,EACfC,YAA0B,EAC1BC,MAAc,EACdC,KAAqB,EACrBC,cAA8B,EAC9BC,mBAAwC;IALxC,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA1BrB,KAAAC,cAAc,GAAG,IAAIrE,OAAO,EAAQ;IAErC,KAAAsE,QAAQ,GAAU,EAAE;IACpB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAhC,gBAAgB,GAAW,EAAE;IAC7B,KAAAiC,EAAE,GAAW,EAAE;IACf,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,MAAM,GAAG,KAAK;IACb,KAAAC,cAAc,GAAQ,EAAE;IAChC,KAAAC,aAAa,GAAU,EAAE;IAElB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,IAAIjF,OAAO,EAAU;IACtC,KAAAkF,YAAY,GAAQ,IAAI,CAACnB,EAAE,CAACoB,KAAK,CAAC;MACvCC,SAAS,EAAE,CAAC,IAAI;KACjB,CAAC;EASC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACP,cAAc,GAAG,CAAC;MAAEzD,WAAW,EAAE;IAAK,CAAE,CAAC;IAC9C,IAAI,CAACoD,EAAE,GAAG,IAAI,CAACP,KAAK,CAACoB,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC9D,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAAC1B,YAAY,CAAC2B,IAAI,CACnBC,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACoE,cAAc,CAAC,CAAC,CACpCwB,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACpB,UAAU,GAAGoB,IAAI,EAAEC,UAAU;MAClC,IAAI,CAACC,gBAAgB,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAE,CAAC;IAC/C,CAAC,CAAC;EACN;EAEAF,gBAAgBA,CAACG,KAAU;IACzB,IAAI,CAAC3B,OAAO,GAAG,IAAI;IACnB,MAAM4B,IAAI,GAAGD,KAAK,CAACF,KAAK,GAAGE,KAAK,CAACD,IAAI,GAAG,CAAC;IACzC,MAAMG,QAAQ,GAAGF,KAAK,CAACD,IAAI;IAC3B,MAAMI,SAAS,GAAGH,KAAK,CAACG,SAAS;IACjC,MAAMC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IACjC,IAAI,CAACvC,YAAY,CACdwC,iBAAiB,CAChB,IAAI,CAAC9B,UAAU,EACf0B,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACT,IAAI,CAAC/D,gBAAgB,CACtB,CACAoD,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACoE,cAAc,CAAC,CAAC,CACpCwB,SAAS,CAAC;MACTY,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACpC,QAAQ,GAAGoC,QAAQ,EAAEZ,IAAI,IAAI,EAAE;QACpC,IAAI,CAACvB,YAAY,GAAGmC,QAAQ,EAAEC,UAAU,EAAEC,KAAK;QAC/C,IAAI,CAACpC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACrC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEA9B,cAAcA,CAACqE,KAAY,EAAEZ,KAAY;IACvC,IAAI,CAACH,gBAAgB,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEQR,aAAaA,CAAA;IACnB,IAAI,CAACsB,UAAU,GAAG9G,MAAM,CACtBC,EAAE,CAAC,IAAI,CAAC2E,cAAc,CAAC;IAAE;IACzB,IAAI,CAACG,cAAc,CAACW,IAAI,CACtBrF,oBAAoB,EAAE,EACtBE,GAAG,CAAC,MAAO,IAAI,CAACuE,eAAe,GAAG,IAAK,CAAC,EACxCxE,SAAS,CAAEyG,IAAS,IAAI;MACtB,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAClC,eAAe,GAAG,KAAK;QAC5B,OAAO7E,EAAE,CAAC,IAAI,CAAC2E,cAAc,CAAC;MAChC;MACA,MAAMqC,MAAM,GAAQ,EAAE;MACtB,IAAIF,IAAI,EAAE;QACRE,MAAM,CAAC,4BAA4B,CAAC,GAAGF,IAAI;QAC3CE,MAAM,CAAC,WAAW,CAAC,GAAG,aAAa;QACnCA,MAAM,CAAC,WAAW,CAAC,GAAG,eAAe;MACvC;MACA,OAAO,IAAI,CAACnD,YAAY,CAACoD,YAAY,CAACD,MAAM,CAAC,CAACvB,IAAI,CAChDxF,GAAG,CAAEiH,GAAQ,IAAI;QACf,IAAIvB,IAAI,GAAGuB,GAAG,CAACvB,IAAI,IAAI,EAAE;QACzB,IAAI,IAAI,CAAChB,cAAc,CAAC,CAAC,CAAC,EAAE;UAC1BgB,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACxC,cAAc,CAAC,CAAC,CAAC,CAAC;QACtC;QACA,OAAOuC,GAAG,CAACvB,IAAI;MACjB,CAAC,CAAC,EACFxF,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAI,CAAC2E,cAAc,CAAC,CAAC;MAAE;MAC3CrE,GAAG,CAAC,MAAO,IAAI,CAACuE,eAAe,GAAG,KAAM,CAAC,CAC1C;IACH,CAAC,CAAC,CACH,CACF;EACH;EAEAuC,eAAeA,CAACjF,MAAW;IACzB,IAAIA,MAAM,CAACjB,WAAW,KAAK,KAAK,EAAE;MAChC,IAAI,CAAC6D,YAAY,CAACsC,UAAU,CAAC;QAAEpC,SAAS,EAAE,IAAI,CAACN;MAAc,CAAE,CAAC;IAClE,CAAC,MAAM;MACL,MAAM2C,YAAY,GAAG,IAAI,CAACC,CAAC,CAACtC,SAAS,CAACuC,KAAK;MAC3C,MAAMC,KAAK,GAAGH,YAAY,CAACI,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACzG,WAAW,KAAK,KAAK,CAAC;MACzE,IAAIuG,KAAK,GAAG,CAAC,CAAC,EAAE;QACdH,YAAY,CAACM,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC1C,YAAY,CAACsC,UAAU,CAAC;UAAEpC,SAAS,EAAEqC;QAAY,CAAE,CAAC;MAC3D;IACF;EACF;EAEMO,cAAcA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClBD,KAAI,CAACrD,SAAS,GAAG,IAAI;MACrBqD,KAAI,CAACpD,MAAM,GAAG,IAAI;MAElB,IAAIoD,KAAI,CAAC/C,YAAY,CAACiD,OAAO,EAAE;QAC7B;MACF;MAEA,MAAMC,UAAU,GAAG;QAAE,GAAGH,KAAI,CAAC/C,YAAY,EAAEyC;MAAK,CAAE;MAClD,IAAIU,KAAK,CAACC,OAAO,CAACF,UAAU,CAAChD,SAAS,CAAC,IAAIgD,UAAU,CAAChD,SAAS,CAAC8B,MAAM,EAAE;QACtE,IAAIkB,UAAU,CAAChD,SAAS,CAACmD,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACnH,WAAW,KAAK,KAAK,CAAC,EAAE;UAClE,MAAM8F,MAAM,GAAQ;YAClB,WAAW,EAAE,YAAY;YACzB,sBAAsB,EAAE;WACzB;UACD,MAAM;YAAErB;UAAI,CAAE,SAAczF,cAAc,CACxC4H,KAAI,CAACjE,YAAY,CAACoD,YAAY,CAACD,MAAM,CAAC,CACvC;UACDiB,UAAU,CAAChD,SAAS,GAAGU,IAAI;QAC7B;QACAsC,UAAU,CAAChD,SAAS,GAAG;UACrBqD,OAAO,EAAEL,UAAU,CAAChD,SAAS,CAAChF,GAAG,CAAEoI,CAAM,IAAKA,CAAC,CAAC/D,EAAE;SACnD;MACH;MAEA,MAAMiE,OAAO,GAAG;QAAE,GAAGN;MAAU,CAAE;MACjC,OAAOM,OAAO,CAACjE,EAAE;MACjB,OAAOiE,OAAO,CAAC3C,UAAU;MACzBkC,KAAI,CAACjE,YAAY,CACd2E,UAAU,CAACV,KAAI,CAACxD,EAAE,EAAEiE,OAAO,CAAC,CAC5B9C,IAAI,CAAC3F,SAAS,CAACgI,KAAI,CAAC5D,cAAc,CAAC,CAAC,CACpCwB,SAAS,CAAC;QACTY,IAAI,EAAEA,CAAA,KAAK;UACTwB,KAAI,CAACrD,SAAS,GAAG,KAAK;UACtBqD,KAAI,CAAC/C,YAAY,CAACsC,UAAU,CAAC;YAAEpC,SAAS,EAAO;UAAE,CAAE,CAAC;UACpD6C,KAAI,CAAC9D,cAAc,CAACyE,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACFb,KAAI,CAAC/F,OAAO,EAAE;QAChB,CAAC;QACD2E,KAAK,EAAEA,CAAA,KAAK;UACVoB,KAAI,CAACrD,SAAS,GAAG,KAAK;UACtBqD,KAAI,CAAC9D,cAAc,CAACyE,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IAAC;EACP;EAEO9F,kBAAkBA,CAAC+F,CAAM;IAC9B,IAAI,CAAC/E,YAAY,CACd2E,UAAU,CAAC,IAAI,CAAClE,EAAE,EAAE;MAAEW,SAAS,EAAE;QAAE4D,UAAU,EAAED,CAAC,CAACtE;MAAE;IAAE,CAAE,CAAC,CACxDmB,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACoE,cAAc,CAAC,CAAC,CACpCwB,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACtC,cAAc,CAACyE,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC5G,OAAO,EAAE;MAChB,CAAC;MACD2E,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC1C,cAAc,CAACyE,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEA5G,OAAOA,CAAA;IACL,IAAI,CAAC8D,gBAAgB,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEAlE,KAAKA,CAAC+E,KAAY;IAChB,IAAI,CAACvE,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACyG,MAAM,CAACC,aAAa,CAACvB,KAAK,GAAG,EAAE;IACpC,IAAI,CAAC3B,gBAAgB,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEAiD,UAAUA,CAAA;IACR,IAAI,CAACvE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,MAAM,GAAG,IAAI;IAElB,IAAI,CAAC,IAAI,CAACE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACmC,MAAM,KAAK,CAAC,EAAE;MAC1D;IACF;IAEA,IAAImB,KAAK,CAACC,OAAO,CAAC,IAAI,CAACvD,aAAa,CAAC,IAAI,IAAI,CAACA,aAAa,CAACmC,MAAM,EAAE;MAClE,MAAMkC,cAAc,GAAG;QACrBJ,UAAU,EAAE,IAAI,CAACjE,aAAa,CAAC3E,GAAG,CAAEoI,CAAM,IAAKA,CAAC,CAAC/D,EAAE;OACpD;MAED,IAAI,CAACT,YAAY,CACd2E,UAAU,CAAC,IAAI,CAAClE,EAAE,EAAE;QAAEW,SAAS,EAAEgE;MAAc,CAAE,CAAC,CAClDxD,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACoE,cAAc,CAAC,CAAC,CACpCwB,SAAS,CAAC;QACTY,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC7B,SAAS,GAAG,KAAK;UACtB,IAAI,CAACG,aAAa,GAAG,EAAE;UACvB,IAAI,CAACZ,cAAc,CAACyE,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAAC5G,OAAO,EAAE;QAChB,CAAC;QACD2E,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACjC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACT,cAAc,CAACyE,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,MAAM,EAAE;WACT,CAAC;QACJ;OACD,CAAC;IACN;EACF;EAEAO,aAAaA,CAAA;IACX,IAAI,CAACjF,mBAAmB,CAACkF,OAAO,CAAC;MAC/BC,OAAO,EAAE,uDAAuD;MAChEC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,4BAA4B;MAClCC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAACP,UAAU,EAAE;MACnB;KACD,CAAC;EACJ;EAEA,IAAIzB,CAACA,CAAA;IACH,OAAO,IAAI,CAACxC,YAAY,CAACyE,QAAQ;EACnC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvF,cAAc,CAACoC,IAAI,EAAE;IAC1B,IAAI,CAACpC,cAAc,CAACwF,QAAQ,EAAE;EAChC;;;uBAlQWhG,wBAAwB,EAAAnD,EAAA,CAAAoJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtJ,EAAA,CAAAoJ,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAxJ,EAAA,CAAAoJ,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA1J,EAAA,CAAAoJ,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAA3J,EAAA,CAAAoJ,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA7J,EAAA,CAAAoJ,iBAAA,CAAAQ,EAAA,CAAAE,mBAAA;IAAA;EAAA;;;YAAxB3G,wBAAwB;MAAA4G,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCvBrBlK,EAJhB,CAAAC,cAAA,aAAkB,aACM,aACE,aAC0C,SAChD;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UACtBF,EADsB,CAAAG,YAAA,EAAK,EACrB;UAIMH,EAHZ,CAAAC,cAAA,cAAiC,aACX,aACe,mBAMyC;;UAC9DD,EAAA,CAAAQ,UAAA,KAAA4J,gDAAA,yBAA2C;UAKnDpK,EADI,CAAAG,YAAA,EAAY,EACV;UAGEH,EAFR,CAAAC,cAAA,eAA6B,eACK,kBAGkB;UADkBD,EAAA,CAAAa,UAAA,mBAAAwJ,2DAAA;YAAArK,EAAA,CAAAe,aAAA,CAAAuJ,GAAA;YAAA,OAAAtK,EAAA,CAAAqB,WAAA,CAAS8I,GAAA,CAAAxB,aAAA,EAAe;UAAA,EAAC;UAC3C3I,EAAA,CAAAG,YAAA,EAAS;UACrDH,EAAA,CAAAC,cAAA,kBAC+B;UAA3BD,EAAA,CAAAa,UAAA,mBAAA0J,2DAAA;YAAAvK,EAAA,CAAAe,aAAA,CAAAuJ,GAAA;YAAA,OAAAtK,EAAA,CAAAqB,WAAA,CAAS8I,GAAA,CAAA7C,cAAA,EAAgB;UAAA,EAAC;UAI9CtH,EAJ+C,CAAAG,YAAA,EAAS,EACtC,EACJ,EACJ,EACH;UAEPH,EAAA,CAAAC,cAAA,sBAOuD;UAPtBD,EAAA,CAAA0B,gBAAA,6BAAA8I,sEAAA5I,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAAuJ,GAAA;YAAAtK,EAAA,CAAA6B,kBAAA,CAAAsI,GAAA,CAAA9F,aAAA,EAAAzC,MAAA,MAAAuI,GAAA,CAAA9F,aAAA,GAAAzC,MAAA;YAAA,OAAA5B,EAAA,CAAAqB,WAAA,CAAAO,MAAA;UAAA,EAA6B;UAC1D5B,EAAA,CAAAa,UAAA,wBAAA4J,iEAAA7I,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAAuJ,GAAA;YAAA,OAAAtK,EAAA,CAAAqB,WAAA,CAAc8I,GAAA,CAAA7E,gBAAA,CAAA1D,MAAA,CAAwB;UAAA,EAAC;UAyIvC5B,EAlIA,CAAAQ,UAAA,KAAAkK,gDAAA,0BAAiC,KAAAC,gDAAA,2BAeD,KAAAC,gDAAA,4BAiDW,KAAAC,gDAAA,0BA6DL,KAAAC,gDAAA,0BAKD;UAQrD9K,EAHY,CAAAG,YAAA,EAAU,EACR,EACJ,EACJ;UACNH,EAAA,CAAAyB,SAAA,uBAAmC;;;UA9KjBzB,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,cAAAuJ,GAAA,CAAA3F,YAAA,CAA0B;UAGExE,EAAA,CAAAI,SAAA,GAA4B;UAG9CJ,EAHkB,CAAAY,UAAA,UAAAZ,EAAA,CAAA+K,WAAA,SAAAZ,GAAA,CAAA7D,UAAA,EAA4B,kBAA0C,sBACnE,YAAA6D,GAAA,CAAA7F,eAAA,CAA4B,oBAAoB,cAAA6F,GAAA,CAAA5F,cAAA,CACb,wBAAwB,2EAG7F;UAUiBvE,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAY,UAAA,cAAAuJ,GAAA,CAAA9F,aAAA,IAAA8F,GAAA,CAAA9F,aAAA,CAAAmC,MAAA,OAAyD;UAS/DxG,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAY,UAAA,UAAAuJ,GAAA,CAAAvG,QAAA,CAAkB;UAAC5D,EAAA,CAAAiC,gBAAA,cAAAkI,GAAA,CAAA9F,aAAA,CAA6B;UAO/DrE,EAP6E,CAAAY,UAAA,YAAW,YAAAuJ,GAAA,CAAArG,OAAA,CACxB,kBAAkB,mBACxB,uBAAA9D,EAAA,CAAAgL,eAAA,KAAAC,GAAA,EAK3D,iBAAAd,GAAA,CAAAtG,YAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}