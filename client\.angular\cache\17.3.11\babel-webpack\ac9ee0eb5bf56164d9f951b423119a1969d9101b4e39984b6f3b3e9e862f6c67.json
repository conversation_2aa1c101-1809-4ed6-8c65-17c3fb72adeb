{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CrmService {\n  constructor(http) {\n    this.http = http;\n    this.crmSubject = new BehaviorSubject(null);\n    this.crm = this.crmSubject.asObservable();\n  }\n  get(type, moduleurl) {\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`);\n  }\n  getPartner() {\n    return this.http.get(`${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}`);\n  }\n  save(data, moduleurl) {\n    return this.http.post(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`, {\n      data: data\n    });\n  }\n  savepartner(data) {\n    return this.http.post(`${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}`, {\n      data: data\n    });\n  }\n  getDetermination(type, moduleurl) {\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`);\n  }\n  update(data, id, moduleurl) {\n    return this.http.put(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`, {\n      data\n    });\n  }\n  updatePartner(data, id) {\n    return this.http.put(`${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}/${id}`, {\n      data\n    });\n  }\n  delete(id, moduleurl) {\n    return this.http.delete(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`);\n  }\n  deletePartner(id) {\n    return this.http.delete(`${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}/${id}`);\n  }\n  getPartnerfunction() {\n    let params = new HttpParams().set('filters[usage][$eq]', 'CRM').set('filters[is_active][$eq]', 'true').set('filters[type][$eq]', 'PARTNER_FUNCTIONS');\n    return this.http.get(`${CMS_APIContstant.CONFIGURATION}`, {\n      params\n    }).pipe(map(response => {\n      let data = response.data || [];\n      return data.map(item => ({\n        label: item.description,\n        // Display text\n        value: item.code // Stored value\n      }));\n    }));\n  }\n  static {\n    this.ɵfac = function CrmService_Factory(t) {\n      return new (t || CrmService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CrmService,\n      factory: CrmService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "map", "CrmService", "constructor", "http", "crmSubject", "crm", "asObservable", "get", "type", "<PERSON><PERSON><PERSON>", "CMSAPI_END_POINT", "<PERSON><PERSON><PERSON><PERSON>", "PARTNER_FUNCTION_CONFIG", "save", "data", "post", "<PERSON>par<PERSON>ner", "getDetermination", "update", "id", "put", "update<PERSON><PERSON><PERSON>", "delete", "deletePartner", "getPartnerfunction", "params", "set", "CONFIGURATION", "pipe", "response", "item", "label", "description", "value", "code", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\crm\\crm.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\nimport { map } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CrmService {\r\n  public crmSubject = new BehaviorSubject<any>(null);\r\n  public crm = this.crmSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  get(type: string, moduleurl: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`\r\n    );\r\n  }\r\n\r\n  getPartner() {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}`\r\n    );\r\n  }\r\n\r\n  save(data: any, moduleurl: string) {\r\n    return this.http.post<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`,\r\n      { data: data }\r\n    );\r\n  }\r\n\r\n  savepartner(data: any) {\r\n    return this.http.post<any>(\r\n      `${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}`,\r\n      { data: data }\r\n    );\r\n  }\r\n\r\n  getDetermination(type: string, moduleurl: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`\r\n    );\r\n  }\r\n\r\n  update(data: any, id: string, moduleurl: string) {\r\n    return this.http.put<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`,\r\n      { data }\r\n    );\r\n  }\r\n\r\n  updatePartner(data: any, id: string) {\r\n    return this.http.put<any>(\r\n      `${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}/${id}`,\r\n      { data }\r\n    );\r\n  }\r\n\r\n  delete(id: string, moduleurl: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`\r\n    );\r\n  }\r\n\r\n  deletePartner(id: string) {\r\n    return this.http.delete<any>(\r\n      `${CMS_APIContstant.PARTNER_FUNCTION_CONFIG}/${id}`\r\n    );\r\n  }\r\n\r\n  getPartnerfunction(): Observable<{ label: string; value: string }[]> {\r\n    let params = new HttpParams()\r\n      .set('filters[usage][$eq]', 'CRM')\r\n      .set('filters[is_active][$eq]', 'true')\r\n      .set('filters[type][$eq]', 'PARTNER_FUNCTIONS');\r\n\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.CONFIGURATION}`, { params })\r\n      .pipe(\r\n        map((response: any) => {\r\n          let data = response.data || [];\r\n          return data.map((item: any) => ({\r\n            label: item.description, // Display text\r\n            value: item.code, // Stored value\r\n          }));\r\n        })\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,GAAG,QAAQ,gBAAgB;;;AAKpC,OAAM,MAAOC,UAAU;EAIrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,UAAU,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC3C,KAAAO,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,YAAY,EAAE;EAEJ;EAEvCC,GAAGA,CAACC,IAAY,EAAEC,SAAiB;IACjC,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAClB,GAAGR,gBAAgB,CAACW,gBAAgB,IAAID,SAAS,EAAE,CACpD;EACH;EAEAE,UAAUA,CAAA;IACR,OAAO,IAAI,CAACR,IAAI,CAACI,GAAG,CAClB,GAAGR,gBAAgB,CAACa,uBAAuB,EAAE,CAC9C;EACH;EAEAC,IAAIA,CAACC,IAAS,EAAEL,SAAiB;IAC/B,OAAO,IAAI,CAACN,IAAI,CAACY,IAAI,CACnB,GAAGhB,gBAAgB,CAACW,gBAAgB,IAAID,SAAS,EAAE,EACnD;MAAEK,IAAI,EAAEA;IAAI,CAAE,CACf;EACH;EAEAE,WAAWA,CAACF,IAAS;IACnB,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CACnB,GAAGhB,gBAAgB,CAACa,uBAAuB,EAAE,EAC7C;MAAEE,IAAI,EAAEA;IAAI,CAAE,CACf;EACH;EAEAG,gBAAgBA,CAACT,IAAY,EAAEC,SAAiB;IAC9C,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAClB,GAAGR,gBAAgB,CAACW,gBAAgB,IAAID,SAAS,EAAE,CACpD;EACH;EAEAS,MAAMA,CAACJ,IAAS,EAAEK,EAAU,EAAEV,SAAiB;IAC7C,OAAO,IAAI,CAACN,IAAI,CAACiB,GAAG,CAClB,GAAGrB,gBAAgB,CAACW,gBAAgB,IAAID,SAAS,IAAIU,EAAE,EAAE,EACzD;MAAEL;IAAI,CAAE,CACT;EACH;EAEAO,aAAaA,CAACP,IAAS,EAAEK,EAAU;IACjC,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAClB,GAAGrB,gBAAgB,CAACa,uBAAuB,IAAIO,EAAE,EAAE,EACnD;MAAEL;IAAI,CAAE,CACT;EACH;EAEAQ,MAAMA,CAACH,EAAU,EAAEV,SAAiB;IAClC,OAAO,IAAI,CAACN,IAAI,CAACmB,MAAM,CACrB,GAAGvB,gBAAgB,CAACW,gBAAgB,IAAID,SAAS,IAAIU,EAAE,EAAE,CAC1D;EACH;EAEAI,aAAaA,CAACJ,EAAU;IACtB,OAAO,IAAI,CAAChB,IAAI,CAACmB,MAAM,CACrB,GAAGvB,gBAAgB,CAACa,uBAAuB,IAAIO,EAAE,EAAE,CACpD;EACH;EAEAK,kBAAkBA,CAAA;IAChB,IAAIC,MAAM,GAAG,IAAI5B,UAAU,EAAE,CAC1B6B,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC,CACjCA,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CACtCA,GAAG,CAAC,oBAAoB,EAAE,mBAAmB,CAAC;IAEjD,OAAO,IAAI,CAACvB,IAAI,CACbI,GAAG,CAAM,GAAGR,gBAAgB,CAAC4B,aAAa,EAAE,EAAE;MAAEF;IAAM,CAAE,CAAC,CACzDG,IAAI,CACH5B,GAAG,CAAE6B,QAAa,IAAI;MACpB,IAAIf,IAAI,GAAGe,QAAQ,CAACf,IAAI,IAAI,EAAE;MAC9B,OAAOA,IAAI,CAACd,GAAG,CAAE8B,IAAS,KAAM;QAC9BC,KAAK,EAAED,IAAI,CAACE,WAAW;QAAE;QACzBC,KAAK,EAAEH,IAAI,CAACI,IAAI,CAAE;OACnB,CAAC,CAAC;IACL,CAAC,CAAC,CACH;EACL;;;uBAjFWjC,UAAU,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVrC,UAAU;MAAAsC,OAAA,EAAVtC,UAAU,CAAAuC,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}