{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  apiEndpoint: 'https://dmsnjyaapidev.cfapps.us10-001.hana.ondemand.com',\n  cmsApiEndpoint: 'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com',\n  cmsApiToken: '81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695'\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "apiEndpoint", "cmsApiEndpoint", "cmsApiToken"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n  apiEndpoint: 'https://dmsnjyaapidev.cfapps.us10-001.hana.ondemand.com',\r\n  cmsApiEndpoint: 'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com',\r\n  cmsApiToken: '81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695'\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAE,yDAAyD;EACtEC,cAAc,EAAE,8DAA8D;EAC9EC,WAAW,EAAE;CACd;AAED;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}