{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../product.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nfunction ClassficationComponent_ng_container_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"label\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 7);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ClassficationComponent_ng_container_1_ng_container_6_Template_input_ngModelChange_4_listener($event) {\n      const val_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(val_r2.formatted_charc_value, $event) || (val_r2.formatted_charc_value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const val_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(val_r2 == null ? null : val_r2.class_charc_type_descr);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", val_r2.formatted_charc_value);\n  }\n}\nfunction ClassficationComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h6\", 3)(2, \"b\");\n    i0.ɵɵtext(3, \"Class: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 4);\n    i0.ɵɵtemplate(6, ClassficationComponent_ng_container_1_ng_container_6_Template, 5, 2, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r3.key, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r3.value);\n  }\n}\nfunction ClassficationComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ClassficationComponent {\n  constructor(fb, productservice, messageservice) {\n    this.fb = fb;\n    this.productservice = productservice;\n    this.messageservice = messageservice;\n    this.unsubscribe$ = new Subject();\n    this.classfications = null;\n    this.loadingProductClassifications = false;\n    this.classification = null;\n    this.classificationForm = this.fb.group({});\n  }\n  ngOnInit() {\n    this.productservice.product.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.getProductClassification();\n    });\n  }\n  getProductClassification() {\n    if (this.classfications?.product_id) {\n      this.loadingProductClassifications = true;\n      this.productservice.getProductClassification(this.classfications?.product_id).subscribe({\n        next: res => {\n          this.loadingProductClassifications = false;\n          const data = res?.data || [];\n          this.classification = data.reduce((r, a) => {\n            r[a.class_type_descr] = r[a.class_type_descr] || [];\n            r[a.class_type_descr].push(a);\n            return r;\n          }, Object.create(null));\n        },\n        error: e => {\n          this.loadingProductClassifications = false;\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ClassficationComponent_Factory(t) {\n      return new (t || ClassficationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassficationComponent,\n      selectors: [[\"app-classfication\"]],\n      decls: 4,\n      vars: 4,\n      consts: [[\"&ngIf\", \"!loadingProductClassifications\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"mb-3\"], [1, \"row\"], [1, \"col-6\", \"form-group\"], [1, \"text-capitalize\"], [\"type\", \"text\", \"disabled\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function ClassficationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, ClassficationComponent_ng_container_1_Template, 7, 2, \"ng-container\", 1);\n          i0.ɵɵpipe(2, \"keyvalue\");\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(3, ClassficationComponent_div_3_Template, 2, 0, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(2, 2, ctx.classification));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadingProductClassifications);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgModel, i4.KeyValuePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "ClassficationComponent_ng_container_1_ng_container_6_Template_input_ngModelChange_4_listener", "$event", "val_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "formatted_charc_value", "ɵɵresetView", "ɵɵadvance", "ɵɵtextInterpolate", "class_charc_type_descr", "ɵɵtwoWayProperty", "ɵɵtemplate", "ClassficationComponent_ng_container_1_ng_container_6_Template", "ɵɵtextInterpolate1", "item_r3", "key", "ɵɵproperty", "value", "ClassficationComponent", "constructor", "fb", "productservice", "messageservice", "unsubscribe$", "classfications", "loadingProductClassifications", "classification", "classificationForm", "group", "ngOnInit", "product", "pipe", "subscribe", "data", "getProductClassification", "product_id", "next", "res", "reduce", "r", "a", "class_type_descr", "push", "Object", "create", "error", "e", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ProductService", "i3", "MessageService", "selectors", "decls", "vars", "consts", "template", "ClassficationComponent_Template", "rf", "ctx", "ClassficationComponent_ng_container_1_Template", "ClassficationComponent_div_3_Template", "ɵɵpipeBind1"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\classfication\\classfication.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\classfication\\classfication.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ProductService } from '../../product.service';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-classfication',\r\n  templateUrl: './classfication.component.html',\r\n  styleUrl: './classfication.component.scss',\r\n})\r\nexport class ClassficationComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public classfications: any = null;\r\n  loadingProductClassifications = false;\r\n  public classification: any = null;\r\n  classificationForm = this.fb.group({});\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private productservice: ProductService,\r\n    private messageservice:MessageService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.productservice.product\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.getProductClassification();\r\n      });\r\n  }\r\n\r\n  getProductClassification() {\r\n    if (this.classfications?.product_id) {\r\n      this.loadingProductClassifications = true;\r\n      this.productservice\r\n        .getProductClassification(this.classfications?.product_id)\r\n        .subscribe({\r\n          next: (res: any) => {\r\n            this.loadingProductClassifications = false;\r\n            const data = res?.data || [];\r\n            this.classification = data.reduce((r: any, a: any) => {\r\n              r[a.class_type_descr] = r[a.class_type_descr] || [];\r\n              r[a.class_type_descr].push(a);\r\n              return r;\r\n            }, Object.create(null));\r\n          },\r\n          error: (e) => {\r\n            this.loadingProductClassifications = false;\r\n          },\r\n        });\r\n    }\r\n  }\r\n}\r\n", "<ng-container &ngIf=\"!loadingProductClassifications\">\r\n    <ng-container *ngFor=\"let item of classification | keyvalue\">\r\n      <h6 class=\"mb-3\"><b>Class: </b> {{item.key}}</h6>\r\n      <div class=\"row\">\r\n        <ng-container *ngFor=\"let val of $any(item).value; let j = index\">\r\n          <div class=\"col-6 form-group\">\r\n            <label class=\"text-capitalize\">{{ val?.class_charc_type_descr }}</label>\r\n            <input class=\"form-control\" type=\"text\" [(ngModel)]=\"val.formatted_charc_value\" disabled />\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </ng-container>\r\n  </ng-container>\r\n  <div *ngIf=\"loadingProductClassifications\">Loading...</div>"], "mappings": "AAGA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;ICCjCC,EAAA,CAAAC,uBAAA,GAAkE;IAE9DD,EADF,CAAAE,cAAA,aAA8B,eACG;IAAAF,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxEJ,EAAA,CAAAE,cAAA,eAA2F;IAAnDF,EAAA,CAAAK,gBAAA,2BAAAC,6FAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAX,EAAA,CAAAY,kBAAA,CAAAJ,MAAA,CAAAK,qBAAA,EAAAN,MAAA,MAAAC,MAAA,CAAAK,qBAAA,GAAAN,MAAA;MAAA,OAAAP,EAAA,CAAAc,WAAA,CAAAP,MAAA;IAAA,EAAuC;IACjFP,EADE,CAAAI,YAAA,EAA2F,EACvF;;;;;IAF2BJ,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,iBAAA,CAAAR,MAAA,kBAAAA,MAAA,CAAAS,sBAAA,CAAiC;IACxBjB,EAAA,CAAAe,SAAA,EAAuC;IAAvCf,EAAA,CAAAkB,gBAAA,YAAAV,MAAA,CAAAK,qBAAA,CAAuC;;;;;IANvFb,EAAA,CAAAC,uBAAA,GAA6D;IAC1CD,EAAjB,CAAAE,cAAA,YAAiB,QAAG;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAACJ,EAAA,CAAAG,MAAA,GAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAE,cAAA,aAAiB;IACfF,EAAA,CAAAmB,UAAA,IAAAC,6DAAA,0BAAkE;IAMpEpB,EAAA,CAAAI,YAAA,EAAM;;;;;IAR0BJ,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAqB,kBAAA,MAAAC,OAAA,CAAAC,GAAA,KAAY;IAEZvB,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAwB,UAAA,YAAAF,OAAA,CAAAG,KAAA,CAAqB;;;;;IASzDzB,EAAA,CAAAE,cAAA,UAA2C;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;ADF7D,OAAM,MAAOsB,sBAAsB;EAOjCC,YACUC,EAAe,EACfC,cAA8B,EAC9BC,cAA6B;IAF7B,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IAThB,KAAAC,YAAY,GAAG,IAAIjC,OAAO,EAAQ;IACnC,KAAAkC,cAAc,GAAQ,IAAI;IACjC,KAAAC,6BAA6B,GAAG,KAAK;IAC9B,KAAAC,cAAc,GAAQ,IAAI;IACjC,KAAAC,kBAAkB,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC,EAAE,CAAC;EAMnC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACR,cAAc,CAACS,OAAO,CACxBC,IAAI,CAACxC,SAAS,CAAC,IAAI,CAACgC,YAAY,CAAC,CAAC,CAClCS,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACN;EAEAA,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACV,cAAc,EAAEW,UAAU,EAAE;MACnC,IAAI,CAACV,6BAA6B,GAAG,IAAI;MACzC,IAAI,CAACJ,cAAc,CAChBa,wBAAwB,CAAC,IAAI,CAACV,cAAc,EAAEW,UAAU,CAAC,CACzDH,SAAS,CAAC;QACTI,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAI,CAACZ,6BAA6B,GAAG,KAAK;UAC1C,MAAMQ,IAAI,GAAGI,GAAG,EAAEJ,IAAI,IAAI,EAAE;UAC5B,IAAI,CAACP,cAAc,GAAGO,IAAI,CAACK,MAAM,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAI;YACnDD,CAAC,CAACC,CAAC,CAACC,gBAAgB,CAAC,GAAGF,CAAC,CAACC,CAAC,CAACC,gBAAgB,CAAC,IAAI,EAAE;YACnDF,CAAC,CAACC,CAAC,CAACC,gBAAgB,CAAC,CAACC,IAAI,CAACF,CAAC,CAAC;YAC7B,OAAOD,CAAC;UACV,CAAC,EAAEI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;QACDC,KAAK,EAAGC,CAAC,IAAI;UACX,IAAI,CAACrB,6BAA6B,GAAG,KAAK;QAC5C;OACD,CAAC;IACN;EACF;;;uBAzCWP,sBAAsB,EAAA1B,EAAA,CAAAuD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzD,EAAA,CAAAuD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3D,EAAA,CAAAuD,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAtBnC,sBAAsB;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCpE,EAAA,CAAAC,uBAAA,MAAqD;UACjDD,EAAA,CAAAmB,UAAA,IAAAmD,8CAAA,0BAA6D;;;UAY/DtE,EAAA,CAAAmB,UAAA,IAAAoD,qCAAA,iBAA2C;;;UAZVvE,EAAA,CAAAe,SAAA,EAA4B;UAA5Bf,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAwE,WAAA,OAAAH,GAAA,CAAAnC,cAAA,EAA4B;UAYvDlC,EAAA,CAAAe,SAAA,GAAmC;UAAnCf,EAAA,CAAAwB,UAAA,SAAA6C,GAAA,CAAApC,6BAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}