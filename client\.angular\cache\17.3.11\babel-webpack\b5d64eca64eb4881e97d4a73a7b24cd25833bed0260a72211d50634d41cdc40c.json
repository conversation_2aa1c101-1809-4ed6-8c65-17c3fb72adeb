{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./crm.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/toast\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/listbox\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/inputtext\";\nfunction CrmComponent_ng_container_5_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 19);\n    i0.ɵɵtext(3, \"No records found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CrmComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function CrmComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template_button_click_3_listener() {\n      const email_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.removeEmail(email_r5));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const email_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(email_r5);\n  }\n}\nfunction CrmComponent_ng_container_5_ng_container_10_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 21)(2, \"p-listbox\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CrmComponent_ng_container_5_ng_container_10_tr_1_Template_p_listbox_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedEmail, $event) || (ctx_r1.selectedEmail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(3, CrmComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template, 4, 1, \"ng-template\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CrmComponent_ng_container_5_ng_container_10_tr_1_Template_button_click_4_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.updateSettings(item_r6));\n    });\n    i0.ɵɵtext(5, \" Submit \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.editCRMAdmin.crm_admin_user_emails);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedEmail);\n  }\n}\nfunction CrmComponent_ng_container_5_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CrmComponent_ng_container_5_ng_container_10_tr_1_Template, 6, 2, \"tr\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.Emails);\n  }\n}\nfunction CrmComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"table\", 14)(3, \"thead\", 15)(4, \"tr\")(5, \"th\", 16);\n    i0.ɵɵtext(6, \" CRM Admin Email \");\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CrmComponent_ng_container_5_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openDialog());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"tbody\", 18);\n    i0.ɵɵtemplate(9, CrmComponent_ng_container_5_ng_container_9_Template, 4, 0, \"ng-container\", 4)(10, CrmComponent_ng_container_5_ng_container_10_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.Emails.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.Emails.length);\n  }\n}\nfunction CrmComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class CrmComponent {\n  constructor(crmservice, messageservice, route) {\n    this.crmservice = crmservice;\n    this.messageservice = messageservice;\n    this.route = route;\n    this.unsubscribe$ = new Subject();\n    this.settingType = '';\n    this.settingTitle = '';\n    this.selectedEmail = '';\n    this.newEmail = '';\n    this.loading = false;\n    this.Emails = [];\n    this.moduleurl = 'settings';\n    this.editCRMAdmin = {\n      crm_admin_user_emails: ''\n    };\n    this.displayDialog = false;\n  }\n  ngOnInit() {\n    const routeData = this.route.snapshot.data;\n    this.settingType = routeData['type'];\n    this.settingTitle = routeData['title'];\n    this.crmservice.crm.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.getSettingData();\n    });\n  }\n  openDialog() {\n    this.displayDialog = true;\n  }\n  addEmail() {\n    if (this.newEmail.trim()) {\n      if (!Array.isArray(this.editCRMAdmin.crm_admin_user_emails)) {\n        this.editCRMAdmin.crm_admin_user_emails = [];\n      }\n      this.editCRMAdmin.crm_admin_user_emails = [...this.editCRMAdmin.crm_admin_user_emails, this.newEmail.trim()];\n      this.newEmail = '';\n      this.displayDialog = false;\n      this.messageservice.add({\n        severity: 'success',\n        summary: 'Added',\n        detail: 'Email added successfully'\n      });\n    }\n  }\n  removeEmail(email) {\n    this.editCRMAdmin.crm_admin_user_emails = this.editCRMAdmin.crm_admin_user_emails.filter(e => e !== email);\n    this.messageservice.add({\n      severity: 'warn',\n      summary: 'Removed',\n      detail: 'Email Removed successfully! '\n    });\n  }\n  getSettingData() {\n    this.loading = true;\n    this.crmservice.get(this.settingType, this.moduleurl).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: value => {\n        this.loading = false;\n        if (value.data?.length) {\n          for (let i = 0; i < value.data.length; i++) {\n            const element = value.data[i];\n            element.crm_admin_user_emails = Array.isArray(element.crm_admin_user_emails) ? element.crm_admin_user_emails : [];\n            this.editCRMAdmin.crm_admin_user_emails = element.crm_admin_user_emails;\n          }\n          this.Emails = value.data;\n        } else {\n          this.Emails = [];\n        }\n      },\n      error: () => {\n        this.loading = false;\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  updateSettings(item) {\n    const obj = {\n      ...this.editCRMAdmin\n    };\n    this.crmservice.update(obj, item.documentId, this.moduleurl).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: () => {\n        item.editing = false;\n        item.crm_admin_user_emails = this.editCRMAdmin.crm_admin_user_emails;\n        this.messageservice.add({\n          severity: 'success',\n          detail: 'Record Updated Successfully!'\n        });\n      },\n      error: () => {\n        this.messageservice.add({\n          severity: 'error',\n          detail: 'Error while processing your request.'\n        });\n      }\n    });\n  }\n  static {\n    this.ɵfac = function CrmComponent_Factory(t) {\n      return new (t || CrmComponent)(i0.ɵɵdirectiveInject(i1.CrmService), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CrmComponent,\n      selectors: [[\"app-crm\"]],\n      decls: 16,\n      vars: 8,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"card\"], [1, \"grid\", \"mx-0\"], [1, \"p-2\", \"fw-bold\"], [4, \"ngIf\"], [\"header\", \"Add New Email\", \"position\", \"center\", 1, \"add-email-dialog\", 3, \"visibleChange\", \"visible\", \"modal\", \"closable\"], [1, \"p-fluid\", \"mt-3\"], [\"for\", \"newItem\"], [1, \"p-inputgroup\", \"mt-2\"], [\"id\", \"newItem\", \"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Enter new email...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"mt-3\"], [\"pButton\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", 1, \"p-button-secondary\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"label\", \"Add\", \"icon\", \"pi pi-check\", 1, \"p-button-success\", 3, \"click\"], [1, \"table-responsive\"], [1, \"p-datatable\"], [1, \"p-datatable-thead\"], [1, \"d-flex\", \"align-items-center\"], [\"pButton\", \"\", \"icon\", \"pi pi-plus\", 1, \"p-button-primary\", \"add-button\", 3, \"click\"], [1, \"p-datatable-tbody\"], [\"colspan\", \"3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"p-datatable-row\"], [1, \"email-listbox\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"item\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"mt-3\", \"submit-button\", 3, \"click\"], [1, \"listbox-item\"], [\"pButton\", \"\", \"icon\", \"pi pi-trash\", 1, \"p-button-danger\", \"p-button-sm\", \"delete-button\", 3, \"click\"]],\n      template: function CrmComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, CrmComponent_ng_container_5_Template, 11, 2, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, CrmComponent_div_6_Template, 2, 0, \"div\", 4);\n          i0.ɵɵelementStart(7, \"p-dialog\", 5);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function CrmComponent_Template_p_dialog_visibleChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.displayDialog, $event) || (ctx.displayDialog = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"label\", 7);\n          i0.ɵɵtext(10, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CrmComponent_Template_input_ngModelChange_12_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.newEmail, $event) || (ctx.newEmail = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function CrmComponent_Template_button_click_14_listener() {\n            return ctx.displayDialog = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function CrmComponent_Template_button_click_15_listener() {\n            return ctx.addEmail();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.settingTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.displayDialog);\n          i0.ɵɵproperty(\"modal\", true)(\"closable\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newEmail);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.Toast, i2.PrimeTemplate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.ButtonDirective, i8.Listbox, i9.Dialog, i10.InputText],\n      styles: [\".p-datatable[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.p-datatable[_ngcontent-%COMP%]   .p-datatable-thead[_ngcontent-%COMP%]    > tr[_ngcontent-%COMP%]    > th[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n}\\n\\n.email-listbox[_ngcontent-%COMP%] {\\n  width: 600px;\\n}\\n\\n.submit-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: block;\\n}\\n\\n.add-email-dialog[_ngcontent-%COMP%] {\\n  width: 500px !important;\\n}\\n\\n.email-listbox[_ngcontent-%COMP%]   .p-listbox-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.listbox-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  padding: 8px;\\n}\\n\\n.add-button[_ngcontent-%COMP%] {\\n  margin-left: 83%;\\n}\\n\\n.delete-button[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmFja29mZmljZS9jcm0vY3JtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksV0FBQTtBQUNKOztBQUVBO0VBQ0ksY0FBQTtBQUNKOztBQUdBO0VBQ0ksWUFBQTtBQUFKOztBQUdBO0VBQ0ksaUJBQUE7RUFDQSxjQUFBO0FBQUo7O0FBR0E7RUFDSSx1QkFBQTtBQUFKOztBQUdBO0VBQ0ksYUFBQTtFQUNBLHNCQUFBO0FBQUo7O0FBR0E7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBQUo7O0FBR0E7RUFDSSxnQkFBQTtBQUFKOztBQUdBO0VBQ0ksaUJBQUE7QUFBSiIsInNvdXJjZXNDb250ZW50IjpbIi5wLWRhdGF0YWJsZSB7XHJcbiAgICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLnAtZGF0YXRhYmxlIC5wLWRhdGF0YWJsZS10aGVhZD50cj50aCB7XHJcbiAgICBjb2xvcjogI2ZmZmZmZjtcclxufVxyXG5cclxuXHJcbi5lbWFpbC1saXN0Ym94IHtcclxuICAgIHdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnN1Ym1pdC1idXR0b24ge1xyXG4gICAgbWFyZ2luLWxlZnQ6IGF1dG87XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxufVxyXG5cclxuLmFkZC1lbWFpbC1kaWFsb2cge1xyXG4gICAgd2lkdGg6IDUwMHB4ICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5lbWFpbC1saXN0Ym94IC5wLWxpc3Rib3gtbGlzdCB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG5cclxuLmxpc3Rib3gtaXRlbSB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgcGFkZGluZzogOHB4O1xyXG59XHJcblxyXG4uYWRkLWJ1dHRvbiB7XHJcbiAgICBtYXJnaW4tbGVmdDogODMlO1xyXG59XHJcblxyXG4uZGVsZXRlLWJ1dHRvbiB7XHJcbiAgICBtYXJnaW4tbGVmdDogYXV0bztcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "CrmComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template_button_click_3_listener", "email_r5", "ɵɵrestoreView", "_r4", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "removeEmail", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtwoWayListener", "CrmComponent_ng_container_5_ng_container_10_tr_1_Template_p_listbox_ngModelChange_2_listener", "$event", "_r3", "ɵɵtwoWayBindingSet", "selectedEmail", "ɵɵtemplate", "CrmComponent_ng_container_5_ng_container_10_tr_1_ng_template_3_Template", "CrmComponent_ng_container_5_ng_container_10_tr_1_Template_button_click_4_listener", "item_r6", "updateSettings", "ɵɵproperty", "editCRMAdmin", "crm_admin_user_emails", "ɵɵtwoWayProperty", "CrmComponent_ng_container_5_ng_container_10_tr_1_Template", "Emails", "CrmComponent_ng_container_5_Template_button_click_7_listener", "_r1", "openDialog", "CrmComponent_ng_container_5_ng_container_9_Template", "CrmComponent_ng_container_5_ng_container_10_Template", "length", "CrmComponent", "constructor", "crmservice", "messageservice", "route", "unsubscribe$", "settingType", "setting<PERSON>itle", "newEmail", "loading", "<PERSON><PERSON><PERSON>", "displayDialog", "ngOnInit", "routeData", "snapshot", "data", "crm", "pipe", "subscribe", "getSettingData", "addEmail", "trim", "Array", "isArray", "add", "severity", "summary", "detail", "email", "filter", "e", "get", "next", "value", "i", "element", "error", "item", "obj", "update", "documentId", "editing", "ɵɵdirectiveInject", "i1", "CrmService", "i2", "MessageService", "i3", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "CrmComponent_Template", "rf", "ctx", "ɵɵelement", "CrmComponent_ng_container_5_Template", "CrmComponent_div_6_Template", "CrmComponent_Template_p_dialog_visibleChange_7_listener", "CrmComponent_Template_input_ngModelChange_12_listener", "CrmComponent_Template_button_click_14_listener", "CrmComponent_Template_button_click_15_listener"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\crm\\crm.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\crm\\crm.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\nimport { CrmService } from './crm.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-crm',\r\n  templateUrl: './crm.component.html',\r\n  styleUrl: './crm.component.scss',\r\n})\r\nexport class CrmComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  settingType: string = '';\r\n  settingTitle: string = '';\r\n  selectedEmail: string = '';\r\n  newEmail: string = '';\r\n  loading = false;\r\n  Emails: any = [];\r\n  moduleurl = 'settings';\r\n  editCRMAdmin: any = {\r\n    crm_admin_user_emails: '',\r\n  };\r\n\r\n  displayDialog = false;\r\n\r\n  constructor(\r\n    private crmservice: CrmService,\r\n    private messageservice: MessageService,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const routeData = this.route.snapshot.data;\r\n    this.settingType = routeData['type'];\r\n    this.settingTitle = routeData['title'];\r\n    this.crmservice.crm.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\r\n      this.getSettingData();\r\n    });\r\n  }\r\n\r\n  openDialog() {\r\n    this.displayDialog = true;\r\n  }\r\n\r\n  addEmail() {\r\n    if (this.newEmail.trim()) {\r\n      if (!Array.isArray(this.editCRMAdmin.crm_admin_user_emails)) {\r\n        this.editCRMAdmin.crm_admin_user_emails = [];\r\n      }\r\n      this.editCRMAdmin.crm_admin_user_emails = [\r\n        ...this.editCRMAdmin.crm_admin_user_emails,\r\n        this.newEmail.trim(),\r\n      ];\r\n      this.newEmail = '';\r\n      this.displayDialog = false;\r\n      this.messageservice.add({\r\n        severity: 'success',\r\n        summary: 'Added',\r\n        detail: 'Email added successfully',\r\n      });\r\n    }\r\n  }\r\n\r\n  removeEmail(email: string) {\r\n    this.editCRMAdmin.crm_admin_user_emails =\r\n      this.editCRMAdmin.crm_admin_user_emails.filter((e: any) => e !== email);\r\n    this.messageservice.add({\r\n      severity: 'warn',\r\n      summary: 'Removed',\r\n      detail: 'Email Removed successfully! ',\r\n    });\r\n  }\r\n\r\n  getSettingData() {\r\n    this.loading = true;\r\n    this.crmservice\r\n      .get(this.settingType, this.moduleurl)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (value) => {\r\n          this.loading = false;\r\n          if (value.data?.length) {\r\n            for (let i = 0; i < value.data.length; i++) {\r\n              const element = value.data[i];\r\n              element.crm_admin_user_emails = Array.isArray(\r\n                element.crm_admin_user_emails\r\n              )\r\n                ? element.crm_admin_user_emails\r\n                : [];\r\n              this.editCRMAdmin.crm_admin_user_emails =\r\n                element.crm_admin_user_emails;\r\n            }\r\n            this.Emails = value.data;\r\n          } else {\r\n            this.Emails = [];\r\n          }\r\n        },\r\n        error: () => {\r\n          this.loading = false;\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  updateSettings(item: any) {\r\n    const obj: any = {\r\n      ...this.editCRMAdmin,\r\n    };\r\n    this.crmservice\r\n      .update(obj, item.documentId, this.moduleurl)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: () => {\r\n          item.editing = false;\r\n          item.crm_admin_user_emails = this.editCRMAdmin.crm_admin_user_emails;\r\n          this.messageservice.add({\r\n            severity: 'success',\r\n            detail: 'Record Updated Successfully!',\r\n          });\r\n        },\r\n        error: () => {\r\n          this.messageservice.add({\r\n            severity: 'error',\r\n            detail: 'Error while processing your request.',\r\n          });\r\n        },\r\n      });\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n\r\n<div class=\"card\">\r\n  <div class=\"grid mx-0\">\r\n    <h5 class=\"p-2 fw-bold\">{{ settingTitle }}</h5>\r\n  </div>\r\n\r\n  <ng-container *ngIf=\"!loading\">\r\n    <div class=\"table-responsive\">\r\n      <table class=\"p-datatable\">\r\n        <thead class=\"p-datatable-thead\">\r\n          <tr>\r\n            <th class=\"d-flex align-items-center\">\r\n              CRM Admin Email\r\n              <button pButton icon=\"pi pi-plus\" class=\"p-button-primary add-button\" (click)=\"openDialog()\"></button>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"p-datatable-tbody\">\r\n          <ng-container *ngIf=\"!Emails.length\">\r\n            <tr>\r\n              <td colspan=\"3\">No records found.</td>\r\n            </tr>\r\n          </ng-container>\r\n          <ng-container *ngIf=\"Emails.length\">\r\n            <tr *ngFor=\"let item of Emails; let i = index\">\r\n              <td class=\"p-datatable-row\">\r\n                <p-listbox [options]=\"editCRMAdmin.crm_admin_user_emails\" [(ngModel)]=\"selectedEmail\"\r\n                  class=\"email-listbox\">\r\n                  <ng-template let-email let-index=\"index\" pTemplate=\"item\">\r\n                    <div class=\"listbox-item\">\r\n                      <span>{{ email }}</span>\r\n                      <button pButton icon=\"pi pi-trash\" class=\"p-button-danger p-button-sm delete-button\"\r\n                        (click)=\"removeEmail(email)\"></button>\r\n                    </div>\r\n                  </ng-template>\r\n                </p-listbox>\r\n\r\n                <button pButton type=\"button\" class=\"mt-3 submit-button\" (click)=\"updateSettings(item)\">\r\n                  Submit\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </ng-container>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </ng-container>\r\n</div>\r\n\r\n<div *ngIf=\"loading\">Loading...</div>\r\n\r\n<p-dialog [(visible)]=\"displayDialog\" header=\"Add New Email\" position=\"center\" [modal]=\"true\" [closable]=\"false\"\r\n  class=\"add-email-dialog\">\r\n  <div class=\"p-fluid mt-3\">\r\n    <label for=\"newItem\">Email</label>\r\n    <div class=\"p-inputgroup mt-2\">\r\n      <input id=\"newItem\" type=\"text\" pInputText [(ngModel)]=\"newEmail\" placeholder=\"Enter new email...\" />\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"d-flex justify-content-end mt-3\">\r\n    <button pButton label=\"Cancel\" icon=\"pi pi-times\" class=\"p-button-secondary mr-2\"\r\n      (click)=\"displayDialog = false\"></button>\r\n    <button pButton label=\"Add\" icon=\"pi pi-check\" class=\"p-button-success\" (click)=\"addEmail()\"></button>\r\n  </div>\r\n</p-dialog>"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICe/BC,EAAA,CAAAC,uBAAA,GAAqC;IAEjCD,EADF,CAAAE,cAAA,SAAI,aACc;IAAAF,EAAA,CAAAG,MAAA,wBAAiB;IACnCH,EADmC,CAAAI,YAAA,EAAK,EACnC;;;;;;;IASKJ,EADF,CAAAE,cAAA,cAA0B,WAClB;IAAAF,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAE,cAAA,iBAC+B;IAA7BF,EAAA,CAAAK,UAAA,mBAAAC,gGAAA;MAAA,MAAAC,QAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,QAAA,CAAkB;IAAA,EAAC;IAChCP,EADiC,CAAAI,YAAA,EAAS,EACpC;;;;IAHEJ,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAT,QAAA,CAAW;;;;;;IAJvBP,EAFJ,CAAAE,cAAA,SAA+C,aACjB,oBAEF;IADkCF,EAAA,CAAAiB,gBAAA,2BAAAC,6FAAAC,MAAA;MAAAnB,EAAA,CAAAQ,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,EAAAH,MAAA,MAAAR,MAAA,CAAAW,aAAA,GAAAH,MAAA;MAAA,OAAAnB,EAAA,CAAAa,WAAA,CAAAM,MAAA;IAAA,EAA2B;IAEnFnB,EAAA,CAAAuB,UAAA,IAAAC,uEAAA,0BAA0D;IAO5DxB,EAAA,CAAAI,YAAA,EAAY;IAEZJ,EAAA,CAAAE,cAAA,iBAAwF;IAA/BF,EAAA,CAAAK,UAAA,mBAAAoB,kFAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAQ,aAAA,CAAAY,GAAA,EAAAV,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAgB,cAAA,CAAAD,OAAA,CAAoB;IAAA,EAAC;IACrF1B,EAAA,CAAAG,MAAA,eACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACN,EACF;;;;IAfUJ,EAAA,CAAAe,SAAA,GAA8C;IAA9Cf,EAAA,CAAA4B,UAAA,YAAAjB,MAAA,CAAAkB,YAAA,CAAAC,qBAAA,CAA8C;IAAC9B,EAAA,CAAA+B,gBAAA,YAAApB,MAAA,CAAAW,aAAA,CAA2B;;;;;IAH3FtB,EAAA,CAAAC,uBAAA,GAAoC;IAClCD,EAAA,CAAAuB,UAAA,IAAAS,yDAAA,iBAA+C;;;;;IAA1BhC,EAAA,CAAAe,SAAA,EAAW;IAAXf,EAAA,CAAA4B,UAAA,YAAAjB,MAAA,CAAAsB,MAAA,CAAW;;;;;;IAlB1CjC,EAAA,CAAAC,uBAAA,GAA+B;IAKrBD,EAJR,CAAAE,cAAA,cAA8B,gBACD,gBACQ,SAC3B,aACoC;IACpCF,EAAA,CAAAG,MAAA,wBACA;IAAAH,EAAA,CAAAE,cAAA,iBAA6F;IAAvBF,EAAA,CAAAK,UAAA,mBAAA6B,6DAAA;MAAAlC,EAAA,CAAAQ,aAAA,CAAA2B,GAAA;MAAA,MAAAxB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAyB,UAAA,EAAY;IAAA,EAAC;IAGlGpC,EAHmG,CAAAI,YAAA,EAAS,EACnG,EACF,EACC;IACRJ,EAAA,CAAAE,cAAA,gBAAiC;IAM/BF,EALA,CAAAuB,UAAA,IAAAc,mDAAA,0BAAqC,KAAAC,oDAAA,0BAKD;IAsB1CtC,EAFI,CAAAI,YAAA,EAAQ,EACF,EACJ;;;;;IA3BeJ,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAA4B,UAAA,UAAAjB,MAAA,CAAAsB,MAAA,CAAAM,MAAA,CAAoB;IAKpBvC,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAA4B,UAAA,SAAAjB,MAAA,CAAAsB,MAAA,CAAAM,MAAA,CAAmB;;;;;IA0B5CvC,EAAA,CAAAE,cAAA,UAAqB;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;ADvCrC,OAAM,MAAOoC,YAAY;EAevBC,YACUC,UAAsB,EACtBC,cAA8B,EAC9BC,KAAqB;IAFrB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IAjBP,KAAAC,YAAY,GAAG,IAAI/C,OAAO,EAAQ;IAC1C,KAAAgD,WAAW,GAAW,EAAE;IACxB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAzB,aAAa,GAAW,EAAE;IAC1B,KAAA0B,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAhB,MAAM,GAAQ,EAAE;IAChB,KAAAiB,SAAS,GAAG,UAAU;IACtB,KAAArB,YAAY,GAAQ;MAClBC,qBAAqB,EAAE;KACxB;IAED,KAAAqB,aAAa,GAAG,KAAK;EAMlB;EAEHC,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,IAAI;IAC1C,IAAI,CAACT,WAAW,GAAGO,SAAS,CAAC,MAAM,CAAC;IACpC,IAAI,CAACN,YAAY,GAAGM,SAAS,CAAC,OAAO,CAAC;IACtC,IAAI,CAACX,UAAU,CAACc,GAAG,CAACC,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAACa,SAAS,CAAC,MAAK;MACpE,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,CAAC;EACJ;EAEAvB,UAAUA,CAAA;IACR,IAAI,CAACe,aAAa,GAAG,IAAI;EAC3B;EAEAS,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,QAAQ,CAACa,IAAI,EAAE,EAAE;MACxB,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAClC,YAAY,CAACC,qBAAqB,CAAC,EAAE;QAC3D,IAAI,CAACD,YAAY,CAACC,qBAAqB,GAAG,EAAE;MAC9C;MACA,IAAI,CAACD,YAAY,CAACC,qBAAqB,GAAG,CACxC,GAAG,IAAI,CAACD,YAAY,CAACC,qBAAqB,EAC1C,IAAI,CAACkB,QAAQ,CAACa,IAAI,EAAE,CACrB;MACD,IAAI,CAACb,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACG,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACR,cAAc,CAACqB,GAAG,CAAC;QACtBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,OAAO;QAChBC,MAAM,EAAE;OACT,CAAC;IACJ;EACF;EAEArD,WAAWA,CAACsD,KAAa;IACvB,IAAI,CAACvC,YAAY,CAACC,qBAAqB,GACrC,IAAI,CAACD,YAAY,CAACC,qBAAqB,CAACuC,MAAM,CAAEC,CAAM,IAAKA,CAAC,KAAKF,KAAK,CAAC;IACzE,IAAI,CAACzB,cAAc,CAACqB,GAAG,CAAC;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAR,cAAcA,CAAA;IACZ,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,UAAU,CACZ6B,GAAG,CAAC,IAAI,CAACzB,WAAW,EAAE,IAAI,CAACI,SAAS,CAAC,CACrCO,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTc,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACxB,OAAO,GAAG,KAAK;QACpB,IAAIwB,KAAK,CAAClB,IAAI,EAAEhB,MAAM,EAAE;UACtB,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAClB,IAAI,CAAChB,MAAM,EAAEmC,CAAC,EAAE,EAAE;YAC1C,MAAMC,OAAO,GAAGF,KAAK,CAAClB,IAAI,CAACmB,CAAC,CAAC;YAC7BC,OAAO,CAAC7C,qBAAqB,GAAGgC,KAAK,CAACC,OAAO,CAC3CY,OAAO,CAAC7C,qBAAqB,CAC9B,GACG6C,OAAO,CAAC7C,qBAAqB,GAC7B,EAAE;YACN,IAAI,CAACD,YAAY,CAACC,qBAAqB,GACrC6C,OAAO,CAAC7C,qBAAqB;UACjC;UACA,IAAI,CAACG,MAAM,GAAGwC,KAAK,CAAClB,IAAI;QAC1B,CAAC,MAAM;UACL,IAAI,CAACtB,MAAM,GAAG,EAAE;QAClB;MACF,CAAC;MACD2C,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC3B,OAAO,GAAG,KAAK;QACpB,IAAI,CAACN,cAAc,CAACqB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBE,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;EAEAxC,cAAcA,CAACkD,IAAS;IACtB,MAAMC,GAAG,GAAQ;MACf,GAAG,IAAI,CAACjD;KACT;IACD,IAAI,CAACa,UAAU,CACZqC,MAAM,CAACD,GAAG,EAAED,IAAI,CAACG,UAAU,EAAE,IAAI,CAAC9B,SAAS,CAAC,CAC5CO,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTc,IAAI,EAAEA,CAAA,KAAK;QACTK,IAAI,CAACI,OAAO,GAAG,KAAK;QACpBJ,IAAI,CAAC/C,qBAAqB,GAAG,IAAI,CAACD,YAAY,CAACC,qBAAqB;QACpE,IAAI,CAACa,cAAc,CAACqB,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBE,MAAM,EAAE;SACT,CAAC;MACJ,CAAC;MACDS,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACjC,cAAc,CAACqB,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBE,MAAM,EAAE;SACT,CAAC;MACJ;KACD,CAAC;EACN;;;uBAxHW3B,YAAY,EAAAxC,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtF,EAAA,CAAAkF,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAZhD,YAAY;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXzB/F,EAAA,CAAAiG,SAAA,iBAAsD;UAIlDjG,EAFJ,CAAAE,cAAA,aAAkB,aACO,YACG;UAAAF,EAAA,CAAAG,MAAA,GAAkB;UAC5CH,EAD4C,CAAAI,YAAA,EAAK,EAC3C;UAENJ,EAAA,CAAAuB,UAAA,IAAA2E,oCAAA,2BAA+B;UAyCjClG,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAuB,UAAA,IAAA4E,2BAAA,iBAAqB;UAErBnG,EAAA,CAAAE,cAAA,kBAC2B;UADjBF,EAAA,CAAAiB,gBAAA,2BAAAmF,wDAAAjF,MAAA;YAAAnB,EAAA,CAAAqB,kBAAA,CAAA2E,GAAA,CAAA7C,aAAA,EAAAhC,MAAA,MAAA6E,GAAA,CAAA7C,aAAA,GAAAhC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAGjCnB,EADF,CAAAE,cAAA,aAA0B,eACH;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAEhCJ,EADF,CAAAE,cAAA,cAA+B,gBACwE;UAA1DF,EAAA,CAAAiB,gBAAA,2BAAAoF,sDAAAlF,MAAA;YAAAnB,EAAA,CAAAqB,kBAAA,CAAA2E,GAAA,CAAAhD,QAAA,EAAA7B,MAAA,MAAA6E,GAAA,CAAAhD,QAAA,GAAA7B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsB;UAErEnB,EAFI,CAAAI,YAAA,EAAqG,EACjG,EACF;UAGJJ,EADF,CAAAE,cAAA,eAA6C,kBAET;UAAhCF,EAAA,CAAAK,UAAA,mBAAAiG,+CAAA;YAAA,OAAAN,GAAA,CAAA7C,aAAA,GAAyB,KAAK;UAAA,EAAC;UAACnD,EAAA,CAAAI,YAAA,EAAS;UAC3CJ,EAAA,CAAAE,cAAA,kBAA6F;UAArBF,EAAA,CAAAK,UAAA,mBAAAkG,+CAAA;YAAA,OAASP,GAAA,CAAApC,QAAA,EAAU;UAAA,EAAC;UAEhG5D,EAFiG,CAAAI,YAAA,EAAS,EAClG,EACG;;;UAlEmBJ,EAAA,CAAA4B,UAAA,cAAa;UAIf5B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAAgB,iBAAA,CAAAgF,GAAA,CAAAjD,YAAA,CAAkB;UAG7B/C,EAAA,CAAAe,SAAA,EAAc;UAAdf,EAAA,CAAA4B,UAAA,UAAAoE,GAAA,CAAA/C,OAAA,CAAc;UA2CzBjD,EAAA,CAAAe,SAAA,EAAa;UAAbf,EAAA,CAAA4B,UAAA,SAAAoE,GAAA,CAAA/C,OAAA,CAAa;UAETjD,EAAA,CAAAe,SAAA,EAA2B;UAA3Bf,EAAA,CAAA+B,gBAAA,YAAAiE,GAAA,CAAA7C,aAAA,CAA2B;UAAyDnD,EAAf,CAAA4B,UAAA,eAAc,mBAAmB;UAK/D5B,EAAA,CAAAe,SAAA,GAAsB;UAAtBf,EAAA,CAAA+B,gBAAA,YAAAiE,GAAA,CAAAhD,QAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}