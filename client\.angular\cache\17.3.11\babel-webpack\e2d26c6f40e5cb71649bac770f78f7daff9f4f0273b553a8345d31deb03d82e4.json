{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { CustomerComponent } from './customer.component';\nimport { CustomerRoutingModule } from './customer-routing.module';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { RippleModule } from 'primeng/ripple';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { SliderModule } from 'primeng/slider';\nimport { RatingModule } from 'primeng/rating';\nimport { HttpClientModule } from '@angular/common/http';\nimport { CustomerDetailsComponent } from './customer-details/customer-details.component';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { TabViewModule } from 'primeng/tabview';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { CustomerInfoComponent } from './customer-details/customer-info/customer-info.component';\nimport { CustomerCompaniesComponent } from './customer-details/customer-companies/customer-companies.component';\nimport { CustomerPartnerComponent } from './customer-details/customer-partner/customer-partner.component';\nimport { CustomerSalesAreaComponent } from './customer-details/customer-sales-area/customer-sales-area.component';\nimport { CustomerSalesTextsComponent } from './customer-details/customer-sales-texts/customer-sales-texts.component';\nimport * as i0 from \"@angular/core\";\nexport class CustomerModule {\n  static {\n    this.ɵfac = function CustomerModule_Factory(t) {\n      return new (t || CustomerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, CustomerRoutingModule, FormsModule, TableModule, RatingModule, ButtonModule, SliderModule, InputTextModule, ToggleButtonModule, RippleModule, MultiSelectModule, DropdownModule, ProgressBarModule, ToastModule, HttpClientModule, InputNumberModule, TabViewModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerModule, {\n    declarations: [CustomerComponent, CustomerDetailsComponent, CustomerInfoComponent, CustomerCompaniesComponent, CustomerPartnerComponent, CustomerSalesAreaComponent, CustomerSalesTextsComponent],\n    imports: [CommonModule, CustomerRoutingModule, FormsModule, TableModule, RatingModule, ButtonModule, SliderModule, InputTextModule, ToggleButtonModule, RippleModule, MultiSelectModule, DropdownModule, ProgressBarModule, ToastModule, HttpClientModule, InputNumberModule, TabViewModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "CustomerComponent", "CustomerRoutingModule", "TableModule", "ButtonModule", "InputTextModule", "ToggleButtonModule", "RippleModule", "MultiSelectModule", "DropdownModule", "ProgressBarModule", "ToastModule", "SliderModule", "RatingModule", "HttpClientModule", "CustomerDetailsComponent", "InputNumberModule", "TabViewModule", "MessageService", "ConfirmationService", "CustomerInfoComponent", "CustomerCompaniesComponent", "CustomerPartnerComponent", "CustomerSalesAreaComponent", "CustomerSalesTextsComponent", "CustomerModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CustomerComponent } from './customer.component';\r\nimport { CustomerRoutingModule } from './customer-routing.module';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { ToggleButtonModule } from 'primeng/togglebutton';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { ProgressBarModule } from 'primeng/progressbar';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { SliderModule } from 'primeng/slider';\r\nimport { RatingModule } from 'primeng/rating';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { CustomerDetailsComponent } from './customer-details/customer-details.component';\r\nimport { InputNumberModule } from 'primeng/inputnumber';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { CustomerInfoComponent } from './customer-details/customer-info/customer-info.component';\r\nimport { CustomerCompaniesComponent } from './customer-details/customer-companies/customer-companies.component';\r\nimport { CustomerPartnerComponent } from './customer-details/customer-partner/customer-partner.component';\r\nimport { CustomerSalesAreaComponent } from './customer-details/customer-sales-area/customer-sales-area.component';\r\nimport { CustomerSalesTextsComponent } from './customer-details/customer-sales-texts/customer-sales-texts.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    CustomerComponent,\r\n    CustomerDetailsComponent,\r\n    CustomerInfoComponent,\r\n    CustomerCompaniesComponent,\r\n    CustomerPartnerComponent,\r\n    CustomerSalesAreaComponent,\r\n    CustomerSalesTextsComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    CustomerRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    RatingModule,\r\n    ButtonModule,\r\n    SliderModule,\r\n    InputTextModule,\r\n    ToggleButtonModule,\r\n    RippleModule,\r\n    MultiSelectModule,\r\n    DropdownModule,\r\n    ProgressBarModule,\r\n    ToastModule,\r\n    HttpClientModule,\r\n    InputNumberModule,\r\n    TabViewModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class CustomerModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,0BAA0B,QAAQ,oEAAoE;AAC/G,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,0BAA0B,QAAQ,sEAAsE;AACjH,SAASC,2BAA2B,QAAQ,wEAAwE;;AAiCpH,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACP,cAAc,EAAEC,mBAAmB,CAAC;MAAAO,OAAA,GAlB9C3B,YAAY,EACZG,qBAAqB,EACrBF,WAAW,EACXG,WAAW,EACXU,YAAY,EACZT,YAAY,EACZQ,YAAY,EACZP,eAAe,EACfC,kBAAkB,EAClBC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,iBAAiB,EACjBC,WAAW,EACXG,gBAAgB,EAChBE,iBAAiB,EACjBC,aAAa;IAAA;EAAA;;;2EAIJQ,cAAc;IAAAE,YAAA,GA7BvB1B,iBAAiB,EACjBc,wBAAwB,EACxBK,qBAAqB,EACrBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,0BAA0B,EAC1BC,2BAA2B;IAAAE,OAAA,GAG3B3B,YAAY,EACZG,qBAAqB,EACrBF,WAAW,EACXG,WAAW,EACXU,YAAY,EACZT,YAAY,EACZQ,YAAY,EACZP,eAAe,EACfC,kBAAkB,EAClBC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,iBAAiB,EACjBC,WAAW,EACXG,gBAAgB,EAChBE,iBAAiB,EACjBC,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}