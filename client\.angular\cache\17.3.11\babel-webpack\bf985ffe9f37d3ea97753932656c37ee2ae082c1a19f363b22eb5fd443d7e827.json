{"ast": null, "code": "import { environment } from '../../environments/environment';\nexport const ApiConstant = {\n  ORDER_HISTORY: `${environment.apiEndpoint}/orders`,\n  GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,\n  GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,\n  PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,\n  PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,\n  PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,\n  USERS: `${environment.apiEndpoint}/users`,\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\n  PARTNERS: `${environment.apiEndpoint}/business-partners`,\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\n  CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\n  GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,\n  PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,\n  PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,\n  GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,\n  GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,\n  GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,\n  ORDER_DETAILS: `${environment.apiEndpoint}/orders`,\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  INVOICE: `${environment.apiEndpoint}/invoices`,\n  IMAGES: `${environment.apiEndpoint}/media`,\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\n  CARTS: `${environment.apiEndpoint}/carts`,\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\n  GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\n  SETTINGS: `${environment.apiEndpoint}/settings`,\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\n  SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\n  TICKETS: `${environment.apiEndpoint}/tickets`,\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\n  SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,\n  SALES_QUOTE: `${environment.apiEndpoint}/sales-quote`,\n  QUOTE: `${environment.apiEndpoint}/quote-statuses`,\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\n  RETURN_ORDER: `${environment.apiEndpoint}/return-order`,\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\n  PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  BANNER: `${environment.apiEndpoint}/banner`\n};\nexport const CMS_APIContstant = {\n  STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\n  USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`\n};\nexport const AppConstant = {\n  SESSION_TIMEOUT: 3600 * 1000,\n  // in MS\n  PRODUCT_IMAGE_FALLBACK: '/assets/images/demo-product.png'\n};\nexport const Permission = {\n  VIEW_INVOICE: 'P0003'\n};", "map": {"version": 3, "names": ["environment", "ApiConstant", "ORDER_HISTORY", "apiEndpoint", "GET_ALL_PRODUCTS", "GET_PRODUCT_IMAGES", "PRODUCT_IMAGE", "PRODUCT_PLANT", "PRODUCT_DESCRIPTION", "USERS", "SINGOUT", "PARTNERS", "CUSTOMER_COMPANIES", "CUSTOMER_TEXT", "CUSTOMER_PARTNER_FUNCTION", "CUSTOMER_SALES_AREA", "GET_ORDER_STATUSES", "GET_INVOICE_FORM_TYPES", "RELATIONSHIP_TYPES", "CONDITIONS", "GET_CATALOGS", "PRODUCT_CATEGORIES", "PRODUCT_CATALOGS", "GET_INVOICE_STATUSES", "GET_SALE_ORDER_STATUSES", "GET_INVOICE_TYPES", "GET_ORDER_TYPES", "ORDER_DETAILS", "SCHEDULED_ORDER_DETAILS", "INVOICE", "IMAGES", "SALES_ORDER_SIMULATION", "SALES_ORDER_CREATION", "ADD_SHIPPING_ADDRESS", "CARTS", "GET_MATERIAL_STOCK", "GET_SALES_PRICE", "CUSTOMERS", "SETTINGS", "COMPANY_LOGO", "SIMILAR_PRODUCTS", "TICKET_LIST", "TICKETS", "TICKET_STATUSES", "SALES_QUOTE_CREATION", "SALES_QUOTE", "QUOTE", "RETURN_STATUS", "RETURN_REASON", "REFUND_PROGRESS", "RETURN_ORDER", "NOTIFICATION", "PRODUCT_REGISTER", "BULK_UPLOAD_STATUS", "CUSTOMER_TEXT_DESCR", "SALES_ORDER_SCHEDULER_CREATION", "SALES_ORDER_SCHEDULER", "BANNER", "CMS_APIContstant", "STORE_DESIGN", "cmsApiEndpoint", "MAIN_MENU_API_DETAILS", "SINGIN", "USER_DETAILS", "USER_CART", "RESET_PASSWORD_REQUEST", "RESET_PASSWORD", "USER_PERMISSIONS", "GET_CATEGORIES", "AppConstant", "SESSION_TIMEOUT", "PRODUCT_IMAGE_FALLBACK", "Permission", "VIEW_INVOICE"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\constants\\api.constants.ts"], "sourcesContent": ["import { environment } from '../../environments/environment';\r\n\r\nexport const ApiConstant = {\r\n    ORDER_HISTORY: `${environment.apiEndpoint}/orders`,\r\n    GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,\r\n    GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,\r\n    PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,\r\n    PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,\r\n    PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,\r\n    USERS: `${environment.apiEndpoint}/users`,\r\n    SINGOUT: `${environment.apiEndpoint}/auth/logout`,\r\n    PARTNERS: `${environment.apiEndpoint}/business-partners`,\r\n    CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\r\n    CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\r\n    CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,\r\n    CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\r\n    GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n    GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\r\n    RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\r\n    CONDITIONS: `${environment.apiEndpoint}/conditions`,\r\n    GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,\r\n\r\n    PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,\r\n    PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,\r\n    GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,\r\n    GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n    GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,\r\n    GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,\r\n    ORDER_DETAILS: `${environment.apiEndpoint}/orders`,\r\n    SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n    INVOICE: `${environment.apiEndpoint}/invoices`,\r\n    IMAGES: `${environment.apiEndpoint}/media`,\r\n    SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,\r\n    SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\r\n    ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\r\n    CARTS: `${environment.apiEndpoint}/carts`,\r\n    GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\r\n    GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,\r\n    CUSTOMERS: `${environment.apiEndpoint}/customers`,\r\n    SETTINGS: `${environment.apiEndpoint}/settings`,\r\n    COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\r\n    SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,\r\n    TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\r\n    TICKETS: `${environment.apiEndpoint}/tickets`,\r\n    TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\r\n    SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,\r\n    SALES_QUOTE: `${environment.apiEndpoint}/sales-quote`,\r\n    QUOTE: `${environment.apiEndpoint}/quote-statuses`,\r\n    RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\r\n    RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\r\n    REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\r\n    RETURN_ORDER: `${environment.apiEndpoint}/return-order`,\r\n    NOTIFICATION: `${environment.apiEndpoint}/notification`,\r\n    PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,\r\n    BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\r\n    CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\r\n    SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n    SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n    BANNER: `${environment.apiEndpoint}/banner`,\r\n};\r\n\r\nexport const CMS_APIContstant = {\r\n    STORE_DESIGN: `${environment.cmsApiEndpoint}/api/store-design?pLevel=6`,\r\n    MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\r\n    SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\r\n    USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\r\n    USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\r\n    RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/auth/forgot-password`,\r\n    RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\r\n    CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\r\n    USER_PERMISSIONS: `${environment.cmsApiEndpoint}/api/storefront-permissions`,\r\n    GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\r\n}\r\n\r\nexport const AppConstant = {\r\n    SESSION_TIMEOUT: 3600 * 1000, // in MS\r\n    PRODUCT_IMAGE_FALLBACK: '/assets/images/demo-product.png'\r\n}\r\n\r\nexport const Permission = {\r\n    VIEW_INVOICE: 'P0003',\r\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gCAAgC;AAE5D,OAAO,MAAMC,WAAW,GAAG;EACvBC,aAAa,EAAE,GAAGF,WAAW,CAACG,WAAW,SAAS;EAClDC,gBAAgB,EAAE,GAAGJ,WAAW,CAACG,WAAW,WAAW;EACvDE,kBAAkB,EAAE,GAAGL,WAAW,CAACG,WAAW,sBAAsB;EACpEG,aAAa,EAAE,GAAGN,WAAW,CAACG,WAAW,QAAQ;EACjDI,aAAa,EAAE,GAAGP,WAAW,CAACG,WAAW,iBAAiB;EAC1DK,mBAAmB,EAAE,GAAGR,WAAW,CAACG,WAAW,uBAAuB;EACtEM,KAAK,EAAE,GAAGT,WAAW,CAACG,WAAW,QAAQ;EACzCO,OAAO,EAAE,GAAGV,WAAW,CAACG,WAAW,cAAc;EACjDQ,QAAQ,EAAE,GAAGX,WAAW,CAACG,WAAW,oBAAoB;EACxDS,kBAAkB,EAAE,GAAGZ,WAAW,CAACG,WAAW,qBAAqB;EACnEU,aAAa,EAAE,GAAGb,WAAW,CAACG,WAAW,gBAAgB;EACzDW,yBAAyB,EAAE,GAAGd,WAAW,CAACG,WAAW,6BAA6B;EAClFY,mBAAmB,EAAE,GAAGf,WAAW,CAACG,WAAW,uBAAuB;EACtEa,kBAAkB,EAAE,GAAGhB,WAAW,CAACG,WAAW,sBAAsB;EACpEc,sBAAsB,EAAE,GAAGjB,WAAW,CAACG,WAAW,qBAAqB;EACvEe,kBAAkB,EAAE,GAAGlB,WAAW,CAACG,WAAW,qBAAqB;EACnEgB,UAAU,EAAE,GAAGnB,WAAW,CAACG,WAAW,aAAa;EACnDiB,YAAY,EAAE,GAAGpB,WAAW,CAACG,WAAW,mBAAmB;EAE3DkB,kBAAkB,EAAE,GAAGrB,WAAW,CAACG,WAAW,mCAAmC;EACjFmB,gBAAgB,EAAE,GAAGtB,WAAW,CAACG,WAAW,iCAAiC;EAC7EoB,oBAAoB,EAAE,GAAGvB,WAAW,CAACG,WAAW,mBAAmB;EACnEqB,uBAAuB,EAAE,GAAGxB,WAAW,CAACG,WAAW,sBAAsB;EACzEsB,iBAAiB,EAAE,GAAGzB,WAAW,CAACG,WAAW,gBAAgB;EAC7DuB,eAAe,EAAE,GAAG1B,WAAW,CAACG,WAAW,cAAc;EACzDwB,aAAa,EAAE,GAAG3B,WAAW,CAACG,WAAW,SAAS;EAClDyB,uBAAuB,EAAE,GAAG5B,WAAW,CAACG,WAAW,yBAAyB;EAC5E0B,OAAO,EAAE,GAAG7B,WAAW,CAACG,WAAW,WAAW;EAC9C2B,MAAM,EAAE,GAAG9B,WAAW,CAACG,WAAW,QAAQ;EAC1C4B,sBAAsB,EAAE,GAAG/B,WAAW,CAACG,WAAW,yBAAyB;EAC3E6B,oBAAoB,EAAE,GAAGhC,WAAW,CAACG,WAAW,uBAAuB;EACvE8B,oBAAoB,EAAE,GAAGjC,WAAW,CAACG,WAAW,mCAAmC;EACnF+B,KAAK,EAAE,GAAGlC,WAAW,CAACG,WAAW,QAAQ;EACzCgC,kBAAkB,EAAE,GAAGnC,WAAW,CAACG,WAAW,+BAA+B;EAC7EiC,eAAe,EAAE,GAAGpC,WAAW,CAACG,WAAW,cAAc;EACzDkC,SAAS,EAAE,GAAGrC,WAAW,CAACG,WAAW,YAAY;EACjDmC,QAAQ,EAAE,GAAGtC,WAAW,CAACG,WAAW,WAAW;EAC/CoC,YAAY,EAAE,GAAGvC,WAAW,CAACG,WAAW,eAAe;EACvDqC,gBAAgB,EAAE,GAAGxC,WAAW,CAACG,WAAW,mBAAmB;EAC/DsC,WAAW,EAAE,GAAGzC,WAAW,CAACG,WAAW,eAAe;EACtDuC,OAAO,EAAE,GAAG1C,WAAW,CAACG,WAAW,UAAU;EAC7CwC,eAAe,EAAE,GAAG3C,WAAW,CAACG,WAAW,kBAAkB;EAC7DyC,oBAAoB,EAAE,GAAG5C,WAAW,CAACG,WAAW,qBAAqB;EACrE0C,WAAW,EAAE,GAAG7C,WAAW,CAACG,WAAW,cAAc;EACrD2C,KAAK,EAAE,GAAG9C,WAAW,CAACG,WAAW,iBAAiB;EAClD4C,aAAa,EAAE,GAAG/C,WAAW,CAACG,WAAW,kBAAkB;EAC3D6C,aAAa,EAAE,GAAGhD,WAAW,CAACG,WAAW,gBAAgB;EACzD8C,eAAe,EAAE,GAAGjD,WAAW,CAACG,WAAW,kBAAkB;EAC7D+C,YAAY,EAAE,GAAGlD,WAAW,CAACG,WAAW,eAAe;EACvDgD,YAAY,EAAE,GAAGnD,WAAW,CAACG,WAAW,eAAe;EACvDiD,gBAAgB,EAAE,GAAGpD,WAAW,CAACG,WAAW,mBAAmB;EAC/DkD,kBAAkB,EAAE,GAAGrD,WAAW,CAACG,WAAW,sBAAsB;EACpEmD,mBAAmB,EAAE,GAAGtD,WAAW,CAACG,WAAW,4BAA4B;EAC3EoD,8BAA8B,EAAE,GAAGvD,WAAW,CAACG,WAAW,yBAAyB;EACnFqD,qBAAqB,EAAE,GAAGxD,WAAW,CAACG,WAAW,yBAAyB;EAC1EsD,MAAM,EAAE,GAAGzD,WAAW,CAACG,WAAW;CACrC;AAED,OAAO,MAAMuD,gBAAgB,GAAG;EAC5BC,YAAY,EAAE,GAAG3D,WAAW,CAAC4D,cAAc,4BAA4B;EACvEC,qBAAqB,EAAE,GAAG7D,WAAW,CAAC4D,cAAc,oBAAoB;EACxEE,MAAM,EAAE,GAAG9D,WAAW,CAAC4D,cAAc,iBAAiB;EACtDG,YAAY,EAAE,GAAG/D,WAAW,CAAC4D,cAAc,YAAY;EACvDI,SAAS,EAAE,GAAGhE,WAAW,CAAC4D,cAAc,YAAY;EACpDK,sBAAsB,EAAE,GAAGjE,WAAW,CAAC4D,cAAc,2BAA2B;EAChFM,cAAc,EAAE,GAAGlE,WAAW,CAAC4D,cAAc,0BAA0B;EACvEvB,SAAS,EAAE,GAAGrC,WAAW,CAAC4D,cAAc,gBAAgB;EACxDO,gBAAgB,EAAE,GAAGnE,WAAW,CAAC4D,cAAc,6BAA6B;EAC5EQ,cAAc,EAAE,GAAGpE,WAAW,CAAC4D,cAAc;CAChD;AAED,OAAO,MAAMS,WAAW,GAAG;EACvBC,eAAe,EAAE,IAAI,GAAG,IAAI;EAAE;EAC9BC,sBAAsB,EAAE;CAC3B;AAED,OAAO,MAAMC,UAAU,GAAG;EACtBC,YAAY,EAAE;CACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}