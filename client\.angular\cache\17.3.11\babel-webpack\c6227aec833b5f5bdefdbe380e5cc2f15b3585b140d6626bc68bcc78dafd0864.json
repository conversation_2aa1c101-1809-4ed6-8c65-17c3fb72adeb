{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../partner.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/ripple\";\nfunction PartnerAddressUsageComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function PartnerAddressUsageComponent_ng_template_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 15);\n    i0.ɵɵelement(5, \"i\", 16);\n    i0.ɵɵelementStart(6, \"input\", 17, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PartnerAddressUsageComponent_ng_template_4_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.globalSearchTerm, $event) || (ctx_r1.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function PartnerAddressUsageComponent_ng_template_4_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGlobalFilter($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi pi-fw \", ctx_r1.isExpanded ? \"pi-minus\" : \"pi-plus\", \"\");\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.isExpanded ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.globalSearchTerm);\n  }\n}\nfunction PartnerAddressUsageComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 18);\n    i0.ɵɵelementStart(2, \"th\", 19);\n    i0.ɵɵtext(3, \" Address ID \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 21);\n    i0.ɵɵtext(6, \" Address Usage \");\n    i0.ɵɵelement(7, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 23);\n    i0.ɵɵtext(9, \" Authorization Group \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 24);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PartnerAddressUsageComponent_ng_template_6_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const address_r4 = ctx_r2.$implicit;\n    const expanded_r5 = ctx_r2.expanded;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", address_r4)(\"icon\", expanded_r5 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.bp_address_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.address_usage) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.authorization_group) || \"-\", \" \");\n  }\n}\nfunction PartnerAddressUsageComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PartnerAddressUsageComponent_ng_template_6_tr_0_Template, 9, 5, \"tr\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.addressdetails == null ? null : ctx_r1.addressdetails.length) > 0);\n  }\n}\nfunction PartnerAddressUsageComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 18);\n    i0.ɵɵelementStart(2, \"td\", 27)(3, \"div\", 28)(4, \"div\", 29)(5, \"span\", 30);\n    i0.ɵɵtext(6, \"Address ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 29)(10, \"span\", 30);\n    i0.ɵɵtext(11, \"Address Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"span\", 30);\n    i0.ɵɵtext(16, \"Authorization Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 31);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"Standard Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 31);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 29)(25, \"span\", 30);\n    i0.ɵɵtext(26, \"Business Partner ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 31);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const address_r6 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.bp_address_id) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.address_usage) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.authorization_group) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.standard_usage) ? \"Yes\" : \"No\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.bp_id) || \"-\", \" \");\n  }\n}\nfunction PartnerAddressUsageComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32);\n    i0.ɵɵtext(2, \"Address Usage are not available for this record.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PartnerAddressUsageComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \"Loading address data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class PartnerAddressUsageComponent {\n  constructor(route, partnerservice) {\n    this.route = route;\n    this.partnerservice = partnerservice;\n    this.unsubscribe$ = new Subject();\n    this.addressdetails = null;\n    this.filteredaddress = [];\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.globalSearchTerm = '';\n    this.loading = true;\n    this.id = '';\n  }\n  ngOnInit() {\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.partnerservice.partner.pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.addressdetails = response?.address_usages || [];\n        this.filteredaddress = [...this.addressdetails];\n      },\n      error: err => {\n        console.error('Error fetching data:', err);\n        this.addressdetails = [];\n        this.filteredaddress = [];\n      }\n    });\n  }\n  expandAll() {\n    if (!this.isExpanded) {\n      this.addressdetails.forEach(address => address?.id ? this.expandedRows[address.id] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n    this.isExpanded = !this.isExpanded;\n  }\n  onGlobalFilter(event) {\n    const filterValue = event.target.value.toLowerCase();\n    if (filterValue) {\n      this.filteredaddress = this.addressdetails.filter(address => Object.values(address).some(value => value?.toString().toLowerCase().includes(filterValue)));\n    } else {\n      this.filteredaddress = [...this.addressdetails]; // Reset filter when input is empty\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function PartnerAddressUsageComponent_Factory(t) {\n      return new (t || PartnerAddressUsageComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.PartnerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PartnerAddressUsageComponent,\n      selectors: [[\"app-partner-address-usage\"]],\n      decls: 10,\n      vars: 3,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"expandedRowKeys\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"gap-2\", \"justify-content-between\"], [\"pButton\", \"\", 3, \"click\", \"icon\", \"label\"], [1, \"flex\", \"table-header\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Address\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"bp_address_id\"], [\"field\", \"bp_address_id\"], [\"pSortableColumn\", \"address_usage\"], [\"field\", \"address_usage\"], [\"pSortableColumn\", \"authorization_group\"], [\"field\", \"authorization_group\"], [4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pRowToggler\", \"icon\"], [\"colspan\", \"4\"], [1, \"grid\", \"mx-0\", \"border-1\"], [1, \"col-12\", \"lg:col-3\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"colspan\", \"6\"], [\"colspan\", \"8\"]],\n      template: function PartnerAddressUsageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"p-table\", 4, 0);\n          i0.ɵɵtemplate(4, PartnerAddressUsageComponent_ng_template_4_Template, 8, 4, \"ng-template\", 5)(5, PartnerAddressUsageComponent_ng_template_5_Template, 11, 0, \"ng-template\", 6)(6, PartnerAddressUsageComponent_ng_template_6_Template, 1, 1, \"ng-template\", 7)(7, PartnerAddressUsageComponent_ng_template_7_Template, 29, 5, \"ng-template\", 8)(8, PartnerAddressUsageComponent_ng_template_8_Template, 3, 0, \"ng-template\", 9)(9, PartnerAddressUsageComponent_ng_template_9_Template, 3, 0, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.filteredaddress)(\"rows\", 10)(\"expandedRowKeys\", ctx.expandedRows);\n        }\n      },\n      dependencies: [i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.RowToggler, i5.SortIcon, i7.ButtonDirective, i8.InputText, i9.Ripple],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "PartnerAddressUsageComponent_ng_template_4_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "expandAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵtwoWayListener", "PartnerAddressUsageComponent_ng_template_4_Template_input_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "globalSearchTerm", "PartnerAddressUsageComponent_ng_template_4_Template_input_input_6_listener", "onGlobalFilter", "ɵɵadvance", "ɵɵpropertyInterpolate1", "isExpanded", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "ɵɵtext", "ɵɵproperty", "address_r4", "expanded_r5", "ɵɵtextInterpolate1", "bp_address_id", "address_usage", "authorization_group", "ɵɵtemplate", "PartnerAddressUsageComponent_ng_template_6_tr_0_Template", "addressdetails", "length", "address_r6", "standard_usage", "bp_id", "PartnerAddressUsageComponent", "constructor", "route", "partnerservice", "unsubscribe$", "filteredaddress", "expandedRows", "loading", "id", "ngOnInit", "parent", "snapshot", "paramMap", "get", "partner", "pipe", "subscribe", "next", "response", "address_usages", "error", "err", "console", "for<PERSON>ach", "address", "event", "filterValue", "target", "value", "toLowerCase", "filter", "Object", "values", "some", "toString", "includes", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "PartnerService", "selectors", "decls", "vars", "consts", "template", "PartnerAddressUsageComponent_Template", "rf", "ctx", "PartnerAddressUsageComponent_ng_template_4_Template", "PartnerAddressUsageComponent_ng_template_5_Template", "PartnerAddressUsageComponent_ng_template_6_Template", "PartnerAddressUsageComponent_ng_template_7_Template", "PartnerAddressUsageComponent_ng_template_8_Template", "PartnerAddressUsageComponent_ng_template_9_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-address-usage\\partner-address-usage.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-address-usage\\partner-address-usage.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { PartnerService } from '../../partner.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-partner-address-usage',\r\n  templateUrl: './partner-address-usage.component.html',\r\n  styleUrl: './partner-address-usage.component.scss',\r\n})\r\nexport class PartnerAddressUsageComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public addressdetails: any = null;\r\n  public filteredaddress: any[] = [];\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public globalSearchTerm: string = '';\r\n  public loading: boolean = true;\r\n  public id: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private partnerservice: PartnerService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.partnerservice.partner.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: (response) => {\r\n        this.addressdetails = response?.address_usages || [];\r\n        this.filteredaddress = [...this.addressdetails];\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching data:', err);\r\n        this.addressdetails = [];\r\n        this.filteredaddress = [];\r\n      },\r\n    });\r\n  }\r\n\r\n  expandAll() {\r\n    if (!this.isExpanded) {\r\n      this.addressdetails.forEach((address: any) =>\r\n        address?.id ? (this.expandedRows[address.id] = true) : ''\r\n      );\r\n    } else {\r\n      this.expandedRows = {};\r\n    }\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  onGlobalFilter(event: Event) {\r\n    const filterValue = (event.target as HTMLInputElement).value.toLowerCase();\r\n\r\n    if (filterValue) {\r\n      this.filteredaddress = this.addressdetails.filter((address: any) =>\r\n        Object.values(address).some((value: any) =>\r\n          value?.toString().toLowerCase().includes(filterValue)\r\n        )\r\n      );\r\n    } else {\r\n      this.filteredaddress = [...this.addressdetails]; // Reset filter when input is empty\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12\">\r\n    <div class=\"card\">\r\n        <p-table #dt1 [value]=\"filteredaddress\" dataKey=\"id\" [rows]=\"10\" [expandedRowKeys]=\"expandedRows\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"caption\">\r\n                <div class=\"flex justify-content-between flex-column sm:flex-row gap-2\">\r\n                    <div class=\"flex flex-row gap-2 justify-content-between\">\r\n                        <button pButton icon=\"pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}\"\r\n                            label=\"{{ isExpanded ? 'Collapse All' : 'Expand All' }}\" (click)=\"expandAll()\"></button>\r\n                        <div class=\"flex table-header\"></div>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                            (input)=\"onGlobalFilter($event)\" placeholder=\"Search Address\" class=\"w-full\" />\r\n                    </span>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"width: 3rem\"></th>\r\n                    <th pSortableColumn=\"bp_address_id\">\r\n                        Address ID <p-sortIcon field=\"bp_address_id\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"address_usage\">\r\n                        Address Usage <p-sortIcon field=\"address_usage\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"authorization_group\">\r\n                        Authorization Group\r\n                        <p-sortIcon field=\"authorization_group\"></p-sortIcon>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-address let-expanded=\"expanded\">\r\n                <tr *ngIf=\"addressdetails?.length > 0\">\r\n                    <td>\r\n                        <button type=\"button\" pButton pRipple [pRowToggler]=\"address\"\r\n                            class=\"p-button-text p-button-rounded p-button-plain\"\r\n                            [icon]=\"expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\"></button>\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.bp_address_id || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.address_usage || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.authorization_group || \"-\" }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"rowexpansion\" let-address>\r\n                <tr>\r\n                    <td style=\"width: 3rem\"></td>\r\n                    <td colspan=\"4\">\r\n                        <div class=\"grid mx-0 border-1\">\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Address ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.bp_address_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Address Usage</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.address_usage || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Authorization Group</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.authorization_group || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Standard Usage</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.standard_usage?\"Yes\":\"No\"}}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Business Partner ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.bp_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">Address Usage are not available for this record.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading address data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICMjBC,EAFR,CAAAC,cAAA,cAAwE,cACX,iBAE8B;IAAtBD,EAAA,CAAAE,UAAA,mBAAAC,4EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAS;IAC5FV,EAAA,CAAAW,SAAA,cAAqC;IACzCX,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAW,SAAA,YAA4B;IAC5BX,EAAA,CAAAC,cAAA,mBACmF;IAD7CD,EAAA,CAAAY,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAe,kBAAA,CAAAT,MAAA,CAAAU,gBAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,gBAAA,GAAAF,MAAA;MAAA,OAAAd,EAAA,CAAAQ,WAAA,CAAAM,MAAA;IAAA,EAA8B;IAChEd,EAAA,CAAAE,UAAA,mBAAAe,2EAAAH,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAE5Cd,EAHQ,CAAAU,YAAA,EACmF,EAChF,EACL;;;;IATkBV,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAAoB,sBAAA,sBAAAd,MAAA,CAAAe,UAAA,8BAAyD;IACrErB,EAAA,CAAAsB,qBAAA,UAAAhB,MAAA,CAAAe,UAAA,iCAAwD;IAKtBrB,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAuB,gBAAA,YAAAjB,MAAA,CAAAU,gBAAA,CAA8B;;;;;IAO5EhB,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAC7BX,EAAA,CAAAC,cAAA,aAAoC;IAChCD,EAAA,CAAAwB,MAAA,mBAAW;IAAAxB,EAAA,CAAAW,SAAA,qBAA+C;IAC9DX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAoC;IAChCD,EAAA,CAAAwB,MAAA,sBAAc;IAAAxB,EAAA,CAAAW,SAAA,qBAA+C;IACjEX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAA0C;IACtCD,EAAA,CAAAwB,MAAA,4BACA;IAAAxB,EAAA,CAAAW,SAAA,sBAAqD;IAE7DX,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAuC,SAC/B;IACAD,EAAA,CAAAW,SAAA,iBAE8E;IAClFX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,GACJ;IACJxB,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;;IAbyCV,EAAA,CAAAmB,SAAA,GAAuB;IAEzDnB,EAFkC,CAAAyB,UAAA,gBAAAC,UAAA,CAAuB,SAAAC,WAAA,gDAEO;IAGpE3B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAG,aAAA,cACJ;IAEI7B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAI,aAAA,cACJ;IAEI9B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAK,mBAAA,cACJ;;;;;IAdJ/B,EAAA,CAAAgC,UAAA,IAAAC,wDAAA,iBAAuC;;;;IAAlCjC,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAA4B,cAAA,kBAAA5B,MAAA,CAAA4B,cAAA,CAAAC,MAAA,MAAgC;;;;;IAkBrCnC,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAIjBX,EAHZ,CAAAC,cAAA,aAAgB,cACoB,cACC,eAC+B;IAAAD,EAAA,CAAAwB,MAAA,iBAAU;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IACzEV,EAAA,CAAAC,cAAA,eAA8C;IAC1CD,EAAA,CAAAwB,MAAA,GACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,cAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,qBAAa;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IAC5EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,2BAAmB;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IAClFV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,sBAAc;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IAC7EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,2BAAmB;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IAClFV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IAIhBxB,EAJgB,CAAAU,YAAA,EAAO,EACL,EACJ,EACL,EACJ;;;;IA7BeV,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAQ,UAAA,kBAAAA,UAAA,CAAAP,aAAA,cACJ;IAKI7B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAQ,UAAA,kBAAAA,UAAA,CAAAN,aAAA,cACJ;IAKI9B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAQ,UAAA,kBAAAA,UAAA,CAAAL,mBAAA,cACJ;IAKI/B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAQ,UAAA,kBAAAA,UAAA,CAAAC,cAAA,sBACJ;IAKIrC,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAQ,UAAA,kBAAAA,UAAA,CAAAE,KAAA,cACJ;;;;;IAQZtC,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAwB,MAAA,uDAAgD;IACpExB,EADoE,CAAAU,YAAA,EAAK,EACpE;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAwB,MAAA,2CAAoC;IACxDxB,EADwD,CAAAU,YAAA,EAAK,EACxD;;;ADrFrB,OAAM,MAAO6B,4BAA4B;EAUvCC,YACUC,KAAqB,EACrBC,cAA8B;IAD9B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IAXhB,KAAAC,YAAY,GAAG,IAAI7C,OAAO,EAAQ;IACnC,KAAAoC,cAAc,GAAQ,IAAI;IAC1B,KAAAU,eAAe,GAAU,EAAE;IAC3B,KAAAvB,UAAU,GAAY,KAAK;IAC3B,KAAAwB,YAAY,GAAiB,EAAE;IAC/B,KAAA7B,gBAAgB,GAAW,EAAE;IAC7B,KAAA8B,OAAO,GAAY,IAAI;IACvB,KAAAC,EAAE,GAAW,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,EAAE,GAAG,IAAI,CAACN,KAAK,CAACQ,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC9D,IAAI,CAACV,cAAc,CAACW,OAAO,CAACC,IAAI,CAACvD,SAAS,CAAC,IAAI,CAAC4C,YAAY,CAAC,CAAC,CAACY,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACvB,cAAc,GAAGuB,QAAQ,EAAEC,cAAc,IAAI,EAAE;QACpD,IAAI,CAACd,eAAe,GAAG,CAAC,GAAG,IAAI,CAACV,cAAc,CAAC;MACjD,CAAC;MACDyB,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAC1B,cAAc,GAAG,EAAE;QACxB,IAAI,CAACU,eAAe,GAAG,EAAE;MAC3B;KACD,CAAC;EACJ;EAEAnC,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACY,UAAU,EAAE;MACpB,IAAI,CAACa,cAAc,CAAC4B,OAAO,CAAEC,OAAY,IACvCA,OAAO,EAAEhB,EAAE,GAAI,IAAI,CAACF,YAAY,CAACkB,OAAO,CAAChB,EAAE,CAAC,GAAG,IAAI,GAAI,EAAE,CAC1D;IACH,CAAC,MAAM;MACL,IAAI,CAACF,YAAY,GAAG,EAAE;IACxB;IACA,IAAI,CAACxB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAH,cAAcA,CAAC8C,KAAY;IACzB,MAAMC,WAAW,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK,CAACC,WAAW,EAAE;IAE1E,IAAIH,WAAW,EAAE;MACf,IAAI,CAACrB,eAAe,GAAG,IAAI,CAACV,cAAc,CAACmC,MAAM,CAAEN,OAAY,IAC7DO,MAAM,CAACC,MAAM,CAACR,OAAO,CAAC,CAACS,IAAI,CAAEL,KAAU,IACrCA,KAAK,EAAEM,QAAQ,EAAE,CAACL,WAAW,EAAE,CAACM,QAAQ,CAACT,WAAW,CAAC,CACtD,CACF;IACH,CAAC,MAAM;MACL,IAAI,CAACrB,eAAe,GAAG,CAAC,GAAG,IAAI,CAACV,cAAc,CAAC,CAAC,CAAC;IACnD;EACF;EAEAyC,WAAWA,CAAA;IACT,IAAI,CAAChC,YAAY,CAACa,IAAI,EAAE;IACxB,IAAI,CAACb,YAAY,CAACiC,QAAQ,EAAE;EAC9B;;;uBA1DWrC,4BAA4B,EAAAvC,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA5B1C,4BAA4B;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZjCxF,EAFR,CAAAC,cAAA,aAAoB,aACE,oBAEgB;UA6F1BD,EA5FA,CAAAgC,UAAA,IAAA0D,mDAAA,yBAAiC,IAAAC,mDAAA,0BAeD,IAAAC,mDAAA,yBAekC,IAAAC,mDAAA,0BAkBhB,IAAAC,mDAAA,yBAuCZ,IAAAC,mDAAA,0BAKD;UAOjD/F,EAFQ,CAAAU,YAAA,EAAU,EACR,EACJ;;;UArGgBV,EAAA,CAAAmB,SAAA,GAAyB;UAA0BnB,EAAnD,CAAAyB,UAAA,UAAAgE,GAAA,CAAA7C,eAAA,CAAyB,YAAyB,oBAAA6C,GAAA,CAAA5C,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}