{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PartnerAddressService {\n  constructor(http) {\n    this.http = http;\n  }\n  getAddress(id) {\n    let params = new HttpParams().set('filters[bp_id][$eq]', id).set('populate[phone_numbers][populate]', '*').set('populate[emails][populate]', '*').set('populate[home_page_urls][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.PARTNERS_ADDRESS}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function PartnerAddressService_Factory(t) {\n      return new (t || PartnerAddressService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PartnerAddressService,\n      factory: PartnerAddressService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "CMS_APIContstant", "PartnerAddressService", "constructor", "http", "get<PERSON><PERSON><PERSON>", "id", "params", "set", "get", "PARTNERS_ADDRESS", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-address.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class PartnerAddressService {\r\n    constructor(private http: HttpClient) { }\r\n\r\n    getAddress(\r\n        id: string,\r\n    ): Observable<any[]> {\r\n        let params = new HttpParams().set('filters[bp_id][$eq]', id).set('populate[phone_numbers][populate]', '*').set('populate[emails][populate]', '*').set('populate[home_page_urls][populate]', '*'); \r\n        return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS_ADDRESS}`, {\r\n            params,\r\n        });\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAG7D,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,qBAAqB;EAC9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,UAAUA,CACNC,EAAU;IAEV,IAAIC,MAAM,GAAG,IAAIP,UAAU,EAAE,CAACQ,GAAG,CAAC,qBAAqB,EAAEF,EAAE,CAAC,CAACE,GAAG,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAACA,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAACA,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC;IAChM,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAQ,GAAGR,gBAAgB,CAACS,gBAAgB,EAAE,EAAE;MAChEH;KACH,CAAC;EACN;;;uBAVSL,qBAAqB,EAAAS,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBZ,qBAAqB;MAAAa,OAAA,EAArBb,qBAAqB,CAAAc,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}