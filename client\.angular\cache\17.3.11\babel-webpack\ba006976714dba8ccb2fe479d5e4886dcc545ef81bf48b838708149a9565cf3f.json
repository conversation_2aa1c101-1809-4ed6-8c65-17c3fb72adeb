{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class VendorService {\n  constructor(http) {\n    this.http = http;\n    this.configurationSubject = new BehaviorSubject(null);\n    this.configuration = this.configurationSubject.asObservable();\n  }\n  get(type, moduleurl) {\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`);\n  }\n  update(data, id, moduleurl) {\n    return this.http.put(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`, {\n      data\n    });\n  }\n  static {\n    this.ɵfac = function VendorService_Factory(t) {\n      return new (t || VendorService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VendorService,\n      factory: VendorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "CMS_APIContstant", "VendorService", "constructor", "http", "configurationSubject", "configuration", "asObservable", "get", "type", "<PERSON><PERSON><PERSON>", "CMSAPI_END_POINT", "update", "data", "id", "put", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\vendor\\vendor.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class VendorService {\r\n  public configurationSubject = new BehaviorSubject<any>(null);\r\n  public configuration = this.configurationSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  get(type: string, moduleurl: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`\r\n    );\r\n  }\r\n\r\n  update(data: any, id: string, moduleurl: string) {\r\n    return this.http.put<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`,\r\n      { data }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAQ,MAAM;AACtC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,aAAa;EAIxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,oBAAoB,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IACrD,KAAAM,aAAa,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;EAExB;EAEvCC,GAAGA,CAACC,IAAY,EAAEC,SAAiB;IACjC,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAACU,gBAAgB,IAAID,SAAS,EAAE,CACpD;EACH;EAEAE,MAAMA,CAACC,IAAS,EAAEC,EAAU,EAAEJ,SAAiB;IAC7C,OAAO,IAAI,CAACN,IAAI,CAACW,GAAG,CAClB,GAAGd,gBAAgB,CAACU,gBAAgB,IAAID,SAAS,IAAII,EAAE,EAAE,EACzD;MAAED;IAAI,CAAE,CACT;EACH;;;uBAjBWX,aAAa,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbjB,aAAa;MAAAkB,OAAA,EAAblB,aAAa,CAAAmB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}