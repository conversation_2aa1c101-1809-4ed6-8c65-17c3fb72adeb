{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CrmService {\n  constructor(http) {\n    this.http = http;\n    this.crmSubject = new BehaviorSubject(null);\n    this.crm = this.crmSubject.asObservable();\n  }\n  get(type, moduleurl) {\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`);\n  }\n  update(data, id, moduleurl) {\n    return this.http.put(`${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`, {\n      data\n    });\n  }\n  static {\n    this.ɵfac = function CrmService_Factory(t) {\n      return new (t || CrmService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CrmService,\n      factory: CrmService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "CMS_APIContstant", "CrmService", "constructor", "http", "crmSubject", "crm", "asObservable", "get", "type", "<PERSON><PERSON><PERSON>", "CMSAPI_END_POINT", "update", "data", "id", "put", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\crm\\crm.service.ts"], "sourcesContent": ["import { HttpClient} from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject} from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CrmService {\r\n  public crmSubject = new BehaviorSubject<any>(null);\r\n  public crm = this.crmSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  get(type: string, moduleurl: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}`\r\n    );\r\n  }\r\n\r\n  update(data: any, id: string, moduleurl: string) {\r\n    return this.http.put<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${moduleurl}/${id}`,\r\n      { data }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAO,MAAM;AACrC,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,UAAU;EAIrBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,UAAU,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC3C,KAAAM,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,YAAY,EAAE;EAEJ;EAEvCC,GAAGA,CAACC,IAAY,EAAEC,SAAiB;IACjC,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAClB,GAAGP,gBAAgB,CAACU,gBAAgB,IAAID,SAAS,EAAE,CACpD;EACH;EAEAE,MAAMA,CAACC,IAAS,EAAEC,EAAU,EAAEJ,SAAiB;IAC7C,OAAO,IAAI,CAACN,IAAI,CAACW,GAAG,CAClB,GAAGd,gBAAgB,CAACU,gBAAgB,IAAID,SAAS,IAAII,EAAE,EAAE,EACzD;MAAED;IAAI,CAAE,CACT;EACH;;;uBAjBWX,UAAU,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAVjB,UAAU;MAAAkB,OAAA,EAAVlB,UAAU,CAAAmB,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}