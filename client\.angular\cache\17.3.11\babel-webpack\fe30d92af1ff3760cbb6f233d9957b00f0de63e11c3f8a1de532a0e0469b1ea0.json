{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { BackofficeComponent } from './backoffice.component';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: BackofficeComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      data: {\n        breadcrumb: 'Dashboard'\n      },\n      loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule)\n    }, {\n      path: 'product',\n      data: {\n        breadcrumb: 'Products'\n      },\n      loadChildren: () => import('./product/product.module').then(m => m.ProductModule)\n    }, {\n      path: 'customer',\n      data: {\n        breadcrumb: 'Customers'\n      },\n      loadChildren: () => import('./customer/customer.module').then(m => m.CustomerModule)\n    }, {\n      path: 'partner',\n      data: {\n        breadcrumb: 'Business Partners'\n      },\n      loadChildren: () => import('./partner/partner.module').then(m => m.PartnerModule)\n    }, {\n      path: 'supplier',\n      data: {\n        breadcrumb: 'Suppliers'\n      },\n      loadChildren: () => import('./supplier/supplier.module').then(m => m.SupplierModule)\n    }, {\n      path: 'contacts',\n      data: {\n        breadcrumb: 'Contacts'\n      },\n      loadChildren: () => import('./partner/partner.module').then(m => m.PartnerModule)\n    }, {\n      path: 'employees',\n      data: {\n        breadcrumb: 'Employees'\n      },\n      loadChildren: () => import('./partner/partner.module').then(m => m.PartnerModule)\n    }, {\n      path: 'prospects',\n      data: {\n        breadcrumb: 'Prospects'\n      },\n      loadChildren: () => import('./partner/partner.module').then(m => m.PartnerModule)\n    }, {\n      path: 'users',\n      data: {\n        breadcrumb: 'Users'\n      },\n      loadChildren: () => import('./users/users.module').then(m => m.UsersModule)\n    }, {\n      path: 'scheduler',\n      data: {\n        breadcrumb: 'Scheduler'\n      },\n      loadChildren: () => import('./scheduler/scheduler.module').then(m => m.SchedulerModule)\n    }, {\n      path: 'administrators',\n      data: {\n        breadcrumb: 'Administrators'\n      },\n      loadChildren: () => import('./administrator/administrator.module').then(m => m.AdministratorModule)\n    }, {\n      path: 'crm',\n      data: {\n        breadcrumb: 'CRM'\n      },\n      loadChildren: () => import('./crm/crm.module').then(m => m.CrmModule)\n    }, {\n      path: 'vendor',\n      data: {\n        breadcrumb: 'Vendor'\n      },\n      loadChildren: () => import('./vendor/vendor.module').then(m => m.VendorModule)\n    }, {\n      path: 'configuration',\n      data: {\n        breadcrumb: 'Configuration'\n      },\n      loadChildren: () => import('./configuration/configuration.module').then(m => m.ConfigurationModule)\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class BackofficeRoutingModule {\n  static {\n    this.ɵfac = function BackofficeRoutingModule_Factory(t) {\n      return new (t || BackofficeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: BackofficeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BackofficeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "BackofficeComponent", "AppLayoutComponent", "routes", "path", "component", "children", "data", "breadcrumb", "loadChildren", "then", "m", "DashboardModule", "ProductModule", "CustomerModule", "PartnerModule", "SupplierModule", "UsersModule", "SchedulerModule", "AdministratorModule", "CrmModule", "VendorModule", "ConfigurationModule", "redirectTo", "BackofficeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\backoffice-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { BackofficeComponent } from './backoffice.component';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: BackofficeComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            data: { breadcrumb: 'Dashboard' },\r\n            loadChildren: () =>\r\n              import('./dashboard/dashboard.module').then(\r\n                (m) => m.DashboardModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'product',\r\n            data: { breadcrumb: 'Products' },\r\n            loadChildren: () =>\r\n              import('./product/product.module').then((m) => m.ProductModule),\r\n          },\r\n          {\r\n            path: 'customer',\r\n            data: { breadcrumb: 'Customers' },\r\n            loadChildren: () =>\r\n              import('./customer/customer.module').then(\r\n                (m) => m.CustomerModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'partner',\r\n            data: { breadcrumb: 'Business Partners' },\r\n            loadChildren: () =>\r\n              import('./partner/partner.module').then((m) => m.PartnerModule),\r\n          },\r\n          {\r\n            path: 'supplier',\r\n            data: { breadcrumb: 'Suppliers' },\r\n            loadChildren: () =>\r\n              import('./supplier/supplier.module').then(\r\n                (m) => m.SupplierModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'contacts',\r\n            data: { breadcrumb: 'Contacts' },\r\n            loadChildren: () =>\r\n              import('./partner/partner.module').then((m) => m.PartnerModule),\r\n          },\r\n          {\r\n            path: 'employees',\r\n            data: { breadcrumb: 'Employees' },\r\n            loadChildren: () =>\r\n              import('./partner/partner.module').then((m) => m.PartnerModule),\r\n          },\r\n          {\r\n            path: 'prospects',\r\n            data: { breadcrumb: 'Prospects' },\r\n            loadChildren: () =>\r\n              import('./partner/partner.module').then((m) => m.PartnerModule),\r\n          },\r\n          {\r\n            path: 'users',\r\n            data: { breadcrumb: 'Users' },\r\n            loadChildren: () =>\r\n              import('./users/users.module').then((m) => m.UsersModule),\r\n          },\r\n          {\r\n            path: 'scheduler',\r\n            data: { breadcrumb: 'Scheduler' },\r\n            loadChildren: () =>\r\n              import('./scheduler/scheduler.module').then(\r\n                (m) => m.SchedulerModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'administrators',\r\n            data: { breadcrumb: 'Administrators' },\r\n            loadChildren: () =>\r\n              import('./administrator/administrator.module').then(\r\n                (m) => m.AdministratorModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'crm',\r\n            data: { breadcrumb: 'CRM' },\r\n            loadChildren: () =>\r\n              import('./crm/crm.module').then((m) => m.CrmModule),\r\n          },\r\n          {\r\n            path: 'vendor',\r\n            data: { breadcrumb: 'Vendor' },\r\n            loadChildren: () =>\r\n              import('./vendor/vendor.module').then((m) => m.VendorModule),\r\n          },\r\n          {\r\n            path: 'configuration',\r\n            data: { breadcrumb: 'Configuration' },\r\n            loadChildren: () =>\r\n              import('./configuration/configuration.module').then(\r\n                (m) => m.ConfigurationModule\r\n              ),\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class BackofficeRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,kBAAkB,QAAQ,+BAA+B;;;AAElE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,mBAAmB;EAC9BK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEH,kBAAkB;IAC7BI,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CACxCC,CAAC,IAAKA,CAAC,CAACC,eAAe;KAE7B,EACD;MACER,IAAI,EAAE,SAAS;MACfG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,aAAa;KACjE,EACD;MACET,IAAI,EAAE,UAAU;MAChBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CACtCC,CAAC,IAAKA,CAAC,CAACG,cAAc;KAE5B,EACD;MACEV,IAAI,EAAE,SAAS;MACfG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAmB,CAAE;MACzCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,aAAa;KACjE,EACD;MACEX,IAAI,EAAE,UAAU;MAChBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CACtCC,CAAC,IAAKA,CAAC,CAACK,cAAc;KAE5B,EACD;MACEZ,IAAI,EAAE,UAAU;MAChBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,aAAa;KACjE,EACD;MACEX,IAAI,EAAE,WAAW;MACjBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,aAAa;KACjE,EACD;MACEX,IAAI,EAAE,WAAW;MACjBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,aAAa;KACjE,EACD;MACEX,IAAI,EAAE,OAAO;MACbG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAC7BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,WAAW;KAC3D,EACD;MACEb,IAAI,EAAE,WAAW;MACjBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAW,CAAE;MACjCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CACxCC,CAAC,IAAKA,CAAC,CAACO,eAAe;KAE7B,EACD;MACEd,IAAI,EAAE,gBAAgB;MACtBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAgB,CAAE;MACtCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACQ,mBAAmB;KAEjC,EACD;MACEf,IAAI,EAAE,KAAK;MACXG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAK,CAAE;MAC3BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kBAAkB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,SAAS;KACrD,EACD;MACEhB,IAAI,EAAE,QAAQ;MACdG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAC9BC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,YAAY;KAC9D,EACD;MACEjB,IAAI,EAAE,eAAe;MACrBG,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAe,CAAE;MACrCC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACW,mBAAmB;KAEjC;GAEJ,EACD;IAAElB,IAAI,EAAE,IAAI;IAAEmB,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAHxBxB,YAAY,CAACyB,QAAQ,CAACtB,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXwB,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAA3B,YAAA;IAAA4B,OAAA,GAFxB5B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}