{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PartnerComponent } from './partner.component';\nimport { PartnerDetailsComponent } from './partner-details/partner-details.component';\nimport { PartnerInfoComponent } from './partner-details/partner-info/partner-info.component';\nimport { PartnerBackendComponent } from './partner-details/partner-backend/partner-backend.component';\nimport { PartnerBankComponent } from './partner-details/partner-bank/partner-bank.component';\nimport { PartnerPaymentCardComponent } from './partner-details/partner-payment-card/partner-payment-card.component';\nimport { PartnerContactComponent } from './partner-details/partner-contact/partner-contact.component';\nimport { PartnerAddressComponent } from './partner-details/partner-address/partner-address.component';\nimport { PartnerRelationshipComponent } from './partner-details/partner-relationship/partner-relationship.component';\nimport { PartnerRoleComponent } from './partner-details/partner-role/partner-role.component';\nimport { PartnerInternationalAddressComponent } from './partner-details/partner-international-address/partner-international-address.component';\nimport { PartnerCreditWorthinessComponent } from './partner-details/partner-credit-worthiness/partner-credit-worthiness.component';\nimport { PartnerAddressILNComponent } from './partner-details/partner-address-iln/partner-address-iln.component';\nimport { PartnerAddressUsageComponent } from './partner-details/partner-address-usage/partner-address-usage.component';\nimport { PartnerEmailAddressComponent } from './partner-details/partner-email-address/partner-email-address.component';\nimport { PartnerFaxNumberComponent } from './partner-details/partner-fax-number/partner-fax-number.component';\nimport { PartnerPhoneNumberComponent } from './partner-details/partner-phone-number/partner-phone-number.component';\nimport { PartnerHomeUrlComponent } from './partner-details/partner-home-url/partner-home-url.component';\nimport { SupplierCompanyComponent } from './partner-details/supplier-company/supplier-company.component';\nimport { SupplierCompanyTextComponent } from './partner-details/supplier-company-text/supplier-company-text.component';\nimport { SupplierTextComponent } from './partner-details/supplier-text/supplier-text.component';\nimport { SupplierPartnerComponent } from './partner-details/supplier-partner/supplier-partner.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PartnerComponent\n}, {\n  path: ':id',\n  component: PartnerDetailsComponent,\n  children: [{\n    path: 'general',\n    component: PartnerInfoComponent\n  }, {\n    path: 'backend',\n    component: PartnerBackendComponent\n  }, {\n    path: 'role',\n    component: PartnerRoleComponent\n  }, {\n    path: 'address',\n    component: PartnerAddressComponent\n  }, {\n    path: 'bank',\n    component: PartnerBankComponent\n  }, {\n    path: 'card',\n    component: PartnerPaymentCardComponent\n  }, {\n    path: 'contact',\n    component: PartnerContactComponent\n  }, {\n    path: 'relationship',\n    component: PartnerRelationshipComponent\n  }, {\n    path: 'international-address',\n    component: PartnerInternationalAddressComponent\n  }, {\n    path: 'credit-worthiness',\n    component: PartnerCreditWorthinessComponent\n  }, {\n    path: 'address-location',\n    component: PartnerAddressILNComponent\n  }, {\n    path: 'address-usage',\n    component: PartnerAddressUsageComponent\n  }, {\n    path: 'email-address',\n    component: PartnerEmailAddressComponent\n  }, {\n    path: 'fax-number',\n    component: PartnerFaxNumberComponent\n  }, {\n    path: 'home-page-url',\n    component: PartnerHomeUrlComponent\n  }, {\n    path: 'phone',\n    component: PartnerPhoneNumberComponent\n  }, {\n    path: 'supplier-company',\n    component: SupplierCompanyComponent\n  }, {\n    path: 'supplier-company-text',\n    component: SupplierCompanyTextComponent\n  }, {\n    path: 'supplier-text',\n    component: SupplierTextComponent\n  }, {\n    path: 'supplier-partner',\n    component: SupplierPartnerComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport class PartnerRoutingModule {\n  static {\n    this.ɵfac = function PartnerRoutingModule_Factory(t) {\n      return new (t || PartnerRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PartnerRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PartnerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PartnerComponent", "PartnerDetailsComponent", "PartnerInfoComponent", "PartnerBackendComponent", "PartnerBankComponent", "PartnerPaymentCardComponent", "PartnerContactComponent", "PartnerAddressComponent", "PartnerRelationshipComponent", "PartnerRoleComponent", "PartnerInternationalAddressComponent", "PartnerCreditWorthinessComponent", "PartnerAddressILNComponent", "PartnerAddressUsageComponent", "PartnerEmailAddressComponent", "PartnerFaxNumberComponent", "PartnerPhoneNumberComponent", "PartnerHomeUrlComponent", "SupplierCompanyComponent", "SupplierCompanyTextComponent", "SupplierTextComponent", "SupplierPartnerComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "PartnerRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { PartnerComponent } from './partner.component';\r\nimport { PartnerDetailsComponent } from './partner-details/partner-details.component';\r\nimport { PartnerInfoComponent } from './partner-details/partner-info/partner-info.component';\r\nimport { PartnerBackendComponent } from './partner-details/partner-backend/partner-backend.component';\r\nimport { PartnerBankComponent } from './partner-details/partner-bank/partner-bank.component';\r\nimport { PartnerPaymentCardComponent } from './partner-details/partner-payment-card/partner-payment-card.component';\r\nimport { PartnerContactComponent } from './partner-details/partner-contact/partner-contact.component';\r\nimport { PartnerAddressComponent } from './partner-details/partner-address/partner-address.component';\r\nimport { PartnerRelationshipComponent } from './partner-details/partner-relationship/partner-relationship.component';\r\nimport { PartnerRoleComponent } from './partner-details/partner-role/partner-role.component';\r\nimport { PartnerInternationalAddressComponent } from './partner-details/partner-international-address/partner-international-address.component';\r\nimport { PartnerCreditWorthinessComponent } from './partner-details/partner-credit-worthiness/partner-credit-worthiness.component';\r\nimport { PartnerAddressILNComponent } from './partner-details/partner-address-iln/partner-address-iln.component';\r\nimport { PartnerAddressUsageComponent } from './partner-details/partner-address-usage/partner-address-usage.component';\r\nimport { PartnerEmailAddressComponent } from './partner-details/partner-email-address/partner-email-address.component';\r\nimport { PartnerFaxNumberComponent } from './partner-details/partner-fax-number/partner-fax-number.component';\r\nimport { PartnerPhoneNumberComponent } from './partner-details/partner-phone-number/partner-phone-number.component';\r\nimport { PartnerHomeUrlComponent } from './partner-details/partner-home-url/partner-home-url.component';\r\nimport { SupplierCompanyComponent } from './partner-details/supplier-company/supplier-company.component';\r\nimport { SupplierCompanyTextComponent } from './partner-details/supplier-company-text/supplier-company-text.component';\r\nimport { SupplierTextComponent } from './partner-details/supplier-text/supplier-text.component';\r\nimport { SupplierPartnerComponent } from './partner-details/supplier-partner/supplier-partner.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: PartnerComponent },\r\n  {\r\n    path: ':id',\r\n    component: PartnerDetailsComponent,\r\n    children: [\r\n      { path: 'general', component: PartnerInfoComponent },\r\n      { path: 'backend', component: PartnerBackendComponent },\r\n      { path: 'role', component: PartnerRoleComponent },\r\n      { path: 'address', component: PartnerAddressComponent },\r\n      { path: 'bank', component: PartnerBankComponent },\r\n      { path: 'card', component: PartnerPaymentCardComponent },\r\n      { path: 'contact', component: PartnerContactComponent },\r\n      { path: 'relationship', component: PartnerRelationshipComponent },\r\n      {\r\n        path: 'international-address',\r\n        component: PartnerInternationalAddressComponent,\r\n      },\r\n      {\r\n        path: 'credit-worthiness',\r\n        component: PartnerCreditWorthinessComponent,\r\n      },\r\n      {\r\n        path: 'address-location',\r\n        component: PartnerAddressILNComponent,\r\n      },\r\n      {\r\n        path: 'address-usage',\r\n        component: PartnerAddressUsageComponent,\r\n      },\r\n      {\r\n        path: 'email-address',\r\n        component: PartnerEmailAddressComponent,\r\n      },\r\n      {\r\n        path: 'fax-number',\r\n        component: PartnerFaxNumberComponent,\r\n      },\r\n      {\r\n        path: 'home-page-url',\r\n        component: PartnerHomeUrlComponent,\r\n      },\r\n      {\r\n        path: 'phone',\r\n        component: PartnerPhoneNumberComponent,\r\n      },\r\n      {\r\n        path: 'supplier-company',\r\n        component: SupplierCompanyComponent,\r\n      },\r\n      {\r\n        path: 'supplier-company-text',\r\n        component: SupplierCompanyTextComponent,\r\n      },\r\n      {\r\n        path: 'supplier-text',\r\n        component: SupplierTextComponent,\r\n      },\r\n      {\r\n        path: 'supplier-partner',\r\n        component: SupplierPartnerComponent,\r\n      },\r\n      { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class PartnerRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,uBAAuB,QAAQ,6DAA6D;AACrG,SAASC,4BAA4B,QAAQ,uEAAuE;AACpH,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,oCAAoC,QAAQ,yFAAyF;AAC9I,SAASC,gCAAgC,QAAQ,iFAAiF;AAClI,SAASC,0BAA0B,QAAQ,qEAAqE;AAChH,SAASC,4BAA4B,QAAQ,yEAAyE;AACtH,SAASC,4BAA4B,QAAQ,yEAAyE;AACtH,SAASC,yBAAyB,QAAQ,mEAAmE;AAC7G,SAASC,2BAA2B,QAAQ,uEAAuE;AACnH,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,4BAA4B,QAAQ,yEAAyE;AACtH,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,wBAAwB,QAAQ,+DAA+D;;;AAExG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAExB;AAAgB,CAAE,EACzC;EACEuB,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEvB,uBAAuB;EAClCwB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEtB;EAAoB,CAAE,EACpD;IAAEqB,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAErB;EAAuB,CAAE,EACvD;IAAEoB,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEf;EAAoB,CAAE,EACjD;IAAEc,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEjB;EAAuB,CAAE,EACvD;IAAEgB,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEpB;EAAoB,CAAE,EACjD;IAAEmB,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEnB;EAA2B,CAAE,EACxD;IAAEkB,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAElB;EAAuB,CAAE,EACvD;IAAEiB,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEhB;EAA4B,CAAE,EACjE;IACEe,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEd;GACZ,EACD;IACEa,IAAI,EAAE,mBAAmB;IACzBC,SAAS,EAAEb;GACZ,EACD;IACEY,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEZ;GACZ,EACD;IACEW,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEX;GACZ,EACD;IACEU,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEV;GACZ,EACD;IACES,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAET;GACZ,EACD;IACEQ,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEP;GACZ,EACD;IACEM,IAAI,EAAE,OAAO;IACbC,SAAS,EAAER;GACZ,EACD;IACEO,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEN;GACZ,EACD;IACEK,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAEL;GACZ,EACD;IACEI,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEJ;GACZ,EACD;IACEG,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAEH;GACZ,EACD;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE3D,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrB7B,YAAY,CAAC8B,QAAQ,CAACP,MAAM,CAAC,EAC7BvB,YAAY;IAAA;EAAA;;;2EAEX6B,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAhC,YAAA;IAAAiC,OAAA,GAFrBjC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}