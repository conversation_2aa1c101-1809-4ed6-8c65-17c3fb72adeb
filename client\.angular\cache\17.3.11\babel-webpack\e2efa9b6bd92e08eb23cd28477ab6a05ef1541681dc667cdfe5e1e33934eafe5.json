{"ast": null, "code": "export const US_STATES = [{\n  name: \"Alabama\",\n  abbreviation: \"AL\"\n}, {\n  name: \"Alaska\",\n  abbreviation: \"AK\"\n}, {\n  name: \"Arizona\",\n  abbreviation: \"AZ\"\n}, {\n  name: \"Arkansas\",\n  abbreviation: \"AR\"\n}, {\n  name: \"California\",\n  abbreviation: \"CA\"\n}, {\n  name: \"Colorado\",\n  abbreviation: \"CO\"\n}, {\n  name: \"Connecticut\",\n  abbreviation: \"CT\"\n}, {\n  name: \"Delaware\",\n  abbreviation: \"DE\"\n}, {\n  name: \"Florida\",\n  abbreviation: \"FL\"\n}, {\n  name: \"Georgia\",\n  abbreviation: \"GA\"\n}, {\n  name: \"Hawaii\",\n  abbreviation: \"HI\"\n}, {\n  name: \"Idaho\",\n  abbreviation: \"ID\"\n}, {\n  name: \"Illinois\",\n  abbreviation: \"IL\"\n}, {\n  name: \"Indiana\",\n  abbreviation: \"IN\"\n}, {\n  name: \"Iowa\",\n  abbreviation: \"IA\"\n}, {\n  name: \"Kansas\",\n  abbreviation: \"KS\"\n}, {\n  name: \"Kentucky\",\n  abbreviation: \"KY\"\n}, {\n  name: \"Louisiana\",\n  abbreviation: \"LA\"\n}, {\n  name: \"Maine\",\n  abbreviation: \"ME\"\n}, {\n  name: \"Maryland\",\n  abbreviation: \"MD\"\n}, {\n  name: \"Massachusetts\",\n  abbreviation: \"MA\"\n}, {\n  name: \"Michigan\",\n  abbreviation: \"MI\"\n}, {\n  name: \"Minnesota\",\n  abbreviation: \"MN\"\n}, {\n  name: \"Mississippi\",\n  abbreviation: \"MS\"\n}, {\n  name: \"Missouri\",\n  abbreviation: \"MO\"\n}, {\n  name: \"Montana\",\n  abbreviation: \"MT\"\n}, {\n  name: \"Nebraska\",\n  abbreviation: \"NE\"\n}, {\n  name: \"Nevada\",\n  abbreviation: \"NV\"\n}, {\n  name: \"New Hampshire\",\n  abbreviation: \"NH\"\n}, {\n  name: \"New Jersey\",\n  abbreviation: \"NJ\"\n}, {\n  name: \"New Mexico\",\n  abbreviation: \"NM\"\n}, {\n  name: \"New York\",\n  abbreviation: \"NY\"\n}, {\n  name: \"North Carolina\",\n  abbreviation: \"NC\"\n}, {\n  name: \"North Dakota\",\n  abbreviation: \"ND\"\n}, {\n  name: \"Ohio\",\n  abbreviation: \"OH\"\n}, {\n  name: \"Oklahoma\",\n  abbreviation: \"OK\"\n}, {\n  name: \"Oregon\",\n  abbreviation: \"OR\"\n}, {\n  name: \"Pennsylvania\",\n  abbreviation: \"PA\"\n}, {\n  name: \"Rhode Island\",\n  abbreviation: \"RI\"\n}, {\n  name: \"South Carolina\",\n  abbreviation: \"SC\"\n}, {\n  name: \"South Dakota\",\n  abbreviation: \"SD\"\n}, {\n  name: \"Tennessee\",\n  abbreviation: \"TN\"\n}, {\n  name: \"Texas\",\n  abbreviation: \"TX\"\n}, {\n  name: \"Utah\",\n  abbreviation: \"UT\"\n}, {\n  name: \"Vermont\",\n  abbreviation: \"VT\"\n}, {\n  name: \"Virginia\",\n  abbreviation: \"VA\"\n}, {\n  name: \"Washington\",\n  abbreviation: \"WA\"\n}, {\n  name: \"West Virginia\",\n  abbreviation: \"WV\"\n}, {\n  name: \"Wisconsin\",\n  abbreviation: \"WI\"\n}, {\n  name: \"Wyoming\",\n  abbreviation: \"WY\"\n}];", "map": {"version": 3, "names": ["US_STATES", "name", "abbreviation"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\constants\\us-states.ts"], "sourcesContent": ["export const US_STATES = [\r\n    { name: \"Alabama\", abbreviation: \"AL\" },\r\n    { name: \"Alaska\", abbreviation: \"AK\" },\r\n    { name: \"Arizona\", abbreviation: \"AZ\" },\r\n    { name: \"Arkansas\", abbreviation: \"AR\" },\r\n    { name: \"California\", abbreviation: \"CA\" },\r\n    { name: \"Colorado\", abbreviation: \"CO\" },\r\n    { name: \"Connecticut\", abbreviation: \"CT\" },\r\n    { name: \"Delaware\", abbreviation: \"DE\" },\r\n    { name: \"Florida\", abbreviation: \"FL\" },\r\n    { name: \"Georgia\", abbreviation: \"GA\" },\r\n    { name: \"Hawaii\", abbreviation: \"HI\" },\r\n    { name: \"Idaho\", abbreviation: \"ID\" },\r\n    { name: \"Illinois\", abbreviation: \"IL\" },\r\n    { name: \"Indiana\", abbreviation: \"IN\" },\r\n    { name: \"Iowa\", abbreviation: \"IA\" },\r\n    { name: \"Kansas\", abbreviation: \"KS\" },\r\n    { name: \"Kentucky\", abbreviation: \"KY\" },\r\n    { name: \"Louisiana\", abbreviation: \"LA\" },\r\n    { name: \"Maine\", abbreviation: \"ME\" },\r\n    { name: \"Maryland\", abbreviation: \"MD\" },\r\n    { name: \"Massachusetts\", abbreviation: \"MA\" },\r\n    { name: \"Michigan\", abbreviation: \"MI\" },\r\n    { name: \"Minnesota\", abbreviation: \"MN\" },\r\n    { name: \"Mississippi\", abbreviation: \"MS\" },\r\n    { name: \"Missouri\", abbreviation: \"MO\" },\r\n    { name: \"Montana\", abbreviation: \"MT\" },\r\n    { name: \"Nebraska\", abbreviation: \"NE\" },\r\n    { name: \"Nevada\", abbreviation: \"NV\" },\r\n    { name: \"New Hampshire\", abbreviation: \"NH\" },\r\n    { name: \"New Jersey\", abbreviation: \"NJ\" },\r\n    { name: \"New Mexico\", abbreviation: \"NM\" },\r\n    { name: \"New York\", abbreviation: \"NY\" },\r\n    { name: \"North Carolina\", abbreviation: \"NC\" },\r\n    { name: \"North Dakota\", abbreviation: \"ND\" },\r\n    { name: \"Ohio\", abbreviation: \"OH\" },\r\n    { name: \"Oklahoma\", abbreviation: \"OK\" },\r\n    { name: \"Oregon\", abbreviation: \"OR\" },\r\n    { name: \"Pennsylvania\", abbreviation: \"PA\" },\r\n    { name: \"Rhode Island\", abbreviation: \"RI\" },\r\n    { name: \"South Carolina\", abbreviation: \"SC\" },\r\n    { name: \"South Dakota\", abbreviation: \"SD\" },\r\n    { name: \"Tennessee\", abbreviation: \"TN\" },\r\n    { name: \"Texas\", abbreviation: \"TX\" },\r\n    { name: \"Utah\", abbreviation: \"UT\" },\r\n    { name: \"Vermont\", abbreviation: \"VT\" },\r\n    { name: \"Virginia\", abbreviation: \"VA\" },\r\n    { name: \"Washington\", abbreviation: \"WA\" },\r\n    { name: \"West Virginia\", abbreviation: \"WV\" },\r\n    { name: \"Wisconsin\", abbreviation: \"WI\" },\r\n    { name: \"Wyoming\", abbreviation: \"WY\" },\r\n  ];\r\n  "], "mappings": "AAAA,OAAO,MAAMA,SAAS,GAAG,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,QAAQ;EAAEC,YAAY,EAAE;AAAI,CAAE,EACtC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,YAAY;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC1C;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,aAAa;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC3C;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,QAAQ;EAAEC,YAAY,EAAE;AAAI,CAAE,EACtC;EAAED,IAAI,EAAE,OAAO;EAAEC,YAAY,EAAE;AAAI,CAAE,EACrC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,MAAM;EAAEC,YAAY,EAAE;AAAI,CAAE,EACpC;EAAED,IAAI,EAAE,QAAQ;EAAEC,YAAY,EAAE;AAAI,CAAE,EACtC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,WAAW;EAAEC,YAAY,EAAE;AAAI,CAAE,EACzC;EAAED,IAAI,EAAE,OAAO;EAAEC,YAAY,EAAE;AAAI,CAAE,EACrC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,eAAe;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC7C;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,WAAW;EAAEC,YAAY,EAAE;AAAI,CAAE,EACzC;EAAED,IAAI,EAAE,aAAa;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC3C;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,QAAQ;EAAEC,YAAY,EAAE;AAAI,CAAE,EACtC;EAAED,IAAI,EAAE,eAAe;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC7C;EAAED,IAAI,EAAE,YAAY;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC1C;EAAED,IAAI,EAAE,YAAY;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC1C;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,gBAAgB;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC9C;EAAED,IAAI,EAAE,cAAc;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC5C;EAAED,IAAI,EAAE,MAAM;EAAEC,YAAY,EAAE;AAAI,CAAE,EACpC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,QAAQ;EAAEC,YAAY,EAAE;AAAI,CAAE,EACtC;EAAED,IAAI,EAAE,cAAc;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC5C;EAAED,IAAI,EAAE,cAAc;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC5C;EAAED,IAAI,EAAE,gBAAgB;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC9C;EAAED,IAAI,EAAE,cAAc;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC5C;EAAED,IAAI,EAAE,WAAW;EAAEC,YAAY,EAAE;AAAI,CAAE,EACzC;EAAED,IAAI,EAAE,OAAO;EAAEC,YAAY,EAAE;AAAI,CAAE,EACrC;EAAED,IAAI,EAAE,MAAM;EAAEC,YAAY,EAAE;AAAI,CAAE,EACpC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,EACvC;EAAED,IAAI,EAAE,UAAU;EAAEC,YAAY,EAAE;AAAI,CAAE,EACxC;EAAED,IAAI,EAAE,YAAY;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC1C;EAAED,IAAI,EAAE,eAAe;EAAEC,YAAY,EAAE;AAAI,CAAE,EAC7C;EAAED,IAAI,EAAE,WAAW;EAAEC,YAAY,EAAE;AAAI,CAAE,EACzC;EAAED,IAAI,EAAE,SAAS;EAAEC,YAAY,EAAE;AAAI,CAAE,CACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}