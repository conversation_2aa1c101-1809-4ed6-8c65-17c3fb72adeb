{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ToastModule } from 'primeng/toast';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { ListboxModule } from 'primeng/listbox';\nimport { DialogModule } from 'primeng/dialog';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { VendorComponent } from './vendor.component';\nimport { MainComponent } from './main/main.component';\nimport { GeneralSettingsComponent } from './general-settings/general-settings.component';\nimport { VendorRoutingModule } from './vendor-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class VendorModule {\n  static {\n    this.ɵfac = function VendorModule_Factory(t) {\n      return new (t || VendorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VendorModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, VendorRoutingModule, ToastModule, FormsModule, TableModule, ButtonModule, ListboxModule, DialogModule, InputTextModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule, RadioButtonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VendorModule, {\n    declarations: [VendorComponent, MainComponent, GeneralSettingsComponent],\n    imports: [CommonModule, VendorRoutingModule, ToastModule, FormsModule, TableModule, ButtonModule, ListboxModule, DialogModule, InputTextModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule, RadioButtonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ToastModule", "FormsModule", "ReactiveFormsModule", "TableModule", "ButtonModule", "InputTextModule", "InputTextareaModule", "DropdownModule", "CheckboxModule", "ListboxModule", "DialogModule", "RadioButtonModule", "MessageService", "ConfirmationService", "VendorComponent", "MainComponent", "GeneralSettingsComponent", "VendorRoutingModule", "VendorModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\vendor\\vendor.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { ListboxModule } from 'primeng/listbox';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { VendorComponent } from './vendor.component';\r\nimport { MainComponent } from './main/main.component';\r\nimport { GeneralSettingsComponent } from './general-settings/general-settings.component';\r\nimport { VendorRoutingModule } from './vendor-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [VendorComponent, MainComponent, GeneralSettingsComponent],\r\n  imports: [\r\n    CommonModule,\r\n    VendorRoutingModule,\r\n    ToastModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    ListboxModule,\r\n    DialogModule,\r\n    InputTextModule,\r\n    InputTextareaModule,\r\n    DropdownModule,\r\n    ReactiveFormsModule,\r\n    CheckboxModule,\r\n    RadioButtonModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class VendorModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,mBAAmB,QAAQ,yBAAyB;;AAsB7D,OAAM,MAAOC,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;iBAFZ,CAACN,cAAc,EAAEC,mBAAmB,CAAC;MAAAM,OAAA,GAf9CpB,YAAY,EACZkB,mBAAmB,EACnBjB,WAAW,EACXC,WAAW,EACXE,WAAW,EACXC,YAAY,EACZK,aAAa,EACbC,YAAY,EACZL,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdL,mBAAmB,EACnBM,cAAc,EACdG,iBAAiB;IAAA;EAAA;;;2EAIRO,YAAY;IAAAE,YAAA,GAnBRN,eAAe,EAAEC,aAAa,EAAEC,wBAAwB;IAAAG,OAAA,GAErEpB,YAAY,EACZkB,mBAAmB,EACnBjB,WAAW,EACXC,WAAW,EACXE,WAAW,EACXC,YAAY,EACZK,aAAa,EACbC,YAAY,EACZL,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdL,mBAAmB,EACnBM,cAAc,EACdG,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}