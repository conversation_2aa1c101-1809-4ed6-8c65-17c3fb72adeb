{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PartnerFaxNumberService {\n  constructor(http) {\n    this.http = http;\n  }\n  getFaxNumber(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][ordinal_number][$containsi]', searchTerm).set('filters[$or][1][person][$containsi]', searchTerm).set('filters[$or][2][fax_country][$containsi]', searchTerm).set('filters[$or][3][fax_number][$containsi]', searchTerm).set('filters[$or][4][bp_address_id][$containsi]', searchTerm).set('filters[$or][5][bp_contact_address_id][$containsi]', searchTerm);\n    }\n    if (id) {\n      params = params.set('filters[$or][0][bp_id][$eq]', id);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS_FAX_NUMBER}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function PartnerFaxNumberService_Factory(t) {\n      return new (t || PartnerFaxNumberService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PartnerFaxNumberService,\n      factory: PartnerFaxNumberService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "CMS_APIContstant", "PartnerFaxNumberService", "constructor", "http", "getFaxNumber", "id", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS_FAX_NUMBER", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-fax-number\\partner-fax-number.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PartnerFaxNumberService {\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getFaxNumber(\r\n    id: string,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][ordinal_number][$containsi]', searchTerm)\r\n        .set('filters[$or][1][person][$containsi]', searchTerm)\r\n        .set('filters[$or][2][fax_country][$containsi]', searchTerm)\r\n        .set('filters[$or][3][fax_number][$containsi]', searchTerm)\r\n        .set('filters[$or][4][bp_address_id][$containsi]', searchTerm)\r\n        .set('filters[$or][5][bp_contact_address_id][$containsi]', searchTerm);\r\n    }\r\n    if (id) {\r\n      params = params.set('filters[$or][0][bp_id][$eq]', id);\r\n    }\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS_FAX_NUMBER}`, {\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAG7D,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,uBAAuB;EAClCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,YAAYA,CACVC,EAAU,EACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIZ,UAAU,EAAE,CAC1Ba,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,6CAA6C,EAAEF,UAAU,CAAC,CAC9DE,GAAG,CAAC,qCAAqC,EAAEF,UAAU,CAAC,CACtDE,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC,CAC3DE,GAAG,CAAC,yCAAyC,EAAEF,UAAU,CAAC,CAC1DE,GAAG,CAAC,4CAA4C,EAAEF,UAAU,CAAC,CAC7DE,GAAG,CAAC,oDAAoD,EAAEF,UAAU,CAAC;IAC1E;IACA,IAAIL,EAAE,EAAE;MACNM,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,6BAA6B,EAAEP,EAAE,CAAC;IACxD;IACA,OAAO,IAAI,CAACF,IAAI,CAACa,GAAG,CAAQ,GAAGhB,gBAAgB,CAACiB,mBAAmB,EAAE,EAAE;MACrEN;KACD,CAAC;EACJ;;;uBAjCWV,uBAAuB,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAvBpB,uBAAuB;MAAAqB,OAAA,EAAvBrB,uBAAuB,CAAAsB,IAAA;MAAAC,UAAA,EAFtB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}