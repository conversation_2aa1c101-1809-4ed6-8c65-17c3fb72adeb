{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Filipino [fil]\n//! author : <PERSON> : https://github.com/hagmandan\n//! author : <PERSON> : https://github.com/matthewdeeco\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var fil = moment.defineLocale('fil', {\n    months: 'Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre'.split('_'),\n    monthsShort: 'Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis'.split('_'),\n    weekdays: 'Linggo_Lunes_Martes_Miyerku<PERSON>_<PERSON><PERSON>_Biyer<PERSON>_Sabado'.split('_'),\n    weekdaysShort: 'Lin_Lun_Mar_Miy_Huw_Biy_Sab'.split('_'),\n    weekdaysMin: 'Li_Lu_Ma_Mi_Hu_Bi_Sab'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'MM/D/YYYY',\n      LL: 'MMMM D, YYYY',\n      LLL: 'MMMM D, YYYY HH:mm',\n      LLLL: 'dddd, MMMM DD, YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: 'LT [ngayong araw]',\n      nextDay: '[Bukas ng] LT',\n      nextWeek: 'LT [sa susunod na] dddd',\n      lastDay: 'LT [kahapon]',\n      lastWeek: 'LT [noong nakaraang] dddd',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'sa loob ng %s',\n      past: '%s ang nakalipas',\n      s: 'ilang segundo',\n      ss: '%d segundo',\n      m: 'isang minuto',\n      mm: '%d minuto',\n      h: 'isang oras',\n      hh: '%d oras',\n      d: 'isang araw',\n      dd: '%d araw',\n      M: 'isang buwan',\n      MM: '%d buwan',\n      y: 'isang taon',\n      yy: '%d taon'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}/,\n    ordinal: function (number) {\n      return number;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fil;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "fil", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/moment/locale/fil.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Filipino [fil]\n//! author : <PERSON> : https://github.com/hagmandan\n//! author : <PERSON> : https://github.com/matthewdeeco\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var fil = moment.defineLocale('fil', {\n        months: 'Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre'.split(\n            '_'\n        ),\n        monthsShort: 'Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis'.split('_'),\n        weekdays: 'Linggo_Lunes_Martes_Miyerku<PERSON>_Hu<PERSON><PERSON>_<PERSON>i<PERSON><PERSON>_Sabado'.split(\n            '_'\n        ),\n        weekdaysShort: 'Lin_Lun_Mar_Miy_Huw_Biy_Sab'.split('_'),\n        weekdaysMin: 'Li_Lu_Ma_Mi_Hu_Bi_Sab'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'MM/D/YYYY',\n            LL: 'MMMM D, YYYY',\n            LLL: 'MMMM D, YYYY HH:mm',\n            LLLL: 'dddd, MMMM DD, YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: 'LT [ngayong araw]',\n            nextDay: '[Bukas ng] LT',\n            nextWeek: 'LT [sa susunod na] dddd',\n            lastDay: 'LT [kahapon]',\n            lastWeek: 'LT [noong nakaraang] dddd',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'sa loob ng %s',\n            past: '%s ang nakalipas',\n            s: 'ilang segundo',\n            ss: '%d segundo',\n            m: 'isang minuto',\n            mm: '%d minuto',\n            h: 'isang oras',\n            hh: '%d oras',\n            d: 'isang araw',\n            dd: '%d araw',\n            M: 'isang buwan',\n            MM: '%d buwan',\n            y: 'isang taon',\n            yy: '%d taon',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}/,\n        ordinal: function (number) {\n            return number;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return fil;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,GAAG,GAAGD,MAAM,CAACE,YAAY,CAAC,KAAK,EAAE;IACjCC,MAAM,EAAE,yFAAyF,CAACC,KAAK,CACnG,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,wDAAwD,CAACF,KAAK,CACpE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,uBAAuB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC/CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,eAAe;MACvBC,IAAI,EAAE,kBAAkB;MACxBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,SAAS;IACjCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OAAOA,MAAM;IACjB,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO1C,GAAG;AAEd,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}