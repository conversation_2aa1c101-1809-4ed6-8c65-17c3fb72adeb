{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport { catchError, of } from \"rxjs\";\nimport { environment } from \"src/environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../forgot-password/forgot-password.component\";\nconst _c0 = () => [\"/auth/signup\"];\nexport class LoginComponent {\n  constructor(fb, auth, router) {\n    this.fb = fb;\n    this.auth = auth;\n    this.router = router;\n    this.API_ENDPOINT = environment.apiEndpoint;\n    this.isSubmitting = false;\n    this.loginForm = this.fb.nonNullable.group({\n      email: [\"\", [Validators.required, Validators.email]],\n      password: [\"\", [Validators.required]],\n      rememberMe: [false]\n    });\n    this.errMsg = null;\n    this.loading = false;\n    this.showPass = false;\n    this.logo = '';\n    this.loginDetails = {};\n    this.privecyPolicy = {};\n    this.termsAndConditions = {};\n    this.copyright = \"\";\n    // forgotPassword() {\n    //   // this.dialog.open(ForgotPasswordComponent);\n    // \n    this.isDialogVisible = false;\n  }\n  ngOnInit() {}\n  get email() {\n    return this.loginForm.get(\"email\");\n  }\n  get password() {\n    return this.loginForm.get(\"password\");\n  }\n  get rememberMe() {\n    return this.loginForm.get(\"rememberMe\");\n  }\n  login() {\n    this.isSubmitting = true;\n    this.auth.login(this.email.value, this.password.value, this.rememberMe.value).pipe(catchError(err => of(err.error))).subscribe(res => {\n      this.isSubmitting = false;\n      if (res.jwt) {\n        this.router.navigate([\"store\"]);\n      } else {\n        this.errMsg = res?.error?.message || 'Error while login';\n      }\n    });\n  }\n  reset() {\n    this.loginForm.reset();\n    this.errMsg = null;\n  }\n  forgotPassword() {\n    this.isDialogVisible = true;\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 53,\n      vars: 14,\n      consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"lg:pt-6\", \"pb-6\", \"md:pt-4\", \"pb-4\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"xl:max-w-15rem\", \"lg:max-w-12rem\", \"max-w-12rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\", \"lg:p-6\", \"md:p-4\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"form-group\", \"relative\", \"mb-3\"], [\"type\", \"email\", \"id\", \"username\", \"formControlName\", \"email\", \"placeholder\", \"Enter Email Address\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [\"formControlName\", \"password\", \"placeholder\", \"Enter Password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\", 3, \"type\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\", 3, \"click\"], [1, \"form-group\", \"relative\", \"mb-4\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"type\", \"checkbox\", \"formControlName\", \"rememberMe\", \"id\", \"exampleCheck1\", 1, \"form-check-box\", \"m-0\", \"w-1rem\", \"h-1rem\"], [\"for\", \"exampleCheck1\", 1, \"text-m\", \"text-gray-700\"], [\"type\", \"button\", 1, \"p-component\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\", \"border-none\", \"bg-white-alpha-10\", \"underline\", \"cursor-pointer\", 3, \"click\"], [1, \"form-footer\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\", 3, \"click\", \"disabled\"], [1, \"mt-3\", \"mb-3\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\"], [1, \"material-symbols-rounded\"], [1, \"copyright-sec\", \"flex\", \"flex-column\", \"position-relative\"], [1, \"m-0\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-0\", \"flex\", \"position-relative\", \"align-items-center\", \"justify-content-center\", \"list-none\", \"gap-3\"], [\"target\", \"_blank\", 1, \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-primary\", \"underline\", 3, \"href\"], [3, \"visibleChange\", \"visible\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtext(7, \" You don't have any account? \");\n          i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n          i0.ɵɵtext(10, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10)(14, \"h1\", 11);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 13);\n          i0.ɵɵelement(19, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 13);\n          i0.ɵɵelement(21, \"input\", 15);\n          i0.ɵɵelementStart(22, \"button\", 16)(23, \"span\", 17);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_23_listener() {\n            return ctx.showPass = !ctx.showPass;\n          });\n          i0.ɵɵtext(24, \"visibility\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19);\n          i0.ɵɵelement(27, \"input\", 20);\n          i0.ɵɵelementStart(28, \"label\", 21);\n          i0.ɵɵtext(29, \"Remember Me\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_30_listener() {\n            return ctx.forgotPassword();\n          });\n          i0.ɵɵtext(31, \"Having Trouble in Login?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 23)(33, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_33_listener() {\n            return ctx.login();\n          });\n          i0.ɵɵtext(34, \" Login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 25)(36, \"span\");\n          i0.ɵɵtext(37, \"Or Login With\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"a\", 26)(39, \"span\", 27);\n          i0.ɵɵtext(40, \"key\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" Login with SSO\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 28)(43, \"p\", 29);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"ul\", 30)(46, \"li\")(47, \"a\", 31);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"li\")(50, \"a\", 31);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"app-forgot-password\", 32);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function LoginComponent_Template_app_forgot_password_visibleChange_52_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.isDialogVisible, $event) || (ctx.isDialogVisible = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.loginDetails.Title, \" Welcome \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.loginDetails.Sub_Title, \" Login in to your account.\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.showPass ? \"text\" : \"password\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"disabled\", !!ctx.loginForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"href\", ctx.API_ENDPOINT + \"/auth/signin\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", ctx.copyright, \" \\u00A9 2024 Consolidated Hospitality Supplies\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"href\", ctx.termsAndConditions.Link, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\"\", ctx.termsAndConditions.Title, \" Terms & Conditions\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"href\", ctx.privecyPolicy.Link, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\"\", ctx.privecyPolicy.Title, \" Privacy Policy\");\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.isDialogVisible);\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i4.ForgotPasswordComponent],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 480px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n\\n@media only screen and (max-width: 996px) {\\n  .login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n    max-width: 380px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2Vzc2lvbi9sb2dpbi9sb2dpbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQztFQUNDLGlCQUFBO0FBQUY7QUFFRTtFQUNDLDJCQUFBO0FBQUg7QUFJSztFQUNDLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtBQUZOO0FBS0s7RUFDQyxpQ0FBQTtBQUhOOztBQVlBO0VBQ0MsWUFBQTtFQUNBLDJCQUFBO0FBVEQ7O0FBWUE7RUFDQyxjQUFBO0FBVEQ7O0FBWUE7RUFHRztJQUNDLDJCQUFBO0VBWEY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5sb2dpbi1zZWMge1xyXG5cdC5sb2dpbi1wYWdlLWJvZHkge1xyXG5cdFx0bWF4LXdpZHRoOiAxNDQwcHg7XHJcblxyXG5cdFx0LmxvZ2luLWZvcm0ge1xyXG5cdFx0XHRtYXgtd2lkdGg6IDQ4MHB4ICFpbXBvcnRhbnQ7XHJcblxyXG5cdFx0XHRmb3JtIHtcclxuXHRcdFx0XHQuZm9ybS1ncm91cCB7XHJcblx0XHRcdFx0XHQucGFzcy1zaG93LWJ0biB7XHJcblx0XHRcdFx0XHRcdHJpZ2h0OiAxMnB4O1xyXG5cdFx0XHRcdFx0XHRoZWlnaHQ6IDI0cHg7XHJcblx0XHRcdFx0XHRcdHdpZHRoOiAyNHB4O1xyXG5cdFx0XHRcdFx0fVxyXG5cclxuXHRcdFx0XHRcdC5mb3JtLWNoZWNrLWJveCB7XHJcblx0XHRcdFx0XHRcdGFjY2VudC1jb2xvcjogdmFyKC0tcHJpbWFyeWNvbG9yKTtcclxuXHRcdFx0XHRcdH1cclxuXHRcdFx0XHR9XHJcblx0XHRcdH1cclxuXHRcdH1cclxuXHR9XHJcbn1cclxuXHJcblxyXG4ucC1pbnB1dHRleHQge1xyXG5cdGhlaWdodDogM3JlbTtcclxuXHRhcHBlYXJhbmNlOiBhdXRvICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5oLTMtM3JlbSB7XHJcblx0aGVpZ2h0OiAzLjNyZW07XHJcbn1cclxuXHJcbkBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDo5OTZweCkge1xyXG5cdC5sb2dpbi1zZWMge1xyXG5cdFx0LmxvZ2luLXBhZ2UtYm9keSB7XHJcblx0XHRcdC5sb2dpbi1mb3JtIHtcclxuXHRcdFx0XHRtYXgtd2lkdGg6IDM4MHB4ICFpbXBvcnRhbnQ7XHJcblx0XHRcdH1cclxuXHRcdH1cclxuXHR9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "catchError", "of", "environment", "LoginComponent", "constructor", "fb", "auth", "router", "API_ENDPOINT", "apiEndpoint", "isSubmitting", "loginForm", "nonNullable", "group", "email", "required", "password", "rememberMe", "errMsg", "loading", "showPass", "logo", "loginDetails", "privecyPolicy", "termsAndConditions", "copyright", "isDialogVisible", "ngOnInit", "get", "login", "value", "pipe", "err", "error", "subscribe", "res", "jwt", "navigate", "message", "reset", "forgotPassword", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_span_click_23_listener", "LoginComponent_Template_button_click_30_listener", "LoginComponent_Template_button_click_33_listener", "ɵɵtwoWayListener", "LoginComponent_Template_app_forgot_password_visibleChange_52_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "Title", "Sub_Title", "invalid", "Link", "ɵɵsanitizeUrl", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\login\\login.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\r\nimport { FormBuilder, Validators } from \"@angular/forms\";\r\nimport { Router } from \"@angular/router\";\r\nimport { catchError, of } from \"rxjs\";\r\nimport { ForgotPasswordComponent } from \"../forgot-password/forgot-password.component\";\r\nimport { environment } from \"src/environments/environment\";\r\nimport { AuthService } from \"src/app/core/authentication/auth.service\";\r\n\r\n\r\n@Component({\r\n  selector: \"app-login\",\r\n  templateUrl: \"./login.component.html\",\r\n  styleUrls: [\"./login.component.scss\"],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  public API_ENDPOINT: any = environment.apiEndpoint;\r\n  public isSubmitting = false;\r\n  public loginForm = this.fb.nonNullable.group({\r\n    email: [\"\", [Validators.required, Validators.email]],\r\n    password: [\"\", [Validators.required]],\r\n    rememberMe: [false],\r\n  });\r\n  public errMsg: any = null;\r\n  loading = false;\r\n  loginPageDetails!: any;\r\n  showPass = false;\r\n  logo = '';\r\n  loginDetails: any = {};\r\n  privecyPolicy: any = {};\r\n  termsAndConditions: any = {};\r\n  copyright = \"\";\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private auth: AuthService,\r\n    public router: Router,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n  }\r\n\r\n  get email() {\r\n    return this.loginForm.get(\"email\")!;\r\n  }\r\n\r\n  get password() {\r\n    return this.loginForm.get(\"password\")!;\r\n  }\r\n\r\n  get rememberMe() {\r\n    return this.loginForm.get(\"rememberMe\")!;\r\n  }\r\n\r\n  login() {\r\n    this.isSubmitting = true;\r\n    this.auth\r\n      .login(this.email.value, this.password.value, this.rememberMe.value)\r\n      .pipe(catchError((err) => of(err.error)))\r\n      .subscribe((res: any) => {\r\n        this.isSubmitting = false;\r\n        if (res.jwt) {\r\n          this.router.navigate([\"store\"]);\r\n        } else {\r\n          this.errMsg = res?.error?.message || 'Error while login';\r\n        }\r\n      });\r\n  }\r\n\r\n  reset() {\r\n    this.loginForm.reset();\r\n    this.errMsg = null;\r\n  }\r\n\r\n  // forgotPassword() {\r\n  //   // this.dialog.open(ForgotPasswordComponent);\r\n  // \r\n  \r\n\r\n  isDialogVisible: boolean = false;\r\n\r\n  forgotPassword() {\r\n    this.isDialogVisible = true;\r\n  }\r\n\r\n}\r\n", "<section class=\"login-sec bg-white h-screen lg:pt-6 pb-6 md:pt-4 pb-4\">\r\n  <div class=\"login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full px-5\">\r\n    <div class=\"login-header relative flex align-items-center justify-content-between\">\r\n      <div class=\"logo w-full xl:max-w-15rem lg:max-w-12rem max-w-12rem\">\r\n        <a href=\"#\" class=\"flex w-full\"><img src=\"/assets/layout/images/chs-logo.svg\" alt=\"Logo\" class=\"w-full\" /></a>\r\n      </div>\r\n      <div class=\"sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium\">\r\n        You don't have any account?\r\n        <button type=\"button\" [routerLink]=\"['/auth/signup']\"\r\n          class=\"sign-up-btn p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1\">\r\n          <span class=\"material-symbols-rounded text-2xl\">person</span> Sign Up\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"login-form mx-auto w-full bg-white border-round-3xl shadow-2 lg:p-6 md:p-4\">\r\n      <form class=\"flex flex-column position-relative\" [formGroup]=\"loginForm\">\r\n        <h1 class=\"mb-2 flex justify-content-center text-4xl font-bold text-primary\">{{ loginDetails.Title }} Welcome\r\n        </h1>\r\n        <p class=\"mb-5 flex justify-content-center text-base font-medium text-gray-900\">{{ loginDetails.Sub_Title }}\r\n          Login\r\n          in to your account.</p>\r\n        <div class=\"form-group relative mb-3\">\r\n          <input type=\"email\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\"\r\n            formControlName=\"email\" placeholder=\"Enter Email Address\" />\r\n        </div>\r\n        <div class=\"form-group relative mb-3\">\r\n          <input [type]=\"showPass ? 'text' : 'password'\" class=\"p-inputtext p-component p-element w-full bg-gray-50\"\r\n            formControlName=\"password\" placeholder=\"Enter Password\" />\r\n          <button type=\"button\"\r\n            class=\"pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer\"><span\r\n              class=\"material-symbols-rounded\" (click)=\"showPass = !showPass\">visibility</span></button>\r\n        </div>\r\n        <div class=\"form-group relative mb-4 flex align-items-center justify-content-between\">\r\n          <div class=\"flex align-items-center gap-1\">\r\n            <input type=\"checkbox\" class=\"form-check-box m-0 w-1rem h-1rem\" formControlName=\"rememberMe\"\r\n              id=\"exampleCheck1\" />\r\n            <label class=\"text-m text-gray-700\" for=\"exampleCheck1\">Remember Me</label>\r\n          </div>\r\n          <button type=\"button\"\r\n            class=\"p-component flex justify-content-center text-base font-medium text-gray-900 border-none bg-white-alpha-10 underline cursor-pointer\"\r\n            (click)=\"forgotPassword()\">Having Trouble in Login?</button>\r\n        </div>\r\n        <div class=\"form-footer\">\r\n          <button type=\"button\"\r\n            class=\"p-element p-ripple p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold\"\r\n            [disabled]=\"!!loginForm.invalid || isSubmitting\" (click)=\"login()\"> Login</button>\r\n          <div class=\"mt-3 mb-3 flex justify-content-center text-base font-medium text-gray-900\"><span>Or Login\r\n              With</span></div>\r\n          <a type=\"button\"\r\n            class=\"p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20\"\r\n            [attr.href]=\"API_ENDPOINT + '/auth/signin'\"><span class=\"material-symbols-rounded\">key</span> Login with\r\n            SSO</a>\r\n        </div>\r\n\r\n      </form>\r\n    </div>\r\n    <div class=\"copyright-sec flex flex-column position-relative\">\r\n      <p class=\"m-0 flex justify-content-center text-base font-medium text-gray-900\">{{ copyright }} © 2024 Consolidated\r\n        Hospitality Supplies</p>\r\n      <ul class=\"p-0 flex position-relative align-items-center justify-content-center list-none gap-3\">\r\n        <li><a [href]=\"termsAndConditions.Link\" target=\"_blank\"\r\n            class=\"flex justify-content-center text-base font-medium text-primary underline\">{{ termsAndConditions.Title\r\n            }} Terms & Conditions</a></li>\r\n        <li><a [href]=\"privecyPolicy.Link\" target=\"_blank\"\r\n            class=\"flex justify-content-center text-base font-medium text-primary underline\">{{ privecyPolicy.Title }}\r\n            Privacy Policy</a></li>\r\n      </ul>\r\n\r\n      <app-forgot-password [(visible)]=\"isDialogVisible\"></app-forgot-password>\r\n\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAAsBA,UAAU,QAAQ,gBAAgB;AAExD,SAASC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAErC,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;AAS1D,OAAM,MAAOC,cAAc;EAkBzBC,YACUC,EAAe,EACfC,IAAiB,EAClBC,MAAc;IAFb,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,IAAI,GAAJA,IAAI;IACL,KAAAC,MAAM,GAANA,MAAM;IApBR,KAAAC,YAAY,GAAQN,WAAW,CAACO,WAAW;IAC3C,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,SAAS,GAAG,IAAI,CAACN,EAAE,CAACO,WAAW,CAACC,KAAK,CAAC;MAC3CC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAACe,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACgB,QAAQ,CAAC,CAAC;MACrCE,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;IACK,KAAAC,MAAM,GAAQ,IAAI;IACzB,KAAAC,OAAO,GAAG,KAAK;IAEf,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,IAAI,GAAG,EAAE;IACT,KAAAC,YAAY,GAAQ,EAAE;IACtB,KAAAC,aAAa,GAAQ,EAAE;IACvB,KAAAC,kBAAkB,GAAQ,EAAE;IAC5B,KAAAC,SAAS,GAAG,EAAE;IA4Cd;IACA;IACA;IAGA,KAAAC,eAAe,GAAY,KAAK;EA3C5B;EAEJC,QAAQA,CAAA,GAER;EAEA,IAAIb,KAAKA,CAAA;IACP,OAAO,IAAI,CAACH,SAAS,CAACiB,GAAG,CAAC,OAAO,CAAE;EACrC;EAEA,IAAIZ,QAAQA,CAAA;IACV,OAAO,IAAI,CAACL,SAAS,CAACiB,GAAG,CAAC,UAAU,CAAE;EACxC;EAEA,IAAIX,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACN,SAAS,CAACiB,GAAG,CAAC,YAAY,CAAE;EAC1C;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACnB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,IAAI,CACNuB,KAAK,CAAC,IAAI,CAACf,KAAK,CAACgB,KAAK,EAAE,IAAI,CAACd,QAAQ,CAACc,KAAK,EAAE,IAAI,CAACb,UAAU,CAACa,KAAK,CAAC,CACnEC,IAAI,CAAC/B,UAAU,CAAEgC,GAAG,IAAK/B,EAAE,CAAC+B,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC,CACxCC,SAAS,CAAEC,GAAQ,IAAI;MACtB,IAAI,CAACzB,YAAY,GAAG,KAAK;MACzB,IAAIyB,GAAG,CAACC,GAAG,EAAE;QACX,IAAI,CAAC7B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,IAAI,CAACnB,MAAM,GAAGiB,GAAG,EAAEF,KAAK,EAAEK,OAAO,IAAI,mBAAmB;MAC1D;IACF,CAAC,CAAC;EACN;EAEAC,KAAKA,CAAA;IACH,IAAI,CAAC5B,SAAS,CAAC4B,KAAK,EAAE;IACtB,IAAI,CAACrB,MAAM,GAAG,IAAI;EACpB;EASAsB,cAAcA,CAAA;IACZ,IAAI,CAACd,eAAe,GAAG,IAAI;EAC7B;;;uBArEWvB,cAAc,EAAAsC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAd7C,cAAc;MAAA8C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVnBd,EAJR,CAAAgB,cAAA,iBAAuE,aAC+B,aACf,aACd,WACjC;UAAAhB,EAAA,CAAAiB,SAAA,aAA0E;UAC5GjB,EAD4G,CAAAkB,YAAA,EAAI,EAC1G;UACNlB,EAAA,CAAAgB,cAAA,aAA6G;UAC3GhB,EAAA,CAAAmB,MAAA,oCACA;UAEEnB,EAFF,CAAAgB,cAAA,gBACqI,cACnF;UAAAhB,EAAA,CAAAmB,MAAA,cAAM;UAAAnB,EAAA,CAAAkB,YAAA,EAAO;UAAClB,EAAA,CAAAmB,MAAA,iBAChE;UAEJnB,EAFI,CAAAkB,YAAA,EAAS,EACL,EACF;UAGFlB,EAFJ,CAAAgB,cAAA,cAAwF,gBACb,cACM;UAAAhB,EAAA,CAAAmB,MAAA,IAC7E;UAAAnB,EAAA,CAAAkB,YAAA,EAAK;UACLlB,EAAA,CAAAgB,cAAA,aAAgF;UAAAhB,EAAA,CAAAmB,MAAA,IAE3D;UAAAnB,EAAA,CAAAkB,YAAA,EAAI;UACzBlB,EAAA,CAAAgB,cAAA,eAAsC;UACpChB,EAAA,CAAAiB,SAAA,iBAC8D;UAChEjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAgB,cAAA,eAAsC;UACpChB,EAAA,CAAAiB,SAAA,iBAC4D;UAE0DjB,EADtH,CAAAgB,cAAA,kBACsH,gBAClD;UAA/BhB,EAAA,CAAAoB,UAAA,mBAAAC,+CAAA;YAAA,OAAAN,GAAA,CAAApC,QAAA,IAAAoC,GAAA,CAAApC,QAAA;UAAA,EAA8B;UAACqB,EAAA,CAAAmB,MAAA,kBAAU;UAChFnB,EADgF,CAAAkB,YAAA,EAAO,EAAS,EAC1F;UAEJlB,EADF,CAAAgB,cAAA,eAAsF,eACzC;UACzChB,EAAA,CAAAiB,SAAA,iBACuB;UACvBjB,EAAA,CAAAgB,cAAA,iBAAwD;UAAAhB,EAAA,CAAAmB,MAAA,mBAAW;UACrEnB,EADqE,CAAAkB,YAAA,EAAQ,EACvE;UACNlB,EAAA,CAAAgB,cAAA,kBAE6B;UAA3BhB,EAAA,CAAAoB,UAAA,mBAAAE,iDAAA;YAAA,OAASP,GAAA,CAAAhB,cAAA,EAAgB;UAAA,EAAC;UAACC,EAAA,CAAAmB,MAAA,gCAAwB;UACvDnB,EADuD,CAAAkB,YAAA,EAAS,EAC1D;UAEJlB,EADF,CAAAgB,cAAA,eAAyB,kBAG8C;UAAlBhB,EAAA,CAAAoB,UAAA,mBAAAG,iDAAA;YAAA,OAASR,GAAA,CAAA3B,KAAA,EAAO;UAAA,EAAC;UAAEY,EAAA,CAAAmB,MAAA,cAAK;UAAAnB,EAAA,CAAAkB,YAAA,EAAS;UACGlB,EAAvF,CAAAgB,cAAA,eAAuF,YAAM;UAAAhB,EAAA,CAAAmB,MAAA,qBACrF;UAAOnB,EAAP,CAAAkB,YAAA,EAAO,EAAM;UAGyBlB,EAF9C,CAAAgB,cAAA,aAE8C,gBAAuC;UAAAhB,EAAA,CAAAmB,MAAA,WAAG;UAAAnB,EAAA,CAAAkB,YAAA,EAAO;UAAClB,EAAA,CAAAmB,MAAA,uBAC3F;UAIXnB,EAJW,CAAAkB,YAAA,EAAI,EACL,EAED,EACH;UAEJlB,EADF,CAAAgB,cAAA,eAA8D,aACmB;UAAAhB,EAAA,CAAAmB,MAAA,IACzD;UAAAnB,EAAA,CAAAkB,YAAA,EAAI;UAEpBlB,EADN,CAAAgB,cAAA,cAAiG,UAC3F,aACiF;UAAAhB,EAAA,CAAAmB,MAAA,IAC5D;UAAInB,EAAJ,CAAAkB,YAAA,EAAI,EAAK;UAC9BlB,EAAJ,CAAAgB,cAAA,UAAI,aACiF;UAAAhB,EAAA,CAAAmB,MAAA,IACnE;UACpBnB,EADoB,CAAAkB,YAAA,EAAI,EAAK,EACxB;UAELlB,EAAA,CAAAgB,cAAA,+BAAmD;UAA9BhB,EAAA,CAAAwB,gBAAA,2BAAAC,sEAAAC,MAAA;YAAA1B,EAAA,CAAA2B,kBAAA,CAAAZ,GAAA,CAAA9B,eAAA,EAAAyC,MAAA,MAAAX,GAAA,CAAA9B,eAAA,GAAAyC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAIxD1B,EAJyD,CAAAkB,YAAA,EAAsB,EAErE,EACF,EACE;;;UAhEoBlB,EAAA,CAAA4B,SAAA,GAA+B;UAA/B5B,EAAA,CAAA6B,UAAA,eAAA7B,EAAA,CAAA8B,eAAA,KAAAC,GAAA,EAA+B;UAON/B,EAAA,CAAA4B,SAAA,GAAuB;UAAvB5B,EAAA,CAAA6B,UAAA,cAAAd,GAAA,CAAA7C,SAAA,CAAuB;UACO8B,EAAA,CAAA4B,SAAA,GAC7E;UAD6E5B,EAAA,CAAAgC,kBAAA,KAAAjB,GAAA,CAAAlC,YAAA,CAAAoD,KAAA,cAC7E;UACgFjC,EAAA,CAAA4B,SAAA,GAE3D;UAF2D5B,EAAA,CAAAgC,kBAAA,KAAAjB,GAAA,CAAAlC,YAAA,CAAAqD,SAAA,+BAE3D;UAMZlC,EAAA,CAAA4B,SAAA,GAAuC;UAAvC5B,EAAA,CAAA6B,UAAA,SAAAd,GAAA,CAAApC,QAAA,uBAAuC;UAmB5CqB,EAAA,CAAA4B,SAAA,IAAgD;UAAhD5B,EAAA,CAAA6B,UAAA,eAAAd,GAAA,CAAA7C,SAAA,CAAAiE,OAAA,IAAApB,GAAA,CAAA9C,YAAA,CAAgD;UAKhD+B,EAAA,CAAA4B,SAAA,GAA2C;;UAO8B5B,EAAA,CAAA4B,SAAA,GACzD;UADyD5B,EAAA,CAAAgC,kBAAA,KAAAjB,GAAA,CAAA/B,SAAA,mDACzD;UAEbgB,EAAA,CAAA4B,SAAA,GAAgC;UAAhC5B,EAAA,CAAA6B,UAAA,SAAAd,GAAA,CAAAhC,kBAAA,CAAAqD,IAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAgC;UAC8CrC,EAAA,CAAA4B,SAAA,EAC5D;UAD4D5B,EAAA,CAAAgC,kBAAA,KAAAjB,GAAA,CAAAhC,kBAAA,CAAAkD,KAAA,wBAC5D;UAClBjC,EAAA,CAAA4B,SAAA,GAA2B;UAA3B5B,EAAA,CAAA6B,UAAA,SAAAd,GAAA,CAAAjC,aAAA,CAAAsD,IAAA,EAAApC,EAAA,CAAAqC,aAAA,CAA2B;UACmDrC,EAAA,CAAA4B,SAAA,EACnE;UADmE5B,EAAA,CAAAgC,kBAAA,KAAAjB,GAAA,CAAAjC,aAAA,CAAAmD,KAAA,oBACnE;UAGCjC,EAAA,CAAA4B,SAAA,EAA6B;UAA7B5B,EAAA,CAAAsC,gBAAA,YAAAvB,GAAA,CAAA9B,eAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}