{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../partner.service\";\nimport * as i2 from \"./partner-address.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/ripple\";\nconst _c0 = () => [\"bp_address_id\", \"city_name\", \"region\", \"country\", \"postal_code\"];\nfunction PartnerAddressComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function PartnerAddressComponent_ng_template_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵelement(5, \"i\", 15);\n    i0.ɵɵelementStart(6, \"input\", 16, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PartnerAddressComponent_ng_template_4_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.globalSearchTerm, $event) || (ctx_r1.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function PartnerAddressComponent_ng_template_4_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const dt1_r3 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(ctx_r1.onGlobalFilter(dt1_r3, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi pi-fw \", ctx_r1.isExpanded ? \"pi-minus\" : \"pi-plus\", \"\");\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.isExpanded ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.globalSearchTerm);\n  }\n}\nfunction PartnerAddressComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 17);\n    i0.ɵɵelementStart(2, \"th\", 18);\n    i0.ɵɵtext(3, \"Address \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 19);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PartnerAddressComponent_ng_template_6_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"button\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const address_r5 = ctx_r3.$implicit;\n    const expanded_r6 = ctx_r3.expanded;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", address_r5)(\"icon\", expanded_r6 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", address_r5 == null ? null : address_r5.bp_address_id, \" \");\n  }\n}\nfunction PartnerAddressComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PartnerAddressComponent_ng_template_6_tr_0_Template, 5, 3, \"tr\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.addressdetails == null ? null : ctx_r1.addressdetails.length) > 0);\n  }\n}\nfunction PartnerAddressComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 17);\n    i0.ɵɵelementStart(2, \"td\", 22)(3, \"div\", 23)(4, \"div\", 24)(5, \"span\", 25);\n    i0.ɵɵtext(6, \"Address ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 26);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"span\", 25);\n    i0.ɵɵtext(11, \"House No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 26);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 24)(15, \"span\", 25);\n    i0.ɵɵtext(16, \"Street Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 26);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 24)(20, \"span\", 25);\n    i0.ɵɵtext(21, \"Town Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 26);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 24)(25, \"span\", 25);\n    i0.ɵɵtext(26, \"District\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 26);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 24)(30, \"span\", 25);\n    i0.ɵɵtext(31, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 26);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 24)(35, \"span\", 25);\n    i0.ɵɵtext(36, \"Region\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 26);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 24)(40, \"span\", 25);\n    i0.ɵɵtext(41, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 26);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 24)(45, \"span\", 25);\n    i0.ɵɵtext(46, \"Postal Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 26);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 24)(50, \"span\", 25);\n    i0.ɵɵtext(51, \"UUID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 26);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 24)(55, \"span\", 25);\n    i0.ɵɵtext(56, \"Partner ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 26);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const address_r7 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.bp_address_id) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.house_number) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.street_name) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.township_name) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.district) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.city_name) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.region) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.country) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.postal_code) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.uuid) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r7 == null ? null : address_r7.bp_id) || \"-\", \" \");\n  }\n}\nfunction PartnerAddressComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27);\n    i0.ɵɵtext(2, \" Address details are not available for this record. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class PartnerAddressComponent {\n  constructor(partnerservice, partneraddressservice) {\n    this.partnerservice = partnerservice;\n    this.partneraddressservice = partneraddressservice;\n    this.unsubscribe$ = new Subject();\n    this.addressdetails = null;\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.globalSearchTerm = '';\n    this.totalRecords = 0;\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.partnerservice.partner.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.addressdetails = data?.addresses;\n    });\n  }\n  expandAll() {\n    if (!this.isExpanded) {\n      this.addressdetails.forEach(address => address?.id ? this.expandedRows[address.id] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n    this.isExpanded = !this.isExpanded;\n  }\n  loadAddress(event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      _this.partneraddressservice.getAddress(page, pageSize, sortField, sortOrder, _this.globalSearchTerm).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: response => {\n          _this.addressdetails = response?.data || [];\n          _this.totalRecords = response?.meta?.pagination.total;\n          _this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching Address', error);\n          _this.loading = false;\n        }\n      });\n    })();\n  }\n  onGlobalFilter(table, event) {\n    this.loadAddress({\n      first: 0,\n      rows: 10\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function PartnerAddressComponent_Factory(t) {\n      return new (t || PartnerAddressComponent)(i0.ɵɵdirectiveInject(i1.PartnerService), i0.ɵɵdirectiveInject(i2.PartnerAddressService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PartnerAddressComponent,\n      selectors: [[\"app-partner-address\"]],\n      decls: 9,\n      vars: 4,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"globalFilterFields\", \"expandedRowKeys\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pTemplate\", \"emptymessage\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"gap-2\", \"justify-content-between\"], [\"pButton\", \"\", 3, \"click\", \"icon\", \"label\"], [1, \"flex\", \"table-header\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Address\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"bp_address_id\"], [\"field\", \"bp_address_id\"], [4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pRowToggler\", \"icon\"], [\"colspan\", \"2\"], [1, \"grid\", \"mx-0\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"colspan\", \"6\"]],\n      template: function PartnerAddressComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"p-table\", 4, 0);\n          i0.ɵɵtemplate(4, PartnerAddressComponent_ng_template_4_Template, 8, 4, \"ng-template\", 5)(5, PartnerAddressComponent_ng_template_5_Template, 5, 0, \"ng-template\", 6)(6, PartnerAddressComponent_ng_template_6_Template, 1, 1, \"ng-template\", 7)(7, PartnerAddressComponent_ng_template_7_Template, 59, 11, \"ng-template\", 8)(8, PartnerAddressComponent_ng_template_8_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.addressdetails)(\"globalFilterFields\", i0.ɵɵpureFunction0(3, _c0))(\"expandedRowKeys\", ctx.expandedRows);\n        }\n      },\n      dependencies: [i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.RowToggler, i5.SortIcon, i7.ButtonDirective, i8.InputText, i9.Ripple],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "PartnerAddressComponent_ng_template_4_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "expandAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵtwoWayListener", "PartnerAddressComponent_ng_template_4_Template_input_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "globalSearchTerm", "PartnerAddressComponent_ng_template_4_Template_input_input_6_listener", "dt1_r3", "ɵɵreference", "onGlobalFilter", "ɵɵadvance", "ɵɵpropertyInterpolate1", "isExpanded", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "ɵɵtext", "ɵɵproperty", "address_r5", "expanded_r6", "ɵɵtextInterpolate1", "bp_address_id", "ɵɵtemplate", "PartnerAddressComponent_ng_template_6_tr_0_Template", "addressdetails", "length", "address_r7", "house_number", "street_name", "township_name", "district", "city_name", "region", "country", "postal_code", "uuid", "bp_id", "PartnerAddressComponent", "constructor", "partnerservice", "partneraddressservice", "unsubscribe$", "expandedRows", "totalRecords", "loading", "ngOnInit", "partner", "pipe", "subscribe", "data", "addresses", "for<PERSON>ach", "address", "id", "loadAddress", "event", "_this", "_asyncToGenerator", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "get<PERSON><PERSON><PERSON>", "next", "response", "meta", "pagination", "total", "error", "console", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "PartnerService", "i2", "PartnerAddressService", "selectors", "decls", "vars", "consts", "template", "PartnerAddressComponent_Template", "rf", "ctx", "PartnerAddressComponent_ng_template_4_Template", "PartnerAddressComponent_ng_template_5_Template", "PartnerAddressComponent_ng_template_6_Template", "PartnerAddressComponent_ng_template_7_Template", "PartnerAddressComponent_ng_template_8_Template", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-address\\partner-address.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-address\\partner-address.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { Table } from 'primeng/table';\r\nimport { PartnerService } from '../../partner.service';\r\nimport { PartnerAddressService } from './partner-address.service';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-partner-address',\r\n  templateUrl: './partner-address.component.html',\r\n  styleUrl: './partner-address.component.scss',\r\n})\r\nexport class PartnerAddressComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public addressdetails: any = null;\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public globalSearchTerm: string = '';\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n\r\n  constructor(\r\n    private partnerservice: PartnerService,\r\n    private partneraddressservice: PartnerAddressService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.partnerservice.partner\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.addressdetails = data?.addresses;\r\n      });\r\n  }\r\n\r\n  expandAll() {\r\n    if (!this.isExpanded) {\r\n      this.addressdetails.forEach((address: any) =>\r\n        address?.id ? (this.expandedRows[address.id] = true) : ''\r\n      );\r\n    } else {\r\n      this.expandedRows = {};\r\n    }\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  async loadAddress(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.partneraddressservice\r\n      .getAddress(page, pageSize, sortField, sortOrder, this.globalSearchTerm)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.addressdetails = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching Address', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadAddress({ first: 0, rows: 10 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12\">\r\n    <div class=\"card\">\r\n        <p-table #dt1 [value]=\"addressdetails\" dataKey=\"id\"\r\n            [globalFilterFields]=\"['bp_address_id','city_name','region','country','postal_code']\"\r\n            [expandedRowKeys]=\"expandedRows\" responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"caption\">\r\n                <div class=\"flex justify-content-between flex-column sm:flex-row gap-2\">\r\n                    <div class=\"flex flex-row gap-2 justify-content-between\">\r\n                        <button pButton icon=\"pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}\"\r\n                            label=\"{{ isExpanded ? 'Collapse All' : 'Expand All' }}\" (click)=\"expandAll()\"></button>\r\n                        <div class=\"flex table-header\"></div>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                            (input)=\"onGlobalFilter(dt1, $event)\" placeholder=\"Search Address\" class=\"w-full\" />\r\n                    </span>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"width: 3rem\"></th>\r\n                    <th pSortableColumn=\"bp_address_id\">Address <p-sortIcon field=\"bp_address_id\"></p-sortIcon></th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-address let-expanded=\"expanded\">\r\n                <tr *ngIf=\"addressdetails?.length > 0\">\r\n                    <td>\r\n                        <button type=\"button\" pButton pRipple [pRowToggler]=\"address\"\r\n                            class=\"p-button-text p-button-rounded p-button-plain\"\r\n                            [icon]=\"expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\"></button>\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.bp_address_id }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"rowexpansion\" let-address>\r\n                <tr>\r\n                    <td style=\"width: 3rem\"></td>\r\n                    <td colspan=\"2\">\r\n                        <div class=\"grid mx-0\">\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Address ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.bp_address_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">House No</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.house_number || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Street Name</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.street_name || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Town Name</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.township_name || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">District</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.district || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">City</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.city_name || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Region</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.region || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Country</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.country || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Postal Code</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.postal_code || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">UUID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.uuid || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Partner ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.bp_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">\r\n                        Address details are not available for this record.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";AACA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;ICOjBC,EAFR,CAAAC,cAAA,cAAwE,cACX,iBAE8B;IAAtBD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAS;IAC5FV,EAAA,CAAAW,SAAA,cAAqC;IACzCX,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAW,SAAA,YAA4B;IAC5BX,EAAA,CAAAC,cAAA,mBACwF;IADlDD,EAAA,CAAAY,gBAAA,2BAAAC,8EAAAC,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAe,kBAAA,CAAAT,MAAA,CAAAU,gBAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,gBAAA,GAAAF,MAAA;MAAA,OAAAd,EAAA,CAAAQ,WAAA,CAAAM,MAAA;IAAA,EAA8B;IAChEd,EAAA,CAAAE,UAAA,mBAAAe,sEAAAH,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,MAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,cAAA,CAAAF,MAAA,EAAAJ,MAAA,CAA2B;IAAA,EAAC;IAEjDd,EAHQ,CAAAU,YAAA,EACwF,EACrF,EACL;;;;IATkBV,EAAA,CAAAqB,SAAA,GAAyD;IAAzDrB,EAAA,CAAAsB,sBAAA,sBAAAhB,MAAA,CAAAiB,UAAA,8BAAyD;IACrEvB,EAAA,CAAAwB,qBAAA,UAAAlB,MAAA,CAAAiB,UAAA,iCAAwD;IAKtBvB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAyB,gBAAA,YAAAnB,MAAA,CAAAU,gBAAA,CAA8B;;;;;IAO5EhB,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAC7BX,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAA0B,MAAA,eAAQ;IAAA1B,EAAA,CAAAW,SAAA,qBAA+C;IAC/FX,EAD+F,CAAAU,YAAA,EAAK,EAC/F;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAuC,SAC/B;IACAD,EAAA,CAAAW,SAAA,iBAE8E;IAClFX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IACJ1B,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;;IAPyCV,EAAA,CAAAqB,SAAA,GAAuB;IAEzDrB,EAFkC,CAAA2B,UAAA,gBAAAC,UAAA,CAAuB,SAAAC,WAAA,gDAEO;IAGpE7B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,MAAAF,UAAA,kBAAAA,UAAA,CAAAG,aAAA,MACJ;;;;;IARJ/B,EAAA,CAAAgC,UAAA,IAAAC,mDAAA,iBAAuC;;;;IAAlCjC,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA4B,cAAA,kBAAA5B,MAAA,CAAA4B,cAAA,CAAAC,MAAA,MAAgC;;;;;IAYrCnC,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAIjBX,EAHZ,CAAAC,cAAA,aAAgB,cACW,cACU,eAC+B;IAAAD,EAAA,CAAA0B,MAAA,iBAAU;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACzEV,EAAA,CAAAC,cAAA,eAA8C;IAC1CD,EAAA,CAAA0B,MAAA,GACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,cAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,gBAAQ;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACvEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,mBAAW;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IAC1EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,iBAAS;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACxEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,gBAAQ;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACvEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,YAAI;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACnEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,cAAM;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACrEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,eAAO;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACtEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,mBAAW;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IAC1EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAGFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,YAAI;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACnEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,kBAAU;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACzEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IAIhB1B,EAJgB,CAAAU,YAAA,EAAO,EACL,EACJ,EACL,EACJ;;;;IAlEeV,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAL,aAAA,cACJ;IAKI/B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAC,YAAA,cACJ;IAKIrC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAE,WAAA,cACJ;IAKItC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAG,aAAA,cACJ;IAKIvC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAI,QAAA,cACJ;IAKIxC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAK,SAAA,cACJ;IAKIzC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAM,MAAA,cACJ;IAKI1C,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAO,OAAA,cACJ;IAKI3C,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAQ,WAAA,cACJ;IAMI5C,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAS,IAAA,cACJ;IAKI7C,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAM,UAAA,kBAAAA,UAAA,CAAAU,KAAA,cACJ;;;;;IAQZ9C,EADJ,CAAAC,cAAA,SAAI,aACgB;IACZD,EAAA,CAAA0B,MAAA,2DACJ;IACJ1B,EADI,CAAAU,YAAA,EAAK,EACJ;;;ADxGrB,OAAM,MAAOqC,uBAAuB;EASlCC,YACUC,cAA8B,EAC9BC,qBAA4C;IAD5C,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,qBAAqB,GAArBA,qBAAqB;IAVvB,KAAAC,YAAY,GAAG,IAAIrD,OAAO,EAAQ;IACnC,KAAAoC,cAAc,GAAQ,IAAI;IAC1B,KAAAX,UAAU,GAAY,KAAK;IAC3B,KAAA6B,YAAY,GAAiB,EAAE;IAC/B,KAAApC,gBAAgB,GAAW,EAAE;IAC7B,KAAAqC,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;EAK3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,cAAc,CAACO,OAAO,CACxBC,IAAI,CAAC1D,SAAS,CAAC,IAAI,CAACoD,YAAY,CAAC,CAAC,CAClCO,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAACzB,cAAc,GAAGyB,IAAI,EAAEC,SAAS;IACvC,CAAC,CAAC;EACN;EAEAnD,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACc,UAAU,EAAE;MACpB,IAAI,CAACW,cAAc,CAAC2B,OAAO,CAAEC,OAAY,IACvCA,OAAO,EAAEC,EAAE,GAAI,IAAI,CAACX,YAAY,CAACU,OAAO,CAACC,EAAE,CAAC,GAAG,IAAI,GAAI,EAAE,CAC1D;IACH,CAAC,MAAM;MACL,IAAI,CAACX,YAAY,GAAG,EAAE;IACxB;IACA,IAAI,CAAC7B,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEMyC,WAAWA,CAACC,KAAU;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC1BD,KAAI,CAACZ,OAAO,GAAG,IAAI;MACnB,MAAMc,IAAI,GAAGH,KAAK,CAACI,KAAK,GAAGJ,KAAK,CAACK,IAAI,GAAG,CAAC;MACzC,MAAMC,QAAQ,GAAGN,KAAK,CAACK,IAAI;MAC3B,MAAME,SAAS,GAAGP,KAAK,CAACO,SAAS;MACjC,MAAMC,SAAS,GAAGR,KAAK,CAACQ,SAAS;MAEjCP,KAAI,CAAChB,qBAAqB,CACvBwB,UAAU,CAACN,IAAI,EAAEG,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEP,KAAI,CAAClD,gBAAgB,CAAC,CACvEyC,IAAI,CAAC1D,SAAS,CAACmE,KAAI,CAACf,YAAY,CAAC,CAAC,CAClCO,SAAS,CAAC;QACTiB,IAAI,EAAGC,QAAa,IAAI;UACtBV,KAAI,CAAChC,cAAc,GAAG0C,QAAQ,EAAEjB,IAAI,IAAI,EAAE;UAC1CO,KAAI,CAACb,YAAY,GAAGuB,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;UACpDb,KAAI,CAACZ,OAAO,GAAG,KAAK;QACtB,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9Cd,KAAI,CAACZ,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;IAAC;EACP;EAEAlC,cAAcA,CAAC8D,KAAY,EAAEjB,KAAY;IACvC,IAAI,CAACD,WAAW,CAAC;MAAEK,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC1C;EAEAa,WAAWA,CAAA;IACT,IAAI,CAAChC,YAAY,CAACwB,IAAI,EAAE;IACxB,IAAI,CAACxB,YAAY,CAACiC,QAAQ,EAAE;EAC9B;;;uBA/DWrC,uBAAuB,EAAA/C,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAvB1C,uBAAuB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb5BhG,EAFR,CAAAC,cAAA,aAAoB,aACE,oBAGiD;UA8G3DD,EA7GA,CAAAgC,UAAA,IAAAkE,8CAAA,yBAAiC,IAAAC,8CAAA,yBAeD,IAAAC,8CAAA,yBAMkC,IAAAC,8CAAA,2BAYhB,IAAAC,8CAAA,yBA4EZ;UASlDtG,EAFQ,CAAAU,YAAA,EAAU,EACR,EACJ;;;UAzHgBV,EAAA,CAAAqB,SAAA,GAAwB;UAElCrB,EAFU,CAAA2B,UAAA,UAAAsE,GAAA,CAAA/D,cAAA,CAAwB,uBAAAlC,EAAA,CAAAuG,eAAA,IAAAC,GAAA,EACmD,oBAAAP,GAAA,CAAA7C,YAAA,CACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}