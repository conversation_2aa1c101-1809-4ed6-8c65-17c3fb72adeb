{"ast": null, "code": "import { environment } from '../../environments/environment';\nexport const ENDPOINT = {\n  NODE: environment.apiEndpoint,\n  CMS: environment.cmsApiEndpoint\n};\nexport const ApiConstant = {\n  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,\n  ADMIN_USER_DETAILS: `${environment.apiEndpoint}/api/admin-users`,\n  ADMIN_USER_ROLES: `${environment.apiEndpoint}/api/admin-users/manageable-roles`,\n  INVOICE: `${environment.apiEndpoint}/api/invoices`,\n  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,\n  ORDER_HISTORY: `${environment.apiEndpoint}/api/sales-orders`,\n  GET_SALES_PRICE: `${environment.apiEndpoint}/api/sales-product/sales-price`,\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/api/sales-orders/simulation`,\n  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\n  USERS: `${environment.apiEndpoint}/users`,\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\n  PARTNERS: `${environment.apiEndpoint}/business-partners`,\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\n  TICKETS: `${environment.apiEndpoint}/tickets`,\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\n  RETURN_ORDER: `${environment.apiEndpoint}/return-order`,\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\n  BANNER: `${environment.apiEndpoint}/banner`\n};\nexport const CMS_APIContstant = {\n  CMSAPI_END_POINT: `${environment.cmsApiEndpoint}/api`,\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\n  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\n  USER_VENDOR: `${environment.cmsApiEndpoint}/api/user-vendors`,\n  USER_CART_ITEMS: `${environment.cmsApiEndpoint}/api/cart-items`,\n  SAVED_CART: `${environment.cmsApiEndpoint}/api/cart-reserves`,\n  SAVED_CART_ITEMS: `${environment.cmsApiEndpoint}/api/cart-reserve-items`,\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/user-vendor/forgot-password`,\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\n  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,\n  PARTNERS_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\n  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,\n  PARTNERS_INTERNATIONAL_ADDRESS: `${environment.cmsApiEndpoint}/api/bp-intl-address-versions`,\n  PARTNERS_ROLE: `${environment.cmsApiEndpoint}/api/business-partner-roles`,\n  PARTNERS_BANK: `${environment.cmsApiEndpoint}/api/business-partner-banks`,\n  PARTNERS_PAYMENT_CARD: `${environment.cmsApiEndpoint}/api/business-partner-payment-cards`,\n  PARTNERS_RELATIONSHIP: `${environment.cmsApiEndpoint}/api/business-partner-relationships`,\n  PARTNERS_ADDRESS_USAGE: `${environment.cmsApiEndpoint}/api/bp-address-usages`,\n  PARTNERS_ADDRESS_LOC_NUMBER: `${environment.cmsApiEndpoint}/api/bp-addr-depdnt-intl-loc-numbers`,\n  PARTNERS_EMAIL_ADDRESS: `${environment.cmsApiEndpoint}/api/bp-email-addresses`,\n  PARTNERS_FAX_NUMBER: `${environment.cmsApiEndpoint}/api/bp-fax-numbers`,\n  PARTNERS_HOME_PAGE_URL: `${environment.cmsApiEndpoint}/api/bp-home-page-urls`,\n  PARTNERS_PHONE_NUMBER: `${environment.cmsApiEndpoint}/api/bp-phone-numbers`,\n  CUSTOMER_COMPANIES: `${environment.cmsApiEndpoint}/api/customer-companies`,\n  CUSTOMER_TEXT: `${environment.cmsApiEndpoint}/api/customer-texts`,\n  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\n  CUSTOMER_SALES_AREA: `${environment.cmsApiEndpoint}/api/customer-sales-areas`,\n  CUSTOMER_TAX_GROUPINGS: `${environment.cmsApiEndpoint}/api/customer-tax-groupings`,\n  CUSTOMER_ADDRESS_DEPENDENT: `${environment.cmsApiEndpoint}/api/cust-addr-depdnt-informations`,\n  SUPPLIERS: `${environment.cmsApiEndpoint}/api/suppliers`,\n  SUPPLIER_COMPANY: `${environment.cmsApiEndpoint}/api/supplier-companies`,\n  SUPPLIER_COMPANY_TEXTS: `${environment.cmsApiEndpoint}/api/supplier-company-texts`,\n  SUPPLIER_TEXTS: `${environment.cmsApiEndpoint}/api/supplier-texts`,\n  SUPPLIER_PURCHASING_ORGANIZATIONS: `${environment.cmsApiEndpoint}/api/supplier-purchasing-orgs`,\n  SUPPLIER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/supplier-partner-funcs`,\n  SCHEDULERS: `${environment.cmsApiEndpoint}/api/schedulers`,\n  GET_ALL_PRODUCTS: `${environment.cmsApiEndpoint}/api/products`,\n  GET_PRODUCT_CHARC_VALUE_TYPES: `${environment.cmsApiEndpoint}/api/product-charc-value-types`,\n  PRODUCT_DESCRIPTION: `${environment.cmsApiEndpoint}/api/product-descriptions`,\n  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,\n  RELATIONSHIP_TYPES: `${environment.cmsApiEndpoint}/api/product-suggestion-types`,\n  SIMILAR_PRODUCTS: `${environment.cmsApiEndpoint}/api/product-suggestions`,\n  PRODUCT_SALES_DELIVERY: `${environment.cmsApiEndpoint}/api/product-sales-deliveries`,\n  PRODUCT_SALES_TAXES: `${environment.cmsApiEndpoint}/api/product-sales-taxes`,\n  PRODUCT_BASIC_TEXTS: `${environment.cmsApiEndpoint}/api/product-basic-texts`,\n  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,\n  CONDITIONS: `${environment.cmsApiEndpoint}/api/conditions`,\n  GET_CATALOGS: `${environment.cmsApiEndpoint}/api/product-catalogs`,\n  CONFIGURATION: `${environment.cmsApiEndpoint}/api/configurations`,\n  PRODUCT_SUGGESTION_TYPES: `${environment.cmsApiEndpoint}/api/product-suggestion-types`,\n  CONTENT_VENDOR: `${environment.cmsApiEndpoint}/api/content-vendors`,\n  SEQURITY_QUESTIONS: `${environment.cmsApiEndpoint}/api/security-questions`,\n  SIGNUP: `${environment.cmsApiEndpoint}/api/user-vendor/registration`\n};\nexport const AppConstant = {\n  SESSION_TIMEOUT: 3600 * 1000,\n  // in MS\n  PRODUCT_IMAGE_FALLBACK: '/assets/images/demo-product.png'\n};\nexport const Permission = {\n  Vendor_Portal: 'Vendor_Portal',\n  Vendor_Portal_Backoffice: 'Vendor_Portal_Backoffice'\n};", "map": {"version": 3, "names": ["environment", "ENDPOINT", "NODE", "apiEndpoint", "CMS", "cmsApiEndpoint", "ApiConstant", "FETCH_TOKEN", "ADMIN_USER_DETAILS", "ADMIN_USER_ROLES", "INVOICE", "SALES_QUOTE", "ORDER_HISTORY", "GET_SALES_PRICE", "SALES_ORDER_SIMULATION", "ADMIN_USERS", "GET_MATERIAL_STOCK", "USERS", "SINGOUT", "PARTNERS", "CUSTOMER_COMPANIES", "CUSTOMER_TEXT", "CUSTOMER_SALES_AREA", "GET_ORDER_STATUSES", "GET_INVOICE_FORM_TYPES", "RELATIONSHIP_TYPES", "CONDITIONS", "SCHEDULED_ORDER_DETAILS", "SALES_ORDER_CREATION", "ADD_SHIPPING_ADDRESS", "CUSTOMERS", "COMPANY_LOGO", "TICKET_LIST", "TICKETS", "TICKET_STATUSES", "RETURN_STATUS", "RETURN_REASON", "REFUND_PROGRESS", "RETURN_ORDER", "NOTIFICATION", "BULK_UPLOAD_STATUS", "CUSTOMER_TEXT_DESCR", "SALES_ORDER_SCHEDULER_CREATION", "SALES_ORDER_SCHEDULER", "BANNER", "CMS_APIContstant", "CMSAPI_END_POINT", "MAIN_MENU_API_DETAILS", "SINGIN", "USER_DETAILS", "USER_ROLES", "USER_CART", "USER_VENDOR", "USER_CART_ITEMS", "SAVED_CART", "SAVED_CART_ITEMS", "RESET_PASSWORD_REQUEST", "RESET_PASSWORD", "GET_CATEGORIES", "PARTNERS_CONTACT", "PARTNERS_ADDRESS", "PARTNERS_INTERNATIONAL_ADDRESS", "PARTNERS_ROLE", "PARTNERS_BANK", "PARTNERS_PAYMENT_CARD", "PARTNERS_RELATIONSHIP", "PARTNERS_ADDRESS_USAGE", "PARTNERS_ADDRESS_LOC_NUMBER", "PARTNERS_EMAIL_ADDRESS", "PARTNERS_FAX_NUMBER", "PARTNERS_HOME_PAGE_URL", "PARTNERS_PHONE_NUMBER", "CUSTOMER_PARTNER_FUNCTION", "CUSTOMER_TAX_GROUPINGS", "CUSTOMER_ADDRESS_DEPENDENT", "SUPPLIERS", "SUPPLIER_COMPANY", "SUPPLIER_COMPANY_TEXTS", "SUPPLIER_TEXTS", "SUPPLIER_PURCHASING_ORGANIZATIONS", "SUPPLIER_PARTNER_FUNCTION", "SCHEDULERS", "GET_ALL_PRODUCTS", "GET_PRODUCT_CHARC_VALUE_TYPES", "PRODUCT_DESCRIPTION", "PRODUCT_MDEIA", "SIMILAR_PRODUCTS", "PRODUCT_SALES_DELIVERY", "PRODUCT_SALES_TAXES", "PRODUCT_BASIC_TEXTS", "SETTINGS", "GET_CATALOGS", "CONFIGURATION", "PRODUCT_SUGGESTION_TYPES", "CONTENT_VENDOR", "SEQURITY_QUESTIONS", "SIGNUP", "AppConstant", "SESSION_TIMEOUT", "PRODUCT_IMAGE_FALLBACK", "Permission", "Vendor_Portal", "Vendor_Portal_Backoffice"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\constants\\api.constants.ts"], "sourcesContent": ["import { environment } from '../../environments/environment';\r\n\r\nexport const ENDPOINT = {\r\n  NODE: environment.apiEndpoint,\r\n  CMS: environment.cmsApiEndpoint,\r\n};\r\n\r\nexport const ApiConstant = {\r\n  FETCH_TOKEN: `${environment.apiEndpoint}/api/auth/token`,\r\n  ADMIN_USER_DETAILS: `${environment.apiEndpoint}/api/admin-users`,\r\n  ADMIN_USER_ROLES: `${environment.apiEndpoint}/api/admin-users/manageable-roles`,\r\n  INVOICE: `${environment.apiEndpoint}/api/invoices`,\r\n  SALES_QUOTE: `${environment.apiEndpoint}/api/sales-quotes`,\r\n  ORDER_HISTORY: `${environment.apiEndpoint}/api/sales-orders`,\r\n  GET_SALES_PRICE: `${environment.apiEndpoint}/api/sales-product/sales-price`,\r\n  SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/api/sales-orders/simulation`,\r\n  ADMIN_USERS: `${environment.apiEndpoint}/api/admin-users`,\r\n\r\n  GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,\r\n  USERS: `${environment.apiEndpoint}/users`,\r\n  SINGOUT: `${environment.apiEndpoint}/auth/logout`,\r\n  PARTNERS: `${environment.apiEndpoint}/business-partners`,\r\n  CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,\r\n  CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,\r\n  CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,\r\n  GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,\r\n  GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,\r\n  RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,\r\n  CONDITIONS: `${environment.apiEndpoint}/conditions`,\r\n  SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,\r\n  ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,\r\n  CUSTOMERS: `${environment.apiEndpoint}/customers`,\r\n  COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,\r\n  TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,\r\n  TICKETS: `${environment.apiEndpoint}/tickets`,\r\n  TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,\r\n  RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,\r\n  RETURN_REASON: `${environment.apiEndpoint}/return-reason`,\r\n  REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,\r\n  RETURN_ORDER: `${environment.apiEndpoint}/return-order`,\r\n  NOTIFICATION: `${environment.apiEndpoint}/notification`,\r\n  BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,\r\n  CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,\r\n  SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,\r\n  BANNER: `${environment.apiEndpoint}/banner`,\r\n};\r\n\r\nexport const CMS_APIContstant = {\r\n  CMSAPI_END_POINT: `${environment.cmsApiEndpoint}/api`,\r\n  MAIN_MENU_API_DETAILS: `${environment.cmsApiEndpoint}/api/menu?pLevel=5`,\r\n  SINGIN: `${environment.cmsApiEndpoint}/api/auth/local`,\r\n  USER_DETAILS: `${environment.cmsApiEndpoint}/api/users`,\r\n  USER_ROLES: `${environment.cmsApiEndpoint}/api/users-permissions/roles`,\r\n  USER_CART: `${environment.cmsApiEndpoint}/api/carts`,\r\n  USER_VENDOR: `${environment.cmsApiEndpoint}/api/user-vendors`,\r\n  USER_CART_ITEMS: `${environment.cmsApiEndpoint}/api/cart-items`,\r\n  SAVED_CART: `${environment.cmsApiEndpoint}/api/cart-reserves`,\r\n  SAVED_CART_ITEMS: `${environment.cmsApiEndpoint}/api/cart-reserve-items`,\r\n  RESET_PASSWORD_REQUEST: `${environment.cmsApiEndpoint}/api/user-vendor/forgot-password`,\r\n  RESET_PASSWORD: `${environment.cmsApiEndpoint}/api/auth/reset-password`,\r\n  CUSTOMERS: `${environment.cmsApiEndpoint}/api/customers`,\r\n  GET_CATEGORIES: `${environment.cmsApiEndpoint}/api/product-categories`,\r\n  PARTNERS: `${environment.cmsApiEndpoint}/api/business-partners`,\r\n  PARTNERS_CONTACT: `${environment.cmsApiEndpoint}/api/business-partner-contacts`,\r\n  PARTNERS_ADDRESS: `${environment.cmsApiEndpoint}/api/business-partner-addresses`,\r\n  PARTNERS_INTERNATIONAL_ADDRESS: `${environment.cmsApiEndpoint}/api/bp-intl-address-versions`,\r\n  PARTNERS_ROLE: `${environment.cmsApiEndpoint}/api/business-partner-roles`,\r\n  PARTNERS_BANK: `${environment.cmsApiEndpoint}/api/business-partner-banks`,\r\n  PARTNERS_PAYMENT_CARD: `${environment.cmsApiEndpoint}/api/business-partner-payment-cards`,\r\n  PARTNERS_RELATIONSHIP: `${environment.cmsApiEndpoint}/api/business-partner-relationships`,\r\n  PARTNERS_ADDRESS_USAGE: `${environment.cmsApiEndpoint}/api/bp-address-usages`,\r\n  PARTNERS_ADDRESS_LOC_NUMBER: `${environment.cmsApiEndpoint}/api/bp-addr-depdnt-intl-loc-numbers`,\r\n  PARTNERS_EMAIL_ADDRESS: `${environment.cmsApiEndpoint}/api/bp-email-addresses`,\r\n  PARTNERS_FAX_NUMBER: `${environment.cmsApiEndpoint}/api/bp-fax-numbers`,\r\n  PARTNERS_HOME_PAGE_URL: `${environment.cmsApiEndpoint}/api/bp-home-page-urls`,\r\n  PARTNERS_PHONE_NUMBER: `${environment.cmsApiEndpoint}/api/bp-phone-numbers`,\r\n  CUSTOMER_COMPANIES: `${environment.cmsApiEndpoint}/api/customer-companies`,\r\n  CUSTOMER_TEXT: `${environment.cmsApiEndpoint}/api/customer-texts`,\r\n  CUSTOMER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/customer-partner-functions`,\r\n  CUSTOMER_SALES_AREA: `${environment.cmsApiEndpoint}/api/customer-sales-areas`,\r\n  CUSTOMER_TAX_GROUPINGS: `${environment.cmsApiEndpoint}/api/customer-tax-groupings`,\r\n  CUSTOMER_ADDRESS_DEPENDENT: `${environment.cmsApiEndpoint}/api/cust-addr-depdnt-informations`,\r\n  SUPPLIERS: `${environment.cmsApiEndpoint}/api/suppliers`,\r\n  SUPPLIER_COMPANY: `${environment.cmsApiEndpoint}/api/supplier-companies`,\r\n  SUPPLIER_COMPANY_TEXTS: `${environment.cmsApiEndpoint}/api/supplier-company-texts`,\r\n  SUPPLIER_TEXTS: `${environment.cmsApiEndpoint}/api/supplier-texts`,\r\n  SUPPLIER_PURCHASING_ORGANIZATIONS: `${environment.cmsApiEndpoint}/api/supplier-purchasing-orgs`,\r\n  SUPPLIER_PARTNER_FUNCTION: `${environment.cmsApiEndpoint}/api/supplier-partner-funcs`,\r\n  SCHEDULERS: `${environment.cmsApiEndpoint}/api/schedulers`,\r\n  GET_ALL_PRODUCTS: `${environment.cmsApiEndpoint}/api/products`,\r\n  GET_PRODUCT_CHARC_VALUE_TYPES: `${environment.cmsApiEndpoint}/api/product-charc-value-types`,\r\n  PRODUCT_DESCRIPTION: `${environment.cmsApiEndpoint}/api/product-descriptions`,\r\n  PRODUCT_MDEIA: `${environment.cmsApiEndpoint}/api/product-medias`,\r\n  RELATIONSHIP_TYPES: `${environment.cmsApiEndpoint}/api/product-suggestion-types`,\r\n  SIMILAR_PRODUCTS: `${environment.cmsApiEndpoint}/api/product-suggestions`,\r\n  PRODUCT_SALES_DELIVERY: `${environment.cmsApiEndpoint}/api/product-sales-deliveries`,\r\n  PRODUCT_SALES_TAXES: `${environment.cmsApiEndpoint}/api/product-sales-taxes`,\r\n  PRODUCT_BASIC_TEXTS: `${environment.cmsApiEndpoint}/api/product-basic-texts`,\r\n  SETTINGS: `${environment.cmsApiEndpoint}/api/settings`,\r\n  CONDITIONS: `${environment.cmsApiEndpoint}/api/conditions`,\r\n  GET_CATALOGS: `${environment.cmsApiEndpoint}/api/product-catalogs`,\r\n  CONFIGURATION: `${environment.cmsApiEndpoint}/api/configurations`,\r\n  PRODUCT_SUGGESTION_TYPES: `${environment.cmsApiEndpoint}/api/product-suggestion-types`,\r\n  CONTENT_VENDOR: `${environment.cmsApiEndpoint}/api/content-vendors`,\r\n  SEQURITY_QUESTIONS: `${environment.cmsApiEndpoint}/api/security-questions`,\r\n  SIGNUP: `${environment.cmsApiEndpoint}/api/user-vendor/registration`,\r\n};\r\n\r\nexport const AppConstant = {\r\n  SESSION_TIMEOUT: 3600 * 1000, // in MS\r\n  PRODUCT_IMAGE_FALLBACK: '/assets/images/demo-product.png',\r\n};\r\n\r\nexport const Permission = {\r\n  Vendor_Portal: 'Vendor_Portal',\r\n  Vendor_Portal_Backoffice: 'Vendor_Portal_Backoffice',\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,gCAAgC;AAE5D,OAAO,MAAMC,QAAQ,GAAG;EACtBC,IAAI,EAAEF,WAAW,CAACG,WAAW;EAC7BC,GAAG,EAAEJ,WAAW,CAACK;CAClB;AAED,OAAO,MAAMC,WAAW,GAAG;EACzBC,WAAW,EAAE,GAAGP,WAAW,CAACG,WAAW,iBAAiB;EACxDK,kBAAkB,EAAE,GAAGR,WAAW,CAACG,WAAW,kBAAkB;EAChEM,gBAAgB,EAAE,GAAGT,WAAW,CAACG,WAAW,mCAAmC;EAC/EO,OAAO,EAAE,GAAGV,WAAW,CAACG,WAAW,eAAe;EAClDQ,WAAW,EAAE,GAAGX,WAAW,CAACG,WAAW,mBAAmB;EAC1DS,aAAa,EAAE,GAAGZ,WAAW,CAACG,WAAW,mBAAmB;EAC5DU,eAAe,EAAE,GAAGb,WAAW,CAACG,WAAW,gCAAgC;EAC3EW,sBAAsB,EAAE,GAAGd,WAAW,CAACG,WAAW,8BAA8B;EAChFY,WAAW,EAAE,GAAGf,WAAW,CAACG,WAAW,kBAAkB;EAEzDa,kBAAkB,EAAE,GAAGhB,WAAW,CAACG,WAAW,+BAA+B;EAC7Ec,KAAK,EAAE,GAAGjB,WAAW,CAACG,WAAW,QAAQ;EACzCe,OAAO,EAAE,GAAGlB,WAAW,CAACG,WAAW,cAAc;EACjDgB,QAAQ,EAAE,GAAGnB,WAAW,CAACG,WAAW,oBAAoB;EACxDiB,kBAAkB,EAAE,GAAGpB,WAAW,CAACG,WAAW,qBAAqB;EACnEkB,aAAa,EAAE,GAAGrB,WAAW,CAACG,WAAW,gBAAgB;EACzDmB,mBAAmB,EAAE,GAAGtB,WAAW,CAACG,WAAW,uBAAuB;EACtEoB,kBAAkB,EAAE,GAAGvB,WAAW,CAACG,WAAW,sBAAsB;EACpEqB,sBAAsB,EAAE,GAAGxB,WAAW,CAACG,WAAW,qBAAqB;EACvEsB,kBAAkB,EAAE,GAAGzB,WAAW,CAACG,WAAW,qBAAqB;EACnEuB,UAAU,EAAE,GAAG1B,WAAW,CAACG,WAAW,aAAa;EACnDwB,uBAAuB,EAAE,GAAG3B,WAAW,CAACG,WAAW,yBAAyB;EAC5EyB,oBAAoB,EAAE,GAAG5B,WAAW,CAACG,WAAW,uBAAuB;EACvE0B,oBAAoB,EAAE,GAAG7B,WAAW,CAACG,WAAW,mCAAmC;EACnF2B,SAAS,EAAE,GAAG9B,WAAW,CAACG,WAAW,YAAY;EACjD4B,YAAY,EAAE,GAAG/B,WAAW,CAACG,WAAW,eAAe;EACvD6B,WAAW,EAAE,GAAGhC,WAAW,CAACG,WAAW,eAAe;EACtD8B,OAAO,EAAE,GAAGjC,WAAW,CAACG,WAAW,UAAU;EAC7C+B,eAAe,EAAE,GAAGlC,WAAW,CAACG,WAAW,kBAAkB;EAC7DgC,aAAa,EAAE,GAAGnC,WAAW,CAACG,WAAW,kBAAkB;EAC3DiC,aAAa,EAAE,GAAGpC,WAAW,CAACG,WAAW,gBAAgB;EACzDkC,eAAe,EAAE,GAAGrC,WAAW,CAACG,WAAW,kBAAkB;EAC7DmC,YAAY,EAAE,GAAGtC,WAAW,CAACG,WAAW,eAAe;EACvDoC,YAAY,EAAE,GAAGvC,WAAW,CAACG,WAAW,eAAe;EACvDqC,kBAAkB,EAAE,GAAGxC,WAAW,CAACG,WAAW,sBAAsB;EACpEsC,mBAAmB,EAAE,GAAGzC,WAAW,CAACG,WAAW,4BAA4B;EAC3EuC,8BAA8B,EAAE,GAAG1C,WAAW,CAACG,WAAW,yBAAyB;EACnFwC,qBAAqB,EAAE,GAAG3C,WAAW,CAACG,WAAW,yBAAyB;EAC1EyC,MAAM,EAAE,GAAG5C,WAAW,CAACG,WAAW;CACnC;AAED,OAAO,MAAM0C,gBAAgB,GAAG;EAC9BC,gBAAgB,EAAE,GAAG9C,WAAW,CAACK,cAAc,MAAM;EACrD0C,qBAAqB,EAAE,GAAG/C,WAAW,CAACK,cAAc,oBAAoB;EACxE2C,MAAM,EAAE,GAAGhD,WAAW,CAACK,cAAc,iBAAiB;EACtD4C,YAAY,EAAE,GAAGjD,WAAW,CAACK,cAAc,YAAY;EACvD6C,UAAU,EAAE,GAAGlD,WAAW,CAACK,cAAc,8BAA8B;EACvE8C,SAAS,EAAE,GAAGnD,WAAW,CAACK,cAAc,YAAY;EACpD+C,WAAW,EAAE,GAAGpD,WAAW,CAACK,cAAc,mBAAmB;EAC7DgD,eAAe,EAAE,GAAGrD,WAAW,CAACK,cAAc,iBAAiB;EAC/DiD,UAAU,EAAE,GAAGtD,WAAW,CAACK,cAAc,oBAAoB;EAC7DkD,gBAAgB,EAAE,GAAGvD,WAAW,CAACK,cAAc,yBAAyB;EACxEmD,sBAAsB,EAAE,GAAGxD,WAAW,CAACK,cAAc,kCAAkC;EACvFoD,cAAc,EAAE,GAAGzD,WAAW,CAACK,cAAc,0BAA0B;EACvEyB,SAAS,EAAE,GAAG9B,WAAW,CAACK,cAAc,gBAAgB;EACxDqD,cAAc,EAAE,GAAG1D,WAAW,CAACK,cAAc,yBAAyB;EACtEc,QAAQ,EAAE,GAAGnB,WAAW,CAACK,cAAc,wBAAwB;EAC/DsD,gBAAgB,EAAE,GAAG3D,WAAW,CAACK,cAAc,gCAAgC;EAC/EuD,gBAAgB,EAAE,GAAG5D,WAAW,CAACK,cAAc,iCAAiC;EAChFwD,8BAA8B,EAAE,GAAG7D,WAAW,CAACK,cAAc,+BAA+B;EAC5FyD,aAAa,EAAE,GAAG9D,WAAW,CAACK,cAAc,6BAA6B;EACzE0D,aAAa,EAAE,GAAG/D,WAAW,CAACK,cAAc,6BAA6B;EACzE2D,qBAAqB,EAAE,GAAGhE,WAAW,CAACK,cAAc,qCAAqC;EACzF4D,qBAAqB,EAAE,GAAGjE,WAAW,CAACK,cAAc,qCAAqC;EACzF6D,sBAAsB,EAAE,GAAGlE,WAAW,CAACK,cAAc,wBAAwB;EAC7E8D,2BAA2B,EAAE,GAAGnE,WAAW,CAACK,cAAc,sCAAsC;EAChG+D,sBAAsB,EAAE,GAAGpE,WAAW,CAACK,cAAc,yBAAyB;EAC9EgE,mBAAmB,EAAE,GAAGrE,WAAW,CAACK,cAAc,qBAAqB;EACvEiE,sBAAsB,EAAE,GAAGtE,WAAW,CAACK,cAAc,wBAAwB;EAC7EkE,qBAAqB,EAAE,GAAGvE,WAAW,CAACK,cAAc,uBAAuB;EAC3Ee,kBAAkB,EAAE,GAAGpB,WAAW,CAACK,cAAc,yBAAyB;EAC1EgB,aAAa,EAAE,GAAGrB,WAAW,CAACK,cAAc,qBAAqB;EACjEmE,yBAAyB,EAAE,GAAGxE,WAAW,CAACK,cAAc,iCAAiC;EACzFiB,mBAAmB,EAAE,GAAGtB,WAAW,CAACK,cAAc,2BAA2B;EAC7EoE,sBAAsB,EAAE,GAAGzE,WAAW,CAACK,cAAc,6BAA6B;EAClFqE,0BAA0B,EAAE,GAAG1E,WAAW,CAACK,cAAc,oCAAoC;EAC7FsE,SAAS,EAAE,GAAG3E,WAAW,CAACK,cAAc,gBAAgB;EACxDuE,gBAAgB,EAAE,GAAG5E,WAAW,CAACK,cAAc,yBAAyB;EACxEwE,sBAAsB,EAAE,GAAG7E,WAAW,CAACK,cAAc,6BAA6B;EAClFyE,cAAc,EAAE,GAAG9E,WAAW,CAACK,cAAc,qBAAqB;EAClE0E,iCAAiC,EAAE,GAAG/E,WAAW,CAACK,cAAc,+BAA+B;EAC/F2E,yBAAyB,EAAE,GAAGhF,WAAW,CAACK,cAAc,6BAA6B;EACrF4E,UAAU,EAAE,GAAGjF,WAAW,CAACK,cAAc,iBAAiB;EAC1D6E,gBAAgB,EAAE,GAAGlF,WAAW,CAACK,cAAc,eAAe;EAC9D8E,6BAA6B,EAAE,GAAGnF,WAAW,CAACK,cAAc,gCAAgC;EAC5F+E,mBAAmB,EAAE,GAAGpF,WAAW,CAACK,cAAc,2BAA2B;EAC7EgF,aAAa,EAAE,GAAGrF,WAAW,CAACK,cAAc,qBAAqB;EACjEoB,kBAAkB,EAAE,GAAGzB,WAAW,CAACK,cAAc,+BAA+B;EAChFiF,gBAAgB,EAAE,GAAGtF,WAAW,CAACK,cAAc,0BAA0B;EACzEkF,sBAAsB,EAAE,GAAGvF,WAAW,CAACK,cAAc,+BAA+B;EACpFmF,mBAAmB,EAAE,GAAGxF,WAAW,CAACK,cAAc,0BAA0B;EAC5EoF,mBAAmB,EAAE,GAAGzF,WAAW,CAACK,cAAc,0BAA0B;EAC5EqF,QAAQ,EAAE,GAAG1F,WAAW,CAACK,cAAc,eAAe;EACtDqB,UAAU,EAAE,GAAG1B,WAAW,CAACK,cAAc,iBAAiB;EAC1DsF,YAAY,EAAE,GAAG3F,WAAW,CAACK,cAAc,uBAAuB;EAClEuF,aAAa,EAAE,GAAG5F,WAAW,CAACK,cAAc,qBAAqB;EACjEwF,wBAAwB,EAAE,GAAG7F,WAAW,CAACK,cAAc,+BAA+B;EACtFyF,cAAc,EAAE,GAAG9F,WAAW,CAACK,cAAc,sBAAsB;EACnE0F,kBAAkB,EAAE,GAAG/F,WAAW,CAACK,cAAc,yBAAyB;EAC1E2F,MAAM,EAAE,GAAGhG,WAAW,CAACK,cAAc;CACtC;AAED,OAAO,MAAM4F,WAAW,GAAG;EACzBC,eAAe,EAAE,IAAI,GAAG,IAAI;EAAE;EAC9BC,sBAAsB,EAAE;CACzB;AAED,OAAO,MAAMC,UAAU,GAAG;EACxBC,aAAa,EAAE,eAAe;EAC9BC,wBAAwB,EAAE;CAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}