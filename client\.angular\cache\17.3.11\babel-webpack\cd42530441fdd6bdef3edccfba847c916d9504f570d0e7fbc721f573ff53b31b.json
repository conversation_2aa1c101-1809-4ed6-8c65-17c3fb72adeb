{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport { StoreComponent } from './store.component';\nimport { contentResolver } from '../core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StoreComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      loadChildren: () => import('./home/<USER>').then(m => m.HomeModule),\n      resolve: {\n        content: contentResolver\n      },\n      data: {\n        breadcrumb: 'Home',\n        slug: 'home'\n      }\n    }, {\n      path: 'vendor-account',\n      data: {\n        breadcrumb: 'Vendor Account'\n      },\n      loadChildren: () => import('./vendor-account/vendor-account.module').then(m => m.VendorAccountModule)\n    }, {\n      path: 'invoice',\n      data: {\n        breadcrumb: 'Invoices'\n      },\n      loadChildren: () => import('./invoice/invoice.module').then(m => m.InvoiceModule)\n    }, {\n      path: 'payment-history',\n      data: {\n        breadcrumb: 'Payment History'\n      },\n      loadChildren: () => import('./payment-history/payment-history.module').then(m => m.PaymentHistoryModule)\n    }, {\n      path: 'payment-details',\n      data: {\n        breadcrumb: 'Payment Details'\n      },\n      loadChildren: () => import('./payment-details/payment-details.module').then(m => m.PaymentDetailsModule)\n    }, {\n      path: 'resource-center',\n      loadChildren: () => import('./resource-center/resource-center.module').then(m => m.ResourceCenterModule),\n      resolve: {\n        content: contentResolver\n      },\n      data: {\n        breadcrumb: 'Resource Center',\n        slug: 'resource-center'\n      }\n    }, {\n      path: 'profile',\n      data: {\n        breadcrumb: 'Profile'\n      },\n      loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class StoreRoutingModule {\n  static {\n    this.ɵfac = function StoreRoutingModule_Factory(t) {\n      return new (t || StoreRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StoreRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StoreRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppLayoutComponent", "StoreComponent", "contentResolver", "routes", "path", "component", "children", "loadChildren", "then", "m", "HomeModule", "resolve", "content", "data", "breadcrumb", "slug", "VendorAccountModule", "InvoiceModule", "PaymentHistoryModule", "PaymentDetailsModule", "ResourceCenterModule", "ProfileModule", "redirectTo", "StoreRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\store-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\nimport { StoreComponent } from './store.component';\r\nimport { contentResolver } from '../core/content-resolver';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: StoreComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            loadChildren: () =>\r\n              import('./home/<USER>').then((m) => m.HomeModule),\r\n            resolve: {\r\n              content: contentResolver,\r\n            },\r\n            data: {\r\n              breadcrumb: 'Home',\r\n              slug: 'home',\r\n            },\r\n          },\r\n          {\r\n            path: 'vendor-account',\r\n            data: { breadcrumb: 'Vendor Account' },\r\n            loadChildren: () =>\r\n              import('./vendor-account/vendor-account.module').then(\r\n                (m) => m.VendorAccountModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'invoice',\r\n            data: { breadcrumb: 'Invoices' },\r\n            loadChildren: () =>\r\n              import('./invoice/invoice.module').then(\r\n                (m) => m.InvoiceModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'payment-history',\r\n            data: { breadcrumb: 'Payment History' },\r\n            loadChildren: () =>\r\n              import('./payment-history/payment-history.module').then(\r\n                (m) => m.PaymentHistoryModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'payment-details',\r\n            data: { breadcrumb: 'Payment Details' },\r\n            loadChildren: () =>\r\n              import('./payment-details/payment-details.module').then(\r\n                (m) => m.PaymentDetailsModule\r\n              ),\r\n          },\r\n          {\r\n            path: 'resource-center',\r\n            loadChildren: () =>\r\n              import('./resource-center/resource-center.module').then(\r\n                (m) => m.ResourceCenterModule\r\n              ),\r\n            resolve: {\r\n              content: contentResolver,\r\n            },\r\n            data: {\r\n              breadcrumb: 'Resource Center',\r\n              slug: 'resource-center',\r\n            },\r\n          },\r\n          {\r\n            path: 'profile',\r\n            data: { breadcrumb: 'Profile' },\r\n            loadChildren: () =>\r\n              import('./profile/profile.module').then(\r\n                (m) => m.ProfileModule\r\n              ),\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class StoreRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,eAAe,QAAQ,0BAA0B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,cAAc;EACzBK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEL,kBAAkB;IAC7BM,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC;MACxDC,OAAO,EAAE;QACPC,OAAO,EAAEV;OACV;MACDW,IAAI,EAAE;QACJC,UAAU,EAAE,MAAM;QAClBC,IAAI,EAAE;;KAET,EACD;MACEX,IAAI,EAAE,gBAAgB;MACtBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAgB,CAAE;MACtCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACO,mBAAmB;KAEjC,EACD;MACEZ,IAAI,EAAE,SAAS;MACfS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAChCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CACpCC,CAAC,IAAKA,CAAC,CAACQ,aAAa;KAE3B,EACD;MACEb,IAAI,EAAE,iBAAiB;MACvBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAiB,CAAE;MACvCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACS,oBAAoB;KAElC,EACD;MACEd,IAAI,EAAE,iBAAiB;MACvBS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAiB,CAAE;MACvCP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACU,oBAAoB;KAElC,EACD;MACEf,IAAI,EAAE,iBAAiB;MACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACW,oBAAoB,CAC9B;MACHT,OAAO,EAAE;QACPC,OAAO,EAAEV;OACV;MACDW,IAAI,EAAE;QACJC,UAAU,EAAE,iBAAiB;QAC7BC,IAAI,EAAE;;KAET,EACD;MACEX,IAAI,EAAE,SAAS;MACfS,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAC/BP,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CACpCC,CAAC,IAAKA,CAAC,CAACY,aAAa;KAE3B;GAEJ,EACD;IAAEjB,IAAI,EAAE,IAAI;IAAEkB,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBxB,YAAY,CAACyB,QAAQ,CAACrB,MAAM,CAAC,EAC7BJ,YAAY;IAAA;EAAA;;;2EAEXwB,kBAAkB;IAAAE,OAAA,GAAAC,EAAA,CAAA3B,YAAA;IAAA4B,OAAA,GAFnB5B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}