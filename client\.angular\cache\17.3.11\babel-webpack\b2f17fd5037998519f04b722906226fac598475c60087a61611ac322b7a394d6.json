{"ast": null, "code": "import { NgSelectModule } from '@ng-select/ng-select';\nimport { CommonModule } from '@angular/common';\nimport { ToastModule } from 'primeng/toast';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ContactComponent } from './contact.component';\nimport { RegisterContactComponent } from './register-contact/register-contact/register-contact.component';\nimport { ContactRoutingModule } from './contact-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class ContactModule {\n  static {\n    this.ɵfac = function ContactModule_Factory(t) {\n      return new (t || ContactModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ContactModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, NgSelectModule, ToastModule, ContactRoutingModule, FormsModule, TableModule, ButtonModule, InputTextModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ContactModule, {\n    declarations: [ContactComponent, RegisterContactComponent],\n    imports: [CommonModule, NgSelectModule, ToastModule, ContactRoutingModule, FormsModule, TableModule, ButtonModule, InputTextModule, InputTextareaModule, DropdownModule, ReactiveFormsModule, CheckboxModule]\n  });\n})();", "map": {"version": 3, "names": ["NgSelectModule", "CommonModule", "ToastModule", "FormsModule", "ReactiveFormsModule", "TableModule", "ButtonModule", "InputTextModule", "InputTextareaModule", "DropdownModule", "CheckboxModule", "MessageService", "ConfirmationService", "ContactComponent", "RegisterContactComponent", "ContactRoutingModule", "ContactModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\contacts\\contact.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { NgSelectModule } from '@ng-select/ng-select';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ContactComponent } from './contact.component';\r\nimport { RegisterContactComponent } from './register-contact/register-contact/register-contact.component';\r\nimport { ContactRoutingModule } from './contact-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [ContactComponent, RegisterContactComponent],\r\n  imports: [\r\n    CommonModule,\r\n    NgSelectModule,\r\n    ToastModule,\r\n    ContactRoutingModule,\r\n    FormsModule,\r\n    TableModule,\r\n    ButtonModule,\r\n    InputTextModule,\r\n    InputTextareaModule,\r\n    DropdownModule,\r\n    ReactiveFormsModule,\r\n    CheckboxModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class ContactModule {}\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,oBAAoB,QAAQ,0BAA0B;;AAoB/D,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBAFb,CAACL,cAAc,EAAEC,mBAAmB,CAAC;MAAAK,OAAA,GAb9ChB,YAAY,EACZD,cAAc,EACdE,WAAW,EACXa,oBAAoB,EACpBZ,WAAW,EACXE,WAAW,EACXC,YAAY,EACZC,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdL,mBAAmB,EACnBM,cAAc;IAAA;EAAA;;;2EAILM,aAAa;IAAAE,YAAA,GAjBTL,gBAAgB,EAAEC,wBAAwB;IAAAG,OAAA,GAEvDhB,YAAY,EACZD,cAAc,EACdE,WAAW,EACXa,oBAAoB,EACpBZ,WAAW,EACXE,WAAW,EACXC,YAAY,EACZC,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdL,mBAAmB,EACnBM,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}