{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CustomerService {\n  constructor(http) {\n    this.http = http;\n    this.customerSubject = new BehaviorSubject(null);\n    this.customer = this.customerSubject.asObservable();\n  }\n  getCustomers(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('populate', 'business_partner').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][customer_id][$containsi]', searchTerm).set('filters[$or][1][customer_name][$containsi]', searchTerm).set('filters[$or][2][bp_id][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  getCustomerByID(custid) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', custid).set('populate[business_partner][populate]', '*').set('populate[supplier][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  getCustomerByIDName(customerdata) {\n    let params = new HttpParams();\n    if (customerdata) {\n      params = params.set('filters[$or][0][customer_id][$containsi]', customerdata).set('filters[$or][1][customer_name][$containsi]', customerdata);\n    }\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function CustomerService_Factory(t) {\n      return new (t || CustomerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CustomerService,\n      factory: CustomerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "CustomerService", "constructor", "http", "customerSubject", "customer", "asObservable", "getCustomers", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "CUSTOMERS", "getCustomerByID", "custid", "getCustomerByIDName", "customerdata", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CustomerService {\r\n  public customerSubject = new BehaviorSubject<any>(null);\r\n  public customer = this.customerSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getCustomers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('populate', 'business_partner')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][customer_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][customer_name][$containsi]', searchTerm)\r\n        .set('filters[$or][2][bp_id][$containsi]', searchTerm);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getCustomerByID(custid: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[bp_id][$eq]', custid)\r\n      .set('populate[business_partner][populate]', '*')\r\n      .set('populate[supplier][populate]', '*');\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, { params });\r\n  }\r\n\r\n  getCustomerByIDName(customerdata: string): Observable<any[]> {\r\n    let params = new HttpParams();\r\n    if (customerdata) {\r\n      params = params\r\n        .set('filters[$or][0][customer_id][$containsi]', customerdata)\r\n        .set('filters[$or][1][customer_name][$containsi]', customerdata);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,eAAe;EAI1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,eAAe,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAChD,KAAAM,QAAQ,GAAG,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAEd;EAEvCC,YAAYA,CACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC1BgB,GAAG,CAAC,UAAU,EAAE,kBAAkB,CAAC,CACnCA,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC,CAC3DE,GAAG,CAAC,4CAA4C,EAAEF,UAAU,CAAC,CAC7DE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;IAC1D;IAEA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,SAAS,EAAE,EAAE;MAC3DN;KACD,CAAC;EACJ;EAEAO,eAAeA,CAACC,MAAc;IAC5B,MAAMR,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC5BgB,GAAG,CAAC,qBAAqB,EAAEO,MAAM,CAAC,CAClCP,GAAG,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAChDA,GAAG,CAAC,8BAA8B,EAAE,GAAG,CAAC;IAE3C,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,SAAS,EAAE,EAAE;MAAEN;IAAM,CAAE,CAAC;EAC1E;EAEAS,mBAAmBA,CAACC,YAAoB;IACtC,IAAIV,MAAM,GAAG,IAAIf,UAAU,EAAE;IAC7B,IAAIyB,YAAY,EAAE;MAChBV,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAES,YAAY,CAAC,CAC7DT,GAAG,CAAC,4CAA4C,EAAES,YAAY,CAAC;IACpE;IAEA,OAAO,IAAI,CAACpB,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,SAAS,EAAE,EAAE;MAC3DN;KACD,CAAC;EACJ;;;uBArDWZ,eAAe,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf1B,eAAe;MAAA2B,OAAA,EAAf3B,eAAe,CAAA4B,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}