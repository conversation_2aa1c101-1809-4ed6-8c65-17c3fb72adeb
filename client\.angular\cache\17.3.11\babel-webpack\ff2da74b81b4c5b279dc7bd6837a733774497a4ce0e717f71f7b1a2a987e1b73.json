{"ast": null, "code": "import { FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/dropdown\";\nconst _c0 = () => [\"/login\"];\nfunction SignupComponent_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", country_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", country_r1.name, \" (\", country_r1.code, \")\");\n  }\n}\nfunction SignupComponent_option_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const state_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", state_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", state_r2.name, \" (\", state_r2.code, \")\");\n  }\n}\nexport class SignupComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.countries = [];\n    this.questions = [];\n    this.passwordVisible = false;\n    this.confirmPasswordVisible = false;\n  }\n  ngOnInit() {\n    this.initializeForm();\n    this.loadSecurityQuestions();\n  }\n  loadSecurityQuestions() {\n    // Example security questions - replace with your actual questions\n    this.questions = [{\n      id: 1,\n      name: \"What is your mother's maiden name?\"\n    }, {\n      id: 2,\n      name: \"What was your first pet's name?\"\n    }, {\n      id: 3,\n      name: \"What city were you born in?\"\n    }, {\n      id: 4,\n      name: \"What is your maternal grandmother's maiden name?\"\n    }];\n  }\n  initializeForm() {\n    this.registrationForm = this.fb.group({\n      firstname: ['', [Validators.required]],\n      lastname: ['', [Validators.required]],\n      username: ['', [Validators.required]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      retypePassword: ['', [Validators.required]],\n      address: ['', [Validators.required]],\n      country: ['', [Validators.required]],\n      city: ['', [Validators.required]],\n      zipcode: ['', [Validators.required]],\n      invoice_ref: [''],\n      purchase_order: [''],\n      vendor_id: ['', [Validators.required]],\n      security_que_1: ['', [Validators.required]],\n      security_que_1_ans: ['', [Validators.required]],\n      security_que_2: ['', [Validators.required]],\n      security_que_2_ans: ['', [Validators.required]]\n    }, {\n      validator: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('retypePassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n  }\n  togglePasswordVisibility(field) {\n    if (field === 'password') {\n      this.passwordVisible = !this.passwordVisible;\n    } else {\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\n    }\n  }\n  onSubmit() {\n    if (this.registrationForm.valid) {\n      const formData = this.registrationForm.value;\n      // Create the submission object matching the required JSON structure\n      const submissionData = {\n        firstname: formData.firstname,\n        lastname: formData.lastname,\n        username: formData.username,\n        email: formData.email,\n        password: formData.password,\n        address: formData.address,\n        country: formData.country,\n        city: formData.city,\n        zipcode: formData.zipcode,\n        invoice_ref: formData.invoice_ref,\n        purchase_order: formData.purchase_order,\n        vendor_id: formData.vendor_id,\n        security_que_1: formData.security_que_1,\n        security_que_1_ans: formData.security_que_1_ans,\n        security_que_2: formData.security_que_2,\n        security_que_2_ans: formData.security_que_2_ans\n      };\n      console.log('Submission data:', submissionData);\n      // Here you would typically call your registration service\n      // this.registrationService.register(submissionData).subscribe(...)\n    } else {\n      this.markFormGroupTouched(this.registrationForm);\n    }\n  }\n  markFormGroupTouched(formGroup) {\n    Object.values(formGroup.controls).forEach(control => {\n      control.markAsTouched();\n      if (control instanceof FormGroup) {\n        this.markFormGroupTouched(control);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 87,\n      vars: 6,\n      consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"pt-6\", \"pb-6\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"max-w-15rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"max-w-30rem\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"ngSubmit\", \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col-6\"], [\"type\", \"text\", \"formControlName\", \"firstname\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"lastname\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"phone_number\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"extension\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [1, \"field\", \"col-12\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"address\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [1, \"field\", \"col-6\"], [\"formControlName\", \"country\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"state\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"city\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"zipcode\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"password\", \"formControlName\", \"retype_password\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"invoice_ref\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"purchase_order\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"formControlName\", \"security_que_1\", \"optionLabel\", \"name\", 1, \"w-full\", \"bg-gray-50\", 3, \"options\"], [\"type\", \"text\", \"formControlName\", \"security_que_1_ans\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"submit\", 1, \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"font-semibold\"], [3, \"ngValue\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtext(7, \" Do you have an account? \");\n          i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n          i0.ɵɵtext(10, \"input\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.submitForm();\n          });\n          i0.ɵɵelementStart(14, \"h1\", 11);\n          i0.ɵɵtext(15, \"Registration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵtext(17, \" Enter your details below to create an account and get started. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14)(20, \"label\");\n          i0.ɵɵtext(21, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"label\");\n          i0.ɵɵtext(25, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"label\");\n          i0.ɵɵtext(29, \"Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 14)(32, \"label\");\n          i0.ɵɵtext(33, \"Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 19)(36, \"label\");\n          i0.ɵɵtext(37, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 19)(40, \"label\");\n          i0.ɵɵtext(41, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 22)(44, \"label\");\n          i0.ɵɵtext(45, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"select\", 23);\n          i0.ɵɵtemplate(47, SignupComponent_option_47_Template, 2, 3, \"option\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 22)(49, \"label\");\n          i0.ɵɵtext(50, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"select\", 25);\n          i0.ɵɵtemplate(52, SignupComponent_option_52_Template, 2, 3, \"option\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 22)(54, \"label\");\n          i0.ɵɵtext(55, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"input\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 22)(58, \"label\");\n          i0.ɵɵtext(59, \"Zip Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(60, \"input\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 22)(62, \"label\");\n          i0.ɵɵtext(63, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 22)(66, \"label\");\n          i0.ɵɵtext(67, \"Retype Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(68, \"input\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 22)(70, \"label\");\n          i0.ɵɵtext(71, \"Invoice Ref #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"input\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 22)(74, \"label\");\n          i0.ɵɵtext(75, \"Purchase Order #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(76, \"input\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 19)(78, \"label\");\n          i0.ɵɵtext(79, \"Security Question 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"p-dropdown\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 19)(82, \"label\");\n          i0.ɵɵtext(83, \"Answer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(84, \"input\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"button\", 34);\n          i0.ɵɵtext(86, \"Register\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.states);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"options\", ctx.questions);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i4.Dropdown],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 600px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2Vzc2lvbi9zaWdudXAvc2lnbnVwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0ksaUJBQUE7QUFBUjtBQUVRO0VBQ0ksMkJBQUE7QUFBWjtBQUlvQjtFQUNJLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtBQUZ4QjtBQUtvQjtFQUNJLGlDQUFBO0FBSHhCOztBQVdBO0VBQ0ksWUFBQTtFQUNBLDJCQUFBO0FBUko7O0FBV0E7RUFDSSxjQUFBO0FBUkoiLCJzb3VyY2VzQ29udGVudCI6WyIubG9naW4tc2VjIHtcclxuICAgIC5sb2dpbi1wYWdlLWJvZHkge1xyXG4gICAgICAgIG1heC13aWR0aDogMTQ0MHB4O1xyXG5cclxuICAgICAgICAubG9naW4tZm9ybSB7XHJcbiAgICAgICAgICAgIG1heC13aWR0aDogNjAwcHggIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAgIGZvcm0ge1xyXG4gICAgICAgICAgICAgICAgLmZvcm0tZ3JvdXAge1xyXG4gICAgICAgICAgICAgICAgICAgIC5wYXNzLXNob3ctYnRuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IDEycHg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMjRweDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDI0cHg7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuZm9ybS1jaGVjay1ib3gge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NlbnQtY29sb3I6IHZhcigtLXByaW1hcnljb2xvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4ucC1pbnB1dHRleHQge1xyXG4gICAgaGVpZ2h0OiAzcmVtO1xyXG4gICAgYXBwZWFyYW5jZTogYXV0byAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uaC0zLTNyZW0ge1xyXG4gICAgaGVpZ2h0OiAzLjNyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FormGroup", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "country_r1", "name", "ɵɵadvance", "ɵɵtextInterpolate2", "code", "state_r2", "SignupComponent", "constructor", "fb", "countries", "questions", "passwordVisible", "confirmPasswordVisible", "ngOnInit", "initializeForm", "loadSecurityQuestions", "id", "registrationForm", "group", "firstname", "required", "lastname", "username", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "retypePassword", "address", "country", "city", "zipcode", "invoice_ref", "purchase_order", "vendor_id", "security_que_1", "security_que_1_ans", "security_que_2", "security_que_2_ans", "validator", "passwordMatchValidator", "form", "get", "confirmPassword", "value", "setErrors", "passwordMismatch", "togglePasswordVisibility", "field", "onSubmit", "valid", "formData", "submissionData", "console", "log", "markFormGroupTouched", "formGroup", "Object", "values", "controls", "for<PERSON>ach", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_13_listener", "submitForm", "ɵɵtemplate", "SignupComponent_option_47_Template", "SignupComponent_option_52_Template", "ɵɵpureFunction0", "_c0", "states"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\signup\\signup.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Form<PERSON><PERSON>, FormBuilder, Valida<PERSON> } from '@angular/forms';\r\n\r\ninterface RegistrationData {\r\n  firstname: string;\r\n  lastname: string;\r\n  username: string;\r\n  email: string;\r\n  password: string;\r\n  address: string;\r\n  country: string;\r\n  city: string;\r\n  zipcode: string;\r\n  invoice_ref: string;\r\n  purchase_order: string;\r\n  vendor_id: string;\r\n  security_que_1: number;\r\n  security_que_1_ans: string;\r\n  security_que_2: number;\r\n  security_que_2_ans: string;\r\n}\r\n@Component({\r\n  selector: 'app-signup',\r\n  templateUrl: './signup.component.html',\r\n  styleUrl: './signup.component.scss'\r\n})\r\nexport class SignupComponent {\r\n  registrationForm!: FormGroup;\r\n  countries: any[] = [];\r\n  questions: any[] = [];\r\n  passwordVisible: boolean = false;\r\n  confirmPasswordVisible: boolean = false;\r\n\r\n  constructor(private fb: FormBuilder) { }\r\n\r\n  ngOnInit() {\r\n    this.initializeForm();\r\n    this.loadSecurityQuestions();\r\n  }\r\n\r\n  loadSecurityQuestions() {\r\n    // Example security questions - replace with your actual questions\r\n    this.questions = [\r\n      { id: 1, name: \"What is your mother's maiden name?\" },\r\n      { id: 2, name: \"What was your first pet's name?\" },\r\n      { id: 3, name: \"What city were you born in?\" },\r\n      { id: 4, name: \"What is your maternal grandmother's maiden name?\" }\r\n    ];\r\n  }\r\n\r\n  initializeForm() {\r\n    this.registrationForm = this.fb.group({\r\n      firstname: ['', [Validators.required]],\r\n      lastname: ['', [Validators.required]],\r\n      username: ['', [Validators.required]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]],\r\n      retypePassword: ['', [Validators.required]],\r\n      address: ['', [Validators.required]],\r\n      country: ['', [Validators.required]],\r\n      city: ['', [Validators.required]],\r\n      zipcode: ['', [Validators.required]],\r\n      invoice_ref: [''],\r\n      purchase_order: [''],\r\n      vendor_id: ['', [Validators.required]],\r\n      security_que_1: ['', [Validators.required]],\r\n      security_que_1_ans: ['', [Validators.required]],\r\n      security_que_2: ['', [Validators.required]],\r\n      security_que_2_ans: ['', [Validators.required]]\r\n    }, {\r\n      validator: this.passwordMatchValidator\r\n    });\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    const password = form.get('password');\r\n    const confirmPassword = form.get('retypePassword');\r\n\r\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\r\n      confirmPassword.setErrors({ passwordMismatch: true });\r\n    } else {\r\n      confirmPassword?.setErrors(null);\r\n    }\r\n  }\r\n\r\n  togglePasswordVisibility(field: 'password' | 'confirmPassword') {\r\n    if (field === 'password') {\r\n      this.passwordVisible = !this.passwordVisible;\r\n    } else {\r\n      this.confirmPasswordVisible = !this.confirmPasswordVisible;\r\n    }\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.registrationForm.valid) {\r\n      const formData = this.registrationForm.value;\r\n\r\n      // Create the submission object matching the required JSON structure\r\n      const submissionData: RegistrationData = {\r\n        firstname: formData.firstname,\r\n        lastname: formData.lastname,\r\n        username: formData.username,\r\n        email: formData.email,\r\n        password: formData.password,\r\n        address: formData.address,\r\n        country: formData.country,\r\n        city: formData.city,\r\n        zipcode: formData.zipcode,\r\n        invoice_ref: formData.invoice_ref,\r\n        purchase_order: formData.purchase_order,\r\n        vendor_id: formData.vendor_id,\r\n        security_que_1: formData.security_que_1,\r\n        security_que_1_ans: formData.security_que_1_ans,\r\n        security_que_2: formData.security_que_2,\r\n        security_que_2_ans: formData.security_que_2_ans\r\n      };\r\n\r\n      console.log('Submission data:', submissionData);\r\n      // Here you would typically call your registration service\r\n      // this.registrationService.register(submissionData).subscribe(...)\r\n    } else {\r\n      this.markFormGroupTouched(this.registrationForm);\r\n    }\r\n  }\r\n\r\n  markFormGroupTouched(formGroup: FormGroup) {\r\n    Object.values(formGroup.controls).forEach(control => {\r\n      control.markAsTouched();\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n}", "<section class=\"login-sec bg-white h-screen pt-6 pb-6\">\r\n  <div class=\"login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full\">\r\n    <div class=\"login-header relative flex align-items-center justify-content-between\">\r\n      <div class=\"logo w-full max-w-15rem\">\r\n        <a href=\"#\" class=\"flex w-full\"><img src=\"/assets/layout/images/chs-logo.svg\" alt=\"Logo\" class=\"w-full\" /></a>\r\n      </div>\r\n      <div class=\"sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium\">\r\n        Do you have an account?\r\n        <button type=\"button\" [routerLink]=\"['/login']\"\r\n          class=\"sign-up-btn p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1\">\r\n          <span class=\"material-symbols-rounded text-2xl\">input</span> Login\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"login-form mx-auto p-5 max-w-30rem w-full bg-white border-round-3xl shadow-2\">\r\n      <form [formGroup]=\"registrationForm\" (ngSubmit)=\"submitForm()\" class=\"flex flex-column position-relative\">\r\n        <h1 class=\"mb-2 flex justify-content-center text-4xl font-bold text-primary\">Registration</h1>\r\n        <p class=\"mb-5 flex justify-content-center text-base font-medium text-gray-900\">\r\n          Enter your details below to create an account and get started.\r\n        </p>\r\n        \r\n        <div class=\"p-fluid p-formgrid grid\">\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>First Name</label>\r\n            <input type=\"text\" formControlName=\"firstname\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>Last Name</label>\r\n            <input type=\"text\" formControlName=\"lastname\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>Phone Number</label>\r\n            <input type=\"text\" formControlName=\"phone_number\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>Extension</label>\r\n            <input type=\"text\" formControlName=\"extension\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Email</label>\r\n            <input type=\"email\" formControlName=\"email\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Address</label>\r\n            <input type=\"text\" formControlName=\"address\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Country</label>\r\n            <select formControlName=\"country\" class=\"p-inputtext w-full bg-gray-50\">\r\n              <option *ngFor=\"let country of countries\" [ngValue]=\"country.name\">{{ country.name }} ({{ country.code }})</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>State</label>\r\n            <select formControlName=\"state\" class=\"p-inputtext w-full bg-gray-50\">\r\n              <option *ngFor=\"let state of states\" [ngValue]=\"state.name\">{{ state.name }} ({{ state.code }})</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>City</label>\r\n            <input type=\"text\" formControlName=\"city\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Zip Code</label>\r\n            <input type=\"text\" formControlName=\"zipcode\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Password</label>\r\n            <input type=\"password\" formControlName=\"password\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Retype Password</label>\r\n            <input type=\"password\" formControlName=\"retype_password\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Invoice Ref #</label>\r\n            <input type=\"text\" formControlName=\"invoice_ref\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Purchase Order #</label>\r\n            <input type=\"text\" formControlName=\"purchase_order\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Security Question 1</label>\r\n            <p-dropdown [options]=\"questions\" formControlName=\"security_que_1\" optionLabel=\"name\" class=\"w-full bg-gray-50\"></p-dropdown>\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Answer</label>\r\n            <input type=\"text\" formControlName=\"security_que_1_ans\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n        </div>\r\n\r\n        <button type=\"submit\" class=\"p-button-rounded p-button p-component w-full font-semibold\">Register</button>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": "AACA,SAASA,SAAS,EAAeC,UAAU,QAAQ,gBAAgB;;;;;;;;;ICgDrDC,EAAA,CAAAC,cAAA,iBAAmE;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAzEH,EAAA,CAAAI,UAAA,YAAAC,UAAA,CAAAC,IAAA,CAAwB;IAACN,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAQ,kBAAA,KAAAH,UAAA,CAAAC,IAAA,QAAAD,UAAA,CAAAI,IAAA,MAAuC;;;;;IAM1GT,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnEH,EAAA,CAAAI,UAAA,YAAAM,QAAA,CAAAJ,IAAA,CAAsB;IAACN,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAQ,kBAAA,KAAAE,QAAA,CAAAJ,IAAA,QAAAI,QAAA,CAAAD,IAAA,MAAmC;;;AD7B7G,OAAM,MAAOE,eAAe;EAO1BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IALtB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,sBAAsB,GAAY,KAAK;EAEA;EAEvCC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAA,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACL,SAAS,GAAG,CACf;MAAEM,EAAE,EAAE,CAAC;MAAEf,IAAI,EAAE;IAAoC,CAAE,EACrD;MAAEe,EAAE,EAAE,CAAC;MAAEf,IAAI,EAAE;IAAiC,CAAE,EAClD;MAAEe,EAAE,EAAE,CAAC;MAAEf,IAAI,EAAE;IAA6B,CAAE,EAC9C;MAAEe,EAAE,EAAE,CAAC;MAAEf,IAAI,EAAE;IAAkD,CAAE,CACpE;EACH;EAEAa,cAAcA,CAAA;IACZ,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACT,EAAE,CAACU,KAAK,CAAC;MACpCC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACtCC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC3B,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACrCE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACrCG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAAC0B,QAAQ,EAAE1B,UAAU,CAAC6B,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC0B,QAAQ,EAAE1B,UAAU,CAAC+B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,cAAc,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MAC3CO,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACpCQ,OAAO,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACpCS,IAAI,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACjCU,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACpCW,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACvC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MACtCc,cAAc,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MAC3Ce,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAACzC,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MAC/CgB,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAAC0B,QAAQ,CAAC,CAAC;MAC3CiB,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC3C,UAAU,CAAC0B,QAAQ,CAAC;KAC/C,EAAE;MACDkB,SAAS,EAAE,IAAI,CAACC;KACjB,CAAC;EACJ;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAMhB,QAAQ,GAAGgB,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMC,eAAe,GAAGF,IAAI,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAElD,IAAIjB,QAAQ,IAAIkB,eAAe,IAAIlB,QAAQ,CAACmB,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;MAC3ED,eAAe,CAACE,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;IACvD,CAAC,MAAM;MACLH,eAAe,EAAEE,SAAS,CAAC,IAAI,CAAC;IAClC;EACF;EAEAE,wBAAwBA,CAACC,KAAqC;IAC5D,IAAIA,KAAK,KAAK,UAAU,EAAE;MACxB,IAAI,CAACpC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC9C,CAAC,MAAM;MACL,IAAI,CAACC,sBAAsB,GAAG,CAAC,IAAI,CAACA,sBAAsB;IAC5D;EACF;EAEAoC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/B,gBAAgB,CAACgC,KAAK,EAAE;MAC/B,MAAMC,QAAQ,GAAG,IAAI,CAACjC,gBAAgB,CAAC0B,KAAK;MAE5C;MACA,MAAMQ,cAAc,GAAqB;QACvChC,SAAS,EAAE+B,QAAQ,CAAC/B,SAAS;QAC7BE,QAAQ,EAAE6B,QAAQ,CAAC7B,QAAQ;QAC3BC,QAAQ,EAAE4B,QAAQ,CAAC5B,QAAQ;QAC3BC,KAAK,EAAE2B,QAAQ,CAAC3B,KAAK;QACrBC,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;QAC3BG,OAAO,EAAEuB,QAAQ,CAACvB,OAAO;QACzBC,OAAO,EAAEsB,QAAQ,CAACtB,OAAO;QACzBC,IAAI,EAAEqB,QAAQ,CAACrB,IAAI;QACnBC,OAAO,EAAEoB,QAAQ,CAACpB,OAAO;QACzBC,WAAW,EAAEmB,QAAQ,CAACnB,WAAW;QACjCC,cAAc,EAAEkB,QAAQ,CAAClB,cAAc;QACvCC,SAAS,EAAEiB,QAAQ,CAACjB,SAAS;QAC7BC,cAAc,EAAEgB,QAAQ,CAAChB,cAAc;QACvCC,kBAAkB,EAAEe,QAAQ,CAACf,kBAAkB;QAC/CC,cAAc,EAAEc,QAAQ,CAACd,cAAc;QACvCC,kBAAkB,EAAEa,QAAQ,CAACb;OAC9B;MAEDe,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,cAAc,CAAC;MAC/C;MACA;IACF,CAAC,MAAM;MACL,IAAI,CAACG,oBAAoB,CAAC,IAAI,CAACrC,gBAAgB,CAAC;IAClD;EACF;EAEAqC,oBAAoBA,CAACC,SAAoB;IACvCC,MAAM,CAACC,MAAM,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,OAAO,IAAG;MAClDA,OAAO,CAACC,aAAa,EAAE;MACvB,IAAID,OAAO,YAAYnE,SAAS,EAAE;QAChC,IAAI,CAAC6D,oBAAoB,CAACM,OAAO,CAAC;MACpC;IACF,CAAC,CAAC;EACJ;;;uBA1GWtD,eAAe,EAAAX,EAAA,CAAAmE,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAf1D,eAAe;MAAA2D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBpB5E,EAJR,CAAAC,cAAA,iBAAuD,aAC0C,aACV,aAC5C,WACH;UAAAD,EAAA,CAAA8E,SAAA,aAA0E;UAC5G9E,EAD4G,CAAAG,YAAA,EAAI,EAC1G;UACNH,EAAA,CAAAC,cAAA,aAA6G;UAC3GD,EAAA,CAAAE,MAAA,gCACA;UAEEF,EAFF,CAAAC,cAAA,gBACkH,cAChE;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAC/D;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,cAA0F,gBACkB;UAArED,EAAA,CAAA+E,UAAA,sBAAAC,mDAAA;YAAA,OAAYH,GAAA,CAAAI,UAAA,EAAY;UAAA,EAAC;UAC5DjF,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9FH,EAAA,CAAAC,cAAA,aAAgF;UAC9ED,EAAA,CAAAE,MAAA,wEACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIAH,EAFJ,CAAAC,cAAA,eAAqC,eACA,aAC1B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzBH,EAAA,CAAA8E,SAAA,iBAAuF;UACzF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAmC,aAC1B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAA8E,SAAA,iBAAsF;UACxF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAmC,aAC1B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3BH,EAAA,CAAA8E,SAAA,iBAA0F;UAC5F9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAmC,aAC1B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAA8E,SAAA,iBAAuF;UACzF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAA8E,SAAA,iBAAoF;UACtF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAA8E,SAAA,iBAAqF;UACvF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAkF,UAAA,KAAAC,kCAAA,qBAAmE;UAEvEnF,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAAC,cAAA,kBAAsE;UACpED,EAAA,CAAAkF,UAAA,KAAAE,kCAAA,qBAA4D;UAEhEpF,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnBH,EAAA,CAAA8E,SAAA,iBAAkF;UACpF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA8E,SAAA,iBAAqF;UACvF9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA8E,SAAA,iBAA0F;UAC5F9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAA8E,SAAA,iBAAiG;UACnG9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAA8E,SAAA,iBAAyF;UAC3F9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/BH,EAAA,CAAA8E,SAAA,iBAA4F;UAC9F9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAA8E,SAAA,sBAA6H;UAC/H9E,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrBH,EAAA,CAAA8E,SAAA,iBAAgG;UAEpG9E,EADE,CAAAG,YAAA,EAAM,EACF;UAENH,EAAA,CAAAC,cAAA,kBAAyF;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAIzGF,EAJyG,CAAAG,YAAA,EAAS,EACrG,EACH,EACF,EACE;;;UAxFoBH,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAqF,eAAA,IAAAC,GAAA,EAAyB;UAO3CtF,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,cAAAyE,GAAA,CAAAvD,gBAAA,CAA8B;UAkCAtB,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAyE,GAAA,CAAA/D,SAAA,CAAY;UAMdd,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAI,UAAA,YAAAyE,GAAA,CAAAU,MAAA,CAAS;UA6BzBvF,EAAA,CAAAO,SAAA,IAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAyE,GAAA,CAAA9D,SAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}