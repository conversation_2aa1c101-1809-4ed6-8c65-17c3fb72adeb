{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/dropdown\";\nconst _c0 = () => [\"/login\"];\nfunction SignupComponent_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", country_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", country_r1.name, \" (\", country_r1.code, \")\");\n  }\n}\nfunction SignupComponent_option_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const state_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", state_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", state_r2.name, \" (\", state_r2.code, \")\");\n  }\n}\nexport class SignupComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.countries = ['USA', 'Canada', 'UK'];\n    this.questions = [{\n      id: 1,\n      name: 'What is your pet’s name?'\n    }, {\n      id: 2,\n      name: 'What is your mother’s maiden name?'\n    }, {\n      id: 3,\n      name: 'What city were you born in?'\n    }, {\n      id: 4,\n      name: 'What is your first school’s name?'\n    }];\n    this.registrationForm = this.fb.group({\n      firstname: ['', Validators.required],\n      lastname: ['', Validators.required],\n      username: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      address: ['', Validators.required],\n      country: ['', Validators.required],\n      city: ['', Validators.required],\n      zipcode: ['', [Validators.required, Validators.pattern('^[0-9]{5}$')]],\n      invoice_ref: [''],\n      purchase_order: [''],\n      vendor_id: [''],\n      security_que_1: ['', Validators.required],\n      security_que_1_ans: ['', Validators.required],\n      security_que_2: ['', Validators.required],\n      security_que_2_ans: ['', Validators.required]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    return form.get('password')?.value === form.get('confirmPassword')?.value ? null : {\n      mismatch: true\n    };\n  }\n  submitForm() {\n    if (this.registrationForm.valid) {\n      console.log('Form Data:', this.registrationForm.value);\n    } else {\n      console.log('Form Invalid');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 87,\n      vars: 6,\n      consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"pt-6\", \"pb-6\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"max-w-15rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"max-w-30rem\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", \"position-relative\", 3, \"ngSubmit\", \"formGroup\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col-6\"], [\"type\", \"text\", \"formControlName\", \"firstname\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"lastname\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"phone_number\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"extension\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [1, \"field\", \"col-12\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"address\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [1, \"field\", \"col-6\"], [\"formControlName\", \"country\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"state\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"city\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"zipcode\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"password\", \"formControlName\", \"retype_password\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"invoice_ref\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"text\", \"formControlName\", \"purchase_order\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"formControlName\", \"security_que_1\", \"optionLabel\", \"name\", 1, \"w-full\", \"bg-gray-50\", 3, \"options\"], [\"type\", \"text\", \"formControlName\", \"security_que_1_ans\", 1, \"p-inputtext\", \"w-full\", \"bg-gray-50\"], [\"type\", \"submit\", 1, \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"font-semibold\"], [3, \"ngValue\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtext(7, \" Do you have an account? \");\n          i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n          i0.ɵɵtext(10, \"input\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.submitForm();\n          });\n          i0.ɵɵelementStart(14, \"h1\", 11);\n          i0.ɵɵtext(15, \"Registration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵtext(17, \" Enter your details below to create an account and get started. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14)(20, \"label\");\n          i0.ɵɵtext(21, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"label\");\n          i0.ɵɵtext(25, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"label\");\n          i0.ɵɵtext(29, \"Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 14)(32, \"label\");\n          i0.ɵɵtext(33, \"Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 19)(36, \"label\");\n          i0.ɵɵtext(37, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 19)(40, \"label\");\n          i0.ɵɵtext(41, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 22)(44, \"label\");\n          i0.ɵɵtext(45, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"select\", 23);\n          i0.ɵɵtemplate(47, SignupComponent_option_47_Template, 2, 3, \"option\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 22)(49, \"label\");\n          i0.ɵɵtext(50, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"select\", 25);\n          i0.ɵɵtemplate(52, SignupComponent_option_52_Template, 2, 3, \"option\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"div\", 22)(54, \"label\");\n          i0.ɵɵtext(55, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"input\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 22)(58, \"label\");\n          i0.ɵɵtext(59, \"Zip Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(60, \"input\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 22)(62, \"label\");\n          i0.ɵɵtext(63, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 22)(66, \"label\");\n          i0.ɵɵtext(67, \"Retype Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(68, \"input\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 22)(70, \"label\");\n          i0.ɵɵtext(71, \"Invoice Ref #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"input\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 22)(74, \"label\");\n          i0.ɵɵtext(75, \"Purchase Order #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(76, \"input\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 19)(78, \"label\");\n          i0.ɵɵtext(79, \"Security Question 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(80, \"p-dropdown\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 19)(82, \"label\");\n          i0.ɵɵtext(83, \"Answer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(84, \"input\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"button\", 34);\n          i0.ɵɵtext(86, \"Register\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.states);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"options\", ctx.questions);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i4.Dropdown],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 600px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2Vzc2lvbi9zaWdudXAvc2lnbnVwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0ksaUJBQUE7QUFBUjtBQUVRO0VBQ0ksMkJBQUE7QUFBWjtBQUlvQjtFQUNJLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtBQUZ4QjtBQUtvQjtFQUNJLGlDQUFBO0FBSHhCOztBQVdBO0VBQ0ksWUFBQTtFQUNBLDJCQUFBO0FBUko7O0FBV0E7RUFDSSxjQUFBO0FBUkoiLCJzb3VyY2VzQ29udGVudCI6WyIubG9naW4tc2VjIHtcclxuICAgIC5sb2dpbi1wYWdlLWJvZHkge1xyXG4gICAgICAgIG1heC13aWR0aDogMTQ0MHB4O1xyXG5cclxuICAgICAgICAubG9naW4tZm9ybSB7XHJcbiAgICAgICAgICAgIG1heC13aWR0aDogNjAwcHggIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAgIGZvcm0ge1xyXG4gICAgICAgICAgICAgICAgLmZvcm0tZ3JvdXAge1xyXG4gICAgICAgICAgICAgICAgICAgIC5wYXNzLXNob3ctYnRuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IDEycHg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMjRweDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDI0cHg7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuZm9ybS1jaGVjay1ib3gge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NlbnQtY29sb3I6IHZhcigtLXByaW1hcnljb2xvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4ucC1pbnB1dHRleHQge1xyXG4gICAgaGVpZ2h0OiAzcmVtO1xyXG4gICAgYXBwZWFyYW5jZTogYXV0byAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uaC0zLTNyZW0ge1xyXG4gICAgaGVpZ2h0OiAzLjNyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "country_r1", "name", "ɵɵadvance", "ɵɵtextInterpolate2", "code", "state_r2", "SignupComponent", "constructor", "fb", "countries", "questions", "id", "registrationForm", "group", "firstname", "required", "lastname", "username", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "address", "country", "city", "zipcode", "pattern", "invoice_ref", "purchase_order", "vendor_id", "security_que_1", "security_que_1_ans", "security_que_2", "security_que_2_ans", "validators", "passwordMatchValidator", "form", "get", "value", "mismatch", "submitForm", "valid", "console", "log", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_13_listener", "ɵɵtemplate", "SignupComponent_option_47_Template", "SignupComponent_option_52_Template", "ɵɵpureFunction0", "_c0", "states"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\signup\\signup.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-signup',\r\n  templateUrl: './signup.component.html',\r\n  styleUrl: './signup.component.scss'\r\n})\r\nexport class SignupComponent {\r\n  registrationForm: FormGroup;\r\n  countries = ['USA', 'Canada', 'UK'];\r\n  questions = [\r\n    { id: 1, name: 'What is your pet’s name?' },\r\n    { id: 2, name: 'What is your mother’s maiden name?' },\r\n    { id: 3, name: 'What city were you born in?' },\r\n    { id: 4, name: 'What is your first school’s name?' }\r\n  ];\r\n\r\n  constructor(private fb: FormBuilder) {\r\n    this.registrationForm = this.fb.group(\r\n      {\r\n        firstname: ['', Validators.required],\r\n        lastname: ['', Validators.required],\r\n        username: ['', Validators.required],\r\n        email: ['', [Validators.required, Validators.email]],\r\n        password: ['', [Validators.required, Validators.minLength(6)]],\r\n        address: ['', Validators.required],\r\n        country: ['', Validators.required],\r\n        city: ['', Validators.required],\r\n        zipcode: ['', [Validators.required, Validators.pattern('^[0-9]{5}$')]],\r\n        invoice_ref: [''],\r\n        purchase_order: [''],\r\n        vendor_id: [''],\r\n        security_que_1: ['', Validators.required],\r\n        security_que_1_ans: ['', Validators.required],\r\n        security_que_2: ['', Validators.required],\r\n        security_que_2_ans: ['', Validators.required],\r\n      },\r\n      { validators: this.passwordMatchValidator }\r\n    );\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    return form.get('password')?.value === form.get('confirmPassword')?.value\r\n      ? null\r\n      : { mismatch: true };\r\n  }\r\n\r\n  submitForm() {\r\n    if (this.registrationForm.valid) {\r\n      console.log('Form Data:', this.registrationForm.value);\r\n    } else {\r\n      console.log('Form Invalid');\r\n    }\r\n  }\r\n}\r\n", "<section class=\"login-sec bg-white h-screen pt-6 pb-6\">\r\n  <div class=\"login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full\">\r\n    <div class=\"login-header relative flex align-items-center justify-content-between\">\r\n      <div class=\"logo w-full max-w-15rem\">\r\n        <a href=\"#\" class=\"flex w-full\"><img src=\"/assets/layout/images/chs-logo.svg\" alt=\"Logo\" class=\"w-full\" /></a>\r\n      </div>\r\n      <div class=\"sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium\">\r\n        Do you have an account?\r\n        <button type=\"button\" [routerLink]=\"['/login']\"\r\n          class=\"sign-up-btn p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1\">\r\n          <span class=\"material-symbols-rounded text-2xl\">input</span> Login\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"login-form mx-auto p-5 max-w-30rem w-full bg-white border-round-3xl shadow-2\">\r\n      <form [formGroup]=\"registrationForm\" (ngSubmit)=\"submitForm()\" class=\"flex flex-column position-relative\">\r\n        <h1 class=\"mb-2 flex justify-content-center text-4xl font-bold text-primary\">Registration</h1>\r\n        <p class=\"mb-5 flex justify-content-center text-base font-medium text-gray-900\">\r\n          Enter your details below to create an account and get started.\r\n        </p>\r\n        \r\n        <div class=\"p-fluid p-formgrid grid\">\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>First Name</label>\r\n            <input type=\"text\" formControlName=\"firstname\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>Last Name</label>\r\n            <input type=\"text\" formControlName=\"lastname\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>Phone Number</label>\r\n            <input type=\"text\" formControlName=\"phone_number\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6\">\r\n            <label>Extension</label>\r\n            <input type=\"text\" formControlName=\"extension\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Email</label>\r\n            <input type=\"email\" formControlName=\"email\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Address</label>\r\n            <input type=\"text\" formControlName=\"address\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Country</label>\r\n            <select formControlName=\"country\" class=\"p-inputtext w-full bg-gray-50\">\r\n              <option *ngFor=\"let country of countries\" [ngValue]=\"country.name\">{{ country.name }} ({{ country.code }})</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>State</label>\r\n            <select formControlName=\"state\" class=\"p-inputtext w-full bg-gray-50\">\r\n              <option *ngFor=\"let state of states\" [ngValue]=\"state.name\">{{ state.name }} ({{ state.code }})</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>City</label>\r\n            <input type=\"text\" formControlName=\"city\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Zip Code</label>\r\n            <input type=\"text\" formControlName=\"zipcode\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Password</label>\r\n            <input type=\"password\" formControlName=\"password\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Retype Password</label>\r\n            <input type=\"password\" formControlName=\"retype_password\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Invoice Ref #</label>\r\n            <input type=\"text\" formControlName=\"invoice_ref\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Purchase Order #</label>\r\n            <input type=\"text\" formControlName=\"purchase_order\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Security Question 1</label>\r\n            <p-dropdown [options]=\"questions\" formControlName=\"security_que_1\" optionLabel=\"name\" class=\"w-full bg-gray-50\"></p-dropdown>\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Answer</label>\r\n            <input type=\"text\" formControlName=\"security_que_1_ans\" class=\"p-inputtext w-full bg-gray-50\" />\r\n          </div>\r\n        </div>\r\n\r\n        <button type=\"submit\" class=\"p-button-rounded p-button p-component w-full font-semibold\">Register</button>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICgDrDC,EAAA,CAAAC,cAAA,iBAAmE;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAzEH,EAAA,CAAAI,UAAA,YAAAC,UAAA,CAAAC,IAAA,CAAwB;IAACN,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAQ,kBAAA,KAAAH,UAAA,CAAAC,IAAA,QAAAD,UAAA,CAAAI,IAAA,MAAuC;;;;;IAM1GT,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnEH,EAAA,CAAAI,UAAA,YAAAM,QAAA,CAAAJ,IAAA,CAAsB;IAACN,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAQ,kBAAA,KAAAE,QAAA,CAAAJ,IAAA,QAAAI,QAAA,CAAAD,IAAA,MAAmC;;;AD/C7G,OAAM,MAAOE,eAAe;EAU1BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IARtB,KAAAC,SAAS,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;IACnC,KAAAC,SAAS,GAAG,CACV;MAAEC,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE;IAA0B,CAAE,EAC3C;MAAEU,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE;IAAoC,CAAE,EACrD;MAAEU,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE;IAA6B,CAAE,EAC9C;MAAEU,EAAE,EAAE,CAAC;MAAEV,IAAI,EAAE;IAAmC,CAAE,CACrD;IAGC,IAAI,CAACW,gBAAgB,GAAG,IAAI,CAACJ,EAAE,CAACK,KAAK,CACnC;MACEC,SAAS,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACqB,QAAQ,CAAC;MACpCC,QAAQ,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACqB,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAEvB,UAAU,CAACqB,QAAQ,CAAC;MACnCG,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACwB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,OAAO,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAACqB,QAAQ,CAAC;MAClCO,OAAO,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAACqB,QAAQ,CAAC;MAClCQ,IAAI,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAACqB,QAAQ,CAAC;MAC/BS,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAAC+B,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;MACtEC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACqB,QAAQ,CAAC;MACzCe,kBAAkB,EAAE,CAAC,EAAE,EAAEpC,UAAU,CAACqB,QAAQ,CAAC;MAC7CgB,cAAc,EAAE,CAAC,EAAE,EAAErC,UAAU,CAACqB,QAAQ,CAAC;MACzCiB,kBAAkB,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAACqB,QAAQ;KAC7C,EACD;MAAEkB,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC5C;EACH;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,OAAOA,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,KAAKF,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEC,KAAK,GACrE,IAAI,GACJ;MAAEC,QAAQ,EAAE;IAAI,CAAE;EACxB;EAEAC,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC3B,gBAAgB,CAAC4B,KAAK,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC9B,gBAAgB,CAACyB,KAAK,CAAC;IACxD,CAAC,MAAM;MACLI,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC7B;EACF;;;uBA9CWpC,eAAe,EAAAX,EAAA,CAAAgD,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAfvC,eAAe;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCJpBzD,EAJR,CAAAC,cAAA,iBAAuD,aAC0C,aACV,aAC5C,WACH;UAAAD,EAAA,CAAA2D,SAAA,aAA0E;UAC5G3D,EAD4G,CAAAG,YAAA,EAAI,EAC1G;UACNH,EAAA,CAAAC,cAAA,aAA6G;UAC3GD,EAAA,CAAAE,MAAA,gCACA;UAEEF,EAFF,CAAAC,cAAA,gBACkH,cAChE;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAC/D;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,cAA0F,gBACkB;UAArED,EAAA,CAAA4D,UAAA,sBAAAC,mDAAA;YAAA,OAAYH,GAAA,CAAAd,UAAA,EAAY;UAAA,EAAC;UAC5D5C,EAAA,CAAAC,cAAA,cAA6E;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9FH,EAAA,CAAAC,cAAA,aAAgF;UAC9ED,EAAA,CAAAE,MAAA,wEACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIAH,EAFJ,CAAAC,cAAA,eAAqC,eACA,aAC1B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzBH,EAAA,CAAA2D,SAAA,iBAAuF;UACzF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAmC,aAC1B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAA2D,SAAA,iBAAsF;UACxF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAmC,aAC1B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3BH,EAAA,CAAA2D,SAAA,iBAA0F;UAC5F3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAmC,aAC1B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAA2D,SAAA,iBAAuF;UACzF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAA2D,SAAA,iBAAoF;UACtF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAA2D,SAAA,iBAAqF;UACvF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAA8D,UAAA,KAAAC,kCAAA,qBAAmE;UAEvE/D,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAAC,cAAA,kBAAsE;UACpED,EAAA,CAAA8D,UAAA,KAAAE,kCAAA,qBAA4D;UAEhEhE,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnBH,EAAA,CAAA2D,SAAA,iBAAkF;UACpF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA2D,SAAA,iBAAqF;UACvF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA2D,SAAA,iBAA0F;UAC5F3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAA2D,SAAA,iBAAiG;UACnG3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAA2D,SAAA,iBAAyF;UAC3F3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/BH,EAAA,CAAA2D,SAAA,iBAA4F;UAC9F3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAA2D,SAAA,sBAA6H;UAC/H3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrBH,EAAA,CAAA2D,SAAA,iBAAgG;UAEpG3D,EADE,CAAAG,YAAA,EAAM,EACF;UAENH,EAAA,CAAAC,cAAA,kBAAyF;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAIzGF,EAJyG,CAAAG,YAAA,EAAS,EACrG,EACH,EACF,EACE;;;UAxFoBH,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAiE,eAAA,IAAAC,GAAA,EAAyB;UAO3ClE,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAI,UAAA,cAAAsD,GAAA,CAAAzC,gBAAA,CAA8B;UAkCAjB,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAsD,GAAA,CAAA5C,SAAA,CAAY;UAMdd,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAI,UAAA,YAAAsD,GAAA,CAAAS,MAAA,CAAS;UA6BzBnE,EAAA,CAAAO,SAAA,IAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAsD,GAAA,CAAA3C,SAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}