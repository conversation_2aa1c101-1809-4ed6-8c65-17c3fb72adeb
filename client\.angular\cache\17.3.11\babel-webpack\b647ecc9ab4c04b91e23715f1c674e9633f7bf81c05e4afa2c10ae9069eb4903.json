{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProductService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api';\n    this.headers = new HttpHeaders({\n      Authorization: 'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695' // Replace with your actual token\n    });\n    this.productSubject = new BehaviorSubject(null);\n    this.product = this.productSubject.asObservable();\n  }\n  getProducts(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][product_id][$containsi]', searchTerm).set('filters[$or][1][item_category_group][$containsi]', searchTerm).set('filters[$or][2][product_status][$containsi]', searchTerm).set('filters[$or][3][base_unit][$containsi]', searchTerm);\n    }\n    return this.http.get(`${this.apiUrl}/products`, {\n      headers: this.headers,\n      params\n    });\n  }\n  getProductByID(productid) {\n    return this.http.get(`${this.apiUrl}/products/${productid}?populate=*`, {\n      headers: this.headers\n    });\n  }\n  updateProduct(id, productData) {\n    return this.http.put(`${this.apiUrl}/products/${id}`, {\n      data: productData\n    }, {\n      headers: this.headers\n    }).pipe(map(res => res.data));\n  }\n  getProductByIDName(productdata) {\n    let params = new HttpParams();\n    if (productdata) {\n      params = params.set('filters[$or][0][product_id][$containsi]', productdata).set('filters[$or][1][name][$containsi]', productdata);\n    }\n    return this.http.get(`${this.apiUrl}/products`, {\n      headers: this.headers,\n      params\n    });\n  }\n  getById(id) {\n    return this.http.get(`${this.apiUrl}/products/${id}?populate=*`, {\n      headers: this.headers\n    });\n  }\n  getAllCategories() {\n    return this.http.get(`${this.apiUrl}/products`, {\n      headers: this.headers\n    });\n  }\n  getAllCategory() {\n    return this.http.get(`${this.apiUrl}/product-categories`, {\n      headers: this.headers\n    }).pipe(map(res => res.data));\n  }\n  getAllCatalog() {\n    return this.http.get(`${this.apiUrl}/product-catalogs`, {\n      headers: this.headers\n    }).pipe(map(res => res.data));\n  }\n  getProductClassification(productId) {\n    return this.http.get(`${this.apiUrl}/${productId}/classification`);\n  }\n  getMedia(id) {\n    return this.http.get(`${this.apiUrl}/product-medias/${id}`, {\n      headers: this.headers\n    });\n  }\n  removeMedia(id) {\n    return this.http.delete(`${this.apiUrl}/product-medias/${id}`, {\n      headers: this.headers\n    });\n  }\n  submitMediaForm(media) {\n    return this.http.post(`${this.apiUrl}/product-medias`, {\n      data: media\n    }, {\n      headers: this.headers\n    });\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "BehaviorSubject", "map", "ProductService", "constructor", "http", "apiUrl", "headers", "Authorization", "productSubject", "product", "asObservable", "getProducts", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "getProductByID", "productid", "updateProduct", "id", "productData", "put", "data", "pipe", "res", "getProductByIDName", "productdata", "getById", "getAllCategories", "getAllCategory", "getAllCatalog", "getProductClassification", "productId", "getMedia", "removeMedia", "delete", "submitMediaForm", "media", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, map } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ProductService {\r\n  private apiUrl =\r\n    'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api';\r\n  private headers = new HttpHeaders({\r\n    Authorization:\r\n      'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695', // Replace with your actual token\r\n  });\r\n\r\n  public productSubject = new BehaviorSubject<any>(null);\r\n  public product = this.productSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getProducts(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][product_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][item_category_group][$containsi]', searchTerm)\r\n        .set('filters[$or][2][product_status][$containsi]', searchTerm)\r\n        .set('filters[$or][3][base_unit][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(`${this.apiUrl}/products`, {\r\n      headers: this.headers,\r\n      params,\r\n    });\r\n  }\r\n\r\n  getProductByID(productid: string) {\r\n    return this.http.get<any>(\r\n      `${this.apiUrl}/products/${productid}?populate=*`,\r\n      { headers: this.headers }\r\n    );\r\n  }\r\n\r\n  updateProduct(id: any, productData: any) {\r\n    return this.http\r\n      .put<any>(\r\n        `${this.apiUrl}/products/${id}`,\r\n        { data: productData },\r\n        { headers: this.headers }\r\n      )\r\n      .pipe(map((res) => res.data));\r\n  }\r\n\r\n  getProductByIDName(productdata: string): Observable<any[]> {\r\n    let params = new HttpParams();\r\n    if (productdata) {\r\n      params = params\r\n        .set('filters[$or][0][product_id][$containsi]', productdata)\r\n        .set('filters[$or][1][name][$containsi]', productdata);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${this.apiUrl}/products`, {\r\n      headers: this.headers,\r\n      params,\r\n    });\r\n  }\r\n\r\n  getById(id: string) {\r\n    return this.http.get<any>(`${this.apiUrl}/products/${id}?populate=*`, {\r\n      headers: this.headers,\r\n    });\r\n  }\r\n\r\n  getAllCategories(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${this.apiUrl}/products`, {\r\n      headers: this.headers,\r\n    });\r\n  }\r\n\r\n  getAllCategory() {\r\n    return this.http\r\n      .get<any>(`${this.apiUrl}/product-categories`, { headers: this.headers })\r\n      .pipe(map((res) => res.data));\r\n  }\r\n\r\n  getAllCatalog() {\r\n    return this.http\r\n      .get<any>(`${this.apiUrl}/product-catalogs`, { headers: this.headers })\r\n      .pipe(map((res) => res.data));\r\n  }\r\n  getProductClassification(productId: any) {\r\n    return this.http.get<any>(`${this.apiUrl}/${productId}/classification`);\r\n  }\r\n\r\n  getMedia(id: any) {\r\n    return this.http.get<any>(`${this.apiUrl}/product-medias/${id}`, {\r\n      headers: this.headers,\r\n    });\r\n  }\r\n\r\n  removeMedia(id: any) {\r\n    return this.http.delete<any>(`${this.apiUrl}/product-medias/${id}`, {\r\n      headers: this.headers,\r\n    });\r\n  }\r\n\r\n  submitMediaForm(media: any) {\r\n    return this.http.post<any>(\r\n      `${this.apiUrl}/product-medias`,\r\n      { data: media },\r\n      { headers: this.headers }\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAE1E,SAASC,eAAe,EAAcC,GAAG,QAAQ,MAAM;;;AAKvD,OAAM,MAAOC,cAAc;EAWzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAVhB,KAAAC,MAAM,GACZ,kEAAkE;IAC5D,KAAAC,OAAO,GAAG,IAAIR,WAAW,CAAC;MAChCS,aAAa,EACX,yQAAyQ,CAAE;KAC9Q,CAAC;IAEK,KAAAC,cAAc,GAAG,IAAIR,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAS,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIlB,UAAU,EAAE,CAC1BmB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,yCAAyC,EAAEF,UAAU,CAAC,CAC1DE,GAAG,CAAC,kDAAkD,EAAEF,UAAU,CAAC,CACnEE,GAAG,CAAC,6CAA6C,EAAEF,UAAU,CAAC,CAC9DE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC;IAC9D;IACA,OAAO,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAQ,GAAG,IAAI,CAACjB,MAAM,WAAW,EAAE;MACrDC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBW;KACD,CAAC;EACJ;EAEAM,cAAcA,CAACC,SAAiB;IAC9B,OAAO,IAAI,CAACpB,IAAI,CAACkB,GAAG,CAClB,GAAG,IAAI,CAACjB,MAAM,aAAamB,SAAS,aAAa,EACjD;MAAElB,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;EACH;EAEAmB,aAAaA,CAACC,EAAO,EAAEC,WAAgB;IACrC,OAAO,IAAI,CAACvB,IAAI,CACbwB,GAAG,CACF,GAAG,IAAI,CAACvB,MAAM,aAAaqB,EAAE,EAAE,EAC/B;MAAEG,IAAI,EAAEF;IAAW,CAAE,EACrB;MAAErB,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B,CACAwB,IAAI,CAAC7B,GAAG,CAAE8B,GAAG,IAAKA,GAAG,CAACF,IAAI,CAAC,CAAC;EACjC;EAEAG,kBAAkBA,CAACC,WAAmB;IACpC,IAAIhB,MAAM,GAAG,IAAIlB,UAAU,EAAE;IAC7B,IAAIkC,WAAW,EAAE;MACfhB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,yCAAyC,EAAEe,WAAW,CAAC,CAC3Df,GAAG,CAAC,mCAAmC,EAAEe,WAAW,CAAC;IAC1D;IAEA,OAAO,IAAI,CAAC7B,IAAI,CAACkB,GAAG,CAAQ,GAAG,IAAI,CAACjB,MAAM,WAAW,EAAE;MACrDC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBW;KACD,CAAC;EACJ;EAEAiB,OAAOA,CAACR,EAAU;IAChB,OAAO,IAAI,CAACtB,IAAI,CAACkB,GAAG,CAAM,GAAG,IAAI,CAACjB,MAAM,aAAaqB,EAAE,aAAa,EAAE;MACpEpB,OAAO,EAAE,IAAI,CAACA;KACf,CAAC;EACJ;EAEA6B,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC/B,IAAI,CAACkB,GAAG,CAAQ,GAAG,IAAI,CAACjB,MAAM,WAAW,EAAE;MACrDC,OAAO,EAAE,IAAI,CAACA;KACf,CAAC;EACJ;EAEA8B,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAChC,IAAI,CACbkB,GAAG,CAAM,GAAG,IAAI,CAACjB,MAAM,qBAAqB,EAAE;MAAEC,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC,CACxEwB,IAAI,CAAC7B,GAAG,CAAE8B,GAAG,IAAKA,GAAG,CAACF,IAAI,CAAC,CAAC;EACjC;EAEAQ,aAAaA,CAAA;IACX,OAAO,IAAI,CAACjC,IAAI,CACbkB,GAAG,CAAM,GAAG,IAAI,CAACjB,MAAM,mBAAmB,EAAE;MAAEC,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAAC,CACtEwB,IAAI,CAAC7B,GAAG,CAAE8B,GAAG,IAAKA,GAAG,CAACF,IAAI,CAAC,CAAC;EACjC;EACAS,wBAAwBA,CAACC,SAAc;IACrC,OAAO,IAAI,CAACnC,IAAI,CAACkB,GAAG,CAAM,GAAG,IAAI,CAACjB,MAAM,IAAIkC,SAAS,iBAAiB,CAAC;EACzE;EAEAC,QAAQA,CAACd,EAAO;IACd,OAAO,IAAI,CAACtB,IAAI,CAACkB,GAAG,CAAM,GAAG,IAAI,CAACjB,MAAM,mBAAmBqB,EAAE,EAAE,EAAE;MAC/DpB,OAAO,EAAE,IAAI,CAACA;KACf,CAAC;EACJ;EAEAmC,WAAWA,CAACf,EAAO;IACjB,OAAO,IAAI,CAACtB,IAAI,CAACsC,MAAM,CAAM,GAAG,IAAI,CAACrC,MAAM,mBAAmBqB,EAAE,EAAE,EAAE;MAClEpB,OAAO,EAAE,IAAI,CAACA;KACf,CAAC;EACJ;EAEAqC,eAAeA,CAACC,KAAU;IACxB,OAAO,IAAI,CAACxC,IAAI,CAACyC,IAAI,CACnB,GAAG,IAAI,CAACxC,MAAM,iBAAiB,EAC/B;MAAEwB,IAAI,EAAEe;IAAK,CAAE,EACf;MAAEtC,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;EACH;;;uBApHWJ,cAAc,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd/C,cAAc;MAAAgD,OAAA,EAAdhD,cAAc,CAAAiD,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}