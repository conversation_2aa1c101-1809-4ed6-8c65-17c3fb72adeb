{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../customer.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/ripple\";\nfunction CustomerAddressDependentComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function CustomerAddressDependentComponent_ng_template_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 15);\n    i0.ɵɵelement(5, \"i\", 16);\n    i0.ɵɵelementStart(6, \"input\", 17, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerAddressDependentComponent_ng_template_4_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.globalSearchTerm, $event) || (ctx_r1.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function CustomerAddressDependentComponent_ng_template_4_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGlobalFilter($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi pi-fw \", ctx_r1.isExpanded ? \"pi-minus\" : \"pi-plus\", \"\");\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.isExpanded ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.globalSearchTerm);\n  }\n}\nfunction CustomerAddressDependentComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 18);\n    i0.ɵɵelementStart(2, \"th\", 19);\n    i0.ɵɵtext(3, \" Customer ID \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 21);\n    i0.ɵɵtext(6, \" Address ID \");\n    i0.ɵɵelement(7, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 23);\n    i0.ɵɵtext(9, \" City Code \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 25);\n    i0.ɵɵtext(12, \" County \");\n    i0.ɵɵelement(13, \"p-sortIcon\", 26);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerAddressDependentComponent_ng_template_6_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"button\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const address_r4 = ctx_r2.$implicit;\n    const expanded_r5 = ctx_r2.expanded;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", address_r4)(\"icon\", expanded_r5 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.customer_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.address_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.city_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (address_r4 == null ? null : address_r4.county) || \"-\", \" \");\n  }\n}\nfunction CustomerAddressDependentComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomerAddressDependentComponent_ng_template_6_tr_0_Template, 11, 6, \"tr\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.addressdetails == null ? null : ctx_r1.addressdetails.length) > 0);\n  }\n}\nfunction CustomerAddressDependentComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 18);\n    i0.ɵɵelementStart(2, \"td\", 29)(3, \"div\", 30)(4, \"div\", 31)(5, \"span\", 32);\n    i0.ɵɵtext(6, \"Customer ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 31)(10, \"span\", 32);\n    i0.ɵɵtext(11, \"Address ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 33);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 31)(15, \"span\", 32);\n    i0.ɵɵtext(16, \"City Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 33);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 31)(20, \"span\", 32);\n    i0.ɵɵtext(21, \"County\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 33);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 31)(25, \"span\", 32);\n    i0.ɵɵtext(26, \"Express Station Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 33);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 31)(30, \"span\", 32);\n    i0.ɵɵtext(31, \"Station Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 33);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const address_r6 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.customer_id) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.address_id) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.city_code) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.county) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.express_train_station_name) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (address_r6 == null ? null : address_r6.train_station_name) || \"-\", \" \");\n  }\n}\nfunction CustomerAddressDependentComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 34);\n    i0.ɵɵtext(2, \" Address Dependent details are not available for this record. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerAddressDependentComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"Loading address data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CustomerAddressDependentComponent {\n  constructor(route, customerservice) {\n    this.route = route;\n    this.customerservice = customerservice;\n    this.unsubscribe$ = new Subject();\n    this.addressdetails = null;\n    this.filteredaddress = [];\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.globalSearchTerm = '';\n    this.totalRecords = 0;\n    this.loading = true;\n    this.id = '';\n  }\n  ngOnInit() {\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\n    this.customerservice.customer.pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.addressdetails = response?.cust_addr_depdnt_informations || [];\n        this.filteredaddress = [...this.addressdetails];\n      },\n      error: err => {\n        console.error('Error fetching data:', err);\n        this.addressdetails = [];\n        this.filteredaddress = [];\n      }\n    });\n  }\n  expandAll() {\n    if (!this.isExpanded) {\n      this.addressdetails.forEach(address => address?.id ? this.expandedRows[address.id] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n    this.isExpanded = !this.isExpanded;\n  }\n  onGlobalFilter(event) {\n    const filterValue = event.target.value.toLowerCase();\n    if (filterValue) {\n      this.filteredaddress = this.addressdetails.filter(address => Object.values(address).some(value => value?.toString().toLowerCase().includes(filterValue)));\n    } else {\n      this.filteredaddress = [...this.addressdetails]; // Reset filter when input is empty\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function CustomerAddressDependentComponent_Factory(t) {\n      return new (t || CustomerAddressDependentComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.CustomerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerAddressDependentComponent,\n      selectors: [[\"app-customer-address-dependent\"]],\n      decls: 10,\n      vars: 3,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"expandedRowKeys\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"gap-2\", \"justify-content-between\"], [\"pButton\", \"\", 3, \"click\", \"icon\", \"label\"], [1, \"flex\", \"table-header\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Address\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"customer_id\"], [\"field\", \"customer_id\"], [\"pSortableColumn\", \"address_id\"], [\"field\", \"address_id\"], [\"pSortableColumn\", \"city_code\"], [\"field\", \"city_code\"], [\"pSortableColumn\", \"county\"], [\"field\", \"county\"], [4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pRowToggler\", \"icon\"], [\"colspan\", \"4\"], [1, \"grid\", \"mx-0\", \"border-1\"], [1, \"col-12\", \"lg:col-3\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"colspan\", \"6\"], [\"colspan\", \"8\"]],\n      template: function CustomerAddressDependentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"p-table\", 4, 0);\n          i0.ɵɵtemplate(4, CustomerAddressDependentComponent_ng_template_4_Template, 8, 4, \"ng-template\", 5)(5, CustomerAddressDependentComponent_ng_template_5_Template, 14, 0, \"ng-template\", 6)(6, CustomerAddressDependentComponent_ng_template_6_Template, 1, 1, \"ng-template\", 7)(7, CustomerAddressDependentComponent_ng_template_7_Template, 34, 6, \"ng-template\", 8)(8, CustomerAddressDependentComponent_ng_template_8_Template, 3, 0, \"ng-template\", 9)(9, CustomerAddressDependentComponent_ng_template_9_Template, 3, 0, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.filteredaddress)(\"rows\", 10)(\"expandedRowKeys\", ctx.expandedRows);\n        }\n      },\n      dependencies: [i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.RowToggler, i5.SortIcon, i7.ButtonDirective, i8.InputText, i9.Ripple],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerAddressDependentComponent_ng_template_4_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "expandAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵtwoWayListener", "CustomerAddressDependentComponent_ng_template_4_Template_input_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "globalSearchTerm", "CustomerAddressDependentComponent_ng_template_4_Template_input_input_6_listener", "onGlobalFilter", "ɵɵadvance", "ɵɵpropertyInterpolate1", "isExpanded", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "ɵɵtext", "ɵɵproperty", "address_r4", "expanded_r5", "ɵɵtextInterpolate1", "customer_id", "address_id", "city_code", "county", "ɵɵtemplate", "CustomerAddressDependentComponent_ng_template_6_tr_0_Template", "addressdetails", "length", "address_r6", "express_train_station_name", "train_station_name", "CustomerAddressDependentComponent", "constructor", "route", "customerservice", "unsubscribe$", "filteredaddress", "expandedRows", "totalRecords", "loading", "id", "ngOnInit", "parent", "snapshot", "paramMap", "get", "customer", "pipe", "subscribe", "next", "response", "cust_addr_depdnt_informations", "error", "err", "console", "for<PERSON>ach", "address", "event", "filterValue", "target", "value", "toLowerCase", "filter", "Object", "values", "some", "toString", "includes", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "CustomerService", "selectors", "decls", "vars", "consts", "template", "CustomerAddressDependentComponent_Template", "rf", "ctx", "CustomerAddressDependentComponent_ng_template_4_Template", "CustomerAddressDependentComponent_ng_template_5_Template", "CustomerAddressDependentComponent_ng_template_6_Template", "CustomerAddressDependentComponent_ng_template_7_Template", "CustomerAddressDependentComponent_ng_template_8_Template", "CustomerAddressDependentComponent_ng_template_9_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer-details\\customer-address-dependent\\customer-address-dependent.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\customer\\customer-details\\customer-address-dependent\\customer-address-dependent.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { CustomerService } from '../../customer.service';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-customer-address-dependent',\r\n  templateUrl: './customer-address-dependent.component.html',\r\n  styleUrl: './customer-address-dependent.component.scss',\r\n})\r\nexport class CustomerAddressDependentComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public addressdetails: any = null;\r\n  public filteredaddress: any[] = [];\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public globalSearchTerm: string = '';\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public id: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private customerservice: CustomerService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.id = this.route.parent?.snapshot.paramMap.get('id') || '';\r\n    this.customerservice.customer.pipe(takeUntil(this.unsubscribe$)).subscribe({\r\n      next: (response) => {\r\n        this.addressdetails = response?.cust_addr_depdnt_informations || [];\r\n        this.filteredaddress = [...this.addressdetails];\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching data:', err);\r\n        this.addressdetails = [];\r\n        this.filteredaddress = [];\r\n      },\r\n    });\r\n  }\r\n\r\n  expandAll() {\r\n    if (!this.isExpanded) {\r\n      this.addressdetails.forEach((address: any) =>\r\n        address?.id ? (this.expandedRows[address.id] = true) : ''\r\n      );\r\n    } else {\r\n      this.expandedRows = {};\r\n    }\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  onGlobalFilter(event: Event) {\r\n    const filterValue = (event.target as HTMLInputElement).value.toLowerCase();\r\n\r\n    if (filterValue) {\r\n      this.filteredaddress = this.addressdetails.filter((address: any) =>\r\n        Object.values(address).some((value: any) =>\r\n          value?.toString().toLowerCase().includes(filterValue)\r\n        )\r\n      );\r\n    } else {\r\n      this.filteredaddress = [...this.addressdetails]; // Reset filter when input is empty\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12\">\r\n    <div class=\"card\">\r\n        <p-table #dt1 [value]=\"filteredaddress\" dataKey=\"id\" [rows]=\"10\" [expandedRowKeys]=\"expandedRows\" responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"caption\">\r\n                <div class=\"flex justify-content-between flex-column sm:flex-row gap-2\">\r\n                    <div class=\"flex flex-row gap-2 justify-content-between\">\r\n                        <button pButton icon=\"pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}\"\r\n                            label=\"{{ isExpanded ? 'Collapse All' : 'Expand All' }}\" (click)=\"expandAll()\"></button>\r\n                        <div class=\"flex table-header\"></div>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                            (input)=\"onGlobalFilter($event)\" placeholder=\"Search Address\" class=\"w-full\" />\r\n                    </span>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"width: 3rem\"></th>\r\n                    <th pSortableColumn=\"customer_id\">\r\n                        Customer ID <p-sortIcon field=\"customer_id\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"address_id\">\r\n                        Address ID <p-sortIcon field=\"address_id\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"city_code\">\r\n                        City Code <p-sortIcon field=\"city_code\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"county\">\r\n                        County <p-sortIcon field=\"county\"></p-sortIcon>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-address let-expanded=\"expanded\">\r\n                <tr *ngIf=\"addressdetails?.length > 0\">\r\n                    <td>\r\n                        <button type=\"button\" pButton pRipple [pRowToggler]=\"address\"\r\n                            class=\"p-button-text p-button-rounded p-button-plain\"\r\n                            [icon]=\"expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\"></button>\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.customer_id || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.address_id || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.city_code || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ address?.county || \"-\" }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"rowexpansion\" let-address>\r\n                <tr>\r\n                    <td style=\"width: 3rem\"></td>\r\n                    <td colspan=\"4\">\r\n                        <div class=\"grid mx-0 border-1\">\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Customer ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.customer_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Address ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.address_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">City Code</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.city_code || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">County</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.county || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Express Station Name</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.express_train_station_name || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-3\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Station Name</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ address?.train_station_name || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">\r\n                        Address Dependent details are not available for this record.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading address data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICIjBC,EAFR,CAAAC,cAAA,cAAwE,cACX,iBAE8B;IAAtBD,EAAA,CAAAE,UAAA,mBAAAC,iFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAS;IAC5FV,EAAA,CAAAW,SAAA,cAAqC;IACzCX,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAW,SAAA,YAA4B;IAC5BX,EAAA,CAAAC,cAAA,mBACmF;IAD7CD,EAAA,CAAAY,gBAAA,2BAAAC,wFAAAC,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAe,kBAAA,CAAAT,MAAA,CAAAU,gBAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,gBAAA,GAAAF,MAAA;MAAA,OAAAd,EAAA,CAAAQ,WAAA,CAAAM,MAAA;IAAA,EAA8B;IAChEd,EAAA,CAAAE,UAAA,mBAAAe,gFAAAH,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAE5Cd,EAHQ,CAAAU,YAAA,EACmF,EAChF,EACL;;;;IATkBV,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAAoB,sBAAA,sBAAAd,MAAA,CAAAe,UAAA,8BAAyD;IACrErB,EAAA,CAAAsB,qBAAA,UAAAhB,MAAA,CAAAe,UAAA,iCAAwD;IAKtBrB,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAuB,gBAAA,YAAAjB,MAAA,CAAAU,gBAAA,CAA8B;;;;;IAO5EhB,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAC7BX,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAAwB,MAAA,oBAAY;IAAAxB,EAAA,CAAAW,SAAA,qBAA6C;IAC7DX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAiC;IAC7BD,EAAA,CAAAwB,MAAA,mBAAW;IAAAxB,EAAA,CAAAW,SAAA,qBAA4C;IAC3DX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAgC;IAC5BD,EAAA,CAAAwB,MAAA,kBAAU;IAAAxB,EAAA,CAAAW,SAAA,sBAA2C;IACzDX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,cAA6B;IACzBD,EAAA,CAAAwB,MAAA,gBAAO;IAAAxB,EAAA,CAAAW,SAAA,sBAAwC;IAEvDX,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAuC,SAC/B;IACAD,EAAA,CAAAW,SAAA,iBAE8E;IAClFX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;;IAhByCV,EAAA,CAAAmB,SAAA,GAAuB;IAEzDnB,EAFkC,CAAAyB,UAAA,gBAAAC,UAAA,CAAuB,SAAAC,WAAA,gDAEO;IAGpE3B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAG,WAAA,cACJ;IAEI7B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAI,UAAA,cACJ;IAEI9B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAK,SAAA,cACJ;IAEI/B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAF,UAAA,kBAAAA,UAAA,CAAAM,MAAA,cACJ;;;;;IAjBJhC,EAAA,CAAAiC,UAAA,IAAAC,6DAAA,kBAAuC;;;;IAAlClC,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAA6B,cAAA,kBAAA7B,MAAA,CAAA6B,cAAA,CAAAC,MAAA,MAAgC;;;;;IAqBrCpC,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAIjBX,EAHZ,CAAAC,cAAA,aAAgB,cACoB,cACC,eAC+B;IAAAD,EAAA,CAAAwB,MAAA,kBAAW;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IAC1EV,EAAA,CAAAC,cAAA,eAA8C;IAC1CD,EAAA,CAAAwB,MAAA,GACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,cAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,kBAAU;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IACzEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,iBAAS;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IACxEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,cAAM;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IACrEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,4BAAoB;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IACnFV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IACJxB,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAAwB,MAAA,oBAAY;IAAAxB,EAAA,CAAAU,YAAA,EAAO;IAC3EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAAwB,MAAA,IACJ;IAIhBxB,EAJgB,CAAAU,YAAA,EAAO,EACL,EACJ,EACL,EACJ;;;;IAnCeV,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAR,WAAA,cACJ;IAKI7B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAP,UAAA,cACJ;IAKI9B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAN,SAAA,cACJ;IAKI/B,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAL,MAAA,cACJ;IAKIhC,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAC,0BAAA,cACJ;IAKItC,EAAA,CAAAmB,SAAA,GACJ;IADInB,EAAA,CAAA4B,kBAAA,OAAAS,UAAA,kBAAAA,UAAA,CAAAE,kBAAA,cACJ;;;;;IAQZvC,EADJ,CAAAC,cAAA,SAAI,aACgB;IACZD,EAAA,CAAAwB,MAAA,qEACJ;IACJxB,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAwB,MAAA,2CAAoC;IACxDxB,EADwD,CAAAU,YAAA,EAAK,EACxD;;;ADjGrB,OAAM,MAAO8B,iCAAiC;EAW5CC,YACUC,KAAqB,EACrBC,eAAgC;IADhC,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IAZjB,KAAAC,YAAY,GAAG,IAAI9C,OAAO,EAAQ;IACnC,KAAAqC,cAAc,GAAQ,IAAI;IAC1B,KAAAU,eAAe,GAAU,EAAE;IAC3B,KAAAxB,UAAU,GAAY,KAAK;IAC3B,KAAAyB,YAAY,GAAiB,EAAE;IAC/B,KAAA9B,gBAAgB,GAAW,EAAE;IAC7B,KAAA+B,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,EAAE,GAAW,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,EAAE,GAAG,IAAI,CAACP,KAAK,CAACS,MAAM,EAAEC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IAC9D,IAAI,CAACX,eAAe,CAACY,QAAQ,CAACC,IAAI,CAACzD,SAAS,CAAC,IAAI,CAAC6C,YAAY,CAAC,CAAC,CAACa,SAAS,CAAC;MACzEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACxB,cAAc,GAAGwB,QAAQ,EAAEC,6BAA6B,IAAI,EAAE;QACnE,IAAI,CAACf,eAAe,GAAG,CAAC,GAAG,IAAI,CAACV,cAAc,CAAC;MACjD,CAAC;MACD0B,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAAC3B,cAAc,GAAG,EAAE;QACxB,IAAI,CAACU,eAAe,GAAG,EAAE;MAC3B;KACD,CAAC;EACJ;EAEApC,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACY,UAAU,EAAE;MACpB,IAAI,CAACc,cAAc,CAAC6B,OAAO,CAAEC,OAAY,IACvCA,OAAO,EAAEhB,EAAE,GAAI,IAAI,CAACH,YAAY,CAACmB,OAAO,CAAChB,EAAE,CAAC,GAAG,IAAI,GAAI,EAAE,CAC1D;IACH,CAAC,MAAM;MACL,IAAI,CAACH,YAAY,GAAG,EAAE;IACxB;IACA,IAAI,CAACzB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAH,cAAcA,CAACgD,KAAY;IACzB,MAAMC,WAAW,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK,CAACC,WAAW,EAAE;IAE1E,IAAIH,WAAW,EAAE;MACf,IAAI,CAACtB,eAAe,GAAG,IAAI,CAACV,cAAc,CAACoC,MAAM,CAAEN,OAAY,IAC7DO,MAAM,CAACC,MAAM,CAACR,OAAO,CAAC,CAACS,IAAI,CAAEL,KAAU,IACrCA,KAAK,EAAEM,QAAQ,EAAE,CAACL,WAAW,EAAE,CAACM,QAAQ,CAACT,WAAW,CAAC,CACtD,CACF;IACH,CAAC,MAAM;MACL,IAAI,CAACtB,eAAe,GAAG,CAAC,GAAG,IAAI,CAACV,cAAc,CAAC,CAAC,CAAC;IACnD;EACF;EAEA0C,WAAWA,CAAA;IACT,IAAI,CAACjC,YAAY,CAACc,IAAI,EAAE;IACxB,IAAI,CAACd,YAAY,CAACkC,QAAQ,EAAE;EAC9B;;;uBA3DWtC,iCAAiC,EAAAxC,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAjC3C,iCAAiC;MAAA4C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZtC1F,EAFR,CAAAC,cAAA,aAAoB,aACE,oBAC8G;UA0GxHD,EAzGA,CAAAiC,UAAA,IAAA2D,wDAAA,yBAAiC,IAAAC,wDAAA,0BAeD,IAAAC,wDAAA,yBAiBkC,IAAAC,wDAAA,0BAqBhB,IAAAC,wDAAA,yBA6CZ,IAAAC,wDAAA,0BAOD;UAOjDjG,EAFQ,CAAAU,YAAA,EAAU,EACR,EACJ;;;UAjHgBV,EAAA,CAAAmB,SAAA,GAAyB;UAA0BnB,EAAnD,CAAAyB,UAAA,UAAAkE,GAAA,CAAA9C,eAAA,CAAyB,YAAyB,oBAAA8C,GAAA,CAAA7C,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}