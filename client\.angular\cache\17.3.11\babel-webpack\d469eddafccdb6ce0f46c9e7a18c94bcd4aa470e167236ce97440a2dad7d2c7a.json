{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { EditorModule } from 'primeng/editor';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { RippleModule } from 'primeng/ripple';\nimport { MultiSelectModule } from 'primeng/multiselect';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { ToastModule } from 'primeng/toast';\nimport { TabMenuModule } from 'primeng/tabmenu';\nimport { SliderModule } from 'primeng/slider';\nimport { RatingModule } from 'primeng/rating';\nimport { HttpClientModule } from '@angular/common/http';\nimport { GroupByPipe } from 'src/app/shared/pipes/group-by.pipe';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { TabViewModule } from 'primeng/tabview';\nimport { AutoCompleteModule } from 'primeng/autocomplete';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ProductRoutingModule } from './product-routing.module';\nimport { ProductComponent } from './product.component';\nimport { ProductDetailsComponent } from './product-details/product-details.component';\nimport { GeneralComponent } from './product-details/general/general.component';\nimport { BackendComponent } from './product-details/backend/backend.component';\nimport { PricingComponent } from './product-details/pricing/pricing.component';\nimport { CategoryComponent } from './product-details/category/category.component';\nimport { MediaComponent } from './product-details/media/media.component';\nimport { ClassficationComponent } from './product-details/classfication/classfication.component';\nimport { PlantComponent } from './product-details/plant/plant.component';\nimport { SimilarProductsComponent } from './product-details/similar-products/similar-products.component';\nimport * as i0 from \"@angular/core\";\nexport class ProductModule {\n  static {\n    this.ɵfac = function ProductModule_Factory(t) {\n      return new (t || ProductModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProductModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [MessageService, ConfirmationService],\n      imports: [CommonModule, ProductRoutingModule, FormsModule, EditorModule, TableModule, RatingModule, ButtonModule, CheckboxModule, SliderModule, InputTextModule, ToggleButtonModule, TabMenuModule, ReactiveFormsModule, RippleModule, MultiSelectModule, DropdownModule, ProgressBarModule, ToastModule, HttpClientModule, InputNumberModule, TabViewModule, AutoCompleteModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProductModule, {\n    declarations: [ProductComponent, ProductDetailsComponent, GeneralComponent, BackendComponent, PricingComponent, CategoryComponent, MediaComponent, ClassficationComponent, PlantComponent, SimilarProductsComponent, GroupByPipe],\n    imports: [CommonModule, ProductRoutingModule, FormsModule, EditorModule, TableModule, RatingModule, ButtonModule, CheckboxModule, SliderModule, InputTextModule, ToggleButtonModule, TabMenuModule, ReactiveFormsModule, RippleModule, MultiSelectModule, DropdownModule, ProgressBarModule, ToastModule, HttpClientModule, InputNumberModule, TabViewModule, AutoCompleteModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "EditorModule", "TableModule", "ButtonModule", "CheckboxModule", "InputTextModule", "ToggleButtonModule", "RippleModule", "MultiSelectModule", "DropdownModule", "ProgressBarModule", "ToastModule", "TabMenuModule", "SliderModule", "RatingModule", "HttpClientModule", "GroupByPipe", "InputNumberModule", "TabViewModule", "AutoCompleteModule", "MessageService", "ConfirmationService", "ReactiveFormsModule", "ProductRoutingModule", "ProductComponent", "ProductDetailsComponent", "GeneralComponent", "BackendComponent", "PricingComponent", "CategoryComponent", "MediaComponent", "ClassficationComponent", "PlantComponent", "SimilarProductsComponent", "ProductModule", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { EditorModule } from 'primeng/editor';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { ToggleButtonModule } from 'primeng/togglebutton';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { MultiSelectModule } from 'primeng/multiselect';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { ProgressBarModule } from 'primeng/progressbar';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { TabMenuModule } from 'primeng/tabmenu';\r\nimport { SliderModule } from 'primeng/slider';\r\nimport { RatingModule } from 'primeng/rating';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { GroupByPipe } from 'src/app/shared/pipes/group-by.pipe';\r\nimport { InputNumberModule } from 'primeng/inputnumber';\r\nimport { TabViewModule } from 'primeng/tabview';\r\nimport { AutoCompleteModule } from 'primeng/autocomplete';\r\nimport { MessageService, ConfirmationService } from 'primeng/api';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { ProductRoutingModule } from './product-routing.module';\r\nimport { ProductComponent } from './product.component';\r\nimport { ProductDetailsComponent } from './product-details/product-details.component';\r\nimport { GeneralComponent } from './product-details/general/general.component';\r\nimport { BackendComponent } from './product-details/backend/backend.component';\r\nimport { PricingComponent } from './product-details/pricing/pricing.component';\r\nimport { CategoryComponent } from './product-details/category/category.component';\r\nimport { MediaComponent } from './product-details/media/media.component';\r\nimport { ClassficationComponent } from './product-details/classfication/classfication.component';\r\nimport { PlantComponent } from './product-details/plant/plant.component';\r\nimport { SimilarProductsComponent } from './product-details/similar-products/similar-products.component';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ProductComponent,\r\n    ProductDetailsComponent,\r\n    GeneralComponent,\r\n    BackendComponent,\r\n    PricingComponent,\r\n    CategoryComponent,\r\n    MediaComponent,\r\n    ClassficationComponent,\r\n    PlantComponent,\r\n    SimilarProductsComponent,\r\n    GroupByPipe\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ProductRoutingModule,\r\n    FormsModule,\r\n    EditorModule,\r\n    TableModule,\r\n    RatingModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    SliderModule,\r\n    InputTextModule,\r\n    ToggleButtonModule,\r\n    TabMenuModule,\r\n    ReactiveFormsModule,\r\n    RippleModule,\r\n    MultiSelectModule,\r\n    DropdownModule,\r\n    ProgressBarModule,\r\n    ToastModule,\r\n    HttpClientModule,\r\n    InputNumberModule,\r\n    TabViewModule,\r\n    AutoCompleteModule,\r\n  ],\r\n  providers: [MessageService, ConfirmationService],\r\n})\r\nexport class ProductModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,aAAa;AACjE,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,wBAAwB,QAAQ,+DAA+D;;AA4CxG,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;iBAFb,CAACd,cAAc,EAAEC,mBAAmB,CAAC;MAAAc,OAAA,GAvB9CpC,YAAY,EACZwB,oBAAoB,EACpBvB,WAAW,EACXC,YAAY,EACZC,WAAW,EACXY,YAAY,EACZX,YAAY,EACZC,cAAc,EACdS,YAAY,EACZR,eAAe,EACfC,kBAAkB,EAClBM,aAAa,EACbU,mBAAmB,EACnBf,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,iBAAiB,EACjBC,WAAW,EACXI,gBAAgB,EAChBE,iBAAiB,EACjBC,aAAa,EACbC,kBAAkB;IAAA;EAAA;;;2EAITe,aAAa;IAAAE,YAAA,GAtCtBZ,gBAAgB,EAChBC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,sBAAsB,EACtBC,cAAc,EACdC,wBAAwB,EACxBjB,WAAW;IAAAmB,OAAA,GAGXpC,YAAY,EACZwB,oBAAoB,EACpBvB,WAAW,EACXC,YAAY,EACZC,WAAW,EACXY,YAAY,EACZX,YAAY,EACZC,cAAc,EACdS,YAAY,EACZR,eAAe,EACfC,kBAAkB,EAClBM,aAAa,EACbU,mBAAmB,EACnBf,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,iBAAiB,EACjBC,WAAW,EACXI,gBAAgB,EAChBE,iBAAiB,EACjBC,aAAa,EACbC,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}