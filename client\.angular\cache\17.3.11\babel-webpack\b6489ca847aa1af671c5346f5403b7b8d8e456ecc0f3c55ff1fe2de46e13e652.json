{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = () => [\"/login\"];\nfunction SignupComponent_option_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(country_r1);\n  }\n}\nfunction SignupComponent_option_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const question_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", question_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(question_r2.name);\n  }\n}\nfunction SignupComponent_option_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const question_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", question_r3.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(question_r3.name);\n  }\n}\nexport class SignupComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.countries = ['USA', 'Canada', 'UK'];\n    this.questions = [{\n      id: 1,\n      name: 'What is your pet’s name?'\n    }, {\n      id: 2,\n      name: 'What is your mother’s maiden name?'\n    }, {\n      id: 3,\n      name: 'What city were you born in?'\n    }, {\n      id: 4,\n      name: 'What is your first school’s name?'\n    }];\n    this.registrationForm = this.fb.group({\n      firstname: ['', Validators.required],\n      lastname: ['', Validators.required],\n      username: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      address: ['', Validators.required],\n      country: ['', Validators.required],\n      city: ['', Validators.required],\n      zipcode: ['', [Validators.required, Validators.pattern('^[0-9]{5}$')]],\n      invoice_ref: [''],\n      purchase_order: [''],\n      vendor_id: [''],\n      security_que_1: ['', Validators.required],\n      security_que_1_ans: ['', Validators.required],\n      security_que_2: ['', Validators.required],\n      security_que_2_ans: ['', Validators.required]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    return form.get('password')?.value === form.get('confirmPassword')?.value ? null : {\n      mismatch: true\n    };\n  }\n  submitForm() {\n    if (this.registrationForm.valid) {\n      console.log('Form Data:', this.registrationForm.value);\n    } else {\n      console.log('Form Invalid');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 97,\n      vars: 6,\n      consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"pt-6\", \"pb-6\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"max-w-15rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"max-w-30rem\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", 3, \"ngSubmit\", \"formGroup\"], [1, \"mb-2\", \"text-4xl\", \"font-bold\", \"text-primary\", \"text-center\"], [1, \"grid\", \"p-fluid\"], [1, \"field\", \"col-6\"], [\"type\", \"text\", \"formControlName\", \"firstname\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"lastname\", 1, \"p-inputtext\", \"w-full\"], [1, \"field\", \"col-12\"], [\"type\", \"text\", \"formControlName\", \"username\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"password\", \"formControlName\", \"password\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"address\", 1, \"p-inputtext\", \"w-full\"], [\"formControlName\", \"country\", 1, \"p-inputtext\", \"w-full\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"city\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"zipcode\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"invoice_ref\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"purchase_order\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"vendor_id\", 1, \"p-inputtext\", \"w-full\"], [\"formControlName\", \"security_que_1\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"security_que_1_ans\", 1, \"p-inputtext\", \"w-full\"], [\"formControlName\", \"security_que_2\", 1, \"p-inputtext\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"security_que_2_ans\", 1, \"p-inputtext\", \"w-full\"], [1, \"form-footer\", \"mt-4\"], [\"type\", \"submit\", 1, \"p-button\", \"p-button-rounded\", \"p-button-primary\", \"w-full\"], [1, \"copyright-sec\", \"flex\", \"flex-column\", \"position-relative\"], [1, \"m-0\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-0\", \"flex\", \"position-relative\", \"align-items-center\", \"justify-content-center\", \"list-none\", \"gap-3\"], [\"target\", \"_blank\", 1, \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-primary\", \"underline\"], [3, \"value\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtext(7, \" Do you have an account? \");\n          i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n          i0.ɵɵtext(10, \"input\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.submitForm();\n          });\n          i0.ɵɵelementStart(14, \"h1\", 11);\n          i0.ɵɵtext(15, \"Registration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13)(18, \"label\");\n          i0.ɵɵtext(19, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 13)(22, \"label\");\n          i0.ɵɵtext(23, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"input\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 16)(26, \"label\");\n          i0.ɵɵtext(27, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 16)(30, \"label\");\n          i0.ɵɵtext(31, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 16)(34, \"label\");\n          i0.ɵɵtext(35, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 16)(38, \"label\");\n          i0.ɵɵtext(39, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 13)(42, \"label\");\n          i0.ɵɵtext(43, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"select\", 21);\n          i0.ɵɵtemplate(45, SignupComponent_option_45_Template, 2, 2, \"option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 13)(47, \"label\");\n          i0.ɵɵtext(48, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"input\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 13)(51, \"label\");\n          i0.ɵɵtext(52, \"Zip Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(53, \"input\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 13)(55, \"label\");\n          i0.ɵɵtext(56, \"Invoice Ref #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(57, \"input\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 13)(59, \"label\");\n          i0.ɵɵtext(60, \"Purchase Order #\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(61, \"input\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 13)(63, \"label\");\n          i0.ɵɵtext(64, \"Vendor ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"input\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 16)(67, \"label\");\n          i0.ɵɵtext(68, \"Security Question 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"select\", 28);\n          i0.ɵɵtemplate(70, SignupComponent_option_70_Template, 2, 2, \"option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 16)(72, \"label\");\n          i0.ɵɵtext(73, \"Answer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"input\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 16)(76, \"label\");\n          i0.ɵɵtext(77, \"Security Question 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"select\", 30);\n          i0.ɵɵtemplate(79, SignupComponent_option_79_Template, 2, 2, \"option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 16)(81, \"label\");\n          i0.ɵɵtext(82, \"Answer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(83, \"input\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 32)(85, \"button\", 33);\n          i0.ɵɵtext(86, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(87, \"div\", 34)(88, \"p\", 35);\n          i0.ɵɵtext(89, \"\\u00A9 2024 Consolidated Hospitality Supplies\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"ul\", 36)(91, \"li\")(92, \"a\", 37);\n          i0.ɵɵtext(93, \"Terms & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"li\")(95, \"a\", 37);\n          i0.ɵɵtext(96, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.questions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.questions);\n        }\n      },\n      dependencies: [i2.NgForOf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 600px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2Vzc2lvbi9zaWdudXAvc2lnbnVwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0ksaUJBQUE7QUFBUjtBQUVRO0VBQ0ksMkJBQUE7QUFBWjtBQUlvQjtFQUNJLFdBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtBQUZ4QjtBQUtvQjtFQUNJLGlDQUFBO0FBSHhCOztBQVdBO0VBQ0ksWUFBQTtFQUNBLDJCQUFBO0FBUko7O0FBV0E7RUFDSSxjQUFBO0FBUkoiLCJzb3VyY2VzQ29udGVudCI6WyIubG9naW4tc2VjIHtcclxuICAgIC5sb2dpbi1wYWdlLWJvZHkge1xyXG4gICAgICAgIG1heC13aWR0aDogMTQ0MHB4O1xyXG5cclxuICAgICAgICAubG9naW4tZm9ybSB7XHJcbiAgICAgICAgICAgIG1heC13aWR0aDogNjAwcHggIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgICAgIGZvcm0ge1xyXG4gICAgICAgICAgICAgICAgLmZvcm0tZ3JvdXAge1xyXG4gICAgICAgICAgICAgICAgICAgIC5wYXNzLXNob3ctYnRuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IDEycHg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMjRweDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDI0cHg7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuZm9ybS1jaGVjay1ib3gge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NlbnQtY29sb3I6IHZhcigtLXByaW1hcnljb2xvcik7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4ucC1pbnB1dHRleHQge1xyXG4gICAgaGVpZ2h0OiAzcmVtO1xyXG4gICAgYXBwZWFyYW5jZTogYXV0byAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uaC0zLTNyZW0ge1xyXG4gICAgaGVpZ2h0OiAzLjNyZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "country_r1", "ɵɵadvance", "ɵɵtextInterpolate", "question_r2", "id", "name", "question_r3", "SignupComponent", "constructor", "fb", "countries", "questions", "registrationForm", "group", "firstname", "required", "lastname", "username", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "address", "country", "city", "zipcode", "pattern", "invoice_ref", "purchase_order", "vendor_id", "security_que_1", "security_que_1_ans", "security_que_2", "security_que_2_ans", "validators", "passwordMatchValidator", "form", "get", "value", "mismatch", "submitForm", "valid", "console", "log", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_13_listener", "ɵɵtemplate", "SignupComponent_option_45_Template", "SignupComponent_option_70_Template", "SignupComponent_option_79_Template", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\signup\\signup.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\session\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormGroup, FormBuilder, Validators } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-signup',\r\n  templateUrl: './signup.component.html',\r\n  styleUrl: './signup.component.scss'\r\n})\r\nexport class SignupComponent {\r\n  registrationForm: FormGroup;\r\n  countries = ['USA', 'Canada', 'UK'];\r\n  questions = [\r\n    { id: 1, name: 'What is your pet’s name?' },\r\n    { id: 2, name: 'What is your mother’s maiden name?' },\r\n    { id: 3, name: 'What city were you born in?' },\r\n    { id: 4, name: 'What is your first school’s name?' }\r\n  ];\r\n\r\n  constructor(private fb: FormBuilder) {\r\n    this.registrationForm = this.fb.group(\r\n      {\r\n        firstname: ['', Validators.required],\r\n        lastname: ['', Validators.required],\r\n        username: ['', Validators.required],\r\n        email: ['', [Validators.required, Validators.email]],\r\n        password: ['', [Validators.required, Validators.minLength(6)]],\r\n        address: ['', Validators.required],\r\n        country: ['', Validators.required],\r\n        city: ['', Validators.required],\r\n        zipcode: ['', [Validators.required, Validators.pattern('^[0-9]{5}$')]],\r\n        invoice_ref: [''],\r\n        purchase_order: [''],\r\n        vendor_id: [''],\r\n        security_que_1: ['', Validators.required],\r\n        security_que_1_ans: ['', Validators.required],\r\n        security_que_2: ['', Validators.required],\r\n        security_que_2_ans: ['', Validators.required],\r\n      },\r\n      { validators: this.passwordMatchValidator }\r\n    );\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    return form.get('password')?.value === form.get('confirmPassword')?.value\r\n      ? null\r\n      : { mismatch: true };\r\n  }\r\n\r\n  submitForm() {\r\n    if (this.registrationForm.valid) {\r\n      console.log('Form Data:', this.registrationForm.value);\r\n    } else {\r\n      console.log('Form Invalid');\r\n    }\r\n  }\r\n}\r\n", "<section class=\"login-sec bg-white h-screen pt-6 pb-6\">\r\n  <div class=\"login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full\">\r\n    <div class=\"login-header relative flex align-items-center justify-content-between\">\r\n      <div class=\"logo w-full max-w-15rem\"><a href=\"#\" class=\"flex w-full\"><img src=\"/assets/layout/images/chs-logo.svg\"\r\n            alt=\"Logo\" class=\"w-full\" /></a></div>\r\n      <div class=\"sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium\">\r\n        Do you have an account?\r\n        <button type=\"button\" [routerLink]=\"['/login']\"\r\n          class=\"sign-up-btn p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1\">\r\n          <span class=\"material-symbols-rounded text-2xl\">input</span> Login\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"login-form mx-auto p-5 max-w-30rem w-full bg-white border-round-3xl shadow-2\">\r\n      <form [formGroup]=\"registrationForm\" (ngSubmit)=\"submitForm()\" class=\"flex flex-column\">\r\n        <h1 class=\"mb-2 text-4xl font-bold text-primary text-center\">Registration</h1>\r\n\r\n        <div class=\"grid p-fluid\">\r\n          <div class=\"field col-6\">\r\n            <label>First Name</label>\r\n            <input type=\"text\" formControlName=\"firstname\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Last Name</label>\r\n            <input type=\"text\" formControlName=\"lastname\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Username</label>\r\n            <input type=\"text\" formControlName=\"username\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Email</label>\r\n            <input type=\"email\" formControlName=\"email\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Password</label>\r\n            <input type=\"password\" formControlName=\"password\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Address</label>\r\n            <input type=\"text\" formControlName=\"address\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Country</label>\r\n            <select formControlName=\"country\" class=\"p-inputtext w-full\">\r\n              <option *ngFor=\"let country of countries\" [value]=\"country\">{{ country }}</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>City</label>\r\n            <input type=\"text\" formControlName=\"city\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Zip Code</label>\r\n            <input type=\"text\" formControlName=\"zipcode\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Invoice Ref #</label>\r\n            <input type=\"text\" formControlName=\"invoice_ref\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Purchase Order #</label>\r\n            <input type=\"text\" formControlName=\"purchase_order\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-6\">\r\n            <label>Vendor ID</label>\r\n            <input type=\"text\" formControlName=\"vendor_id\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Security Question 1</label>\r\n            <select formControlName=\"security_que_1\" class=\"p-inputtext w-full\">\r\n              <option *ngFor=\"let question of questions\" [value]=\"question.id\">{{ question.name }}</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Answer</label>\r\n            <input type=\"text\" formControlName=\"security_que_1_ans\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Security Question 2</label>\r\n            <select formControlName=\"security_que_2\" class=\"p-inputtext w-full\">\r\n              <option *ngFor=\"let question of questions\" [value]=\"question.id\">{{ question.name }}</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"field col-12\">\r\n            <label>Answer</label>\r\n            <input type=\"text\" formControlName=\"security_que_2_ans\" class=\"p-inputtext w-full\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-footer mt-4\">\r\n          <button type=\"submit\" class=\"p-button p-button-rounded p-button-primary w-full\">Submit</button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n    <div class=\"copyright-sec flex flex-column position-relative\">\r\n      <p class=\"m-0 flex justify-content-center text-base font-medium text-gray-900\">© 2024 Consolidated Hospitality\r\n        Supplies</p>\r\n      <ul class=\"p-0 flex position-relative align-items-center justify-content-center list-none gap-3\">\r\n        <li><a target=\"_blank\" class=\"flex justify-content-center text-base font-medium text-primary underline\">Terms &\r\n            Conditions</a></li>\r\n        <li><a target=\"_blank\" class=\"flex justify-content-center text-base font-medium text-primary underline\">Privacy\r\n            Policy</a></li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;IC4CrDC,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAxCH,EAAA,CAAAI,UAAA,UAAAC,UAAA,CAAiB;IAACL,EAAA,CAAAM,SAAA,EAAa;IAAbN,EAAA,CAAAO,iBAAA,CAAAF,UAAA,CAAa;;;;;IA0BzEL,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlDH,EAAA,CAAAI,UAAA,UAAAI,WAAA,CAAAC,EAAA,CAAqB;IAACT,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,iBAAA,CAAAC,WAAA,CAAAE,IAAA,CAAmB;;;;;IAUpFV,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlDH,EAAA,CAAAI,UAAA,UAAAO,WAAA,CAAAF,EAAA,CAAqB;IAACT,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,iBAAA,CAAAI,WAAA,CAAAD,IAAA,CAAmB;;;ADzElG,OAAM,MAAOE,eAAe;EAU1BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IARtB,KAAAC,SAAS,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;IACnC,KAAAC,SAAS,GAAG,CACV;MAAEP,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAA0B,CAAE,EAC3C;MAAED,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAoC,CAAE,EACrD;MAAED,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAA6B,CAAE,EAC9C;MAAED,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAmC,CAAE,CACrD;IAGC,IAAI,CAACO,gBAAgB,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CACnC;MACEC,SAAS,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACqB,QAAQ,CAAC;MACpCC,QAAQ,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACqB,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAEvB,UAAU,CAACqB,QAAQ,CAAC;MACnCG,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACwB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACzB,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,OAAO,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAACqB,QAAQ,CAAC;MAClCO,OAAO,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAACqB,QAAQ,CAAC;MAClCQ,IAAI,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAACqB,QAAQ,CAAC;MAC/BS,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAAC+B,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;MACtEC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACqB,QAAQ,CAAC;MACzCe,kBAAkB,EAAE,CAAC,EAAE,EAAEpC,UAAU,CAACqB,QAAQ,CAAC;MAC7CgB,cAAc,EAAE,CAAC,EAAE,EAAErC,UAAU,CAACqB,QAAQ,CAAC;MACzCiB,kBAAkB,EAAE,CAAC,EAAE,EAAEtC,UAAU,CAACqB,QAAQ;KAC7C,EACD;MAAEkB,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC5C;EACH;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,OAAOA,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,KAAKF,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEC,KAAK,GACrE,IAAI,GACJ;MAAEC,QAAQ,EAAE;IAAI,CAAE;EACxB;EAEAC,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC3B,gBAAgB,CAAC4B,KAAK,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC9B,gBAAgB,CAACyB,KAAK,CAAC;IACxD,CAAC,MAAM;MACLI,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC7B;EACF;;;uBA9CWnC,eAAe,EAAAZ,EAAA,CAAAgD,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAftC,eAAe;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLezD,EAH3C,CAAAC,cAAA,iBAAuD,aAC0C,aACV,aAC5C,WAAgC;UAAAD,EAAA,CAAA2D,SAAA,aACnC;UAAI3D,EAAJ,CAAAG,YAAA,EAAI,EAAM;UAC5CH,EAAA,CAAAC,cAAA,aAA6G;UAC3GD,EAAA,CAAAE,MAAA,gCACA;UAEEF,EAFF,CAAAC,cAAA,gBACqI,cACnF;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAC/D;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,cAA0F,gBACA;UAAnDD,EAAA,CAAA4D,UAAA,sBAAAC,mDAAA;YAAA,OAAYH,GAAA,CAAAd,UAAA,EAAY;UAAA,EAAC;UAC5D5C,EAAA,CAAAC,cAAA,cAA6D;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI1EH,EAFJ,CAAAC,cAAA,eAA0B,eACC,aAChB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzBH,EAAA,CAAA2D,SAAA,iBAA4E;UAC9E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAA2D,SAAA,iBAA2E;UAC7E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA2D,SAAA,iBAA2E;UAC7E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAA2D,SAAA,iBAAyE;UAC3E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA2D,SAAA,iBAA+E;UACjF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAA2D,SAAA,iBAA0E;UAC5E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAAC,cAAA,kBAA6D;UAC3DD,EAAA,CAAA8D,UAAA,KAAAC,kCAAA,qBAA4D;UAEhE/D,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnBH,EAAA,CAAA2D,SAAA,iBAAuE;UACzE3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAA2D,SAAA,iBAA0E;UAC5E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAA2D,SAAA,iBAA8E;UAChF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/BH,EAAA,CAAA2D,SAAA,iBAAiF;UACnF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAA2D,SAAA,iBAA4E;UAC9E3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,kBAAoE;UAClED,EAAA,CAAA8D,UAAA,KAAAE,kCAAA,qBAAiE;UAErEhE,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrBH,EAAA,CAAA2D,SAAA,iBAAqF;UACvF3D,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,kBAAoE;UAClED,EAAA,CAAA8D,UAAA,KAAAG,kCAAA,qBAAiE;UAErEjE,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,eAA0B,aACjB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrBH,EAAA,CAAA2D,SAAA,iBAAqF;UAEzF3D,EADE,CAAAG,YAAA,EAAM,EACF;UAGJH,EADF,CAAAC,cAAA,eAA8B,kBACoD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAG5FF,EAH4F,CAAAG,YAAA,EAAS,EAC3F,EACD,EACH;UAEJH,EADF,CAAAC,cAAA,eAA8D,aACmB;UAAAD,EAAA,CAAAE,MAAA,qDACrE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAERH,EADN,CAAAC,cAAA,cAAiG,UAC3F,aAAoG;UAAAD,EAAA,CAAAE,MAAA,0BAC1F;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UACnBH,EAAJ,CAAAC,cAAA,UAAI,aAAoG;UAAAD,EAAA,CAAAE,MAAA,sBAC9F;UAIlBF,EAJkB,CAAAG,YAAA,EAAI,EAAK,EAChB,EACD,EACF,EACE;;;UAnGoBH,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAyB;UAO3CnE,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAI,UAAA,cAAAsD,GAAA,CAAAzC,gBAAA,CAA8B;UA+BAjB,EAAA,CAAAM,SAAA,IAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAsD,GAAA,CAAA3C,SAAA,CAAY;UA0BXf,EAAA,CAAAM,SAAA,IAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAsD,GAAA,CAAA1C,SAAA,CAAY;UAUZhB,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAsD,GAAA,CAAA1C,SAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}