{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nexport class ProfileComponent {\n  constructor() {\n    this.toggle = {\n      profile: true,\n      password: false\n    };\n  }\n  toggleState(key) {\n    this.toggle[key] = !this.toggle[key];\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 58,\n      vars: 0,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"m-0\"], [1, \"card\", \"shadow-1\", \"p-4\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-15rem\", \"gap-1\"], [1, \"material-symbols-rounded\"], [1, \"form-input\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"John\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"Smith\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"placeholder\", \"\", \"value\", \"<EMAIL>\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"password\", \"placeholder\", \"\", \"value\", \"Admin@123\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [1, \"form-submit-sec\", \"mt-5\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-12rem\", \"h-3rem\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"My Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"form\", 4)(6, \"div\", 5)(7, \"label\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" User Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtext(12, \": John Smith \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 6)(15, \"span\", 7);\n          i0.ɵɵtext(16, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 8);\n          i0.ɵɵelement(19, \"input\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 5)(21, \"label\", 6)(22, \"span\", 7);\n          i0.ɵɵtext(23, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24, \" Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 8);\n          i0.ɵɵelement(26, \"input\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 5)(28, \"label\", 6)(29, \"span\", 7);\n          i0.ɵɵtext(30, \"phone_in_talk\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(31, \" Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 8);\n          i0.ɵɵelement(33, \"input\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 5)(35, \"label\", 6)(36, \"span\", 7);\n          i0.ɵɵtext(37, \"mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" E-mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 8);\n          i0.ɵɵelement(40, \"input\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 5)(42, \"label\", 6)(43, \"span\", 7);\n          i0.ɵɵtext(44, \"key\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \" Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 8);\n          i0.ɵɵelement(47, \"input\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 5)(49, \"label\", 6)(50, \"span\", 7);\n          i0.ɵɵtext(51, \"key\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(52, \" Retype Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 8);\n          i0.ɵɵelement(54, \"input\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 14)(56, \"button\", 15);\n          i0.ɵɵtext(57, \"Submit\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ProfileComponent", "constructor", "toggle", "profile", "password", "toggleState", "key", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\profile\\profile.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\profile\\profile.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrl: './profile.component.scss'\r\n})\r\nexport class ProfileComponent {\r\n  toggle: { [key: string]: boolean } = {\r\n    profile: true,\r\n    password: false\r\n  }\r\n\r\n  toggleState(key: string) {\r\n    this.toggle[key] = !this.toggle[key];\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-4 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n        <h3 class=\"m-0\">My Account</h3>\r\n    </div>\r\n\r\n    <div class=\"card shadow-1 p-4\">\r\n        <form class=\"relative flex flex-column gap-1\">\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">person</span> User Name</label>\r\n                <div class=\"form-input\">: <PERSON> </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">person</span> First Name</label>\r\n                <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\" placeholder=\"\" value=\"John\"> </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">person</span> Last Name</label>\r\n                <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\" placeholder=\"\" value=\"Smith\"> </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">phone_in_talk</span> Phone Number</label>\r\n                <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\" placeholder=\"\" value=\"\"> </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">mail</span> E-mail</label>\r\n                <div class=\"form-input\"><input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\" placeholder=\"\" value=\"<EMAIL>\"> </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">key</span> Password</label>\r\n                <div class=\"form-input\"><input type=\"password\" class=\"p-inputtext p-component p-element w-30rem\" placeholder=\"\" value=\"Admin@123\"> </div>\r\n            </div>\r\n            <div class=\"field flex align-items-center text-base\">\r\n                <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span class=\"material-symbols-rounded\">key</span> Retype Password</label>\r\n                <div class=\"form-input\"><input type=\"password\" class=\"p-inputtext p-component p-element w-30rem\" placeholder=\"\" value=\"Admin@123\"> </div>\r\n            </div>\r\n            <div class=\"form-submit-sec mt-5\">\r\n                <button type=\"button\" class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-12rem h-3rem\">Submit</button>\r\n            </div>\r\n        </form>\r\n    </div>\r\n\r\n</div>"], "mappings": ";;AAOA,OAAM,MAAOA,gBAAgB;EAL7BC,YAAA;IAME,KAAAC,MAAM,GAA+B;MACnCC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX;;EAEDC,WAAWA,CAACC,GAAW;IACrB,IAAI,CAACJ,MAAM,CAACI,GAAG,CAAC,GAAG,CAAC,IAAI,CAACJ,MAAM,CAACI,GAAG,CAAC;EACtC;;;uBARWN,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAO,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLrBE,EAFR,CAAAC,cAAA,aAA2E,aACc,YACjE;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;UAKuFH,EAH7F,CAAAC,cAAA,aAA+B,cACmB,aACW,eACgC,cAAuC;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvJH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACzCF,EADyC,CAAAG,YAAA,EAAM,EACzC;UAE+EH,EADrF,CAAAC,cAAA,cAAqD,gBACgC,eAAuC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxJH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAI,SAAA,gBAAiG;UAC7HJ,EAD8H,CAAAG,YAAA,EAAM,EAC9H;UAE+EH,EADrF,CAAAC,cAAA,cAAqD,gBACgC,eAAuC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvJH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAI,SAAA,iBAAkG;UAC9HJ,EAD+H,CAAAG,YAAA,EAAM,EAC/H;UAE+EH,EADrF,CAAAC,cAAA,cAAqD,gBACgC,eAAuC;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjKH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAI,SAAA,iBAA6F;UACzHJ,EAD0H,CAAAG,YAAA,EAAM,EAC1H;UAE+EH,EADrF,CAAAC,cAAA,cAAqD,gBACgC,eAAuC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClJH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAI,SAAA,iBAAsH;UAClJJ,EADmJ,CAAAG,YAAA,EAAM,EACnJ;UAE+EH,EADrF,CAAAC,cAAA,cAAqD,gBACgC,eAAuC;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnJH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAI,SAAA,iBAA0G;UACtIJ,EADuI,CAAAG,YAAA,EAAM,EACvI;UAE+EH,EADrF,CAAAC,cAAA,cAAqD,gBACgC,eAAuC;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1JH,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAI,SAAA,iBAA0G;UACtIJ,EADuI,CAAAG,YAAA,EAAM,EACvI;UAEFH,EADJ,CAAAC,cAAA,eAAkC,kBAC+F;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAKnJF,EALmJ,CAAAG,YAAA,EAAS,EAC1I,EACH,EACL,EAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}