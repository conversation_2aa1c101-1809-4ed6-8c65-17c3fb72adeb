{"ast": null, "code": "import * as i0 from \"@angular/core\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵproperty(\"item\", item_r2)(\"index\", i_r3)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2)(2, AppMenuComponent_ng_container_1_li_2_Template, 1, 0, \"li\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r2.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.separator);\n  }\n}\nexport class AppMenuComponent {\n  constructor() {\n    this.model = [];\n  }\n  ngOnInit() {\n    this.model = [{\n      // label: 'Apps',\n      icon: 'pi pi-th-large',\n      items: [{\n        label: 'Home',\n        icon: 'pi pi-fw pi-home',\n        routerLink: ['/store']\n      }, {\n        label: 'Vendor Account',\n        icon: 'pi pi-fw pi-user',\n        routerLink: ['/store/vendor-account']\n      }, {\n        label: 'Invoices',\n        icon: 'pi pi-fw pi-file',\n        routerLink: ['/store/invoice']\n      }, {\n        label: 'Payment History',\n        icon: 'pi pi-fw pi-money-bill',\n        routerLink: ['/store/payment-history']\n      }]\n    }];\n  }\n  static {\n    this.ɵfac = function AppMenuComponent_Factory(t) {\n      return new (t || AppMenuComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppMenuComponent,\n      selectors: [[\"app-menu\"]],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"class\", \"menu-separator\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"], [1, \"menu-separator\"]],\n      template: function AppMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0);\n          i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "item_r2", "i_r3", "ɵɵelementContainerStart", "ɵɵtemplate", "AppMenuComponent_ng_container_1_li_1_Template", "AppMenuComponent_ng_container_1_li_2_Template", "ɵɵadvance", "separator", "AppMenuComponent", "constructor", "model", "ngOnInit", "icon", "items", "label", "routerLink", "selectors", "decls", "vars", "consts", "template", "AppMenuComponent_Template", "rf", "ctx", "ɵɵelementStart", "AppMenuComponent_ng_container_1_Template", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.menu.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.menu.component.html"], "sourcesContent": ["import { OnInit } from '@angular/core';\r\nimport { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-menu',\r\n  templateUrl: './app.menu.component.html',\r\n})\r\nexport class AppMenuComponent implements OnInit {\r\n  model: any[] = [];\r\n\r\n  ngOnInit() {\r\n    this.model = [\r\n      {\r\n        // label: 'Apps',\r\n        icon: 'pi pi-th-large',\r\n        items: [\r\n          {\r\n            label: 'Home',\r\n            icon: 'pi pi-fw pi-home',\r\n            routerLink: ['/store'],\r\n          },\r\n          {\r\n            label: 'Vendor Account',\r\n            icon: 'pi pi-fw pi-user',\r\n            routerLink: ['/store/vendor-account'],\r\n          },\r\n          {\r\n            label: 'Invoices',\r\n            icon: 'pi pi-fw pi-file',\r\n            routerLink: ['/store/invoice'],\r\n          },\r\n          {\r\n            label: 'Payment History',\r\n            icon: 'pi pi-fw pi-money-bill',\r\n            routerLink: ['/store/payment-history'],\r\n          },\r\n        ],\r\n      },\r\n    ];\r\n  }\r\n}\r\n", "<ul class=\"layout-menu\">\r\n    <ng-container *ngFor=\"let item of model; let i = index;\">\r\n        <li app-menuitem *ngIf=\"!item.separator\" [item]=\"item\" [index]=\"i\" [root]=\"true\"></li>\r\n        <li *ngIf=\"item.separator\" class=\"menu-separator\"></li>\r\n    </ng-container>\r\n</ul>"], "mappings": ";;;ICEQA,EAAA,CAAAC,SAAA,YAAsF;;;;;;IAAnBD,EAA1B,CAAAE,UAAA,SAAAC,OAAA,CAAa,UAAAC,IAAA,CAAY,cAAc;;;;;IAChFJ,EAAA,CAAAC,SAAA,YAAuD;;;;;IAF3DD,EAAA,CAAAK,uBAAA,GAAyD;IAErDL,EADA,CAAAM,UAAA,IAAAC,6CAAA,gBAAiF,IAAAC,6CAAA,gBAC/B;;;;;IADhCR,EAAA,CAAAS,SAAA,EAAqB;IAArBT,EAAA,CAAAE,UAAA,UAAAC,OAAA,CAAAO,SAAA,CAAqB;IAClCV,EAAA,CAAAS,SAAA,EAAoB;IAApBT,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAO,SAAA,CAAoB;;;ADIjC,OAAM,MAAOC,gBAAgB;EAJ7BC,YAAA;IAKE,KAAAC,KAAK,GAAU,EAAE;;EAEjBC,QAAQA,CAAA;IACN,IAAI,CAACD,KAAK,GAAG,CACX;MACE;MACAE,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,MAAM;QACbF,IAAI,EAAE,kBAAkB;QACxBG,UAAU,EAAE,CAAC,QAAQ;OACtB,EACD;QACED,KAAK,EAAE,gBAAgB;QACvBF,IAAI,EAAE,kBAAkB;QACxBG,UAAU,EAAE,CAAC,uBAAuB;OACrC,EACD;QACED,KAAK,EAAE,UAAU;QACjBF,IAAI,EAAE,kBAAkB;QACxBG,UAAU,EAAE,CAAC,gBAAgB;OAC9B,EACD;QACED,KAAK,EAAE,iBAAiB;QACxBF,IAAI,EAAE,wBAAwB;QAC9BG,UAAU,EAAE,CAAC,wBAAwB;OACtC;KAEJ,CACF;EACH;;;uBAhCWP,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BzB,EAAA,CAAA2B,cAAA,YAAwB;UACpB3B,EAAA,CAAAM,UAAA,IAAAsB,wCAAA,0BAAyD;UAI7D5B,EAAA,CAAA6B,YAAA,EAAK;;;UAJ8B7B,EAAA,CAAAS,SAAA,EAAU;UAAVT,EAAA,CAAAE,UAAA,YAAAwB,GAAA,CAAAb,KAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}