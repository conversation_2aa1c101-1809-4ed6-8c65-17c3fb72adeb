{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AdministratorService {\n  constructor(http) {\n    this.http = http;\n    this.adminSubject = new BehaviorSubject(null);\n    this.admin = this.adminSubject.asObservable();\n  }\n  createUser(userData) {\n    return this.http.post(`${CMS_APIContstant.USER_DETAILS}`, userData);\n  }\n  getUsers(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][email][$containsi]', searchTerm).set('filters[$or][1][username][$containsi]', searchTerm).set('filters[$or][2][firstname][$containsi]', searchTerm).set('filters[$or][3][lastname][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list?populate=*`, {\n      params\n    });\n  }\n  getUserRoles() {\n    return this.http.get(`${CMS_APIContstant.USER_ROLES}`);\n  }\n  getUserByID(id) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate=*`);\n  }\n  getUserByIDName(data) {\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`);\n  }\n  getAllCustomers() {\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`);\n  }\n  getUserByCustomer(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('populate[business_partner][populate]', 'addresses').set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][customer_id][$containsi]', searchTerm).set('filters[$or][1][customer_name][$containsi]', searchTerm).set('filters[$or][2][business_partner][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}/customers`, {\n      params\n    });\n  }\n  updateUser(userId, updatedData) {\n    return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n  }\n  getCustomers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function AdministratorService_Factory(t) {\n      return new (t || AdministratorService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdministratorService,\n      factory: AdministratorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "AdministratorService", "constructor", "http", "adminSubject", "admin", "asObservable", "createUser", "userData", "post", "USER_DETAILS", "getUsers", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "getUserRoles", "USER_ROLES", "getUserByID", "id", "getUserByIDName", "data", "CUSTOMERS", "getAllCustomers", "getUserByCustomer", "updateUser", "userId", "updatedData", "put", "getCustomers", "appendAll", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\administrator\\administrator.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, map } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AdministratorService {\r\n  public adminSubject = new BehaviorSubject<any>(null);\r\n  public admin = this.adminSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  createUser(userData: any): Observable<any> {\r\n    return this.http.post(`${CMS_APIContstant.USER_DETAILS}`, userData);\r\n  }\r\n\r\n  getUsers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][email][$containsi]', searchTerm)\r\n        .set('filters[$or][1][username][$containsi]', searchTerm)\r\n        .set('filters[$or][2][firstname][$containsi]', searchTerm)\r\n        .set('filters[$or][3][lastname][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/list?populate=*`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  getUserRoles() {\r\n    return this.http.get<any[]>(`${CMS_APIContstant.USER_ROLES}`);\r\n  }\r\n\r\n  getUserByID(id: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}?populate=*`\r\n    );\r\n  }\r\n\r\n  getUserByIDName(data: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.CUSTOMERS}/?filters[name][$eq]=${data}`\r\n    );\r\n  }\r\n\r\n  getAllCustomers() {\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`);\r\n  }\r\n\r\n  getUserByCustomer(\r\n    id: any,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('populate[business_partner][populate]', 'addresses')\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][customer_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][customer_name][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][2][business_partner][phone][$containsi]',\r\n          searchTerm\r\n        );\r\n    }\r\n\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}/customers`,\r\n      {\r\n        params,\r\n      }\r\n    );\r\n  }\r\n\r\n  updateUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}`,\r\n      updatedData\r\n    );\r\n  }\r\n\r\n  getCustomers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAyB,MAAM;AACvD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,YAAY,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC7C,KAAAM,KAAK,GAAG,IAAI,CAACD,YAAY,CAACE,YAAY,EAAE;EAER;EAEvCC,UAAUA,CAACC,QAAa;IACtB,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAC,GAAGT,gBAAgB,CAACU,YAAY,EAAE,EAAEF,QAAQ,CAAC;EACrE;EAEAG,QAAQA,CACNC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAInB,UAAU,EAAE,CAC1BoB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC,CACxDE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC,CACzDE,GAAG,CAAC,uCAAuC,EAAEF,UAAU,CAAC;IAC7D;IACA,OAAO,IAAI,CAACb,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,kBAAkB,EAClD;MACEO;KACD,CACF;EACH;EAEAM,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpB,IAAI,CAACmB,GAAG,CAAQ,GAAGtB,gBAAgB,CAACwB,UAAU,EAAE,CAAC;EAC/D;EAEAC,WAAWA,CAACC,EAAO;IACjB,OAAO,IAAI,CAACvB,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,IAAIgB,EAAE,aAAa,CACpD;EACH;EAEAC,eAAeA,CAACC,IAAS;IACvB,OAAO,IAAI,CAACzB,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAAC6B,SAAS,wBAAwBD,IAAI,EAAE,CAC5D;EACH;EAEAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC3B,IAAI,CAACmB,GAAG,CAAQ,GAAGtB,gBAAgB,CAAC6B,SAAS,EAAE,CAAC;EAC9D;EAEAE,iBAAiBA,CACfL,EAAO,EACPd,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAInB,UAAU,EAAE,CAC1BoB,GAAG,CAAC,sCAAsC,EAAE,WAAW,CAAC,CACxDA,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAEF,UAAU,CAAC,CAC3DE,GAAG,CAAC,4CAA4C,EAAEF,UAAU,CAAC,CAC7DE,GAAG,CACF,sDAAsD,EACtDF,UAAU,CACX;IACL;IAEA,OAAO,IAAI,CAACb,IAAI,CAACmB,GAAG,CAClB,GAAGtB,gBAAgB,CAACU,YAAY,IAAIgB,EAAE,YAAY,EAClD;MACET;KACD,CACF;EACH;EAEAe,UAAUA,CAACC,MAAc,EAAEC,WAAgB;IACzC,OAAO,IAAI,CAAC/B,IAAI,CAACgC,GAAG,CAClB,GAAGnC,gBAAgB,CAACU,YAAY,IAAIuB,MAAM,EAAE,EAC5CC,WAAW,CACZ;EACH;EAEAE,YAAYA,CAACR,IAAS;IACpB,MAAMX,MAAM,GAAG,IAAInB,UAAU,EAAE,CAACuC,SAAS,CAAC;MAAE,GAAGT;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACzB,IAAI,CAACmB,GAAG,CAAQ,GAAGtB,gBAAgB,CAAC6B,SAAS,EAAE,EAAE;MAC3DZ;KACD,CAAC;EACJ;;;uBAzGWhB,oBAAoB,EAAAqC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBxC,oBAAoB;MAAAyC,OAAA,EAApBzC,oBAAoB,CAAA0C,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}