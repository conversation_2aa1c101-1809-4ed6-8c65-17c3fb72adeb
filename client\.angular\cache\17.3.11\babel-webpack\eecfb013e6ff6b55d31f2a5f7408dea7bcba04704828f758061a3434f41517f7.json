{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SupplierComponent } from './supplier.component';\nimport { SupplierDetailsComponent } from './supplier-details/supplier-details.component';\nimport { SupplierInfoComponent } from './supplier-details/supplier-info/supplier-info.component';\nimport { SupplierBackendComponent } from './supplier-details/supplier-backend/supplier-backend.component';\nimport { SupplierCompanyComponent } from './supplier-details/supplier-company/supplier-company.component';\nimport { SupplierCompanyTextComponent } from './supplier-details/supplier-company-text/supplier-company-text.component';\nimport { SupplierPartnerComponent } from './supplier-details/supplier-partner/supplier-partner.component';\nimport { SupplierTextComponent } from './supplier-details/supplier-text/supplier-text.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SupplierComponent\n}, {\n  path: ':id',\n  component: SupplierDetailsComponent,\n  children: [{\n    path: 'general',\n    component: SupplierInfoComponent\n  }, {\n    path: 'backend',\n    component: SupplierBackendComponent\n  }, {\n    path: 'company',\n    component: SupplierCompanyComponent\n  }, {\n    path: 'company-text',\n    component: SupplierCompanyTextComponent\n  }, {\n    path: 'partner',\n    component: SupplierPartnerComponent\n  }, {\n    path: 'text',\n    component: SupplierTextComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport class SupplierRoutingModule {\n  static {\n    this.ɵfac = function SupplierRoutingModule_Factory(t) {\n      return new (t || SupplierRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SupplierRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SupplierRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SupplierComponent", "SupplierDetailsComponent", "SupplierInfoComponent", "SupplierBackendComponent", "SupplierCompanyComponent", "SupplierCompanyTextComponent", "SupplierPartnerComponent", "SupplierTextComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "SupplierRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\supplier\\supplier-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { SupplierComponent } from './supplier.component';\r\nimport { SupplierDetailsComponent } from './supplier-details/supplier-details.component';\r\nimport { SupplierInfoComponent } from './supplier-details/supplier-info/supplier-info.component';\r\nimport { SupplierBackendComponent } from './supplier-details/supplier-backend/supplier-backend.component';\r\nimport { SupplierCompanyComponent } from './supplier-details/supplier-company/supplier-company.component';\r\nimport { SupplierCompanyTextComponent } from './supplier-details/supplier-company-text/supplier-company-text.component';\r\nimport { SupplierPartnerComponent } from './supplier-details/supplier-partner/supplier-partner.component';\r\nimport { SupplierTextComponent } from './supplier-details/supplier-text/supplier-text.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: SupplierComponent },\r\n  {\r\n    path: ':id',\r\n    component: SupplierDetailsComponent,\r\n    children: [\r\n      { path: 'general', component: SupplierInfoComponent },\r\n      { path: 'backend', component: SupplierBackendComponent },\r\n      { path: 'company', component: SupplierCompanyComponent },\r\n      { path: 'company-text', component: SupplierCompanyTextComponent },\r\n      { path: 'partner', component: SupplierPartnerComponent },\r\n      { path: 'text', component: SupplierTextComponent },\r\n      { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SupplierRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,4BAA4B,QAAQ,0EAA0E;AACvH,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,qBAAqB,QAAQ,0DAA0D;;;AAEhG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEV;AAAiB,CAAE,EAC1C;EACES,IAAI,EAAE,KAAK;EACXC,SAAS,EAAET,wBAAwB;EACnCU,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAER;EAAqB,CAAE,EACrD;IAAEO,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEP;EAAwB,CAAE,EACxD;IAAEM,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEN;EAAwB,CAAE,EACxD;IAAEK,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEL;EAA4B,CAAE,EACjE;IAAEI,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEJ;EAAwB,CAAE,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEH;EAAqB,CAAE,EAClD;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE3D,CACF;AAMD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBf,YAAY,CAACgB,QAAQ,CAACP,MAAM,CAAC,EAC7BT,YAAY;IAAA;EAAA;;;2EAEXe,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAFtBnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}