{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/backoffice/users/users.service\";\nimport * as i3 from \"src/app/core/authentication/auth.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nfunction ProfileComponent_div_12_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_10_div_1_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"userName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_19_div_1_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"firstName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_28_div_1_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"lastName\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_37_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_37_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid email address.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_37_div_1_Template, 2, 0, \"div\", 24)(2, ProfileComponent_div_12_div_37_div_2_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"email\"].errors[\"email\"]);\n  }\n}\nfunction ProfileComponent_div_12_div_46_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_12_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_12_div_46_div_1_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.f[\"phone\"].errors[\"required\"]);\n  }\n}\nfunction ProfileComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"form\", 11);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_12_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveChanges());\n    });\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"label\", 13)(4, \"span\", 8);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" User Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"div\", 14);\n    i0.ɵɵelement(9, \"input\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ProfileComponent_div_12_div_10_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"label\", 13)(13, \"span\", 8);\n    i0.ɵɵtext(14, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"div\", 14);\n    i0.ɵɵelement(18, \"input\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProfileComponent_div_12_div_19_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 12)(21, \"label\", 13)(22, \"span\", 8);\n    i0.ɵɵtext(23, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\")(26, \"div\", 14);\n    i0.ɵɵelement(27, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, ProfileComponent_div_12_div_28_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 12)(30, \"label\", 13)(31, \"span\", 8);\n    i0.ɵɵtext(32, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" E-mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\")(35, \"div\", 14);\n    i0.ɵɵelement(36, \"input\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, ProfileComponent_div_12_div_37_Template, 3, 2, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 12)(39, \"label\", 13)(40, \"span\", 8);\n    i0.ɵɵtext(41, \"phone_in_talk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\")(44, \"div\", 14);\n    i0.ɵɵelement(45, \"input\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(46, ProfileComponent_div_12_div_46_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 21)(48, \"button\", 22);\n    i0.ɵɵtext(49, \"Submit\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.profileForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"userName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"firstName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"lastName\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"email\"].errors);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFormSubmitted && ctx_r1.f[\"phone\"].errors);\n  }\n}\nfunction ProfileComponent_div_20_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Capital Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Letter in Small Case\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_10_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Must contain at least one Special Character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_20_div_10_div_1_Template, 2, 0, \"div\", 24)(2, ProfileComponent_div_20_div_10_div_2_Template, 2, 0, \"div\", 24)(3, ProfileComponent_div_20_div_10_div_3_Template, 2, 0, \"div\", 24)(4, ProfileComponent_div_20_div_10_div_4_Template, 2, 0, \"div\", 24)(5, ProfileComponent_div_20_div_10_div_5_Template, 2, 0, \"div\", 24)(6, ProfileComponent_div_20_div_10_div_6_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"minlength\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasNumber\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasCapitalCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasSmallCase\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"currentPassword\"].errors[\"hasSpecialCharacters\"]);\n  }\n}\nfunction ProfileComponent_div_20_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" This field is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Passwords must match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_20_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, ProfileComponent_div_20_div_19_div_1_Template, 2, 0, \"div\", 24)(2, ProfileComponent_div_20_div_19_div_2_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"confirmPassword\"].errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pf[\"confirmPassword\"].errors[\"confirmedValidator\"]);\n  }\n}\nfunction ProfileComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"form\", 25);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_20_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changePassword());\n    });\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"label\", 13)(4, \"span\", 8);\n    i0.ɵɵtext(5, \"key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"div\", 14);\n    i0.ɵɵelement(9, \"input\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ProfileComponent_div_20_div_10_Template, 7, 6, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 12)(12, \"label\", 13)(13, \"span\", 8);\n    i0.ɵɵtext(14, \"key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Retype Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"div\", 14);\n    i0.ɵɵelement(18, \"input\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ProfileComponent_div_20_div_19_Template, 3, 2, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 21)(21, \"button\", 22);\n    i0.ɵɵtext(22, \"Submit\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.passwordsForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"currentPassword\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"currentPassword\"].errors);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"confirmPassword\"].errors));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPasswordFormSubmitted && ctx_r1.pf[\"confirmPassword\"].errors);\n  }\n}\nfunction ConfirmedValidator(controlName, matchingControlName) {\n  return formGroup => {\n    const control = formGroup.controls[controlName];\n    const matchingControl = formGroup.controls[matchingControlName];\n    if (matchingControl.errors && !matchingControl.errors['confirmedValidator']) {\n      return;\n    }\n    if (control.value !== matchingControl.value) {\n      matchingControl.setErrors({\n        confirmedValidator: true\n      });\n    } else {\n      matchingControl.setErrors(null);\n    }\n  };\n}\nfunction patternValidator(regex, error) {\n  return control => {\n    if (!control.value) {\n      // if control is empty return no error\n      return null;\n    }\n    // test the value of the control against the regexp supplied\n    const valid = regex.test(control.value);\n    // if true, return no error (no error), else return error passed in the second parameter\n    return valid ? null : error;\n  };\n}\nexport class ProfileComponent {\n  constructor(fb, manageUserService, authService) {\n    this.fb = fb;\n    this.manageUserService = manageUserService;\n    this.authService = authService;\n    this.toggle = {\n      profile: true,\n      password: false\n    };\n    this.isFormSubmitted = false;\n    this.isPasswordFormSubmitted = false;\n    this.loadingUserDetails = false;\n    this.userDetails = {};\n    this.saving = false;\n    this.changingPassword = false;\n  }\n  ngOnInit() {\n    this.passwordsForm = this.fb.group({\n      currentPassword: ['', [Validators.required, Validators.minLength(8),\n      // check whether the entered password has a number\n      patternValidator(/\\d/, {\n        hasNumber: true\n      }),\n      // check whether the entered password has upper case letter\n      patternValidator(/[A-Z]/, {\n        hasCapitalCase: true\n      }),\n      // check whether the entered password has a lower case letter\n      patternValidator(/[a-z]/, {\n        hasSmallCase: true\n      }),\n      // check whether the entered password has a special character\n      patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\n        hasSpecialCharacters: true\n      })]],\n      confirmPassword: ['']\n    }, {\n      validators: ConfirmedValidator('currentPassword', 'confirmPassword')\n    });\n    this.profileForm = this.fb.group({\n      userName: ['', Validators.required],\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['']\n    });\n    this.fetchUserDetails();\n  }\n  get f() {\n    return this.profileForm.controls;\n  }\n  get pf() {\n    return this.passwordsForm.controls;\n  }\n  saveChanges() {\n    this.isFormSubmitted = true;\n    if (!this.profileForm.valid) {\n      return;\n    }\n    const value = this.profileForm.value;\n    this.saving = true;\n    const isAdmin = this.authService.userDetail.isAdmin || false;\n    if (isAdmin) {\n      this.manageUserService.updateAdminUser(this.userDetails.id, {\n        firstname: value.firstName,\n        lastname: value.lastName,\n        address: value.address,\n        email: value.email,\n        phone: value.phone,\n        username: value.userName\n      }).subscribe(user => {\n        this.saving = false;\n        this.userDetails = user;\n        this.authService.checkAdminUser().subscribe();\n      }, err => {\n        this.saving = false;\n      });\n    } else {\n      this.manageUserService.updateUser(this.userDetails.id, {\n        firstname: value.firstName,\n        lastname: value.lastName,\n        address: value.address,\n        email: value.email,\n        username: value.userName\n      }).subscribe(user => {\n        this.saving = false;\n        this.userDetails = user;\n        this.authService.checkAdminUser().subscribe();\n      }, err => {\n        this.saving = false;\n      });\n    }\n  }\n  fetchUserDetails() {\n    this.loadingUserDetails = true;\n    const user = this.authService.userDetail;\n    this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\n      if (user) {\n        this.userDetails = user;\n        this.profileForm.patchValue({\n          firstName: user.firstname,\n          lastName: user.lastname,\n          address: user.address,\n          email: user.email,\n          phone: user.phone,\n          userName: user.username\n        });\n      }\n      this.loadingUserDetails = false;\n    });\n  }\n  changePassword() {\n    this.isPasswordFormSubmitted = true;\n    if (!this.passwordsForm.valid) {\n      return;\n    }\n    this.changingPassword = true;\n    const isAdmin = this.authService.userDetail.isAdmin || false;\n    if (isAdmin) {\n      this.manageUserService.updateAdminUser(this.userDetails.id, {\n        password: this.passwordsForm.value.currentPassword\n      }).subscribe(() => {\n        this.changingPassword = false;\n        this.passwordsForm.reset();\n        this.isPasswordFormSubmitted = false;\n      }, () => {\n        this.changingPassword = false;\n      });\n    } else {\n      this.manageUserService.updateUser(this.userDetails.id, {\n        password: this.passwordsForm.value.currentPassword\n      }).subscribe(() => {\n        this.changingPassword = false;\n        this.passwordsForm.reset();\n      }, () => {\n        this.changingPassword = false;\n      });\n    }\n  }\n  toggleState(key) {\n    this.toggle[key] = !this.toggle[key];\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UsersService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 21,\n      vars: 6,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-4\", \"border-round-lg\", \"surface-card\"], [1, \"all-page-title-sec\", \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"m-0\"], [1, \"card-list\", \"flex\", \"flex-column\", \"gap-2\", \"mb-3\"], [1, \"card\", \"shadow-2\", \"p-4\", \"h-full\", \"flex\", \"flex-column\", \"border-left-5\", \"gap-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"cursor-pointer\", 3, \"click\"], [1, \"block\", \"font-bold\", \"text-xl\", \"m-0\", \"text-primary\"], [1, \"show-hide-btn\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"p-button-outlined\", \"justify-content-center\", \"w-2rem\", \"h-2rem\"], [1, \"material-symbols-rounded\"], [\"class\", \"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\", 4, \"ngIf\"], [1, \"v-details-sec\", \"mt-3\", \"pt-5\", \"border-solid\", \"border-black-alpha-20\", \"border-none\", \"border-top-1\"], [\"autocomplete\", \"off\", 1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"ngSubmit\", \"formGroup\"], [1, \"field\", \"flex\", \"align-items-center\", \"text-base\"], [1, \"relative\", \"flex\", \"align-items-center\", \"text\", \"font-semibold\", \"w-15rem\", \"gap-1\"], [1, \"form-input\"], [\"type\", \"text\", \"formControlName\", \"userName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"class\", \"text-red-600 mt-2\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"firstName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"formControlName\", \"lastName\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [\"type\", \"text\", \"formControlName\", \"phone\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\"], [1, \"form-submit-sec\", \"mt-5\"], [\"type\", \"submit\", 1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-rounded\", \"justify-content-center\", \"w-12rem\", \"h-3rem\"], [1, \"text-red-600\", \"mt-2\"], [4, \"ngIf\"], [1, \"relative\", \"flex\", \"flex-column\", \"gap-1\", 3, \"ngSubmit\", \"formGroup\"], [\"type\", \"password\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"new-password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\", 3, \"ngClass\"], [\"type\", \"password\", \"formControlName\", \"confirmPassword\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-30rem\", 3, \"ngClass\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3, \"My Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_div_click_6_listener() {\n            return ctx.toggleState(\"profile\");\n          });\n          i0.ɵɵelementStart(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"User Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11, \"keyboard_arrow_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(12, ProfileComponent_div_12_Template, 50, 6, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function ProfileComponent_Template_div_click_14_listener() {\n            return ctx.toggleState(\"password\");\n          });\n          i0.ɵɵelementStart(15, \"h3\", 6);\n          i0.ɵɵtext(16, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 7)(18, \"span\", 8);\n          i0.ɵɵtext(19, \"keyboard_arrow_down\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(20, ProfileComponent_div_20_Template, 23, 9, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"active\", ctx.toggle[\"profile\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.toggle[\"profile\"]);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.toggle[\"password\"]);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.toggle[\"password\"]);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ProfileComponent_div_12_div_10_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "f", "errors", "ProfileComponent_div_12_div_19_div_1_Template", "ProfileComponent_div_12_div_28_div_1_Template", "ProfileComponent_div_12_div_37_div_1_Template", "ProfileComponent_div_12_div_37_div_2_Template", "ProfileComponent_div_12_div_46_div_1_Template", "ɵɵlistener", "ProfileComponent_div_12_Template_form_ngSubmit_1_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "saveChanges", "ɵɵelement", "ProfileComponent_div_12_div_10_Template", "ProfileComponent_div_12_div_19_Template", "ProfileComponent_div_12_div_28_Template", "ProfileComponent_div_12_div_37_Template", "ProfileComponent_div_12_div_46_Template", "profileForm", "isFormSubmitted", "ProfileComponent_div_20_div_10_div_1_Template", "ProfileComponent_div_20_div_10_div_2_Template", "ProfileComponent_div_20_div_10_div_3_Template", "ProfileComponent_div_20_div_10_div_4_Template", "ProfileComponent_div_20_div_10_div_5_Template", "ProfileComponent_div_20_div_10_div_6_Template", "pf", "ProfileComponent_div_20_div_19_div_1_Template", "ProfileComponent_div_20_div_19_div_2_Template", "ProfileComponent_div_20_Template_form_ngSubmit_1_listener", "_r3", "changePassword", "ProfileComponent_div_20_div_10_Template", "ProfileComponent_div_20_div_19_Template", "passwordsForm", "ɵɵpureFunction1", "_c0", "isPasswordFormSubmitted", "ConfirmedValidator", "controlName", "matchingControlName", "formGroup", "control", "controls", "matchingControl", "value", "setErrors", "confirmedValidator", "patternValidator", "regex", "error", "valid", "test", "ProfileComponent", "constructor", "fb", "manageUserService", "authService", "toggle", "profile", "password", "loadingUserDetails", "userDetails", "saving", "changingPassword", "ngOnInit", "group", "currentPassword", "required", "<PERSON><PERSON><PERSON><PERSON>", "hasNumber", "hasCapitalCase", "hasSmallCase", "hasSpecialCharacters", "confirmPassword", "validators", "userName", "firstName", "lastName", "email", "phone", "fetchUserDetails", "isAdmin", "userDetail", "updateAdminUser", "id", "firstname", "lastname", "address", "username", "subscribe", "user", "checkAdminUser", "err", "updateUser", "getUserForallRoles", "documentId", "patchValue", "reset", "toggleState", "key", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UsersService", "i3", "AuthService", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_Template_div_click_6_listener", "ProfileComponent_div_12_Template", "ProfileComponent_Template_div_click_14_listener", "ProfileComponent_div_20_Template", "ɵɵclassProp"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\profile\\profile.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormGroup, ValidationErrors, ValidatorFn, AbstractControl, FormBuilder, Validators } from '@angular/forms';\r\nimport { UsersService } from 'src/app/backoffice/users/users.service';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n\r\n\r\nfunction ConfirmedValidator(controlName: string, matchingControlName: string) {\r\n  return (formGroup: FormGroup) => {\r\n    const control = formGroup.controls[controlName];\r\n    const matchingControl = formGroup.controls[matchingControlName];\r\n    if (\r\n      matchingControl.errors &&\r\n      !matchingControl.errors['confirmedValidator']\r\n    ) {\r\n      return;\r\n    }\r\n    if (control.value !== matchingControl.value) {\r\n      matchingControl.setErrors({ confirmedValidator: true });\r\n    } else {\r\n      matchingControl.setErrors(null);\r\n    }\r\n  };\r\n}\r\n\r\n\r\nfunction patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {\r\n  return (control: AbstractControl) => {\r\n    if (!control.value) {\r\n      // if control is empty return no error\r\n      return null;\r\n    }\r\n\r\n    // test the value of the control against the regexp supplied\r\n    const valid = regex.test(control.value);\r\n\r\n    // if true, return no error (no error), else return error passed in the second parameter\r\n    return valid ? null : error;\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  templateUrl: './profile.component.html',\r\n  styleUrl: './profile.component.scss'\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  toggle: { [key: string]: boolean } = {\r\n    profile: true,\r\n    password: false\r\n  };\r\n  profileForm!: FormGroup;\r\n  passwordsForm!: FormGroup;\r\n  isFormSubmitted = false;\r\n  isPasswordFormSubmitted = false;\r\n  loadingUserDetails = false;\r\n  userDetails: any = {};\r\n  saving = false;\r\n  changingPassword = false;\r\n\r\n  constructor(private fb: FormBuilder, private manageUserService: UsersService, private authService: AuthService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.passwordsForm = this.fb.group({\r\n      currentPassword: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(8),\r\n          // check whether the entered password has a number\r\n          patternValidator(/\\d/, {\r\n            hasNumber: true,\r\n          }),\r\n          // check whether the entered password has upper case letter\r\n          patternValidator(/[A-Z]/, {\r\n            hasCapitalCase: true,\r\n          }),\r\n          // check whether the entered password has a lower case letter\r\n          patternValidator(/[a-z]/, {\r\n            hasSmallCase: true,\r\n          }),\r\n          // check whether the entered password has a special character\r\n          patternValidator(/[ !@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/, {\r\n            hasSpecialCharacters: true,\r\n          }),\r\n        ],\r\n      ],\r\n      confirmPassword: [''],\r\n    }, {\r\n      validators: ConfirmedValidator('currentPassword', 'confirmPassword'),\r\n    });\r\n    this.profileForm = this.fb.group({\r\n      userName: ['', Validators.required],\r\n      firstName: ['', Validators.required],\r\n      lastName: ['', Validators.required],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phone: [''],\r\n    });\r\n    this.fetchUserDetails();\r\n  }\r\n\r\n  get f(): { [key: string]: AbstractControl } {\r\n    return this.profileForm.controls;\r\n  }\r\n\r\n  get pf(): { [key: string]: AbstractControl } {\r\n    return this.passwordsForm.controls;\r\n  }\r\n\r\n  saveChanges(): void {\r\n    this.isFormSubmitted = true;\r\n    if (!this.profileForm.valid) {\r\n      return;\r\n    }\r\n    const value = this.profileForm.value;\r\n    this.saving = true;\r\n    const isAdmin = this.authService.userDetail.isAdmin || false;\r\n    if (isAdmin) {\r\n      this.manageUserService.updateAdminUser(this.userDetails.id, {\r\n        firstname: value.firstName,\r\n        lastname: value.lastName,\r\n        address: value.address,\r\n        email: value.email,\r\n        phone: value.phone,\r\n        username: value.userName,\r\n      }).subscribe((user) => {\r\n        this.saving = false;\r\n        this.userDetails = user;\r\n        this.authService.checkAdminUser().subscribe();\r\n      }, (err) => {\r\n        this.saving = false;\r\n      });\r\n    } else {\r\n      this.manageUserService.updateUser(this.userDetails.id, {\r\n        firstname: value.firstName,\r\n        lastname: value.lastName,\r\n        address: value.address,\r\n        email: value.email,\r\n        username: value.userName,\r\n      }).subscribe((user) => {\r\n        this.saving = false;\r\n        this.userDetails = user;\r\n        this.authService.checkAdminUser().subscribe();\r\n      }, (err) => {\r\n        this.saving = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  fetchUserDetails() {\r\n    this.loadingUserDetails = true;\r\n    const user = this.authService.userDetail;\r\n    this.manageUserService.getUserForallRoles(user.documentId).subscribe(user => {\r\n      if (user) {\r\n        this.userDetails = user;\r\n        this.profileForm.patchValue({\r\n          firstName: user.firstname,\r\n          lastName: user.lastname,\r\n          address: user.address,\r\n          email: user.email,\r\n          phone: user.phone,\r\n          userName: user.username,\r\n        });\r\n      }\r\n      this.loadingUserDetails = false;\r\n    });\r\n  }\r\n\r\n  changePassword() {\r\n    this.isPasswordFormSubmitted = true;\r\n    if (!this.passwordsForm.valid) {\r\n      return\r\n    }\r\n    this.changingPassword = true;\r\n    const isAdmin = this.authService.userDetail.isAdmin || false;\r\n    if (isAdmin) {\r\n      this.manageUserService.updateAdminUser(this.userDetails.id, {\r\n        password: this.passwordsForm.value.currentPassword,\r\n      }).subscribe(() => {\r\n        this.changingPassword = false;\r\n        this.passwordsForm.reset();\r\n        this.isPasswordFormSubmitted = false;\r\n      }, () => {\r\n        this.changingPassword = false;\r\n      });\r\n    } else {\r\n      this.manageUserService.updateUser(this.userDetails.id, {\r\n        password: this.passwordsForm.value.currentPassword,\r\n      }).subscribe(() => {\r\n        this.changingPassword = false;\r\n        this.passwordsForm.reset();\r\n      }, () => {\r\n        this.changingPassword = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  toggleState(key: string) {\r\n    this.toggle[key] = !this.toggle[key];\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-4 border-round-lg surface-card\">\r\n    <div class=\"all-page-title-sec flex justify-content-between align-items-center mb-4\">\r\n        <h3 class=\"m-0\">My Account</h3>\r\n    </div>\r\n\r\n    <div class=\"card-list flex flex-column gap-2 mb-3\">\r\n        <div class=\"card shadow-2 p-4 h-full flex flex-column border-left-5 gap-2\">\r\n            <div class=\"flex align-items-center justify-content-between cursor-pointer\"\r\n                (click)=\"toggleState('profile')\">\r\n                <h3 class=\"block font-bold text-xl m-0 text-primary\">User Details</h3>\r\n                <button\r\n                    class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\"\r\n                    [class.active]=\"toggle['profile']\">\r\n                    <span class='material-symbols-rounded'>keyboard_arrow_down</span>\r\n                </button>\r\n            </div>\r\n            <div *ngIf=\"toggle['profile']\"\r\n                class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <form class=\"relative flex flex-column gap-1\" [formGroup]=\"profileForm\" (ngSubmit)=\"saveChanges()\"\r\n                    autocomplete=\"off\">\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> User Name</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    formControlName=\"userName\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['userName'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['userName'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> First Name</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    formControlName=\"firstName\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['firstName'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['firstName'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">person</span> Last Name</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\" formControlName=\"lastName\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['lastName'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['lastName'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">mail</span> E-mail</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"email\" class=\"p-inputtext p-component p-element w-30rem\" formControlName=\"email\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['email'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['email'].errors['required']\">\r\n                                    This field is required</div>\r\n                                <div *ngIf=\"f['email'].errors['email']\">\r\n                                    Please enter a valid email address.</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">phone_in_talk</span> Phone Number</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"text\" class=\"p-inputtext p-component p-element w-30rem\" formControlName=\"phone\">\r\n                            </div>\r\n                            <div *ngIf=\"isFormSubmitted && f['phone'].errors\" class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"f['phone'].errors['required']\">\r\n                                    This field is required</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"form-submit-sec mt-5\">\r\n                        <button type=\"submit\"\r\n                            class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-12rem h-3rem\">Submit</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"card shadow-2 p-4 h-full flex flex-column border-left-5 gap-2\">\r\n            <div class=\"flex align-items-center justify-content-between cursor-pointer\"\r\n                (click)=\"toggleState('password')\">\r\n                <h3 class=\"block font-bold text-xl m-0 text-primary\">Password</h3>\r\n                <button\r\n                    class=\"show-hide-btn p-element p-ripple p-button p-component p-button-rounded p-button-outlined justify-content-center w-2rem h-2rem\"\r\n                    [class.active]=\"toggle['password']\">\r\n                    <span class='material-symbols-rounded'>keyboard_arrow_down</span>\r\n                </button>\r\n            </div>\r\n            <div *ngIf=\"toggle['password']\"\r\n                class=\"v-details-sec mt-3 pt-5 border-solid border-black-alpha-20 border-none border-top-1\">\r\n                <form [formGroup]=\"passwordsForm\" (ngSubmit)=\"changePassword()\" class=\"relative flex flex-column gap-1\">\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">key</span> Password</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"password\" formControlName=\"currentPassword\"\r\n                                    class=\"p-inputtext p-component p-element w-30rem\" autocomplete=\"new-password\"\r\n                                    [ngClass]=\"{ 'is-invalid': isPasswordFormSubmitted && pf['currentPassword'].errors }\" />\r\n                            </div>\r\n                            <div *ngIf=\"isPasswordFormSubmitted && pf['currentPassword'].errors\"\r\n                                class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"pf['currentPassword'].errors['required']\">\r\n                                    This field is required</div>\r\n                                <div *ngIf=\"pf['currentPassword'].errors['minlength']\">\r\n                                    Must be at least 8 characters</div>\r\n                                <div *ngIf=\"pf['currentPassword'].errors['hasNumber']\">\r\n                                    Must contain at least one number</div>\r\n                                <div *ngIf=\"pf['currentPassword'].errors['hasCapitalCase']\">\r\n                                    Must contain at least one Letter in Capital Case</div>\r\n                                <div *ngIf=\"pf['currentPassword'].errors['hasSmallCase']\">\r\n                                    Must contain at least one Letter in Small Case</div>\r\n                                <div *ngIf=\"pf['currentPassword'].errors['hasSpecialCharacters']\">\r\n                                    Must contain at least one Special Character</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field flex align-items-center text-base\">\r\n                        <label class=\"relative flex align-items-center text font-semibold w-15rem gap-1\"><span\r\n                                class=\"material-symbols-rounded\">key</span> Retype Password</label>\r\n                        <div>\r\n                            <div class=\"form-input\">\r\n                                <input type=\"password\" formControlName=\"confirmPassword\"\r\n                                    class=\"p-inputtext p-component p-element w-30rem\"\r\n                                    [ngClass]=\"{ 'is-invalid': isPasswordFormSubmitted && pf['confirmPassword'].errors }\" />\r\n                            </div>\r\n                            <div *ngIf=\"isPasswordFormSubmitted && pf['confirmPassword'].errors\"\r\n                                class=\"text-red-600 mt-2\">\r\n                                <div *ngIf=\"pf['confirmPassword'].errors['required']\">\r\n                                    This field is required</div>\r\n                                <div *ngIf=\"pf['confirmPassword'].errors['confirmedValidator']\">\r\n                                    Passwords must match\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"form-submit-sec mt-5\">\r\n                        <button type=\"submit\"\r\n                            class=\"p-element p-ripple p-button p-component p-button-rounded justify-content-center w-12rem h-3rem\">Submit</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n</div>"], "mappings": "AACA,SAAiFA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;IC4BnFC,EAAA,CAAAC,cAAA,UAA8C;IAC1CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAA8C;IAElDL,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,aAAsC;;;;;IAc5CV,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAAgF;IAC5ED,EAAA,CAAAI,UAAA,IAAAO,6CAAA,kBAA+C;IAEnDX,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,cAAAC,MAAA,aAAuC;;;;;IAa7CV,EAAA,CAAAC,cAAA,UAA8C;IAC1CD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAI,UAAA,IAAAQ,6CAAA,kBAA8C;IAElDZ,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,aAAAC,MAAA,aAAsC;;;;;IAa5CV,EAAA,CAAAC,cAAA,UAA2C;IACvCD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAAwC;IACpCD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJjDH,EAAA,CAAAC,cAAA,cAA4E;IAGxED,EAFA,CAAAI,UAAA,IAAAS,6CAAA,kBAA2C,IAAAC,6CAAA,kBAEH;IAE5Cd,EAAA,CAAAG,YAAA,EAAM;;;;IAJIH,EAAA,CAAAM,SAAA,EAAmC;IAAnCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,aAAmC;IAEnCV,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,UAAgC;;;;;IAatCV,EAAA,CAAAC,cAAA,UAA2C;IACvCD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFpCH,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAI,UAAA,IAAAW,6CAAA,kBAA2C;IAE/Cf,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAM,SAAA,EAAmC;IAAnCN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAC,CAAA,UAAAC,MAAA,aAAmC;;;;;;IAlEzDV,EAFJ,CAAAC,cAAA,cACgG,eAErE;IADiDD,EAAA,CAAAgB,UAAA,sBAAAC,0DAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAYb,MAAA,CAAAc,WAAA,EAAa;IAAA,EAAC;IAGTtB,EADrF,CAAAC,cAAA,cAAqD,gBACgC,cACxC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpEH,EADJ,CAAAC,cAAA,UAAK,cACuB;IACpBD,EAAA,CAAAuB,SAAA,gBAC+B;IACnCvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAoB,uCAAA,kBAA+E;IAKvFxB,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAErEH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAuB,SAAA,iBACgC;IACpCvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAqB,uCAAA,kBAAgF;IAKxFzB,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpEH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAuB,SAAA,iBAAgG;IACpGvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAsB,uCAAA,kBAA+E;IAKvF1B,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE/DH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAuB,SAAA,iBAA8F;IAClGvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAuB,uCAAA,kBAA4E;IAOpF3B,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE9EH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAuB,SAAA,iBAA6F;IACjGvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAwB,uCAAA,kBAA4E;IAKpF5B,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAAkC,kBAE6E;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAG7HF,EAH6H,CAAAG,YAAA,EAAS,EACxH,EACH,EACL;;;;IA5E4CH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAO,UAAA,cAAAC,MAAA,CAAAqB,WAAA,CAAyB;IAUrD7B,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAsB,eAAA,IAAAtB,MAAA,CAAAC,CAAA,aAAAC,MAAA,CAA6C;IAc7CV,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAsB,eAAA,IAAAtB,MAAA,CAAAC,CAAA,cAAAC,MAAA,CAA8C;IAa9CV,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAsB,eAAA,IAAAtB,MAAA,CAAAC,CAAA,aAAAC,MAAA,CAA6C;IAa7CV,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAsB,eAAA,IAAAtB,MAAA,CAAAC,CAAA,UAAAC,MAAA,CAA0C;IAe1CV,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAsB,eAAA,IAAAtB,MAAA,CAAAC,CAAA,UAAAC,MAAA,CAA0C;;;;;IAsC5CV,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,qCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1CH,EAAA,CAAAC,cAAA,UAA4D;IACxDD,EAAA,CAAAE,MAAA,wDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1DH,EAAA,CAAAC,cAAA,UAA0D;IACtDD,EAAA,CAAAE,MAAA,sDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxDH,EAAA,CAAAC,cAAA,UAAkE;IAC9DD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAbzDH,EAAA,CAAAC,cAAA,cAC8B;IAW1BD,EAVA,CAAAI,UAAA,IAAA2B,6CAAA,kBAAsD,IAAAC,6CAAA,kBAEC,IAAAC,6CAAA,kBAEA,IAAAC,6CAAA,kBAEK,IAAAC,6CAAA,kBAEF,IAAAC,6CAAA,kBAEQ;IAEtEpC,EAAA,CAAAG,YAAA,EAAM;;;;IAZIH,EAAA,CAAAM,SAAA,EAA8C;IAA9CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,aAA8C;IAE9CV,EAAA,CAAAM,SAAA,EAA+C;IAA/CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,cAA+C;IAE/CV,EAAA,CAAAM,SAAA,EAA+C;IAA/CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,cAA+C;IAE/CV,EAAA,CAAAM,SAAA,EAAoD;IAApDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,mBAAoD;IAEpDV,EAAA,CAAAM,SAAA,EAAkD;IAAlDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,iBAAkD;IAElDV,EAAA,CAAAM,SAAA,EAA0D;IAA1DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,yBAA0D;;;;;IAgBhEV,EAAA,CAAAC,cAAA,UAAsD;IAClDD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAChCH,EAAA,CAAAC,cAAA,UAAgE;IAC5DD,EAAA,CAAAE,MAAA,6BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IANVH,EAAA,CAAAC,cAAA,cAC8B;IAG1BD,EAFA,CAAAI,UAAA,IAAAkC,6CAAA,kBAAsD,IAAAC,6CAAA,kBAEU;IAGpEvC,EAAA,CAAAG,YAAA,EAAM;;;;IALIH,EAAA,CAAAM,SAAA,EAA8C;IAA9CN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,aAA8C;IAE9CV,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,uBAAwD;;;;;;IAxC9EV,EAFJ,CAAAC,cAAA,cACgG,eACY;IAAtED,EAAA,CAAAgB,UAAA,sBAAAwB,0DAAA;MAAAxC,EAAA,CAAAkB,aAAA,CAAAuB,GAAA;MAAA,MAAAjC,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAYb,MAAA,CAAAkC,cAAA,EAAgB;IAAA,EAAC;IAE0B1C,EADrF,CAAAC,cAAA,cAAqD,gBACgC,cACxC;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEhEH,EADJ,CAAAC,cAAA,UAAK,cACuB;IACpBD,EAAA,CAAAuB,SAAA,gBAE4F;IAChGvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAuC,uCAAA,kBAC8B;IAetC3C,EADI,CAAAG,YAAA,EAAM,EACJ;IAE+EH,EADrF,CAAAC,cAAA,eAAqD,iBACgC,eACxC;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,wBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEvEH,EADJ,CAAAC,cAAA,WAAK,eACuB;IACpBD,EAAA,CAAAuB,SAAA,iBAE4F;IAChGvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,KAAAwC,uCAAA,kBAC8B;IAQtC5C,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAAkC,kBAE6E;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAG7HF,EAH6H,CAAAG,YAAA,EAAS,EACxH,EACH,EACL;;;;IAnDIH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,cAAAC,MAAA,CAAAqC,aAAA,CAA2B;IAQb7C,EAAA,CAAAM,SAAA,GAAqF;IAArFN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAvC,MAAA,CAAAwC,uBAAA,IAAAxC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,EAAqF;IAEvFV,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAwC,uBAAA,IAAAxC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,CAA6D;IAwB3DV,EAAA,CAAAM,SAAA,GAAqF;IAArFN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAvC,MAAA,CAAAwC,uBAAA,IAAAxC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,EAAqF;IAEvFV,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAO,UAAA,SAAAC,MAAA,CAAAwC,uBAAA,IAAAxC,MAAA,CAAA6B,EAAA,oBAAA3B,MAAA,CAA6D;;;AD1I/F,SAASuC,kBAAkBA,CAACC,WAAmB,EAAEC,mBAA2B;EAC1E,OAAQC,SAAoB,IAAI;IAC9B,MAAMC,OAAO,GAAGD,SAAS,CAACE,QAAQ,CAACJ,WAAW,CAAC;IAC/C,MAAMK,eAAe,GAAGH,SAAS,CAACE,QAAQ,CAACH,mBAAmB,CAAC;IAC/D,IACEI,eAAe,CAAC7C,MAAM,IACtB,CAAC6C,eAAe,CAAC7C,MAAM,CAAC,oBAAoB,CAAC,EAC7C;MACA;IACF;IACA,IAAI2C,OAAO,CAACG,KAAK,KAAKD,eAAe,CAACC,KAAK,EAAE;MAC3CD,eAAe,CAACE,SAAS,CAAC;QAAEC,kBAAkB,EAAE;MAAI,CAAE,CAAC;IACzD,CAAC,MAAM;MACLH,eAAe,CAACE,SAAS,CAAC,IAAI,CAAC;IACjC;EACF,CAAC;AACH;AAGA,SAASE,gBAAgBA,CAACC,KAAa,EAAEC,KAAuB;EAC9D,OAAQR,OAAwB,IAAI;IAClC,IAAI,CAACA,OAAO,CAACG,KAAK,EAAE;MAClB;MACA,OAAO,IAAI;IACb;IAEA;IACA,MAAMM,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACV,OAAO,CAACG,KAAK,CAAC;IAEvC;IACA,OAAOM,KAAK,GAAG,IAAI,GAAGD,KAAK;EAC7B,CAAC;AACH;AAOA,OAAM,MAAOG,gBAAgB;EAc3BC,YAAoBC,EAAe,EAAUC,iBAA+B,EAAUC,WAAwB;IAA1F,KAAAF,EAAE,GAAFA,EAAE;IAAuB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IAAwB,KAAAC,WAAW,GAAXA,WAAW;IAbjG,KAAAC,MAAM,GAA+B;MACnCC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;KACX;IAGD,KAAAzC,eAAe,GAAG,KAAK;IACvB,KAAAkB,uBAAuB,GAAG,KAAK;IAC/B,KAAAwB,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,WAAW,GAAQ,EAAE;IACrB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,KAAK;EAE0F;EAElHC,QAAQA,CAAA;IACN,IAAI,CAAC/B,aAAa,GAAG,IAAI,CAACqB,EAAE,CAACW,KAAK,CAAC;MACjCC,eAAe,EAAE,CACf,EAAE,EACF,CACE/E,UAAU,CAACgF,QAAQ,EACnBhF,UAAU,CAACiF,SAAS,CAAC,CAAC,CAAC;MACvB;MACArB,gBAAgB,CAAC,IAAI,EAAE;QACrBsB,SAAS,EAAE;OACZ,CAAC;MACF;MACAtB,gBAAgB,CAAC,OAAO,EAAE;QACxBuB,cAAc,EAAE;OACjB,CAAC;MACF;MACAvB,gBAAgB,CAAC,OAAO,EAAE;QACxBwB,YAAY,EAAE;OACf,CAAC;MACF;MACAxB,gBAAgB,CAAC,wCAAwC,EAAE;QACzDyB,oBAAoB,EAAE;OACvB,CAAC,CACH,CACF;MACDC,eAAe,EAAE,CAAC,EAAE;KACrB,EAAE;MACDC,UAAU,EAAErC,kBAAkB,CAAC,iBAAiB,EAAE,iBAAiB;KACpE,CAAC;IACF,IAAI,CAACpB,WAAW,GAAG,IAAI,CAACqC,EAAE,CAACW,KAAK,CAAC;MAC/BU,QAAQ,EAAE,CAAC,EAAE,EAAExF,UAAU,CAACgF,QAAQ,CAAC;MACnCS,SAAS,EAAE,CAAC,EAAE,EAAEzF,UAAU,CAACgF,QAAQ,CAAC;MACpCU,QAAQ,EAAE,CAAC,EAAE,EAAE1F,UAAU,CAACgF,QAAQ,CAAC;MACnCW,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC3F,UAAU,CAACgF,QAAQ,EAAEhF,UAAU,CAAC2F,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;IACF,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA,IAAInF,CAACA,CAAA;IACH,OAAO,IAAI,CAACoB,WAAW,CAACyB,QAAQ;EAClC;EAEA,IAAIjB,EAAEA,CAAA;IACJ,OAAO,IAAI,CAACQ,aAAa,CAACS,QAAQ;EACpC;EAEAhC,WAAWA,CAAA;IACT,IAAI,CAACQ,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC,IAAI,CAACD,WAAW,CAACiC,KAAK,EAAE;MAC3B;IACF;IACA,MAAMN,KAAK,GAAG,IAAI,CAAC3B,WAAW,CAAC2B,KAAK;IACpC,IAAI,CAACkB,MAAM,GAAG,IAAI;IAClB,MAAMmB,OAAO,GAAG,IAAI,CAACzB,WAAW,CAAC0B,UAAU,CAACD,OAAO,IAAI,KAAK;IAC5D,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC1B,iBAAiB,CAAC4B,eAAe,CAAC,IAAI,CAACtB,WAAW,CAACuB,EAAE,EAAE;QAC1DC,SAAS,EAAEzC,KAAK,CAACgC,SAAS;QAC1BU,QAAQ,EAAE1C,KAAK,CAACiC,QAAQ;QACxBU,OAAO,EAAE3C,KAAK,CAAC2C,OAAO;QACtBT,KAAK,EAAElC,KAAK,CAACkC,KAAK;QAClBC,KAAK,EAAEnC,KAAK,CAACmC,KAAK;QAClBS,QAAQ,EAAE5C,KAAK,CAAC+B;OACjB,CAAC,CAACc,SAAS,CAAEC,IAAI,IAAI;QACpB,IAAI,CAAC5B,MAAM,GAAG,KAAK;QACnB,IAAI,CAACD,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAClC,WAAW,CAACmC,cAAc,EAAE,CAACF,SAAS,EAAE;MAC/C,CAAC,EAAGG,GAAG,IAAI;QACT,IAAI,CAAC9B,MAAM,GAAG,KAAK;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACP,iBAAiB,CAACsC,UAAU,CAAC,IAAI,CAAChC,WAAW,CAACuB,EAAE,EAAE;QACrDC,SAAS,EAAEzC,KAAK,CAACgC,SAAS;QAC1BU,QAAQ,EAAE1C,KAAK,CAACiC,QAAQ;QACxBU,OAAO,EAAE3C,KAAK,CAAC2C,OAAO;QACtBT,KAAK,EAAElC,KAAK,CAACkC,KAAK;QAClBU,QAAQ,EAAE5C,KAAK,CAAC+B;OACjB,CAAC,CAACc,SAAS,CAAEC,IAAI,IAAI;QACpB,IAAI,CAAC5B,MAAM,GAAG,KAAK;QACnB,IAAI,CAACD,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAClC,WAAW,CAACmC,cAAc,EAAE,CAACF,SAAS,EAAE;MAC/C,CAAC,EAAGG,GAAG,IAAI;QACT,IAAI,CAAC9B,MAAM,GAAG,KAAK;MACrB,CAAC,CAAC;IACJ;EACF;EAEAkB,gBAAgBA,CAAA;IACd,IAAI,CAACpB,kBAAkB,GAAG,IAAI;IAC9B,MAAM8B,IAAI,GAAG,IAAI,CAAClC,WAAW,CAAC0B,UAAU;IACxC,IAAI,CAAC3B,iBAAiB,CAACuC,kBAAkB,CAACJ,IAAI,CAACK,UAAU,CAAC,CAACN,SAAS,CAACC,IAAI,IAAG;MAC1E,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC7B,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAACzE,WAAW,CAAC+E,UAAU,CAAC;UAC1BpB,SAAS,EAAEc,IAAI,CAACL,SAAS;UACzBR,QAAQ,EAAEa,IAAI,CAACJ,QAAQ;UACvBC,OAAO,EAAEG,IAAI,CAACH,OAAO;UACrBT,KAAK,EAAEY,IAAI,CAACZ,KAAK;UACjBC,KAAK,EAAEW,IAAI,CAACX,KAAK;UACjBJ,QAAQ,EAAEe,IAAI,CAACF;SAChB,CAAC;MACJ;MACA,IAAI,CAAC5B,kBAAkB,GAAG,KAAK;IACjC,CAAC,CAAC;EACJ;EAEA9B,cAAcA,CAAA;IACZ,IAAI,CAACM,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAC,IAAI,CAACH,aAAa,CAACiB,KAAK,EAAE;MAC7B;IACF;IACA,IAAI,CAACa,gBAAgB,GAAG,IAAI;IAC5B,MAAMkB,OAAO,GAAG,IAAI,CAACzB,WAAW,CAAC0B,UAAU,CAACD,OAAO,IAAI,KAAK;IAC5D,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC1B,iBAAiB,CAAC4B,eAAe,CAAC,IAAI,CAACtB,WAAW,CAACuB,EAAE,EAAE;QAC1DzB,QAAQ,EAAE,IAAI,CAAC1B,aAAa,CAACW,KAAK,CAACsB;OACpC,CAAC,CAACuB,SAAS,CAAC,MAAK;QAChB,IAAI,CAAC1B,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC9B,aAAa,CAACgE,KAAK,EAAE;QAC1B,IAAI,CAAC7D,uBAAuB,GAAG,KAAK;MACtC,CAAC,EAAE,MAAK;QACN,IAAI,CAAC2B,gBAAgB,GAAG,KAAK;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACR,iBAAiB,CAACsC,UAAU,CAAC,IAAI,CAAChC,WAAW,CAACuB,EAAE,EAAE;QACrDzB,QAAQ,EAAE,IAAI,CAAC1B,aAAa,CAACW,KAAK,CAACsB;OACpC,CAAC,CAACuB,SAAS,CAAC,MAAK;QAChB,IAAI,CAAC1B,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC9B,aAAa,CAACgE,KAAK,EAAE;MAC5B,CAAC,EAAE,MAAK;QACN,IAAI,CAAClC,gBAAgB,GAAG,KAAK;MAC/B,CAAC,CAAC;IACJ;EACF;EAEAmC,WAAWA,CAACC,GAAW;IACrB,IAAI,CAAC1C,MAAM,CAAC0C,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC1C,MAAM,CAAC0C,GAAG,CAAC;EACtC;;;uBAzJW/C,gBAAgB,EAAAhE,EAAA,CAAAgH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlH,EAAA,CAAAgH,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAApH,EAAA,CAAAgH,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBtD,gBAAgB;MAAAuD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CrB7H,EAFR,CAAAC,cAAA,aAA2E,aACc,YACjE;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;UAIEH,EAFR,CAAAC,cAAA,aAAmD,aAC4B,aAElC;UAAjCD,EAAA,CAAAgB,UAAA,mBAAA+G,+CAAA;YAAA,OAASD,GAAA,CAAAhB,WAAA,CAAY,SAAS,CAAC;UAAA,EAAC;UAChC9G,EAAA,CAAAC,cAAA,YAAqD;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIlEH,EAHJ,CAAAC,cAAA,gBAEuC,eACI;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAElEF,EAFkE,CAAAG,YAAA,EAAO,EAC5D,EACP;UACNH,EAAA,CAAAI,UAAA,KAAA4H,gCAAA,kBACgG;UA8EpGhI,EAAA,CAAAG,YAAA,EAAM;UAGFH,EADJ,CAAAC,cAAA,cAA2E,cAEjC;UAAlCD,EAAA,CAAAgB,UAAA,mBAAAiH,gDAAA;YAAA,OAASH,GAAA,CAAAhB,WAAA,CAAY,UAAU,CAAC;UAAA,EAAC;UACjC9G,EAAA,CAAAC,cAAA,aAAqD;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI9DH,EAHJ,CAAAC,cAAA,iBAEwC,eACG;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAElEF,EAFkE,CAAAG,YAAA,EAAO,EAC5D,EACP;UACNH,EAAA,CAAAI,UAAA,KAAA8H,gCAAA,kBACgG;UAwD5GlI,EAHQ,CAAAG,YAAA,EAAM,EACJ,EAEJ;;;UAxJcH,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAmI,WAAA,WAAAL,GAAA,CAAAzD,MAAA,YAAkC;UAIpCrE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAO,UAAA,SAAAuH,GAAA,CAAAzD,MAAA,YAAuB;UAuFrBrE,EAAA,CAAAM,SAAA,GAAmC;UAAnCN,EAAA,CAAAmI,WAAA,WAAAL,GAAA,CAAAzD,MAAA,aAAmC;UAIrCrE,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAO,UAAA,SAAAuH,GAAA,CAAAzD,MAAA,aAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}