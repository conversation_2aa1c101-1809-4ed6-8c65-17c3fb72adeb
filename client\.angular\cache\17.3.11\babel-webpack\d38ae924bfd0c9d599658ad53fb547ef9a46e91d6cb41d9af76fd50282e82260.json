{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, map } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProductService {\n  constructor(http) {\n    this.http = http;\n    this.productSubject = new BehaviorSubject(null);\n    this.product = this.productSubject.asObservable();\n  }\n  getProducts(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][product_id][$containsi]', searchTerm).set('filters[$or][1][item_category_group][$containsi]', searchTerm).set('filters[$or][2][product_status][$containsi]', searchTerm).set('filters[$or][3][base_unit][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.GET_ALL_PRODUCTS}`, {\n      params\n    });\n  }\n  getProductByID(productid) {\n    const params = new HttpParams().set('populate[catalogs][populate]', '*').set('populate[categories][populate]', '*').set('populate[descriptions][populate]', '*').set('populate[medias][populate]', '*').set('populate[plants][populate]', '*').set('populate[sale][populate]', '*').set('populate[storage][populate]', '*').set('populate[basic_texts][populate]', '*').set('populate[sales_deliveries][populate]', '*').set('populate[sales_taxes][populate]', '*').set('populate[units_of_measures][populate]', '*').set('populate[suggestions][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.GET_ALL_PRODUCTS}/${productid}`, {\n      params\n    });\n  }\n  updateProduct(id, productData) {\n    return this.http.put(`${CMS_APIContstant.GET_ALL_PRODUCTS}/${id}`, {\n      data: productData\n    }).pipe(map(res => res.data));\n  }\n  getProductByIDName(productdata) {\n    let params = new HttpParams();\n    if (productdata) {\n      params = params.set('filters[$or][0][product_id][$containsi]', productdata).set('filters[$or][1][name][$containsi]', productdata);\n    }\n    return this.http.get(`${CMS_APIContstant.GET_ALL_PRODUCTS}`, {\n      params\n    });\n  }\n  getById(id) {\n    return this.http.get(`${CMS_APIContstant.GET_ALL_PRODUCTS}/${id}?populate=*`);\n  }\n  getAllCategories() {\n    return this.http.get(`${CMS_APIContstant.GET_ALL_PRODUCTS}`);\n  }\n  getAllCategory() {\n    return this.http.get(`${CMS_APIContstant.GET_CATEGORIES}`).pipe(map(res => res.data));\n  }\n  getAllCatalog() {\n    return this.http.get(`${CMS_APIContstant.GET_CATALOGS}`).pipe(map(res => res.data));\n  }\n  getProductClassification(productId) {\n    return this.http.get(`${CMS_APIContstant.CMSAPI_END_POINT}/${productId}/classification`);\n  }\n  getMedia(id) {\n    return this.http.get(`${CMS_APIContstant.PRODUCT_MDEIA}/${id}`);\n  }\n  removeMedia(id) {\n    return this.http.delete(`${CMS_APIContstant.PRODUCT_MDEIA}/${id}`);\n  }\n  submitMediaForm(media) {\n    return this.http.post(`${CMS_APIContstant.PRODUCT_MDEIA}`, {\n      data: media\n    });\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "map", "CMS_APIContstant", "ProductService", "constructor", "http", "productSubject", "product", "asObservable", "getProducts", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "GET_ALL_PRODUCTS", "getProductByID", "productid", "updateProduct", "id", "productData", "put", "data", "pipe", "res", "getProductByIDName", "productdata", "getById", "getAllCategories", "getAllCategory", "GET_CATEGORIES", "getAllCatalog", "GET_CATALOGS", "getProductClassification", "productId", "CMSAPI_END_POINT", "getMedia", "PRODUCT_MDEIA", "removeMedia", "delete", "submitMediaForm", "media", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, map } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ProductService {\r\n  public productSubject = new BehaviorSubject<any>(null);\r\n  public product = this.productSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getProducts(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][product_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][item_category_group][$containsi]', searchTerm)\r\n        .set('filters[$or][2][product_status][$containsi]', searchTerm)\r\n        .set('filters[$or][3][base_unit][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(`${CMS_APIContstant.GET_ALL_PRODUCTS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getProductByID(productid: string) {\r\n    const params = new HttpParams()\r\n      .set('populate[catalogs][populate]', '*')\r\n      .set('populate[categories][populate]', '*')\r\n      .set('populate[descriptions][populate]', '*')\r\n      .set('populate[medias][populate]', '*')\r\n      .set('populate[plants][populate]', '*')\r\n      .set('populate[sale][populate]', '*')\r\n      .set('populate[storage][populate]', '*')\r\n      .set('populate[basic_texts][populate]', '*')\r\n      .set('populate[sales_deliveries][populate]', '*')\r\n      .set('populate[sales_taxes][populate]', '*')\r\n      .set('populate[units_of_measures][populate]', '*')\r\n      .set('populate[suggestions][populate]', '*');\r\n\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.GET_ALL_PRODUCTS}/${productid}`,\r\n      { params }\r\n    );\r\n  }\r\n\r\n  updateProduct(id: any, productData: any) {\r\n    return this.http\r\n      .put<any>(`${CMS_APIContstant.GET_ALL_PRODUCTS}/${id}`, {\r\n        data: productData,\r\n      })\r\n      .pipe(map((res) => res.data));\r\n  }\r\n\r\n  getProductByIDName(productdata: string): Observable<any[]> {\r\n    let params = new HttpParams();\r\n    if (productdata) {\r\n      params = params\r\n        .set('filters[$or][0][product_id][$containsi]', productdata)\r\n        .set('filters[$or][1][name][$containsi]', productdata);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.GET_ALL_PRODUCTS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getById(id: string) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.GET_ALL_PRODUCTS}/${id}?populate=*`\r\n    );\r\n  }\r\n\r\n  getAllCategories(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${CMS_APIContstant.GET_ALL_PRODUCTS}`);\r\n  }\r\n\r\n  getAllCategory() {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.GET_CATEGORIES}`)\r\n      .pipe(map((res) => res.data));\r\n  }\r\n\r\n  getAllCatalog() {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.GET_CATALOGS}`)\r\n      .pipe(map((res) => res.data));\r\n  }\r\n  getProductClassification(productId: any) {\r\n    return this.http.get<any>(\r\n      `${CMS_APIContstant.CMSAPI_END_POINT}/${productId}/classification`\r\n    );\r\n  }\r\n\r\n  getMedia(id: any) {\r\n    return this.http.get<any>(`${CMS_APIContstant.PRODUCT_MDEIA}/${id}`);\r\n  }\r\n\r\n  removeMedia(id: any) {\r\n    return this.http.delete<any>(`${CMS_APIContstant.PRODUCT_MDEIA}/${id}`);\r\n  }\r\n\r\n  submitMediaForm(media: any) {\r\n    return this.http.post<any>(`${CMS_APIContstant.PRODUCT_MDEIA}`, {\r\n      data: media,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,EAAcC,GAAG,QAAQ,MAAM;AACvD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,cAAc;EAIzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAO,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC1BiB,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,yCAAyC,EAAEF,UAAU,CAAC,CAC1DE,GAAG,CAAC,kDAAkD,EAAEF,UAAU,CAAC,CACnEE,GAAG,CAAC,6CAA6C,EAAEF,UAAU,CAAC,CAC9DE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC;IAC9D;IACA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,gBAAgB,EAAE,EAAE;MAClEN;KACD,CAAC;EACJ;EAEAO,cAAcA,CAACC,SAAiB;IAC9B,MAAMR,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC5BiB,GAAG,CAAC,8BAA8B,EAAE,GAAG,CAAC,CACxCA,GAAG,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAC1CA,GAAG,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAC5CA,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC,CACtCA,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC,CACtCA,GAAG,CAAC,0BAA0B,EAAE,GAAG,CAAC,CACpCA,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CACvCA,GAAG,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAC3CA,GAAG,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAChDA,GAAG,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAC3CA,GAAG,CAAC,uCAAuC,EAAE,GAAG,CAAC,CACjDA,GAAG,CAAC,iCAAiC,EAAE,GAAG,CAAC;IAE9C,OAAO,IAAI,CAACX,IAAI,CAACe,GAAG,CAClB,GAAGlB,gBAAgB,CAACmB,gBAAgB,IAAIE,SAAS,EAAE,EACnD;MAAER;IAAM,CAAE,CACX;EACH;EAEAS,aAAaA,CAACC,EAAO,EAAEC,WAAgB;IACrC,OAAO,IAAI,CAACrB,IAAI,CACbsB,GAAG,CAAM,GAAGzB,gBAAgB,CAACmB,gBAAgB,IAAII,EAAE,EAAE,EAAE;MACtDG,IAAI,EAAEF;KACP,CAAC,CACDG,IAAI,CAAC5B,GAAG,CAAE6B,GAAG,IAAKA,GAAG,CAACF,IAAI,CAAC,CAAC;EACjC;EAEAG,kBAAkBA,CAACC,WAAmB;IACpC,IAAIjB,MAAM,GAAG,IAAIhB,UAAU,EAAE;IAC7B,IAAIiC,WAAW,EAAE;MACfjB,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,yCAAyC,EAAEgB,WAAW,CAAC,CAC3DhB,GAAG,CAAC,mCAAmC,EAAEgB,WAAW,CAAC;IAC1D;IAEA,OAAO,IAAI,CAAC3B,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,gBAAgB,EAAE,EAAE;MAClEN;KACD,CAAC;EACJ;EAEAkB,OAAOA,CAACR,EAAU;IAChB,OAAO,IAAI,CAACpB,IAAI,CAACe,GAAG,CAClB,GAAGlB,gBAAgB,CAACmB,gBAAgB,IAAII,EAAE,aAAa,CACxD;EACH;EAEAS,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC7B,IAAI,CAACe,GAAG,CAAQ,GAAGlB,gBAAgB,CAACmB,gBAAgB,EAAE,CAAC;EACrE;EAEAc,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9B,IAAI,CACbe,GAAG,CAAM,GAAGlB,gBAAgB,CAACkC,cAAc,EAAE,CAAC,CAC9CP,IAAI,CAAC5B,GAAG,CAAE6B,GAAG,IAAKA,GAAG,CAACF,IAAI,CAAC,CAAC;EACjC;EAEAS,aAAaA,CAAA;IACX,OAAO,IAAI,CAAChC,IAAI,CACbe,GAAG,CAAM,GAAGlB,gBAAgB,CAACoC,YAAY,EAAE,CAAC,CAC5CT,IAAI,CAAC5B,GAAG,CAAE6B,GAAG,IAAKA,GAAG,CAACF,IAAI,CAAC,CAAC;EACjC;EACAW,wBAAwBA,CAACC,SAAc;IACrC,OAAO,IAAI,CAACnC,IAAI,CAACe,GAAG,CAClB,GAAGlB,gBAAgB,CAACuC,gBAAgB,IAAID,SAAS,iBAAiB,CACnE;EACH;EAEAE,QAAQA,CAACjB,EAAO;IACd,OAAO,IAAI,CAACpB,IAAI,CAACe,GAAG,CAAM,GAAGlB,gBAAgB,CAACyC,aAAa,IAAIlB,EAAE,EAAE,CAAC;EACtE;EAEAmB,WAAWA,CAACnB,EAAO;IACjB,OAAO,IAAI,CAACpB,IAAI,CAACwC,MAAM,CAAM,GAAG3C,gBAAgB,CAACyC,aAAa,IAAIlB,EAAE,EAAE,CAAC;EACzE;EAEAqB,eAAeA,CAACC,KAAU;IACxB,OAAO,IAAI,CAAC1C,IAAI,CAAC2C,IAAI,CAAM,GAAG9C,gBAAgB,CAACyC,aAAa,EAAE,EAAE;MAC9Df,IAAI,EAAEmB;KACP,CAAC;EACJ;;;uBAjHW5C,cAAc,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdjD,cAAc;MAAAkD,OAAA,EAAdlD,cAAc,CAAAmD,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}