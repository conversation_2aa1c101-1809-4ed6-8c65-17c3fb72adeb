{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.isSidebarCollapsed = true;\n  }\n  toggleSidebar() {\n    this.isSidebarCollapsed = !this.isSidebarCollapsed;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "isSidebarCollapsed", "toggleSidebar", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\app.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n})\r\nexport class AppComponent {\r\n  public isSidebarCollapsed = true;\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarCollapsed = !this.isSidebarCollapsed;\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAMS,KAAAC,kBAAkB,GAAG,IAAI;;EAEhCC,aAAaA,CAAA;IACX,IAAI,CAACD,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;;;uBALWF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}