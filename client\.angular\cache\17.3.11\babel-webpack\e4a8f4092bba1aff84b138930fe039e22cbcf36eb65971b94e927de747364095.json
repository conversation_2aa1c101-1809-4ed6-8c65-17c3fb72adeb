{"ast": null, "code": "import { Permission } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/authentication/auth.service\";\nfunction AppMenuComponent_ng_container_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const item_r2 = ctx_r0.$implicit;\n    const i_r3 = ctx_r0.index;\n    i0.ɵɵproperty(\"item\", item_r2)(\"index\", i_r3)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_ng_container_1_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n}\nfunction AppMenuComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_li_1_Template, 1, 3, \"li\", 2)(2, AppMenuComponent_ng_container_1_li_2_Template, 1, 0, \"li\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r2.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.separator);\n  }\n}\nexport class AppMenuComponent {\n  constructor(auth) {\n    this.auth = auth;\n    this.model = [{\n      // label: 'Apps',\n      icon: 'pi pi-th-large',\n      items: [{\n        label: 'Home',\n        icon: 'pi pi-fw pi-home',\n        routerLink: ['/store']\n      }, {\n        label: 'Vendor Account',\n        icon: 'pi pi-fw pi-user',\n        routerLink: ['/store/vendor-account']\n      }]\n    }];\n  }\n  ngOnInit() {\n    const permission = this.auth.getPermissions;\n    if (permission.includes(Permission.Vendor_Portal_Backoffice)) {\n      this.model[0].items.push({\n        label: 'Vendor Contact',\n        icon: 'pi pi-fw pi-phone',\n        routerLink: ['/store/vendor-contact']\n      });\n    }\n    this.model[0].items.push({\n      label: 'Invoices',\n      icon: 'pi pi-fw pi-file',\n      routerLink: ['/store/invoice']\n    }, {\n      label: 'Payment History',\n      icon: 'pi pi-fw pi-money-bill',\n      routerLink: ['/store/payment-history']\n    }, {\n      label: 'Resource Center',\n      icon: 'pi pi-fw pi-file',\n      routerLink: ['/store/resource-center']\n    });\n  }\n  static {\n    this.ɵfac = function AppMenuComponent_Factory(t) {\n      return new (t || AppMenuComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppMenuComponent,\n      selectors: [[\"app-menu\"]],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"layout-menu\"], [4, \"ngFor\", \"ngForOf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"class\", \"menu-separator\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\"], [1, \"menu-separator\"]],\n      template: function AppMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0);\n          i0.ɵɵtemplate(1, AppMenuComponent_ng_container_1_Template, 3, 2, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Permission", "i0", "ɵɵelement", "ɵɵproperty", "item_r2", "i_r3", "ɵɵelementContainerStart", "ɵɵtemplate", "AppMenuComponent_ng_container_1_li_1_Template", "AppMenuComponent_ng_container_1_li_2_Template", "ɵɵadvance", "separator", "AppMenuComponent", "constructor", "auth", "model", "icon", "items", "label", "routerLink", "ngOnInit", "permission", "getPermissions", "includes", "Vendor_Portal_Backoffice", "push", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "decls", "vars", "consts", "template", "AppMenuComponent_Template", "rf", "ctx", "ɵɵelementStart", "AppMenuComponent_ng_container_1_Template", "ɵɵelementEnd"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.menu.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\app.menu.component.html"], "sourcesContent": ["import { OnInit } from '@angular/core';\r\nimport { Component } from '@angular/core';\r\nimport { Permission } from 'src/app/constants/api.constants';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-menu',\r\n  templateUrl: './app.menu.component.html',\r\n})\r\nexport class AppMenuComponent implements OnInit {\r\n  model: any[] = [\r\n    {\r\n      // label: 'Apps',\r\n      icon: 'pi pi-th-large',\r\n      items: [\r\n        {\r\n          label: 'Home',\r\n          icon: 'pi pi-fw pi-home',\r\n          routerLink: ['/store'],\r\n        },\r\n        {\r\n          label: 'Vendor Account',\r\n          icon: 'pi pi-fw pi-user',\r\n          routerLink: ['/store/vendor-account'],\r\n        },\r\n\r\n      ],\r\n    }\r\n  ];\r\n\r\n  constructor(private auth: AuthService) { }\r\n\r\n  ngOnInit() {\r\n    const permission = this.auth.getPermissions;\r\n    if (permission.includes(Permission.Vendor_Portal_Backoffice)) {\r\n      this.model[0].items.push(\r\n        {\r\n          label: 'Vendor Contact',\r\n          icon: 'pi pi-fw pi-phone',\r\n          routerLink: ['/store/vendor-contact'],\r\n        });\r\n    }\r\n    this.model[0].items.push(\r\n      {\r\n        label: 'Invoices',\r\n        icon: 'pi pi-fw pi-file',\r\n        routerLink: ['/store/invoice'],\r\n      },\r\n      {\r\n        label: 'Payment History',\r\n        icon: 'pi pi-fw pi-money-bill',\r\n        routerLink: ['/store/payment-history'],\r\n      },\r\n      {\r\n        label: 'Resource Center',\r\n        icon: 'pi pi-fw pi-file',\r\n        routerLink: ['/store/resource-center'],\r\n      },)\r\n\r\n  }\r\n}\r\n", "<ul class=\"layout-menu\">\r\n    <ng-container *ngFor=\"let item of model; let i = index;\">\r\n        <li app-menuitem *ngIf=\"!item.separator\" [item]=\"item\" [index]=\"i\" [root]=\"true\"></li>\r\n        <li *ngIf=\"item.separator\" class=\"menu-separator\"></li>\r\n    </ng-container>\r\n</ul>"], "mappings": "AAEA,SAASA,UAAU,QAAQ,iCAAiC;;;;;ICApDC,EAAA,CAAAC,SAAA,YAAsF;;;;;;IAAnBD,EAA1B,CAAAE,UAAA,SAAAC,OAAA,CAAa,UAAAC,IAAA,CAAY,cAAc;;;;;IAChFJ,EAAA,CAAAC,SAAA,YAAuD;;;;;IAF3DD,EAAA,CAAAK,uBAAA,GAAyD;IAErDL,EADA,CAAAM,UAAA,IAAAC,6CAAA,gBAAiF,IAAAC,6CAAA,gBAC/B;;;;;IADhCR,EAAA,CAAAS,SAAA,EAAqB;IAArBT,EAAA,CAAAE,UAAA,UAAAC,OAAA,CAAAO,SAAA,CAAqB;IAClCV,EAAA,CAAAS,SAAA,EAAoB;IAApBT,EAAA,CAAAE,UAAA,SAAAC,OAAA,CAAAO,SAAA,CAAoB;;;ADMjC,OAAM,MAAOC,gBAAgB;EAqB3BC,YAAoBC,IAAiB;IAAjB,KAAAA,IAAI,GAAJA,IAAI;IApBxB,KAAAC,KAAK,GAAU,CACb;MACE;MACAC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,MAAM;QACbF,IAAI,EAAE,kBAAkB;QACxBG,UAAU,EAAE,CAAC,QAAQ;OACtB,EACD;QACED,KAAK,EAAE,gBAAgB;QACvBF,IAAI,EAAE,kBAAkB;QACxBG,UAAU,EAAE,CAAC,uBAAuB;OACrC;KAGJ,CACF;EAEwC;EAEzCC,QAAQA,CAAA;IACN,MAAMC,UAAU,GAAG,IAAI,CAACP,IAAI,CAACQ,cAAc;IAC3C,IAAID,UAAU,CAACE,QAAQ,CAACvB,UAAU,CAACwB,wBAAwB,CAAC,EAAE;MAC5D,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAACQ,IAAI,CACtB;QACEP,KAAK,EAAE,gBAAgB;QACvBF,IAAI,EAAE,mBAAmB;QACzBG,UAAU,EAAE,CAAC,uBAAuB;OACrC,CAAC;IACN;IACA,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAACQ,IAAI,CACtB;MACEP,KAAK,EAAE,UAAU;MACjBF,IAAI,EAAE,kBAAkB;MACxBG,UAAU,EAAE,CAAC,gBAAgB;KAC9B,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBF,IAAI,EAAE,wBAAwB;MAC9BG,UAAU,EAAE,CAAC,wBAAwB;KACtC,EACD;MACED,KAAK,EAAE,iBAAiB;MACxBF,IAAI,EAAE,kBAAkB;MACxBG,UAAU,EAAE,CAAC,wBAAwB;KACtC,CAAE;EAEP;;;uBAlDWP,gBAAgB,EAAAX,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBhB,gBAAgB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT7BlC,EAAA,CAAAoC,cAAA,YAAwB;UACpBpC,EAAA,CAAAM,UAAA,IAAA+B,wCAAA,0BAAyD;UAI7DrC,EAAA,CAAAsC,YAAA,EAAK;;;UAJ8BtC,EAAA,CAAAS,SAAA,EAAU;UAAVT,EAAA,CAAAE,UAAA,YAAAiC,GAAA,CAAArB,KAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}