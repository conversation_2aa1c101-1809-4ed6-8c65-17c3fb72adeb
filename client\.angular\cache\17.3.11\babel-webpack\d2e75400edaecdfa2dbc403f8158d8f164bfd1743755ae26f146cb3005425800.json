{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PartnerBankService {\n  constructor(http) {\n    this.http = http;\n  }\n  getBank(id, page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bank_identification][$containsi]', searchTerm).set('filters[$or][1][bank_account][$containsi]', searchTerm).set('filters[$or][2][bank_account_name][$containsi]', searchTerm).set('filters[$or][3][bank_name][$containsi]', searchTerm).set('filters[$or][4][bank_account_holder_name][$containsi]', searchTerm);\n    }\n    if (id) {\n      params = params.set('filters[$and][0][bp_id][$eq]', id);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS_BANK}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function PartnerBankService_Factory(t) {\n      return new (t || PartnerBankService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PartnerBankService,\n      factory: PartnerBankService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "CMS_APIContstant", "PartnerBankService", "constructor", "http", "getBank", "id", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "PARTNERS_BANK", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\partner-bank\\partner-bank.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PartnerBankService {\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getBank(\r\n    id: string,\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][bank_identification][$containsi]', searchTerm)\r\n        .set('filters[$or][1][bank_account][$containsi]', searchTerm)\r\n        .set('filters[$or][2][bank_account_name][$containsi]', searchTerm)\r\n        .set('filters[$or][3][bank_name][$containsi]', searchTerm)\r\n        .set(\r\n          'filters[$or][4][bank_account_holder_name][$containsi]',\r\n          searchTerm\r\n        );\r\n    }\r\n    if (id) {\r\n      params = params.set('filters[$and][0][bp_id][$eq]', id);\r\n    }\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS_BANK}`, {\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAG7D,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,kBAAkB;EAC7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,OAAOA,CACLC,EAAU,EACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIZ,UAAU,EAAE,CAC1Ba,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,kDAAkD,EAAEF,UAAU,CAAC,CACnEE,GAAG,CAAC,2CAA2C,EAAEF,UAAU,CAAC,CAC5DE,GAAG,CAAC,gDAAgD,EAAEF,UAAU,CAAC,CACjEE,GAAG,CAAC,wCAAwC,EAAEF,UAAU,CAAC,CACzDE,GAAG,CACF,uDAAuD,EACvDF,UAAU,CACX;IACL;IACA,IAAIL,EAAE,EAAE;MACNM,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,8BAA8B,EAAEP,EAAE,CAAC;IACzD;IACA,OAAO,IAAI,CAACF,IAAI,CAACa,GAAG,CAAQ,GAAGhB,gBAAgB,CAACiB,aAAa,EAAE,EAAE;MAC/DN;KACD,CAAC;EACJ;;;uBAnCWV,kBAAkB,EAAAiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBpB,kBAAkB;MAAAqB,OAAA,EAAlBrB,kBAAkB,CAAAsB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}