{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { VendorEmailComponent } from './vendor-email/vendor-email.component';\nimport { VendorContactComponent } from './vendor-contact/vendor-contact.component';\nimport { VendorContactDetailsComponent } from './vendor-contact/vendor-contact-details/vendor-contact-details.component';\nimport { GeneralComponent } from './vendor-contact/vendor-contact-details/general/general.component';\nimport { PasswordComponent } from './vendor-contact/vendor-contact-details/password/password.component';\nimport { VendorDetailComponent } from './vendor-contact/vendor-contact-details/vendor-detail/vendor-detail.component';\nimport { VendorPoInstructionsComponent } from './vendor-po-instructions/vendor-po-instructions.component';\nimport { VendorMiscellaneousInfoComponent } from './vendor-miscellaneous-info/vendor-miscellaneous-info.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'admin-email',\n  component: VendorEmailComponent,\n  data: {\n    type: 'VENDOR_ADMIN_EMAILS',\n    title: 'Vendor Admin Email'\n  },\n  children: [{\n    path: '',\n    redirectTo: 'general-settings',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general-settings',\n    pathMatch: 'full'\n  }]\n}, {\n  path: 'po-instructions',\n  component: VendorPoInstructionsComponent,\n  data: {\n    type: 'VENDOR_PO_INSTRUCTIONS',\n    title: 'Vendor PO Instructions'\n  }\n}, {\n  path: 'miscellaneous-info',\n  component: VendorMiscellaneousInfoComponent,\n  data: {\n    type: 'VENDOR_MISCELLANEOUS_INFO',\n    title: 'Vendor Miscellaneous Info'\n  }\n}, {\n  path: 'contact',\n  component: VendorContactComponent\n}, {\n  path: 'contact/:id',\n  component: VendorContactDetailsComponent,\n  children: [{\n    path: 'general',\n    component: GeneralComponent\n  }, {\n    path: 'vendor-detail',\n    component: VendorDetailComponent\n  }, {\n    path: 'password',\n    component: PasswordComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport class VendorRoutingModule {\n  static {\n    this.ɵfac = function VendorRoutingModule_Factory(t) {\n      return new (t || VendorRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: VendorRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(VendorRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "VendorEmailComponent", "VendorContactComponent", "VendorContactDetailsComponent", "GeneralComponent", "PasswordComponent", "VendorDetailComponent", "VendorPoInstructionsComponent", "VendorMiscellaneousInfoComponent", "routes", "path", "component", "data", "type", "title", "children", "redirectTo", "pathMatch", "VendorRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\vendor\\vendor-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { VendorEmailComponent } from './vendor-email/vendor-email.component';\r\nimport { VendorContactComponent } from './vendor-contact/vendor-contact.component';\r\nimport { VendorContactDetailsComponent } from './vendor-contact/vendor-contact-details/vendor-contact-details.component';\r\nimport { GeneralComponent } from './vendor-contact/vendor-contact-details/general/general.component';\r\nimport { PasswordComponent } from './vendor-contact/vendor-contact-details/password/password.component';\r\nimport { VendorDetailComponent } from './vendor-contact/vendor-contact-details/vendor-detail/vendor-detail.component';\r\nimport { VendorPoInstructionsComponent } from './vendor-po-instructions/vendor-po-instructions.component';\r\nimport { VendorMiscellaneousInfoComponent } from './vendor-miscellaneous-info/vendor-miscellaneous-info.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'admin-email',\r\n    component: VendorEmailComponent,\r\n    data: { type: 'VENDOR_ADMIN_EMAILS', title: 'Vendor Admin Email' },\r\n    children: [\r\n      { path: '', redirectTo: 'general-settings', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general-settings', pathMatch: 'full' },\r\n    ],\r\n  },\r\n  {\r\n    path: 'po-instructions',\r\n    component: VendorPoInstructionsComponent,\r\n    data: { type: 'VENDOR_PO_INSTRUCTIONS', title: 'Vendor PO Instructions' },\r\n  },\r\n  {\r\n    path: 'miscellaneous-info',\r\n    component: VendorMiscellaneousInfoComponent,\r\n    data: {\r\n      type: 'VENDOR_MISCELLANEOUS_INFO',\r\n      title: 'Vendor Miscellaneous Info',\r\n    },\r\n  },\r\n  {\r\n    path: 'contact',\r\n    component: VendorContactComponent,\r\n  },\r\n  {\r\n    path: 'contact/:id',\r\n    component: VendorContactDetailsComponent,\r\n    children: [\r\n      { path: 'general', component: GeneralComponent },\r\n      { path: 'vendor-detail', component: VendorDetailComponent },\r\n      { path: 'password', component: PasswordComponent },\r\n      { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class VendorRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,6BAA6B,QAAQ,0EAA0E;AACxH,SAASC,gBAAgB,QAAQ,mEAAmE;AACpG,SAASC,iBAAiB,QAAQ,qEAAqE;AACvG,SAASC,qBAAqB,QAAQ,+EAA+E;AACrH,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,gCAAgC,QAAQ,iEAAiE;;;AAElH,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEV,oBAAoB;EAC/BW,IAAI,EAAE;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAoB,CAAE;EAClEC,QAAQ,EAAE,CACR;IAAEL,IAAI,EAAE,EAAE;IAAEM,UAAU,EAAE,kBAAkB;IAAEC,SAAS,EAAE;EAAM,CAAE,EAC/D;IAAEP,IAAI,EAAE,IAAI;IAAEM,UAAU,EAAE,kBAAkB;IAAEC,SAAS,EAAE;EAAM,CAAE;CAEpE,EACD;EACEP,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEJ,6BAA6B;EACxCK,IAAI,EAAE;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,KAAK,EAAE;EAAwB;CACxE,EACD;EACEJ,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEH,gCAAgC;EAC3CI,IAAI,EAAE;IACJC,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE;;CAEV,EACD;EACEJ,IAAI,EAAE,SAAS;EACfC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAER,6BAA6B;EACxCY,QAAQ,EAAE,CACR;IAAEL,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEP;EAAgB,CAAE,EAChD;IAAEM,IAAI,EAAE,eAAe;IAAEC,SAAS,EAAEL;EAAqB,CAAE,EAC3D;IAAEI,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEN;EAAiB,CAAE,EAClD;IAAEK,IAAI,EAAE,EAAE;IAAEM,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEP,IAAI,EAAE,IAAI;IAAEM,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE3D,CACF;AAMD,OAAM,MAAOC,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;gBAHpBlB,YAAY,CAACmB,QAAQ,CAACV,MAAM,CAAC,EAC7BT,YAAY;IAAA;EAAA;;;2EAEXkB,mBAAmB;IAAAE,OAAA,GAAAC,EAAA,CAAArB,YAAA;IAAAsB,OAAA,GAFpBtB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}