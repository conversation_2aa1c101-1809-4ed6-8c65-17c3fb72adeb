{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { switchMap } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"src/app/shared/services/toast.service\";\nimport * as i3 from \"../service/manage-user.service\";\nimport * as i4 from \"src/app/core/authentication/auth.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/table\";\nimport * as i10 from \"primeng/inputgroupaddon\";\nimport * as i11 from \"primeng/inputgroup\";\nconst _c0 = [\"filter\"];\nconst _c1 = () => [\"customer_id\", \"customer_name\", \"cpf_address\"];\nfunction SelectCustomerComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"select\", 14);\n    i0.ɵɵlistener(\"ngModelChange\", function SelectCustomerComponent_ng_template_4_Template_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchByChange($event));\n    });\n    i0.ɵɵelementStart(2, \"option\", 15);\n    i0.ɵɵtext(3, \"Customer ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"option\", 16);\n    i0.ɵɵtext(5, \"Customer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 17);\n    i0.ɵɵtext(7, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 18);\n    i0.ɵɵtext(9, \"Phone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"p-inputGroup\", 19)(11, \"p-inputGroupAddon\");\n    i0.ɵɵelement(12, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 21, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SelectCustomerComponent_ng_template_4_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.globalSearchTerm, $event) || (ctx_r2.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function SelectCustomerComponent_ng_template_4_Template_input_input_13_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dt1_r4 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(ctx_r2.onGlobalFilter(dt1_r4, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SelectCustomerComponent_ng_template_4_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dt1_r4 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(ctx_r2.clear(dt1_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchBy);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.globalSearchTerm);\n  }\n}\nfunction SelectCustomerComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 23);\n    i0.ɵɵtext(2, \" ID \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 24);\n    i0.ɵɵtext(4, \" Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 25);\n    i0.ɵɵtext(6, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 26);\n    i0.ɵɵtext(8, \" Phone \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SelectCustomerComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const customer_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"pSelectableRow\", customer_r5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.customer_id, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.customer_name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.cpf_address, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", customer_r5.business_partner == null ? null : customer_r5.business_partner.phone, \" \");\n  }\n}\nfunction SelectCustomerComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2, \"No customers found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SelectCustomerComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28);\n    i0.ɵɵtext(2, \"Loading customers data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SelectCustomerComponent {\n  constructor(ref, _snackBar, manageUserService, auth) {\n    this.ref = ref;\n    this._snackBar = _snackBar;\n    this.manageUserService = manageUserService;\n    this.auth = auth;\n    this.selectedCustomerId = null;\n    this.customers = [];\n    this.searchBy = \"customer_name\";\n    this.totalRecords = 0;\n    this.loading = true;\n    this.globalSearchTerm = '';\n    this.saving = false;\n  }\n  ngOnInit() {\n    // this.loadCustomers({ first: 0, rows: 10 });\n  }\n  loadCustomers(event) {\n    this.loading = true;\n    const page = event.first / event.rows + 1;\n    const pageSize = event.rows;\n    this.manageUserService.getLoggedInUserCustomersSearch({\n      perPage: pageSize,\n      search: this.globalSearchTerm,\n      searchBy: this.searchBy,\n      sortBy: event.sortField ? `${event.sortField}:${event.sortOrder == 1 ? \"asc\" : 'desc'}` : '',\n      pageNo: page\n    }).subscribe({\n      next: ({\n        data,\n        total\n      }) => {\n        this.customers = data || [];\n        const selected = this.auth.userDetail.customer?.documentId;\n        if (selected) {\n          const selectedCustomer = this.customers.find(c => c.documentId === selected);\n          selectedCustomer && (this.selectedCustomerId = selectedCustomer);\n        }\n        this.totalRecords = total;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error fetching customers', error);\n        this.loading = false;\n      }\n    });\n  }\n  onGlobalFilter(table, event) {\n    this.loadCustomers({\n      first: 0,\n      rows: 10\n    });\n  }\n  clear(table) {\n    this.globalSearchTerm = '';\n    this.filter.nativeElement.value = '';\n    this.loadCustomers({\n      first: 0,\n      rows: 10\n    });\n  }\n  onSearchByChange(option) {\n    this.searchBy = option;\n    this.loadCustomers({\n      first: 0,\n      rows: 10\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    if (!this.selectedCustomerId) {\n      this._snackBar.open(\"Please select customer.\", {\n        type: \"Warning\"\n      });\n      return;\n    }\n    this.saving = true;\n    this.manageUserService.updateSelectedCustomer(this.selectedCustomerId, null).pipe(switchMap(res => {\n      const user = this.auth.userDetail;\n      return this.auth.getCartDetails(user.documentId);\n    })).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (cartres) {\n          _this.saving = false;\n          if (cartres?.cart) {\n            const cart = cartres.cart || null;\n            _this.auth.updateAuth({\n              cart,\n              customer: cart.customer\n            });\n          }\n          _this.ref.close();\n          location.reload();\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }(),\n      error: () => {\n        this.saving = false;\n        this._snackBar.open(\"Error while processing your request.\", {\n          type: \"Error\"\n        });\n      }\n    });\n  }\n  onReset() {\n    this.selectedCustomerId = null;\n  }\n  static {\n    this.ɵfac = function SelectCustomerComponent_Factory(t) {\n      return new (t || SelectCustomerComponent)(i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i2.AppToastService), i0.ɵɵdirectiveInject(i3.ManageUserService), i0.ɵɵdirectiveInject(i4.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SelectCustomerComponent,\n      selectors: [[\"app-select-customer\"]],\n      viewQuery: function SelectCustomerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n        }\n      },\n      decls: 14,\n      vars: 12,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"switch-acc-popup-body\", \"d-flex\", \"flex-column\", \"justify-content-between\"], [1, \"switch-acc-popup-cnt\"], [\"dataKey\", \"id\", \"styleClass\", \"p-datatable-gridlines\", \"dataKey\", \"documentId\", \"selectionMode\", \"single\", \"responsiveLayout\", \"scroll\", 3, \"onLazyLoad\", \"selectionChange\", \"value\", \"rows\", \"loading\", \"rowHover\", \"paginator\", \"globalFilterFields\", \"totalRecords\", \"selection\", \"lazy\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"p-button-outlined\", 3, \"click\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"click\", \"disabled\"], [1, \"flex\", \"gap-3\", \"flex-row\", \"justify-content-end\"], [3, \"ngModelChange\", \"ngModel\"], [\"value\", \"customer_id\"], [\"value\", \"customer_name\", \"selected\", \"\"], [\"value\", \"[business_partner][addresses][street_name]\"], [\"value\", \"[business_partner][phone]\"], [1, \"w-auto\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Keyword\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"pButton\", \"\", \"label\", \"Clear\", \"icon\", \"pi pi-filter-slash\", 1, \"p-button-outlined\", 3, \"click\"], [\"pSortableColumn\", \"customer_id\", 2, \"min-width\", \"12rem\"], [\"pSortableColumn\", \"customer_name\", 2, \"min-width\", \"12rem\"], [2, \"min-width\", \"10rem\"], [\"pSortableColumn\", \"business_partner.phone\", 2, \"min-width\", \"12rem\"], [1, \"cursor-pointer\", 3, \"pSelectableRow\"], [\"colspan\", \"4\"]],\n      template: function SelectCustomerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"p-table\", 4, 0);\n          i0.ɵɵlistener(\"onLazyLoad\", function SelectCustomerComponent_Template_p_table_onLazyLoad_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadCustomers($event));\n          });\n          i0.ɵɵtwoWayListener(\"selectionChange\", function SelectCustomerComponent_Template_p_table_selectionChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCustomerId, $event) || (ctx.selectedCustomerId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(4, SelectCustomerComponent_ng_template_4_Template, 16, 2, \"ng-template\", 5)(5, SelectCustomerComponent_ng_template_5_Template, 9, 0, \"ng-template\", 6)(6, SelectCustomerComponent_ng_template_6_Template, 9, 5, \"ng-template\", 7)(7, SelectCustomerComponent_ng_template_7_Template, 3, 0, \"ng-template\", 8)(8, SelectCustomerComponent_ng_template_8_Template, 3, 0, \"ng-template\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 10)(10, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function SelectCustomerComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵtext(11, \"Clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function SelectCustomerComponent_Template_button_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.customers)(\"rows\", 10)(\"loading\", ctx.loading)(\"rowHover\", true)(\"paginator\", true)(\"globalFilterFields\", i0.ɵɵpureFunction0(11, _c1))(\"totalRecords\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"selection\", ctx.selectedCustomerId);\n          i0.ɵɵproperty(\"lazy\", true);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"disabled\", ctx.saving);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.saving ? \"Selecting\" : \"Select\");\n        }\n      },\n      dependencies: [i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.InputText, i7.PrimeTemplate, i8.ButtonDirective, i9.Table, i9.SortableColumn, i9.SelectableRow, i10.InputGroupAddon, i11.InputGroup],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["switchMap", "i0", "ɵɵelementStart", "ɵɵlistener", "SelectCustomerComponent_ng_template_4_Template_select_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onSearchByChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtwoWayListener", "SelectCustomerComponent_ng_template_4_Template_input_ngModelChange_13_listener", "ɵɵtwoWayBindingSet", "globalSearchTerm", "SelectCustomerComponent_ng_template_4_Template_input_input_13_listener", "dt1_r4", "ɵɵreference", "onGlobalFilter", "SelectCustomerComponent_ng_template_4_Template_button_click_15_listener", "clear", "ɵɵadvance", "ɵɵproperty", "searchBy", "ɵɵtwoWayProperty", "customer_r5", "ɵɵtextInterpolate1", "customer_id", "customer_name", "cpf_address", "business_partner", "phone", "SelectCustomerComponent", "constructor", "ref", "_snackBar", "manageUserService", "auth", "selectedCustomerId", "customers", "totalRecords", "loading", "saving", "ngOnInit", "loadCustomers", "event", "page", "first", "rows", "pageSize", "getLoggedInUserCustomersSearch", "perPage", "search", "sortBy", "sortField", "sortOrder", "pageNo", "subscribe", "next", "data", "total", "selected", "userDetail", "customer", "documentId", "selectedCustomer", "find", "c", "error", "console", "table", "filter", "nativeElement", "value", "option", "onSubmit", "_this", "open", "type", "updateSelectedCustomer", "pipe", "res", "user", "getCartDetails", "_ref", "_asyncToGenerator", "cartres", "cart", "updateAuth", "close", "location", "reload", "_x", "apply", "arguments", "onReset", "ɵɵdirectiveInject", "i1", "DynamicDialogRef", "i2", "AppToastService", "i3", "ManageUserService", "i4", "AuthService", "selectors", "viewQuery", "SelectCustomerComponent_Query", "rf", "ctx", "SelectCustomerComponent_Template_p_table_onLazyLoad_2_listener", "_r1", "SelectCustomerComponent_Template_p_table_selectionChange_2_listener", "ɵɵtemplate", "SelectCustomerComponent_ng_template_4_Template", "SelectCustomerComponent_ng_template_5_Template", "SelectCustomerComponent_ng_template_6_Template", "SelectCustomerComponent_ng_template_7_Template", "SelectCustomerComponent_ng_template_8_Template", "SelectCustomerComponent_Template_button_click_10_listener", "SelectCustomerComponent_Template_button_click_12_listener", "ɵɵpureFunction0", "_c1", "ɵɵtextInterpolate"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\select-customer\\select-customer.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\layout\\select-customer\\select-customer.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from \"@angular/core\";\r\nimport { DynamicDialogRef } from \"primeng/dynamicdialog\";\r\nimport { Table } from \"primeng/table\";\r\nimport { switchMap } from \"rxjs\";\r\nimport { AppToastService } from \"src/app/shared/services/toast.service\";\r\nimport { ManageUserService } from \"../service/manage-user.service\";\r\nimport { AuthService } from \"src/app/core/authentication/auth.service\";\r\n\r\n@Component({\r\n  selector: \"app-select-customer\",\r\n  templateUrl: \"./select-customer.component.html\",\r\n  styleUrls: [\"./select-customer.component.scss\"],\r\n})\r\nexport class SelectCustomerComponent {\r\n  public selectedCustomerId: any = null;\r\n  public customers: any[] = [];\r\n  public searchBy: string = \"customer_name\";\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n  public globalSearchTerm: string = '';\r\n  public saving = false;\r\n  @ViewChild('filter') filter!: ElementRef;\r\n\r\n  constructor(\r\n    public ref: DynamicDialogRef,\r\n    private _snackBar: AppToastService,\r\n    private manageUserService: ManageUserService,\r\n    private auth: AuthService,\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n\r\n    // this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  loadCustomers(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    this.manageUserService\r\n      .getLoggedInUserCustomersSearch({\r\n        perPage: pageSize,\r\n        search: this.globalSearchTerm,\r\n        searchBy: this.searchBy,\r\n        sortBy: event.sortField ? `${event.sortField}:${event.sortOrder == 1 ? \"asc\" : 'desc'}` : '',\r\n        pageNo: page,\r\n      }).subscribe({\r\n        next: ({ data, total }: any) => {\r\n          this.customers = data || [];\r\n          const selected = this.auth.userDetail.customer?.documentId;\r\n          if (selected) {\r\n            const selectedCustomer = this.customers.find(c => c.documentId === selected);\r\n            selectedCustomer && (this.selectedCustomerId = selectedCustomer);\r\n          }\r\n          this.totalRecords = total;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching customers', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  clear(table: Table) {\r\n    this.globalSearchTerm = '';\r\n    this.filter.nativeElement.value = '';\r\n    this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  onSearchByChange(option: string) {\r\n    this.searchBy = option;\r\n    this.loadCustomers({ first: 0, rows: 10 });\r\n  }\r\n\r\n  onSubmit() {\r\n    if (!this.selectedCustomerId) {\r\n      this._snackBar.open(\"Please select customer.\", {\r\n        type: \"Warning\",\r\n      });\r\n      return;\r\n    }\r\n    this.saving = true;\r\n    this.manageUserService\r\n      .updateSelectedCustomer(this.selectedCustomerId, null)\r\n      .pipe(\r\n        switchMap((res) => {\r\n          const user = this.auth.userDetail;\r\n          return this.auth.getCartDetails(user.documentId);\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: async (cartres: any) => {\r\n          this.saving = false;\r\n          if (cartres?.cart) {\r\n            const cart = cartres.cart || null;\r\n            this.auth.updateAuth({\r\n              cart,\r\n              customer: cart.customer\r\n            });\r\n          }\r\n          this.ref.close();\r\n          location.reload();\r\n        },\r\n        error: () => {\r\n          this.saving = false;\r\n          this._snackBar.open(\"Error while processing your request.\", {\r\n            type: \"Error\",\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  onReset() {\r\n    this.selectedCustomerId = null;\r\n  }\r\n}\r\n", "<div class=\"switch-acc-popup-body d-flex flex-column justify-content-between\">\r\n    <div class=\"switch-acc-popup-cnt\">\r\n        <p-table #dt1 [value]=\"customers\" dataKey=\"id\" [rows]=\"10\" (onLazyLoad)=\"loadCustomers($event)\"\r\n            [loading]=\"loading\" [rowHover]=\"true\" styleClass=\"p-datatable-gridlines\" [paginator]=\"true\" dataKey=\"documentId\"\r\n            [globalFilterFields]=\"[\r\n          'customer_id',\r\n          'customer_name',\r\n          'cpf_address',\r\n        ]\" [totalRecords]=\"totalRecords\" selectionMode=\"single\" [(selection)]=\"selectedCustomerId\" [lazy]=\"true\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"caption\">\r\n                <div class=\"flex gap-3 flex-row justify-content-end\">\r\n                    <select [ngModel]=\"searchBy\" (ngModelChange)=\"onSearchByChange($event)\">\r\n                        <option value=\"customer_id\">Customer ID</option>\r\n                        <option value=\"customer_name\" selected>Customer Name</option>\r\n                        <option value=\"[business_partner][addresses][street_name]\">Address</option>\r\n                        <option value=\"[business_partner][phone]\">Phone</option>\r\n                    </select>\r\n                    <p-inputGroup class=\"w-auto\">\r\n                        <p-inputGroupAddon>\r\n                            <i class=\"pi pi-search\"></i>\r\n                        </p-inputGroupAddon>\r\n                        <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                            (input)=\"onGlobalFilter(dt1, $event)\" placeholder=\"Search Keyword\" />\r\n                    </p-inputGroup>\r\n                    <button pButton label=\"Clear\" class=\"p-button-outlined\" icon=\"pi pi-filter-slash\"\r\n                        (click)=\"clear(dt1)\"></button>\r\n                </div>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"min-width: 12rem\" pSortableColumn=\"customer_id\">\r\n                        ID\r\n                    </th>\r\n                    <th style=\"min-width: 12rem\" pSortableColumn=\"customer_name\">\r\n                        Name\r\n                    </th>\r\n                    <th style=\"min-width: 10rem\">\r\n                        Address\r\n                    </th>\r\n                    <th style=\"min-width: 12rem\" pSortableColumn=\"business_partner.phone\">\r\n                        Phone\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-customer>\r\n                <tr class=\"cursor-pointer\" [pSelectableRow]=\"customer\">\r\n                    <td>\r\n                        {{ customer.customer_id }}\r\n                    </td>\r\n                    <td>\r\n                        {{ customer.customer_name }}\r\n                    </td>\r\n                    <td>\r\n                        {{ customer.cpf_address }}\r\n                    </td>\r\n                    <td>\r\n                        {{ customer.business_partner?.phone }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"4\">No customers found.</td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"4\">Loading customers data. Please wait.</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n    <div class=\"flex align-items-center justify-content-center gap-3\">\r\n        <button type=\"button\" pButton (click)=\"onReset()\" class=\"p-button-outlined\">Clear</button>\r\n        <button type=\"button\" pButton (click)=\"onSubmit()\" [disabled]=\"saving\">{{ saving ? 'Selecting' :\r\n            'Select' }}</button>\r\n    </div>\r\n\r\n</div>"], "mappings": ";AAGA,SAASA,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;ICSZC,EADJ,CAAAC,cAAA,cAAqD,iBACuB;IAA3CD,EAAA,CAAAE,UAAA,2BAAAC,+EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAiBF,MAAA,CAAAG,gBAAA,CAAAN,MAAA,CAAwB;IAAA,EAAC;IACnEJ,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAW,MAAA,kBAAW;IAAAX,EAAA,CAAAY,YAAA,EAAS;IAChDZ,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAW,MAAA,oBAAa;IAAAX,EAAA,CAAAY,YAAA,EAAS;IAC7DZ,EAAA,CAAAC,cAAA,iBAA2D;IAAAD,EAAA,CAAAW,MAAA,cAAO;IAAAX,EAAA,CAAAY,YAAA,EAAS;IAC3EZ,EAAA,CAAAC,cAAA,iBAA0C;IAAAD,EAAA,CAAAW,MAAA,YAAK;IACnDX,EADmD,CAAAY,YAAA,EAAS,EACnD;IAELZ,EADJ,CAAAC,cAAA,wBAA6B,yBACN;IACfD,EAAA,CAAAa,SAAA,aAA4B;IAChCb,EAAA,CAAAY,YAAA,EAAoB;IACpBZ,EAAA,CAAAC,cAAA,oBACyE;IADnCD,EAAA,CAAAc,gBAAA,2BAAAC,+EAAAX,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAgB,kBAAA,CAAAT,MAAA,CAAAU,gBAAA,EAAAb,MAAA,MAAAG,MAAA,CAAAU,gBAAA,GAAAb,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAA8B;IAChEJ,EAAA,CAAAE,UAAA,mBAAAgB,uEAAAd,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAW,MAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAc,cAAA,CAAAF,MAAA,EAAAf,MAAA,CAA2B;IAAA,EAAC;IAC7CJ,EAFI,CAAAY,YAAA,EACyE,EAC9D;IACfZ,EAAA,CAAAC,cAAA,kBACyB;IAArBD,EAAA,CAAAE,UAAA,mBAAAoB,wEAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAW,MAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAgB,KAAA,CAAAJ,MAAA,CAAU;IAAA,EAAC;IAC5BnB,EAD6B,CAAAY,YAAA,EAAS,EAChC;;;;IAfMZ,EAAA,CAAAwB,SAAA,EAAoB;IAApBxB,EAAA,CAAAyB,UAAA,YAAAlB,MAAA,CAAAmB,QAAA,CAAoB;IAUc1B,EAAA,CAAAwB,SAAA,IAA8B;IAA9BxB,EAAA,CAAA2B,gBAAA,YAAApB,MAAA,CAAAU,gBAAA,CAA8B;;;;;IASxEjB,EADJ,CAAAC,cAAA,SAAI,aAC2D;IACvDD,EAAA,CAAAW,MAAA,WACJ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAA6D;IACzDD,EAAA,CAAAW,MAAA,aACJ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAA6B;IACzBD,EAAA,CAAAW,MAAA,gBACJ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAAsE;IAClED,EAAA,CAAAW,MAAA,cACJ;IACJX,EADI,CAAAY,YAAA,EAAK,EACJ;;;;;IAIDZ,EADJ,CAAAC,cAAA,aAAuD,SAC/C;IACAD,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,MAAA,GACJ;IACJX,EADI,CAAAY,YAAA,EAAK,EACJ;;;;IAbsBZ,EAAA,CAAAyB,UAAA,mBAAAG,WAAA,CAA2B;IAE9C5B,EAAA,CAAAwB,SAAA,GACJ;IADIxB,EAAA,CAAA6B,kBAAA,MAAAD,WAAA,CAAAE,WAAA,MACJ;IAEI9B,EAAA,CAAAwB,SAAA,GACJ;IADIxB,EAAA,CAAA6B,kBAAA,MAAAD,WAAA,CAAAG,aAAA,MACJ;IAEI/B,EAAA,CAAAwB,SAAA,GACJ;IADIxB,EAAA,CAAA6B,kBAAA,MAAAD,WAAA,CAAAI,WAAA,MACJ;IAEIhC,EAAA,CAAAwB,SAAA,GACJ;IADIxB,EAAA,CAAA6B,kBAAA,MAAAD,WAAA,CAAAK,gBAAA,kBAAAL,WAAA,CAAAK,gBAAA,CAAAC,KAAA,MACJ;;;;;IAKAlC,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAW,MAAA,0BAAmB;IACvCX,EADuC,CAAAY,YAAA,EAAK,EACvC;;;;;IAIDZ,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAAW,MAAA,2CAAoC;IACxDX,EADwD,CAAAY,YAAA,EAAK,EACxD;;;ADxDrB,OAAM,MAAOuB,uBAAuB;EAUlCC,YACSC,GAAqB,EACpBC,SAA0B,EAC1BC,iBAAoC,EACpCC,IAAiB;IAHlB,KAAAH,GAAG,GAAHA,GAAG;IACF,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,IAAI,GAAJA,IAAI;IAbP,KAAAC,kBAAkB,GAAQ,IAAI;IAC9B,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAhB,QAAQ,GAAW,eAAe;IAClC,KAAAiB,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAA3B,gBAAgB,GAAW,EAAE;IAC7B,KAAA4B,MAAM,GAAG,KAAK;EAQjB;EAEJC,QAAQA,CAAA;IAEN;EAAA;EAGFC,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,MAAMK,IAAI,GAAGD,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,IAAI,GAAG,CAAC;IACzC,MAAMC,QAAQ,GAAGJ,KAAK,CAACG,IAAI;IAC3B,IAAI,CAACZ,iBAAiB,CACnBc,8BAA8B,CAAC;MAC9BC,OAAO,EAAEF,QAAQ;MACjBG,MAAM,EAAE,IAAI,CAACtC,gBAAgB;MAC7BS,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB8B,MAAM,EAAER,KAAK,CAACS,SAAS,GAAG,GAAGT,KAAK,CAACS,SAAS,IAAIT,KAAK,CAACU,SAAS,IAAI,CAAC,GAAG,KAAK,GAAG,MAAM,EAAE,GAAG,EAAE;MAC5FC,MAAM,EAAEV;KACT,CAAC,CAACW,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAK,CAAO,KAAI;QAC7B,IAAI,CAACrB,SAAS,GAAGoB,IAAI,IAAI,EAAE;QAC3B,MAAME,QAAQ,GAAG,IAAI,CAACxB,IAAI,CAACyB,UAAU,CAACC,QAAQ,EAAEC,UAAU;QAC1D,IAAIH,QAAQ,EAAE;UACZ,MAAMI,gBAAgB,GAAG,IAAI,CAAC1B,SAAS,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,UAAU,KAAKH,QAAQ,CAAC;UAC5EI,gBAAgB,KAAK,IAAI,CAAC3B,kBAAkB,GAAG2B,gBAAgB,CAAC;QAClE;QACA,IAAI,CAACzB,YAAY,GAAGoB,KAAK;QACzB,IAAI,CAACnB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2B,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACN;EAEAvB,cAAcA,CAACoD,KAAY,EAAEzB,KAAY;IACvC,IAAI,CAACD,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEA5B,KAAKA,CAACkD,KAAY;IAChB,IAAI,CAACxD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACyD,MAAM,CAACC,aAAa,CAACC,KAAK,GAAG,EAAE;IACpC,IAAI,CAAC7B,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEAzC,gBAAgBA,CAACmE,MAAc;IAC7B,IAAI,CAACnD,QAAQ,GAAGmD,MAAM;IACtB,IAAI,CAAC9B,aAAa,CAAC;MAAEG,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EAC5C;EAEA2B,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACN,IAAI,CAAC,IAAI,CAACtC,kBAAkB,EAAE;MAC5B,IAAI,CAACH,SAAS,CAAC0C,IAAI,CAAC,yBAAyB,EAAE;QAC7CC,IAAI,EAAE;OACP,CAAC;MACF;IACF;IACA,IAAI,CAACpC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACN,iBAAiB,CACnB2C,sBAAsB,CAAC,IAAI,CAACzC,kBAAkB,EAAE,IAAI,CAAC,CACrD0C,IAAI,CACHpF,SAAS,CAAEqF,GAAG,IAAI;MAChB,MAAMC,IAAI,GAAG,IAAI,CAAC7C,IAAI,CAACyB,UAAU;MACjC,OAAO,IAAI,CAACzB,IAAI,CAAC8C,cAAc,CAACD,IAAI,CAAClB,UAAU,CAAC;IAClD,CAAC,CAAC,CACH,CACAP,SAAS,CAAC;MACTC,IAAI;QAAA,IAAA0B,IAAA,GAAAC,iBAAA,CAAE,WAAOC,OAAY,EAAI;UAC3BV,KAAI,CAAClC,MAAM,GAAG,KAAK;UACnB,IAAI4C,OAAO,EAAEC,IAAI,EAAE;YACjB,MAAMA,IAAI,GAAGD,OAAO,CAACC,IAAI,IAAI,IAAI;YACjCX,KAAI,CAACvC,IAAI,CAACmD,UAAU,CAAC;cACnBD,IAAI;cACJxB,QAAQ,EAAEwB,IAAI,CAACxB;aAChB,CAAC;UACJ;UACAa,KAAI,CAAC1C,GAAG,CAACuD,KAAK,EAAE;UAChBC,QAAQ,CAACC,MAAM,EAAE;QACnB,CAAC;QAAA,gBAXDjC,IAAIA,CAAAkC,EAAA;UAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;QAAA;MAAA,GAWH;MACD1B,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC1B,MAAM,GAAG,KAAK;QACnB,IAAI,CAACP,SAAS,CAAC0C,IAAI,CAAC,sCAAsC,EAAE;UAC1DC,IAAI,EAAE;SACP,CAAC;MACJ;KACD,CAAC;EACN;EAEAiB,OAAOA,CAAA;IACL,IAAI,CAACzD,kBAAkB,GAAG,IAAI;EAChC;;;uBA1GWN,uBAAuB,EAAAnC,EAAA,CAAAmG,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAArG,EAAA,CAAAmG,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvG,EAAA,CAAAmG,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAzG,EAAA,CAAAmG,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBxE,uBAAuB;MAAAyE,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCX5B/G,EAFR,CAAAC,cAAA,aAA8E,aACxC,oBAQA;UAP6BD,EAAA,CAAAE,UAAA,wBAAA+G,+DAAA7G,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAA6G,GAAA;YAAA,OAAAlH,EAAA,CAAAS,WAAA,CAAcuG,GAAA,CAAAjE,aAAA,CAAA3C,MAAA,CAAqB;UAAA,EAAC;UAMvCJ,EAAA,CAAAc,gBAAA,6BAAAqG,oEAAA/G,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAA6G,GAAA;YAAAlH,EAAA,CAAAgB,kBAAA,CAAAgG,GAAA,CAAAvE,kBAAA,EAAArC,MAAA,MAAA4G,GAAA,CAAAvE,kBAAA,GAAArC,MAAA;YAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;UAAA,EAAkC;UA0DtFJ,EAxDA,CAAAoH,UAAA,IAAAC,8CAAA,0BAAiC,IAAAC,8CAAA,yBAmBD,IAAAC,8CAAA,yBAgBW,IAAAC,8CAAA,yBAgBL,IAAAC,8CAAA,yBAKD;UAM7CzH,EADI,CAAAY,YAAA,EAAU,EACR;UAEFZ,EADJ,CAAAC,cAAA,cAAkE,kBACc;UAA9CD,EAAA,CAAAE,UAAA,mBAAAwH,0DAAA;YAAA1H,EAAA,CAAAK,aAAA,CAAA6G,GAAA;YAAA,OAAAlH,EAAA,CAAAS,WAAA,CAASuG,GAAA,CAAAd,OAAA,EAAS;UAAA,EAAC;UAA2BlG,EAAA,CAAAW,MAAA,aAAK;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAC1FZ,EAAA,CAAAC,cAAA,kBAAuE;UAAzCD,EAAA,CAAAE,UAAA,mBAAAyH,0DAAA;YAAA3H,EAAA,CAAAK,aAAA,CAAA6G,GAAA;YAAA,OAAAlH,EAAA,CAAAS,WAAA,CAASuG,GAAA,CAAAlC,QAAA,EAAU;UAAA,EAAC;UAAqB9E,EAAA,CAAAW,MAAA,IACxD;UAGvBX,EAHuB,CAAAY,YAAA,EAAS,EACtB,EAEJ;;;UA7EgBZ,EAAA,CAAAwB,SAAA,GAAmB;UAM9BxB,EANW,CAAAyB,UAAA,UAAAuF,GAAA,CAAAtE,SAAA,CAAmB,YAAyB,YAAAsE,GAAA,CAAApE,OAAA,CACnC,kBAAkB,mBAAsD,uBAAA5C,EAAA,CAAA4H,eAAA,KAAAC,GAAA,EAK7F,iBAAAb,GAAA,CAAArE,YAAA,CAA8B;UAAwB3C,EAAA,CAAA2B,gBAAA,cAAAqF,GAAA,CAAAvE,kBAAA,CAAkC;UAACzC,EAAA,CAAAyB,UAAA,cAAa;UAmErDzB,EAAA,CAAAwB,SAAA,IAAmB;UAAnBxB,EAAA,CAAAyB,UAAA,aAAAuF,GAAA,CAAAnE,MAAA,CAAmB;UAAC7C,EAAA,CAAAwB,SAAA,EACxD;UADwDxB,EAAA,CAAA8H,iBAAA,CAAAd,GAAA,CAAAnE,MAAA,0BACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}