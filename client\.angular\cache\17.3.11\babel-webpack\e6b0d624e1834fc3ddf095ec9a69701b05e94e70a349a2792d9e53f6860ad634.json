{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PartnerService {\n  constructor(http) {\n    this.http = http;\n    this.partnerSubject = new BehaviorSubject(null);\n    this.partner = this.partnerSubject.asObservable();\n  }\n  getPartners(page, pageSize, sortField, sortOrder, searchTerm, routeURL) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (routeURL?.includes('contacts')) {\n      params = params.set('filters[roles][bp_role][$contains]`', 'BUP001');\n    } else if (routeURL?.includes('employees')) {\n      params = params.set('filters[roles][bp_role][$contains]`', 'BUP003');\n    } else if (routeURL?.includes('prospects')) {\n      params = params.set('filters[roles][bp_role][$contains]`', 'PRO001');\n    } else {\n      params = params.set('filters[$or][0][roles][bp_role][$contains]', 'FLCU01').set('filters[$or][1][roles][bp_role][$contains]', 'FLCU00');\n    }\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  getPartnerByID(id) {\n    const params = new HttpParams().set('filters[bp_id][$eq]', id).set('populate[credit_worthiness][populate]', '*').set('populate[intl_loc_number][populate]', '*').set('populate[roles][populate]', '*').set('populate[addresses][populate]', '*').set('populate[bp_intl_address_versions][populate]', '*').set('populate[address_usages][populate]', '*').set('populate[banks][populate]', '*').set('populate[payment_cards][populate]', '*').set('populate[supplier][populate]', '*');\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  getPartnerByIDName(partnerdata) {\n    let params = new HttpParams();\n    if (partnerdata) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', partnerdata).set('filters[$or][1][bp_full_name][$containsi]', partnerdata);\n    }\n    return this.http.get(`${CMS_APIContstant.PARTNERS}`, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function PartnerService_Factory(t) {\n      return new (t || PartnerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PartnerService,\n      factory: PartnerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "BehaviorSubject", "CMS_APIContstant", "PartnerService", "constructor", "http", "partnerSubject", "partner", "asObservable", "getPartners", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "routeURL", "params", "set", "toString", "includes", "undefined", "order", "get", "PARTNERS", "getPartnerByID", "id", "getPartnerByIDName", "partnerdata", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PartnerService {\r\n  public partnerSubject = new BehaviorSubject<any>(null);\r\n  public partner = this.partnerSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getPartners(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string,\r\n    routeURL?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n\r\n    if (routeURL?.includes('contacts')) {\r\n      params = params.set('filters[roles][bp_role][$contains]`', 'BUP001');\r\n    } else if (routeURL?.includes('employees')) {\r\n      params = params.set('filters[roles][bp_role][$contains]`', 'BUP003');\r\n    } else if (routeURL?.includes('prospects')) {\r\n      params = params.set('filters[roles][bp_role][$contains]`', 'PRO001');\r\n    } else {\r\n      params = params\r\n        .set('filters[$or][0][roles][bp_role][$contains]', 'FLCU01')\r\n        .set('filters[$or][1][roles][bp_role][$contains]', 'FLCU00');\r\n    }\r\n\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  getPartnerByID(id: string) {\r\n    const params = new HttpParams()\r\n      .set('filters[bp_id][$eq]', id)\r\n      .set('populate[credit_worthiness][populate]', '*')\r\n      .set('populate[intl_loc_number][populate]', '*')\r\n      .set('populate[roles][populate]', '*')\r\n      .set('populate[addresses][populate]', '*')\r\n      .set('populate[bp_intl_address_versions][populate]', '*')\r\n      .set('populate[address_usages][populate]', '*')\r\n      .set('populate[banks][populate]', '*')\r\n      .set('populate[payment_cards][populate]', '*')\r\n      .set('populate[supplier][populate]', '*');\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, { params });\r\n  }\r\n\r\n  getPartnerByIDName(partnerdata: string): Observable<any[]> {\r\n    let params = new HttpParams();\r\n    if (partnerdata) {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', partnerdata)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', partnerdata);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${CMS_APIContstant.PARTNERS}`, {\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,cAAc;EAIzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,cAAc,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IAC/C,KAAAM,OAAO,GAAG,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;EAEZ;EAEvCC,WAAWA,CACTC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB,EACnBC,QAAiB;IAEjB,IAAIC,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC1BiB,GAAG,CAAC,kBAAkB,EAAEP,IAAI,CAACQ,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEN,QAAQ,CAACO,QAAQ,EAAE,CAAC;IAEnD,IAAIH,QAAQ,EAAEI,QAAQ,CAAC,UAAU,CAAC,EAAE;MAClCH,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,qCAAqC,EAAE,QAAQ,CAAC;IACtE,CAAC,MAAM,IAAIF,QAAQ,EAAEI,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC1CH,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,qCAAqC,EAAE,QAAQ,CAAC;IACtE,CAAC,MAAM,IAAIF,QAAQ,EAAEI,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC1CH,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,qCAAqC,EAAE,QAAQ,CAAC;IACtE,CAAC,MAAM;MACLD,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAC3DA,GAAG,CAAC,4CAA4C,EAAE,QAAQ,CAAC;IAChE;IAEA,IAAIL,SAAS,IAAIC,SAAS,KAAKO,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGR,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDG,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGL,SAAS,IAAIS,KAAK,EAAE,CAAC;IACtD;IACA,IAAIP,UAAU,EAAE;MACdE,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEH,UAAU,CAAC,CACrDG,GAAG,CAAC,2CAA2C,EAAEH,UAAU,CAAC;IACjE;IACA,OAAO,IAAI,CAACT,IAAI,CAACiB,GAAG,CAAQ,GAAGpB,gBAAgB,CAACqB,QAAQ,EAAE,EAAE;MAC1DP;KACD,CAAC;EACJ;EAEAQ,cAAcA,CAACC,EAAU;IACvB,MAAMT,MAAM,GAAG,IAAIhB,UAAU,EAAE,CAC5BiB,GAAG,CAAC,qBAAqB,EAAEQ,EAAE,CAAC,CAC9BR,GAAG,CAAC,uCAAuC,EAAE,GAAG,CAAC,CACjDA,GAAG,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAC/CA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CAAC,+BAA+B,EAAE,GAAG,CAAC,CACzCA,GAAG,CAAC,8CAA8C,EAAE,GAAG,CAAC,CACxDA,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAC9CA,GAAG,CAAC,2BAA2B,EAAE,GAAG,CAAC,CACrCA,GAAG,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAC7CA,GAAG,CAAC,8BAA8B,EAAE,GAAG,CAAC;IAE3C,OAAO,IAAI,CAACZ,IAAI,CAACiB,GAAG,CAAQ,GAAGpB,gBAAgB,CAACqB,QAAQ,EAAE,EAAE;MAAEP;IAAM,CAAE,CAAC;EACzE;EAEAU,kBAAkBA,CAACC,WAAmB;IACpC,IAAIX,MAAM,GAAG,IAAIhB,UAAU,EAAE;IAC7B,IAAI2B,WAAW,EAAE;MACfX,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEU,WAAW,CAAC,CACtDV,GAAG,CAAC,2CAA2C,EAAEU,WAAW,CAAC;IAClE;IAEA,OAAO,IAAI,CAACtB,IAAI,CAACiB,GAAG,CAAQ,GAAGpB,gBAAgB,CAACqB,QAAQ,EAAE,EAAE;MAC1DP;KACD,CAAC;EACJ;;;uBAvEWb,cAAc,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAd5B,cAAc;MAAA6B,OAAA,EAAd7B,cAAc,CAAA8B,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}