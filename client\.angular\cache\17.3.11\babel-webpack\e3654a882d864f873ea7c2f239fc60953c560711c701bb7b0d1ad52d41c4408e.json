{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../partner.service\";\nimport * as i2 from \"./supplier-company-text.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/ripple\";\nfunction SupplierCompanyTextComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SupplierCompanyTextComponent_ng_template_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.expandAll());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 15);\n    i0.ɵɵelement(5, \"i\", 16);\n    i0.ɵɵelementStart(6, \"input\", 17, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SupplierCompanyTextComponent_ng_template_4_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.globalSearchTerm, $event) || (ctx_r1.globalSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function SupplierCompanyTextComponent_ng_template_4_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const dt1_r3 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(ctx_r1.onGlobalFilter(dt1_r3, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi pi-fw \", ctx_r1.isExpanded ? \"pi-minus\" : \"pi-plus\", \"\");\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.isExpanded ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.globalSearchTerm);\n  }\n}\nfunction SupplierCompanyTextComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\", 18);\n    i0.ɵɵelementStart(2, \"th\", 19);\n    i0.ɵɵtext(3, \" Text ID \");\n    i0.ɵɵelement(4, \"p-sortIcon\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 21);\n    i0.ɵɵtext(6, \" Company Code \");\n    i0.ɵɵelement(7, \"p-sortIcon\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 23);\n    i0.ɵɵtext(9, \" Supplier ID \");\n    i0.ɵɵelement(10, \"p-sortIcon\", 24);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SupplierCompanyTextComponent_ng_template_6_tr_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    const company_text_r5 = ctx_r3.$implicit;\n    const expanded_r6 = ctx_r3.expanded;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pRowToggler\", company_text_r5)(\"icon\", expanded_r6 ? \"pi pi-chevron-down\" : \"pi pi-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r5 == null ? null : company_text_r5.long_text_id) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r5 == null ? null : company_text_r5.company_code) || \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r5 == null ? null : company_text_r5.supplier_id) || \"-\", \" \");\n  }\n}\nfunction SupplierCompanyTextComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SupplierCompanyTextComponent_ng_template_6_tr_0_Template, 9, 5, \"tr\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.supplier_company_text == null ? null : ctx_r1.supplier_company_text.length) > 0);\n  }\n}\nfunction SupplierCompanyTextComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"td\", 18);\n    i0.ɵɵelementStart(2, \"td\", 27)(3, \"div\", 28)(4, \"div\", 29)(5, \"span\", 30);\n    i0.ɵɵtext(6, \"Long Text ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 29)(10, \"span\", 30);\n    i0.ɵɵtext(11, \"Long Text\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 31);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"span\", 30);\n    i0.ɵɵtext(16, \"Company Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 31);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 29)(20, \"span\", 30);\n    i0.ɵɵtext(21, \"Supplier ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 31);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const company_text_r7 = ctx.$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r7 == null ? null : company_text_r7.long_text_id) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r7 == null ? null : company_text_r7.long_text) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r7 == null ? null : company_text_r7.company_code) || \"-\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (company_text_r7 == null ? null : company_text_r7.supplier_id) || \"-\", \" \");\n  }\n}\nfunction SupplierCompanyTextComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32);\n    i0.ɵɵtext(2, \" Supplier Company Text details are not available for this record. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SupplierCompanyTextComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 33);\n    i0.ɵɵtext(2, \"Loading company text data. Please wait...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SupplierCompanyTextComponent {\n  constructor(partnerservice, suppliercompanytextservice) {\n    this.partnerservice = partnerservice;\n    this.suppliercompanytextservice = suppliercompanytextservice;\n    this.unsubscribe$ = new Subject();\n    this.supplier_company_text = null;\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.globalSearchTerm = '';\n    this.totalRecords = 0;\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.partnerservice.partner.pipe(takeUntil(this.unsubscribe$)).subscribe(data => {\n      this.supplier_company_text = data?.supplier?.company_texts;\n    });\n  }\n  expandAll() {\n    if (!this.isExpanded) {\n      this.supplier_company_text.forEach(company => company?.id ? this.expandedRows[company.id] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n    this.isExpanded = !this.isExpanded;\n  }\n  loadSupplierCompanyText(event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.loading = true;\n      const page = event.first / event.rows + 1;\n      const pageSize = event.rows;\n      const sortField = event.sortField;\n      const sortOrder = event.sortOrder;\n      _this.suppliercompanytextservice.getSupplierCompanyText(page, pageSize, sortField, sortOrder, _this.globalSearchTerm).pipe(takeUntil(_this.unsubscribe$)).subscribe({\n        next: response => {\n          _this.supplier_company_text = response?.data || [];\n          _this.totalRecords = response?.meta?.pagination.total;\n          _this.loading = false;\n        },\n        error: error => {\n          console.error('Error fetching Supplier Company Text', error);\n          _this.loading = false;\n        }\n      });\n    })();\n  }\n  onGlobalFilter(table, event) {\n    this.loadSupplierCompanyText({\n      first: 0,\n      rows: 10\n    });\n  }\n  ngOnDestroy() {\n    this.unsubscribe$.next();\n    this.unsubscribe$.complete();\n  }\n  static {\n    this.ɵfac = function SupplierCompanyTextComponent_Factory(t) {\n      return new (t || SupplierCompanyTextComponent)(i0.ɵɵdirectiveInject(i1.PartnerService), i0.ɵɵdirectiveInject(i2.SupplierCompanyTextService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SupplierCompanyTextComponent,\n      selectors: [[\"app-supplier-company-text\"]],\n      decls: 10,\n      vars: 3,\n      consts: [[\"dt1\", \"\"], [\"filter\", \"\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"expandedRowKeys\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"rowexpansion\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"gap-2\", \"justify-content-between\"], [\"pButton\", \"\", 3, \"click\", \"icon\", \"label\"], [1, \"flex\", \"table-header\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Company Text\", 1, \"w-full\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"long_text_id\"], [\"field\", \"long_text_id\"], [\"pSortableColumn\", \"company_code\"], [\"field\", \"company_code\"], [\"pSortableColumn\", \"supplier_id\"], [\"field\", \"supplier_id\"], [4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-button-text\", \"p-button-rounded\", \"p-button-plain\", 3, \"pRowToggler\", \"icon\"], [\"colspan\", \"3\"], [1, \"grid\", \"mx-0\", \"border-1\"], [1, \"col-12\", \"lg:col-4\"], [1, \"text-900\", \"block\", \"font-medium\", \"mb-3\", \"font-bold\"], [1, \"block\", \"font-medium\", \"mb-3\", \"text-600\"], [\"colspan\", \"6\"], [\"colspan\", \"8\"]],\n      template: function SupplierCompanyTextComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"p-table\", 4, 0);\n          i0.ɵɵtemplate(4, SupplierCompanyTextComponent_ng_template_4_Template, 8, 4, \"ng-template\", 5)(5, SupplierCompanyTextComponent_ng_template_5_Template, 11, 0, \"ng-template\", 6)(6, SupplierCompanyTextComponent_ng_template_6_Template, 1, 1, \"ng-template\", 7)(7, SupplierCompanyTextComponent_ng_template_7_Template, 24, 4, \"ng-template\", 8)(8, SupplierCompanyTextComponent_ng_template_8_Template, 3, 0, \"ng-template\", 9)(9, SupplierCompanyTextComponent_ng_template_9_Template, 3, 0, \"ng-template\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.supplier_company_text)(\"rows\", 10)(\"expandedRowKeys\", ctx.expandedRows);\n        }\n      },\n      dependencies: [i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.Table, i6.PrimeTemplate, i5.SortableColumn, i5.RowToggler, i5.SortIcon, i7.ButtonDirective, i8.InputText, i9.Ripple],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "SupplierCompanyTextComponent_ng_template_4_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "expandAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵtwoWayListener", "SupplierCompanyTextComponent_ng_template_4_Template_input_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "globalSearchTerm", "SupplierCompanyTextComponent_ng_template_4_Template_input_input_6_listener", "dt1_r3", "ɵɵreference", "onGlobalFilter", "ɵɵadvance", "ɵɵpropertyInterpolate1", "isExpanded", "ɵɵpropertyInterpolate", "ɵɵtwoWayProperty", "ɵɵtext", "ɵɵproperty", "company_text_r5", "expanded_r6", "ɵɵtextInterpolate1", "long_text_id", "company_code", "supplier_id", "ɵɵtemplate", "SupplierCompanyTextComponent_ng_template_6_tr_0_Template", "supplier_company_text", "length", "company_text_r7", "long_text", "SupplierCompanyTextComponent", "constructor", "partnerservice", "suppliercompanytextservice", "unsubscribe$", "expandedRows", "totalRecords", "loading", "ngOnInit", "partner", "pipe", "subscribe", "data", "supplier", "company_texts", "for<PERSON>ach", "company", "id", "loadSupplierCompanyText", "event", "_this", "_asyncToGenerator", "page", "first", "rows", "pageSize", "sortField", "sortOrder", "getSupplierCompanyText", "next", "response", "meta", "pagination", "total", "error", "console", "table", "ngOnDestroy", "complete", "ɵɵdirectiveInject", "i1", "PartnerService", "i2", "SupplierCompanyTextService", "selectors", "decls", "vars", "consts", "template", "SupplierCompanyTextComponent_Template", "rf", "ctx", "SupplierCompanyTextComponent_ng_template_4_Template", "SupplierCompanyTextComponent_ng_template_5_Template", "SupplierCompanyTextComponent_ng_template_6_Template", "SupplierCompanyTextComponent_ng_template_7_Template", "SupplierCompanyTextComponent_ng_template_8_Template", "SupplierCompanyTextComponent_ng_template_9_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\supplier-company-text\\supplier-company-text.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\partner\\partner-details\\supplier-company-text\\supplier-company-text.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Table } from 'primeng/table';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { PartnerService } from '../../partner.service';\r\nimport { SupplierCompanyTextService } from './supplier-company-text.service';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-supplier-company-text',\r\n  templateUrl: './supplier-company-text.component.html',\r\n  styleUrl: './supplier-company-text.component.scss',\r\n})\r\nexport class SupplierCompanyTextComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public supplier_company_text: any = null;\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public globalSearchTerm: string = '';\r\n  public totalRecords: number = 0;\r\n  public loading: boolean = true;\r\n\r\n  constructor(\r\n    private partnerservice: PartnerService,\r\n    private suppliercompanytextservice: SupplierCompanyTextService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.partnerservice.partner\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((data: any) => {\r\n        this.supplier_company_text = data?.supplier?.company_texts;\r\n      });\r\n  }\r\n\r\n  expandAll() {\r\n    if (!this.isExpanded) {\r\n      this.supplier_company_text.forEach((company: any) =>\r\n        company?.id ? (this.expandedRows[company.id] = true) : ''\r\n      );\r\n    } else {\r\n      this.expandedRows = {};\r\n    }\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  async loadSupplierCompanyText(event: any) {\r\n    this.loading = true;\r\n    const page = event.first / event.rows + 1;\r\n    const pageSize = event.rows;\r\n    const sortField = event.sortField;\r\n    const sortOrder = event.sortOrder;\r\n\r\n    this.suppliercompanytextservice\r\n      .getSupplierCompanyText(\r\n        page,\r\n        pageSize,\r\n        sortField,\r\n        sortOrder,\r\n        this.globalSearchTerm\r\n      )\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.supplier_company_text = response?.data || [];\r\n          this.totalRecords = response?.meta?.pagination.total;\r\n          this.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching Supplier Company Text', error);\r\n          this.loading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onGlobalFilter(table: Table, event: Event) {\r\n    this.loadSupplierCompanyText({ first: 0, rows: 10 });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.unsubscribe$.next();\r\n    this.unsubscribe$.complete();\r\n  }\r\n}\r\n", "<div class=\"col-12\">\r\n    <div class=\"card\">\r\n        <p-table #dt1 [value]=\"supplier_company_text\" dataKey=\"id\" [rows]=\"10\" [expandedRowKeys]=\"expandedRows\"\r\n            responsiveLayout=\"scroll\">\r\n            <ng-template pTemplate=\"caption\">\r\n                <div class=\"flex justify-content-between flex-column sm:flex-row gap-2\">\r\n                    <div class=\"flex flex-row gap-2 justify-content-between\">\r\n                        <button pButton icon=\"pi pi-fw {{ isExpanded ? 'pi-minus' : 'pi-plus' }}\"\r\n                            label=\"{{ isExpanded ? 'Collapse All' : 'Expand All' }}\" (click)=\"expandAll()\"></button>\r\n                        <div class=\"flex table-header\"></div>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input pInputText type=\"text\" #filter [(ngModel)]=\"globalSearchTerm\"\r\n                            (input)=\"onGlobalFilter(dt1, $event)\" placeholder=\"Search Company Text\" class=\"w-full\" />\r\n                    </span>\r\n                </div>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"width: 3rem\"></th>\r\n                    <th pSortableColumn=\"long_text_id\">\r\n                        Text ID <p-sortIcon field=\"long_text_id\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"company_code\">\r\n                        Company Code <p-sortIcon field=\"company_code\"></p-sortIcon>\r\n                    </th>\r\n                    <th pSortableColumn=\"supplier_id\">\r\n                        Supplier ID <p-sortIcon field=\"supplier_id\"></p-sortIcon>\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-company_text let-expanded=\"expanded\">\r\n                <tr *ngIf=\"supplier_company_text?.length > 0\">\r\n                    <td>\r\n                        <button type=\"button\" pButton pRipple [pRowToggler]=\"company_text\"\r\n                            class=\"p-button-text p-button-rounded p-button-plain\"\r\n                            [icon]=\"expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'\"></button>\r\n                    </td>\r\n                    <td>\r\n                        {{ company_text?.long_text_id || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ company_text?.company_code || \"-\" }}\r\n                    </td>\r\n                    <td>\r\n                        {{ company_text?.supplier_id || \"-\" }}\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"rowexpansion\" let-company_text>\r\n                <tr>\r\n                    <td style=\"width: 3rem\"></td>\r\n                    <td colspan=\"3\">\r\n                        <div class=\"grid mx-0 border-1\">\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Long Text ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ company_text?.long_text_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Long Text</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ company_text?.long_text || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Company Code</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ company_text?.company_code || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"col-12 lg:col-4\">\r\n                                <span class=\"text-900 block font-medium mb-3 font-bold\">Supplier ID</span>\r\n                                <span class=\"block font-medium mb-3 text-600\">\r\n                                    {{ company_text?.supplier_id || \"-\" }}\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"emptymessage\">\r\n                <tr>\r\n                    <td colspan=\"6\">\r\n                        Supplier Company Text details are not available for this record.\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"loadingbody\">\r\n                <tr>\r\n                    <td colspan=\"8\">Loading company text data. Please wait...</td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n    </div>\r\n</div>"], "mappings": ";AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;ICKjBC,EAFR,CAAAC,cAAA,cAAwE,cACX,iBAE8B;IAAtBD,EAAA,CAAAE,UAAA,mBAAAC,4EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAS;IAC5FV,EAAA,CAAAW,SAAA,cAAqC;IACzCX,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAW,SAAA,YAA4B;IAC5BX,EAAA,CAAAC,cAAA,mBAC6F;IADvDD,EAAA,CAAAY,gBAAA,2BAAAC,mFAAAC,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAe,kBAAA,CAAAT,MAAA,CAAAU,gBAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,gBAAA,GAAAF,MAAA;MAAA,OAAAd,EAAA,CAAAQ,WAAA,CAAAM,MAAA;IAAA,EAA8B;IAChEd,EAAA,CAAAE,UAAA,mBAAAe,2EAAAH,MAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAW,MAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,cAAA,CAAAF,MAAA,EAAAJ,MAAA,CAA2B;IAAA,EAAC;IAEjDd,EAHQ,CAAAU,YAAA,EAC6F,EAC1F,EACL;;;;IATkBV,EAAA,CAAAqB,SAAA,GAAyD;IAAzDrB,EAAA,CAAAsB,sBAAA,sBAAAhB,MAAA,CAAAiB,UAAA,8BAAyD;IACrEvB,EAAA,CAAAwB,qBAAA,UAAAlB,MAAA,CAAAiB,UAAA,iCAAwD;IAKtBvB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAyB,gBAAA,YAAAnB,MAAA,CAAAU,gBAAA,CAA8B;;;;;IAM5EhB,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAC7BX,EAAA,CAAAC,cAAA,aAAmC;IAC/BD,EAAA,CAAA0B,MAAA,gBAAQ;IAAA1B,EAAA,CAAAW,SAAA,qBAA8C;IAC1DX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAmC;IAC/BD,EAAA,CAAA0B,MAAA,qBAAa;IAAA1B,EAAA,CAAAW,SAAA,qBAA8C;IAC/DX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAA0B,MAAA,oBAAY;IAAA1B,EAAA,CAAAW,SAAA,sBAA6C;IAEjEX,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAA8C,SACtC;IACAD,EAAA,CAAAW,SAAA,iBAE8E;IAClFX,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IAAA1B,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IAAA1B,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAA0B,MAAA,GACJ;IACJ1B,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;;IAbyCV,EAAA,CAAAqB,SAAA,GAA4B;IAE9DrB,EAFkC,CAAA2B,UAAA,gBAAAC,eAAA,CAA4B,SAAAC,WAAA,gDAEE;IAGpE7B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAF,eAAA,kBAAAA,eAAA,CAAAG,YAAA,cACJ;IAEI/B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAF,eAAA,kBAAAA,eAAA,CAAAI,YAAA,cACJ;IAEIhC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAF,eAAA,kBAAAA,eAAA,CAAAK,WAAA,cACJ;;;;;IAdJjC,EAAA,CAAAkC,UAAA,IAAAC,wDAAA,iBAA8C;;;;IAAzCnC,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA8B,qBAAA,kBAAA9B,MAAA,CAAA8B,qBAAA,CAAAC,MAAA,MAAuC;;;;;IAkB5CrC,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAW,SAAA,aAA6B;IAIjBX,EAHZ,CAAAC,cAAA,aAAgB,cACoB,cACC,eAC+B;IAAAD,EAAA,CAAA0B,MAAA,mBAAY;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IAC3EV,EAAA,CAAAC,cAAA,eAA8C;IAC1CD,EAAA,CAAA0B,MAAA,GACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,cAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,iBAAS;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IACxEV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,oBAAY;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IAC3EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IACJ1B,EADI,CAAAU,YAAA,EAAO,EACL;IAEFV,EADJ,CAAAC,cAAA,eAA6B,gBAC+B;IAAAD,EAAA,CAAA0B,MAAA,mBAAW;IAAA1B,EAAA,CAAAU,YAAA,EAAO;IAC1EV,EAAA,CAAAC,cAAA,gBAA8C;IAC1CD,EAAA,CAAA0B,MAAA,IACJ;IAIhB1B,EAJgB,CAAAU,YAAA,EAAO,EACL,EACJ,EACL,EACJ;;;;IAvBeV,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,eAAA,kBAAAA,eAAA,CAAAP,YAAA,cACJ;IAKI/B,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,eAAA,kBAAAA,eAAA,CAAAC,SAAA,cACJ;IAKIvC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,eAAA,kBAAAA,eAAA,CAAAN,YAAA,cACJ;IAKIhC,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA8B,kBAAA,OAAAQ,eAAA,kBAAAA,eAAA,CAAAL,WAAA,cACJ;;;;;IAQZjC,EADJ,CAAAC,cAAA,SAAI,aACgB;IACZD,EAAA,CAAA0B,MAAA,yEACJ;IACJ1B,EADI,CAAAU,YAAA,EAAK,EACJ;;;;;IAIDV,EADJ,CAAAC,cAAA,SAAI,aACgB;IAAAD,EAAA,CAAA0B,MAAA,gDAAyC;IAC7D1B,EAD6D,CAAAU,YAAA,EAAK,EAC7D;;;AD9ErB,OAAM,MAAO8B,4BAA4B;EASvCC,YACUC,cAA8B,EAC9BC,0BAAsD;IADtD,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,0BAA0B,GAA1BA,0BAA0B;IAV5B,KAAAC,YAAY,GAAG,IAAI9C,OAAO,EAAQ;IACnC,KAAAsC,qBAAqB,GAAQ,IAAI;IACjC,KAAAb,UAAU,GAAY,KAAK;IAC3B,KAAAsB,YAAY,GAAiB,EAAE;IAC/B,KAAA7B,gBAAgB,GAAW,EAAE;IAC7B,KAAA8B,YAAY,GAAW,CAAC;IACxB,KAAAC,OAAO,GAAY,IAAI;EAK3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,cAAc,CAACO,OAAO,CACxBC,IAAI,CAACnD,SAAS,CAAC,IAAI,CAAC6C,YAAY,CAAC,CAAC,CAClCO,SAAS,CAAEC,IAAS,IAAI;MACvB,IAAI,CAAChB,qBAAqB,GAAGgB,IAAI,EAAEC,QAAQ,EAAEC,aAAa;IAC5D,CAAC,CAAC;EACN;EAEA7C,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACc,UAAU,EAAE;MACpB,IAAI,CAACa,qBAAqB,CAACmB,OAAO,CAAEC,OAAY,IAC9CA,OAAO,EAAEC,EAAE,GAAI,IAAI,CAACZ,YAAY,CAACW,OAAO,CAACC,EAAE,CAAC,GAAG,IAAI,GAAI,EAAE,CAC1D;IACH,CAAC,MAAM;MACL,IAAI,CAACZ,YAAY,GAAG,EAAE;IACxB;IACA,IAAI,CAACtB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEMmC,uBAAuBA,CAACC,KAAU;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtCD,KAAI,CAACb,OAAO,GAAG,IAAI;MACnB,MAAMe,IAAI,GAAGH,KAAK,CAACI,KAAK,GAAGJ,KAAK,CAACK,IAAI,GAAG,CAAC;MACzC,MAAMC,QAAQ,GAAGN,KAAK,CAACK,IAAI;MAC3B,MAAME,SAAS,GAAGP,KAAK,CAACO,SAAS;MACjC,MAAMC,SAAS,GAAGR,KAAK,CAACQ,SAAS;MAEjCP,KAAI,CAACjB,0BAA0B,CAC5ByB,sBAAsB,CACrBN,IAAI,EACJG,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTP,KAAI,CAAC5C,gBAAgB,CACtB,CACAkC,IAAI,CAACnD,SAAS,CAAC6D,KAAI,CAAChB,YAAY,CAAC,CAAC,CAClCO,SAAS,CAAC;QACTkB,IAAI,EAAGC,QAAa,IAAI;UACtBV,KAAI,CAACxB,qBAAqB,GAAGkC,QAAQ,EAAElB,IAAI,IAAI,EAAE;UACjDQ,KAAI,CAACd,YAAY,GAAGwB,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAACC,KAAK;UACpDb,KAAI,CAACb,OAAO,GAAG,KAAK;QACtB,CAAC;QACD2B,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5Dd,KAAI,CAACb,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;IAAC;EACP;EAEA3B,cAAcA,CAACwD,KAAY,EAAEjB,KAAY;IACvC,IAAI,CAACD,uBAAuB,CAAC;MAAEK,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC;EACtD;EAEAa,WAAWA,CAAA;IACT,IAAI,CAACjC,YAAY,CAACyB,IAAI,EAAE;IACxB,IAAI,CAACzB,YAAY,CAACkC,QAAQ,EAAE;EAC9B;;;uBArEWtC,4BAA4B,EAAAxC,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,0BAAA;IAAA;EAAA;;;YAA5B3C,4BAA4B;MAAA4C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbjC1F,EAFR,CAAAC,cAAA,aAAoB,aACE,oBAEgB;UAuF1BD,EAtFA,CAAAkC,UAAA,IAAA0D,mDAAA,yBAAiC,IAAAC,mDAAA,0BAcD,IAAAC,mDAAA,yBAcuC,IAAAC,mDAAA,0BAkBhB,IAAAC,mDAAA,yBAiCjB,IAAAC,mDAAA,0BAOD;UAOjDjG,EAFQ,CAAAU,YAAA,EAAU,EACR,EACJ;;;UA/FgBV,EAAA,CAAAqB,SAAA,GAA+B;UAA0BrB,EAAzD,CAAA2B,UAAA,UAAAgE,GAAA,CAAAvD,qBAAA,CAA+B,YAAyB,oBAAAuD,GAAA,CAAA9C,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}