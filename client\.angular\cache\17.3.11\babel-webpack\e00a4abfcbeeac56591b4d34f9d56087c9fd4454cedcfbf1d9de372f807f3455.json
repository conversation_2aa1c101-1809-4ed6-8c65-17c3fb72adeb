{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/shared/services/content-vendor.service\";\nimport * as i3 from \"primeng/galleria\";\nimport * as i4 from \"primeng/api\";\nconst _c0 = () => [\"/store/vendor-account\"];\nconst _c1 = () => [\"/store/invoice\"];\nconst _c2 = () => [\"/store/payment-history\"];\nconst _c3 = () => [\"/store/resource-center\"];\nfunction HomeComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"img\", 9);\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"h1\", 11);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r1.url, i0.ɵɵsanitizeUrl)(\"alt\", item_r1.alt);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.content == null ? null : ctx_r1.content.i18n == null ? null : ctx_r1.content.i18n[\"label.title\"]) || \"\", \" \");\n  }\n}\nexport class HomeComponent {\n  constructor(route, CMSservice) {\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.images = [];\n  }\n  ngOnInit() {\n    this.content = this.route.snapshot.data['content'];\n    const mediaComponent = this.CMSservice.getDataByComponentName(this.content.body, \"vendor.media\");\n    this.images = mediaComponent.Images;\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContentVendorService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 28,\n      vars: 14,\n      consts: [[1, \"col-12\", \"all-overview-body\", \"m-0\", \"p-3\", \"border-round-lg\", \"surface-card\"], [3, \"value\", \"circular\", \"showItemNavigators\", \"showThumbnails\", \"autoPlay\", \"transitionInterval\"], [\"pTemplate\", \"item\"], [1, \"home-box-list\", \"relative\", \"flex\", \"justify-content-center\", \"mt-8\", \"w-full\", \"gap-5\", \"mx-auto\"], [1, \"home-box\", \"flex\", \"flex-column\", \"justify-content-between\", \"pt-4\", \"pb-4\", \"h-8rem\", \"w-12rem\", \"border-round-2xl\", \"cursor-pointer\", \"surface-0\", \"border-1\", \"border-solid\", \"border-bluegray-100\", \"hover:border-primary\", 3, \"routerLink\"], [1, \"home-icon\", \"flex\", \"align-items-center\", \"justify-content-center\", \"mx-auto\", \"w-2rem\", \"h-2rem\", \"text-primary\"], [1, \"material-symbols-rounded\", \"text-5xl\"], [1, \"m-0\", \"relative\", \"block\", \"text-center\", \"text-lg\", \"text-color\"], [1, \"overview-banner\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-center\", \"h-32rem\", \"w-full\", \"border-round-lg\", \"overflow-hidden\"], [1, \"max-w-full\", \"w-full\", \"h-full\", 3, \"src\", \"alt\"], [1, \"banner-overlay\", \"absolute\", \"flex\", \"align-items-end\", \"justify-content-center\", \"w-full\", \"h-full\", \"top-0\", \"left-0\", \"p-8\"], [1, \"m-0\", \"p-0\", \"relative\", \"text-4xl\", \"font-semibold\", \"text-white\", \"text-center\", \"max-w-1200\", \"text-shadow-l-black\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"p-galleria\", 1);\n          i0.ɵɵtemplate(2, HomeComponent_ng_template_2_Template, 5, 3, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"a\", 4)(5, \"div\", 5)(6, \"span\", 6);\n          i0.ɵɵtext(7, \"person\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"h5\", 7);\n          i0.ɵɵtext(9, \"Vendor Account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"a\", 4)(11, \"div\", 5)(12, \"span\", 6);\n          i0.ɵɵtext(13, \"description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"h5\", 7);\n          i0.ɵɵtext(15, \"Invoices\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"a\", 4)(17, \"div\", 5)(18, \"span\", 6);\n          i0.ɵɵtext(19, \"request_quote\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"h5\", 7);\n          i0.ɵɵtext(21, \"Payment History\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"a\", 4)(23, \"div\", 5)(24, \"span\", 6);\n          i0.ɵɵtext(25, \"perm_media\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"h5\", 7);\n          i0.ɵɵtext(27, \"Resource Center\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", ctx.images)(\"circular\", true)(\"showItemNavigators\", true)(\"showThumbnails\", false)(\"autoPlay\", true)(\"transitionInterval\", 2000);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(10, _c0));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c1));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(12, _c2));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c3));\n        }\n      },\n      dependencies: [i1.RouterLink, i3.Galleria, i4.PrimeTemplate],\n      styles: [\".surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  object-fit: cover;\\n}\\n.surface-card[_ngcontent-%COMP%]   .overview-banner[_ngcontent-%COMP%]:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4392156863), transparent);\\n  mix-blend-mode: multiply;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmUvaG9tZS9ob21lLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVRO0VBQ0ksaUJBQUE7QUFEWjtBQUdRO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsT0FBQTtFQUNBLE1BQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDJFQUFBO0VBQ0Esd0JBQUE7QUFEWiIsInNvdXJjZXNDb250ZW50IjpbIi5zdXJmYWNlLWNhcmQge1xyXG4gICAgLm92ZXJ2aWV3LWJhbm5lciB7XHJcbiAgICAgICAgaW1nIHtcclxuICAgICAgICAgICAgb2JqZWN0LWZpdDogY292ZXI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgICY6YmVmb3JlIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICBjb250ZW50OiBcIlwiO1xyXG4gICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgICB0b3A6IDA7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgwZGVnLCAjMDAwMDAwNzAsIHRyYW5zcGFyZW50KTtcclxuICAgICAgICAgICAgbWl4LWJsZW5kLW1vZGU6IG11bHRpcGx5O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "item_r1", "url", "ɵɵsanitizeUrl", "alt", "ɵɵtextInterpolate1", "ctx_r1", "content", "i18n", "HomeComponent", "constructor", "route", "CMSservice", "images", "ngOnInit", "snapshot", "data", "mediaComponent", "getDataByComponentName", "body", "Images", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ContentVendorService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵtemplate", "HomeComponent_ng_template_2_Template", "ɵɵpureFunction0", "_c0", "_c1", "_c2", "_c3"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\home\\home.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\home\\home.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { ContentVendorService } from 'src/app/shared/services/content-vendor.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  images: any[] = [];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentVendorService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.content = this.route.snapshot.data['content'];\r\n    const mediaComponent = this.CMSservice.getDataByComponentName(this.content.body, \"vendor.media\");\r\n    this.images = mediaComponent.Images;\r\n  }\r\n\r\n}\r\n", "<div class=\"col-12 all-overview-body m-0 p-3 border-round-lg surface-card\">\r\n\r\n    <p-galleria [value]=\"images\" [circular]=\"true\" [showItemNavigators]=\"true\" [showThumbnails]=\"false\"\r\n        [autoPlay]=\"true\" [transitionInterval]=\"2000\">\r\n        <ng-template pTemplate=\"item\" let-item>\r\n            <div\r\n                class=\"overview-banner relative flex align-items-center justify-content-center h-32rem w-full border-round-lg overflow-hidden\">\r\n                <img [src]=\"item.url\" [alt]=\"item.alt\" class=\"max-w-full w-full h-full\" />\r\n                <div\r\n                    class=\"banner-overlay absolute flex align-items-end justify-content-center w-full h-full top-0 left-0 p-8\">\r\n                    <h1\r\n                        class=\"m-0 p-0 relative text-4xl font-semibold text-white text-center max-w-1200 text-shadow-l-black\">\r\n                        {{ content?.i18n?.['label.title'] || '' }}\r\n                    </h1>\r\n                </div>\r\n            </div>\r\n        </ng-template>\r\n    </p-galleria>\r\n\r\n    <!-- <div class=\"our-brand-sec flex flex-column align-items-center mt-6\">\r\n        <h2 class=\"text-5xl font-bold\">Our Celebrated Brands</h2>\r\n        <p class=\"text-lg mb-5\">Offering unified distribution and competitive advantages. Learn More</p>\r\n        <div class=\"our-brand-list flex align-items-center gap-5\">\r\n            <div class=\"our-brand-img d-flex align-items-center justify-content-center w-20rem\">\r\n                <img src=\"assets/layout/images/brand-img-1.svg\" alt=\"\">\r\n            </div>\r\n            <div class=\"our-brand-img d-flex align-items-center justify-content-center w-20rem\">\r\n                <img src=\"assets/layout/images/brand-img-2.svg\" alt=\"\">\r\n            </div>\r\n        </div>\r\n    </div> -->\r\n\r\n    <div class=\"home-box-list relative flex justify-content-center mt-8 w-full gap-5 mx-auto\">\r\n        <a [routerLink]=\"['/store/vendor-account']\"\r\n            class=\"home-box flex flex-column justify-content-between pt-4 pb-4 h-8rem w-12rem border-round-2xl cursor-pointer surface-0 border-1 border-solid border-bluegray-100 hover:border-primary\">\r\n            <div class=\"home-icon flex align-items-center justify-content-center mx-auto w-2rem h-2rem text-primary\">\r\n                <span class=\"material-symbols-rounded text-5xl\">person</span>\r\n            </div>\r\n            <h5 class=\"m-0 relative block text-center text-lg text-color\">Vendor Account</h5>\r\n        </a>\r\n        <a [routerLink]=\"['/store/invoice']\"\r\n            class=\"home-box flex flex-column justify-content-between pt-4 pb-4 h-8rem w-12rem border-round-2xl cursor-pointer surface-0 border-1 border-solid border-bluegray-100 hover:border-primary\">\r\n            <div class=\"home-icon flex align-items-center justify-content-center mx-auto w-2rem h-2rem text-primary\">\r\n                <span class=\"material-symbols-rounded text-5xl\">description</span>\r\n            </div>\r\n            <h5 class=\"m-0 relative block text-center text-lg text-color\">Invoices</h5>\r\n        </a>\r\n        <a [routerLink]=\"['/store/payment-history']\"\r\n            class=\"home-box flex flex-column justify-content-between pt-4 pb-4 h-8rem w-12rem border-round-2xl cursor-pointer surface-0 border-1 border-solid border-bluegray-100 hover:border-primary\">\r\n            <div class=\"home-icon flex align-items-center justify-content-center mx-auto w-2rem h-2rem text-primary\">\r\n                <span class=\"material-symbols-rounded text-5xl\">request_quote</span>\r\n            </div>\r\n            <h5 class=\"m-0 relative block text-center text-lg text-color\">Payment History</h5>\r\n        </a>\r\n        <a [routerLink]=\"['/store/resource-center']\"\r\n            class=\"home-box flex flex-column justify-content-between pt-4 pb-4 h-8rem w-12rem border-round-2xl cursor-pointer surface-0 border-1 border-solid border-bluegray-100 hover:border-primary\">\r\n            <div class=\"home-icon flex align-items-center justify-content-center mx-auto w-2rem h-2rem text-primary\">\r\n                <span class=\"material-symbols-rounded text-5xl\">perm_media</span>\r\n            </div>\r\n            <h5 class=\"m-0 relative block text-center text-lg text-color\">Resource Center</h5>\r\n        </a>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;;ICKYA,EAAA,CAAAC,cAAA,aACmI;IAC/HD,EAAA,CAAAE,SAAA,aAA0E;IAGtEF,EAFJ,CAAAC,cAAA,cAC+G,aAED;IACtGD,EAAA,CAAAG,MAAA,GACJ;IAERH,EAFQ,CAAAI,YAAA,EAAK,EACH,EACJ;;;;;IARGJ,EAAA,CAAAK,SAAA,EAAgB;IAACL,EAAjB,CAAAM,UAAA,QAAAC,OAAA,CAAAC,GAAA,EAAAR,EAAA,CAAAS,aAAA,CAAgB,QAAAF,OAAA,CAAAG,GAAA,CAAiB;IAK9BV,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAW,kBAAA,OAAAC,MAAA,CAAAC,OAAA,kBAAAD,MAAA,CAAAC,OAAA,CAAAC,IAAA,kBAAAF,MAAA,CAAAC,OAAA,CAAAC,IAAA,4BACJ;;;ADJpB,OAAM,MAAOC,aAAa;EAKxBC,YACUC,KAAqB,EACrBC,UAAgC;IADhC,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IAJpB,KAAAC,MAAM,GAAU,EAAE;EAKd;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACP,OAAO,GAAG,IAAI,CAACI,KAAK,CAACI,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClD,MAAMC,cAAc,GAAG,IAAI,CAACL,UAAU,CAACM,sBAAsB,CAAC,IAAI,CAACX,OAAO,CAACY,IAAI,EAAE,cAAc,CAAC;IAChG,IAAI,CAACN,MAAM,GAAGI,cAAc,CAACG,MAAM;EACrC;;;uBAdWX,aAAa,EAAAf,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7B,EAAA,CAAA2B,iBAAA,CAAAG,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAbhB,aAAa;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPtBtC,EAFJ,CAAAC,cAAA,aAA2E,oBAGrB;UAC9CD,EAAA,CAAAwC,UAAA,IAAAC,oCAAA,yBAAuC;UAa3CzC,EAAA,CAAAI,YAAA,EAAa;UAmBDJ,EAJZ,CAAAC,cAAA,aAA0F,WAE0G,aACnF,cACrD;UAAAD,EAAA,CAAAG,MAAA,aAAM;UAC1DH,EAD0D,CAAAI,YAAA,EAAO,EAC3D;UACNJ,EAAA,CAAAC,cAAA,YAA8D;UAAAD,EAAA,CAAAG,MAAA,qBAAc;UAChFH,EADgF,CAAAI,YAAA,EAAK,EACjF;UAIIJ,EAHR,CAAAC,cAAA,YACgM,cACnF,eACrD;UAAAD,EAAA,CAAAG,MAAA,mBAAW;UAC/DH,EAD+D,CAAAI,YAAA,EAAO,EAChE;UACNJ,EAAA,CAAAC,cAAA,aAA8D;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAC1EH,EAD0E,CAAAI,YAAA,EAAK,EAC3E;UAIIJ,EAHR,CAAAC,cAAA,YACgM,cACnF,eACrD;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UACjEH,EADiE,CAAAI,YAAA,EAAO,EAClE;UACNJ,EAAA,CAAAC,cAAA,aAA8D;UAAAD,EAAA,CAAAG,MAAA,uBAAe;UACjFH,EADiF,CAAAI,YAAA,EAAK,EAClF;UAIIJ,EAHR,CAAAC,cAAA,YACgM,cACnF,eACrD;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAC9DH,EAD8D,CAAAI,YAAA,EAAO,EAC/D;UACNJ,EAAA,CAAAC,cAAA,aAA8D;UAAAD,EAAA,CAAAG,MAAA,uBAAe;UAGzFH,EAHyF,CAAAI,YAAA,EAAK,EAClF,EACF,EACJ;;;UA5DUJ,EAAA,CAAAK,SAAA,EAAgB;UACNL,EADV,CAAAM,UAAA,UAAAiC,GAAA,CAAApB,MAAA,CAAgB,kBAAkB,4BAA4B,yBAAyB,kBAC9E,4BAA4B;UA8B1CnB,EAAA,CAAAK,SAAA,GAAwC;UAAxCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA0C,eAAA,KAAAC,GAAA,EAAwC;UAOxC3C,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA0C,eAAA,KAAAE,GAAA,EAAiC;UAOjC5C,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA0C,eAAA,KAAAG,GAAA,EAAyC;UAOzC7C,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAA0C,eAAA,KAAAI,GAAA,EAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}