{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { stringify } from 'qs';\nimport { BehaviorSubject, map } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class VendorContactService {\n  constructor(http) {\n    this.http = http;\n    this.userSubject = new BehaviorSubject(null);\n    this.user = this.userSubject.asObservable();\n  }\n  getUsers(page, pageSize, sortField, sortOrder, searchParams) {\n    const obj = {\n      populate: '*',\n      pagination: {\n        page: page,\n        pageSize: pageSize\n      },\n      filters: {\n        $and: [{\n          role: {\n            id: {\n              '$eq': 3\n            }\n          }\n        }]\n      }\n    };\n    if (searchParams.user) {\n      obj.filters.$and.push({\n        $or: [{\n          email: {\n            $containsi: searchParams.user\n          }\n        }, {\n          username: {\n            $containsi: searchParams.user\n          }\n        }, {\n          firstname: {\n            $containsi: searchParams.user\n          }\n        }, {\n          lastname: {\n            $containsi: searchParams.user\n          }\n        }]\n      });\n    }\n    if (searchParams.status) {\n      obj.filters.$and.push({\n        blocked: {\n          $eq: searchParams.status\n        }\n      });\n    }\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc';\n      obj.sort = `${sortField}:${order}`;\n    }\n    const query = stringify(obj);\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/list??${query}`);\n  }\n  getUserByID(id) {\n    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${id}?populate[customers]=*&populate[vendor]=*`);\n  }\n  getCustomers(data) {\n    const params = new HttpParams().appendAll({\n      ...data\n    });\n    return this.http.get(`${CMS_APIContstant.CUSTOMERS}`, {\n      params\n    });\n  }\n  updateUser(userId, updatedData) {\n    return this.http.put(`${CMS_APIContstant.USER_DETAILS}/${userId}`, updatedData);\n  }\n  getSettingVendorRole() {\n    return this.http.get(`${CMS_APIContstant.SETTINGS}?populate=vendor_user_role`).pipe(map(response => response?.data?.[0]?.vendor_user_role.id || null));\n  }\n  static {\n    this.ɵfac = function VendorContactService_Factory(t) {\n      return new (t || VendorContactService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VendorContactService,\n      factory: VendorContactService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpParams", "stringify", "BehaviorSubject", "map", "CMS_APIContstant", "VendorContactService", "constructor", "http", "userSubject", "user", "asObservable", "getUsers", "page", "pageSize", "sortField", "sortOrder", "searchParams", "obj", "populate", "pagination", "filters", "$and", "role", "id", "push", "$or", "email", "$containsi", "username", "firstname", "lastname", "status", "blocked", "$eq", "undefined", "order", "sort", "query", "get", "USER_DETAILS", "getUserByID", "getCustomers", "data", "params", "appendAll", "CUSTOMERS", "updateUser", "userId", "updatedData", "put", "getSettingVendorRole", "SETTINGS", "pipe", "response", "vendor_user_role", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\vendor-contact\\vendor-contact.service.ts"], "sourcesContent": ["import { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { stringify } from 'qs';\r\nimport { BehaviorSubject, Observable, map } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class VendorContactService {\r\n  public userSubject = new BehaviorSubject<any>(null);\r\n  public user = this.userSubject.asObservable();\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getUsers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchParams?: any\r\n  ): Observable<any[]> {\r\n    const obj: any = {\r\n      populate: '*',\r\n      pagination: {\r\n        page: page,\r\n        pageSize: pageSize\r\n      },\r\n      filters: {\r\n        $and: [{\r\n          role: {\r\n            id: {\r\n              '$eq': 3\r\n            }\r\n          }\r\n        }]\r\n      }\r\n    };\r\n    if (searchParams.user) {\r\n      obj.filters.$and.push({\r\n        $or: [{\r\n          email: { $containsi: searchParams.user }\r\n        }, {\r\n          username: { $containsi: searchParams.user }\r\n        }, {\r\n          firstname: { $containsi: searchParams.user }\r\n        }, {\r\n          lastname: { $containsi: searchParams.user }\r\n        }]\r\n      })\r\n    }\r\n    if (searchParams.status) {\r\n      obj.filters.$and.push({\r\n        blocked: {\r\n          $eq: searchParams.status\r\n        }\r\n      });\r\n    }\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc';\r\n      obj.sort = `${sortField}:${order}`\r\n    }\r\n    const query = stringify(obj);\r\n    return this.http.get<any[]>(`${CMS_APIContstant.USER_DETAILS}/list??${query}`);\r\n  }\r\n\r\n  getUserByID(id: any) {\r\n    return this.http.get<any[]>(\r\n      `${CMS_APIContstant.USER_DETAILS}/${id}?populate[customers]=*&populate[vendor]=*`\r\n    );\r\n  }\r\n\r\n  getCustomers(data: any) {\r\n    const params = new HttpParams().appendAll({ ...data });\r\n    return this.http.get<any[]>(`${CMS_APIContstant.CUSTOMERS}`, {\r\n      params,\r\n    });\r\n  }\r\n\r\n  updateUser(userId: string, updatedData: any): Observable<any> {\r\n    return this.http.put(\r\n      `${CMS_APIContstant.USER_DETAILS}/${userId}`,\r\n      updatedData\r\n    );\r\n  }\r\n\r\n  getSettingVendorRole() {\r\n    return this.http\r\n      .get<any>(`${CMS_APIContstant.SETTINGS}?populate=vendor_user_role`)\r\n      .pipe(\r\n        map((response) => response?.data?.[0]?.vendor_user_role.id || null)\r\n      );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,SAAS,QAAQ,IAAI;AAC9B,SAASC,eAAe,EAAcC,GAAG,QAAQ,MAAM;AACvD,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAI/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHjB,KAAAC,WAAW,GAAG,IAAIN,eAAe,CAAM,IAAI,CAAC;IAC5C,KAAAO,IAAI,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;EAEL;EAExCC,QAAQA,CACNC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,YAAkB;IAElB,MAAMC,GAAG,GAAQ;MACfC,QAAQ,EAAE,GAAG;MACbC,UAAU,EAAE;QACVP,IAAI,EAAEA,IAAI;QACVC,QAAQ,EAAEA;OACX;MACDO,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;UACLC,IAAI,EAAE;YACJC,EAAE,EAAE;cACF,KAAK,EAAE;;;SAGZ;;KAEJ;IACD,IAAIP,YAAY,CAACP,IAAI,EAAE;MACrBQ,GAAG,CAACG,OAAO,CAACC,IAAI,CAACG,IAAI,CAAC;QACpBC,GAAG,EAAE,CAAC;UACJC,KAAK,EAAE;YAAEC,UAAU,EAAEX,YAAY,CAACP;UAAI;SACvC,EAAE;UACDmB,QAAQ,EAAE;YAAED,UAAU,EAAEX,YAAY,CAACP;UAAI;SAC1C,EAAE;UACDoB,SAAS,EAAE;YAAEF,UAAU,EAAEX,YAAY,CAACP;UAAI;SAC3C,EAAE;UACDqB,QAAQ,EAAE;YAAEH,UAAU,EAAEX,YAAY,CAACP;UAAI;SAC1C;OACF,CAAC;IACJ;IACA,IAAIO,YAAY,CAACe,MAAM,EAAE;MACvBd,GAAG,CAACG,OAAO,CAACC,IAAI,CAACG,IAAI,CAAC;QACpBQ,OAAO,EAAE;UACPC,GAAG,EAAEjB,YAAY,CAACe;;OAErB,CAAC;IACJ;IACA,IAAIjB,SAAS,IAAIC,SAAS,KAAKmB,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGpB,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;MAC9CE,GAAG,CAACmB,IAAI,GAAG,GAAGtB,SAAS,IAAIqB,KAAK,EAAE;IACpC;IACA,MAAME,KAAK,GAAGpC,SAAS,CAACgB,GAAG,CAAC;IAC5B,OAAO,IAAI,CAACV,IAAI,CAAC+B,GAAG,CAAQ,GAAGlC,gBAAgB,CAACmC,YAAY,UAAUF,KAAK,EAAE,CAAC;EAChF;EAEAG,WAAWA,CAACjB,EAAO;IACjB,OAAO,IAAI,CAAChB,IAAI,CAAC+B,GAAG,CAClB,GAAGlC,gBAAgB,CAACmC,YAAY,IAAIhB,EAAE,2CAA2C,CAClF;EACH;EAEAkB,YAAYA,CAACC,IAAS;IACpB,MAAMC,MAAM,GAAG,IAAI3C,UAAU,EAAE,CAAC4C,SAAS,CAAC;MAAE,GAAGF;IAAI,CAAE,CAAC;IACtD,OAAO,IAAI,CAACnC,IAAI,CAAC+B,GAAG,CAAQ,GAAGlC,gBAAgB,CAACyC,SAAS,EAAE,EAAE;MAC3DF;KACD,CAAC;EACJ;EAEAG,UAAUA,CAACC,MAAc,EAAEC,WAAgB;IACzC,OAAO,IAAI,CAACzC,IAAI,CAAC0C,GAAG,CAClB,GAAG7C,gBAAgB,CAACmC,YAAY,IAAIQ,MAAM,EAAE,EAC5CC,WAAW,CACZ;EACH;EAEAE,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3C,IAAI,CACb+B,GAAG,CAAM,GAAGlC,gBAAgB,CAAC+C,QAAQ,4BAA4B,CAAC,CAClEC,IAAI,CACHjD,GAAG,CAAEkD,QAAQ,IAAKA,QAAQ,EAAEX,IAAI,GAAG,CAAC,CAAC,EAAEY,gBAAgB,CAAC/B,EAAE,IAAI,IAAI,CAAC,CACpE;EACL;;;uBAnFWlB,oBAAoB,EAAAkD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBrD,oBAAoB;MAAAsD,OAAA,EAApBtD,oBAAoB,CAAAuD,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}