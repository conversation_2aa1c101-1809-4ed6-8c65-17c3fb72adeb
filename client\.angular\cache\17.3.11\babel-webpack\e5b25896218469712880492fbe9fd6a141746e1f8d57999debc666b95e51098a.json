{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ResourceCenterRoutingModule } from './resource-center-routing.module';\nimport { ResourceCenterComponent } from './resource-center.component';\nimport { ButtonModule } from 'primeng/button';\nimport { TabViewModule } from 'primeng/tabview';\nimport * as i0 from \"@angular/core\";\nexport class ResourceCenterModule {\n  static {\n    this.ɵfac = function ResourceCenterModule_Factory(t) {\n      return new (t || ResourceCenterModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ResourceCenterModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ResourceCenterRoutingModule, ButtonModule, TabViewModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ResourceCenterModule, {\n    declarations: [ResourceCenterComponent],\n    imports: [CommonModule, ResourceCenterRoutingModule, ButtonModule, TabViewModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ResourceCenterRoutingModule", "ResourceCenterComponent", "ButtonModule", "TabViewModule", "ResourceCenterModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\store\\resource-center\\resource-center.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ResourceCenterRoutingModule } from './resource-center-routing.module';\r\nimport { ResourceCenterComponent } from './resource-center.component';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { TabViewModule } from 'primeng/tabview';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ResourceCenterComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ResourceCenterRoutingModule,\r\n    ButtonModule,\r\n    TabViewModule\r\n  ]\r\n})\r\nexport class ResourceCenterModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;;AAc/C,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAN7BL,YAAY,EACZC,2BAA2B,EAC3BE,YAAY,EACZC,aAAa;IAAA;EAAA;;;2EAGJC,oBAAoB;IAAAC,YAAA,GAT7BJ,uBAAuB;IAAAK,OAAA,GAGvBP,YAAY,EACZC,2BAA2B,EAC3BE,YAAY,EACZC,aAAa;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}