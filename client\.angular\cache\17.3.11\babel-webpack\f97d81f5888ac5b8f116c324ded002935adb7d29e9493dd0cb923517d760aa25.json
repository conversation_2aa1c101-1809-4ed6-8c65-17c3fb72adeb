{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  let bufferSize;\n  let refCount = false;\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    ({\n      bufferSize = Infinity,\n      windowTime = Infinity,\n      refCount = false,\n      scheduler\n    } = configOrBufferSize);\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n  return share({\n    connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n}", "map": {"version": 3, "names": ["ReplaySubject", "share", "shareReplay", "configOrBufferSize", "windowTime", "scheduler", "bufferSize", "refCount", "Infinity", "connector", "resetOnError", "resetOnComplete", "resetOnRefCountZero"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/rxjs/dist/esm/internal/operators/shareReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n    let bufferSize;\n    let refCount = false;\n    if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n        ({ bufferSize = Infinity, windowTime = Infinity, refCount = false, scheduler } = configOrBufferSize);\n    }\n    else {\n        bufferSize = (configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity);\n    }\n    return share({\n        connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n        resetOnError: true,\n        resetOnComplete: false,\n        resetOnRefCountZero: refCount,\n    });\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,WAAWA,CAACC,kBAAkB,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACnE,IAAIC,UAAU;EACd,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIJ,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;IAC9D,CAAC;MAAEG,UAAU,GAAGE,QAAQ;MAAEJ,UAAU,GAAGI,QAAQ;MAAED,QAAQ,GAAG,KAAK;MAAEF;IAAU,CAAC,GAAGF,kBAAkB;EACvG,CAAC,MACI;IACDG,UAAU,GAAIH,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGK,QAAS;EAC/G;EACA,OAAOP,KAAK,CAAC;IACTQ,SAAS,EAAEA,CAAA,KAAM,IAAIT,aAAa,CAACM,UAAU,EAAEF,UAAU,EAAEC,SAAS,CAAC;IACrEK,YAAY,EAAE,IAAI;IAClBC,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAEL;EACzB,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}