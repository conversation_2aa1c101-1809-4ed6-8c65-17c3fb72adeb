{"ast": null, "code": "'use strict';\n\n/** @type {import('./type')} */\nmodule.exports = TypeError;", "map": {"version": 3, "names": ["module", "exports", "TypeError"], "sources": ["D:/Code/ASAR/Azure/CHS/CHS-SNJYA-Vendor-Portal/client/node_modules/es-errors/type.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./type')} */\nmodule.exports = TypeError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}