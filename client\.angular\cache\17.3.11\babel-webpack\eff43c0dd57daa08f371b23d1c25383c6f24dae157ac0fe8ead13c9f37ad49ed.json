{"ast": null, "code": "import { map } from 'rxjs';\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ContentVendorService {\n  constructor(http) {\n    this.http = http;\n  }\n  getContentBySlug(slug) {\n    const language = localStorage.getItem('lang') || 'en';\n    return this.http.get(`${CMS_APIContstant.CONTENT_VENDOR}?locale=${language}&filters[slug]=${slug}&populate=*&pLevel=5`).pipe(map(response => {\n      const data = response?.data || [];\n      if (Array.isArray(data) && data.length > 0) {\n        return data[0];\n      } else {\n        return null;\n      }\n    }));\n  }\n  getDataByComponentName(body, componentName) {\n    const data = body.find(item => item.__component === componentName);\n    return data || null;\n  }\n  static {\n    this.ɵfac = function ContentVendorService_Factory(t) {\n      return new (t || ContentVendorService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ContentVendorService,\n      factory: ContentVendorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "CMS_APIContstant", "ContentVendorService", "constructor", "http", "getContentBySlug", "slug", "language", "localStorage", "getItem", "get", "CONTENT_VENDOR", "pipe", "response", "data", "Array", "isArray", "length", "getDataByComponentName", "body", "componentName", "find", "item", "__component", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\shared\\services\\content-vendor.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { map } from 'rxjs';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ContentVendorService {\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getContentBySlug(slug: string) {\r\n    const language = localStorage.getItem('lang') || 'en';\r\n    return this.http\r\n      .get<{ data: any[] }>(\r\n        `${CMS_APIContstant.CONTENT_VENDOR}?locale=${language}&filters[slug]=${slug}&populate=*&pLevel=5`\r\n      )\r\n      .pipe(\r\n        map((response) => {\r\n          const data = response?.data || [];\r\n          if (Array.isArray(data) && data.length > 0) {\r\n            return data[0];\r\n          } else {\r\n            return null;\r\n          }\r\n        })\r\n      );\r\n  }\r\n\r\n  getDataByComponentName(body: any[], componentName: string) {\r\n    const data = body.find((item) => item.__component === componentName);\r\n    return data || null;\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,QAAQ,MAAM;AAC1B,SAASC,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAC/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,gBAAgBA,CAACC,IAAY;IAC3B,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;IACrD,OAAO,IAAI,CAACL,IAAI,CACbM,GAAG,CACF,GAAGT,gBAAgB,CAACU,cAAc,WAAWJ,QAAQ,kBAAkBD,IAAI,sBAAsB,CAClG,CACAM,IAAI,CACHZ,GAAG,CAAEa,QAAQ,IAAI;MACf,MAAMC,IAAI,GAAGD,QAAQ,EAAEC,IAAI,IAAI,EAAE;MACjC,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAIA,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;QAC1C,OAAOH,IAAI,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC,CAAC,CACH;EACL;EAEAI,sBAAsBA,CAACC,IAAW,EAAEC,aAAqB;IACvD,MAAMN,IAAI,GAAGK,IAAI,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,WAAW,KAAKH,aAAa,CAAC;IACpE,OAAON,IAAI,IAAI,IAAI;EACrB;;;uBAxBWZ,oBAAoB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBzB,oBAAoB;MAAA0B,OAAA,EAApB1B,oBAAoB,CAAA2B,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}