{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ProductComponent } from './product.component';\nimport { ProductDetailsComponent } from './product-details/product-details.component';\nimport { GeneralComponent } from './product-details/general/general.component';\nimport { MediaComponent } from './product-details/media/media.component';\nimport { SimilarProductsComponent } from './product-details/similar-products/similar-products.component';\nimport { BackendComponent } from './product-details/backend/backend.component';\nimport { PricingComponent } from './product-details/pricing/pricing.component';\nimport { CategoryComponent } from './product-details/category/category.component';\nimport { PlantComponent } from './product-details/plant/plant.component';\nimport { SalesDeliveryComponent } from './product-details/sales-delivery/sales-delivery.component';\nimport { SalesTaxComponent } from './product-details/sales-tax/sales-tax.component';\nimport { BasicTextComponent } from './product-details/basic-text/basic-text.component';\nimport { UnitsOfMeasureComponent } from './product-details/units-of-measure/units-of-measure.component';\nimport { DescriptionComponent } from './product-details/description/description.component';\nimport { WebAttributesComponent } from './product-details/web-attributes/web-attributes.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProductComponent\n}, {\n  path: ':id',\n  component: ProductDetailsComponent,\n  children: [{\n    path: 'general',\n    component: GeneralComponent\n  }, {\n    path: 'backend',\n    component: BackendComponent\n  }, {\n    path: 'description',\n    component: DescriptionComponent\n  }, {\n    path: 'pricing',\n    component: PricingComponent\n  }, {\n    path: 'category',\n    component: CategoryComponent\n  }, {\n    path: 'media',\n    component: MediaComponent\n  },\n  // { path: 'classification', component: ClassficationComponent },\n  {\n    path: 'plant',\n    component: PlantComponent\n  }, {\n    path: 'similar',\n    component: SimilarProductsComponent\n  }, {\n    path: 'sales-delivery',\n    component: SalesDeliveryComponent\n  }, {\n    path: 'sales-tax',\n    component: SalesTaxComponent\n  }, {\n    path: 'basic-text',\n    component: BasicTextComponent\n  }, {\n    path: 'units-of-measure',\n    component: UnitsOfMeasureComponent\n  }, {\n    path: 'web-attributes',\n    component: WebAttributesComponent\n  }, {\n    path: '',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }, {\n    path: '**',\n    redirectTo: 'general',\n    pathMatch: 'full'\n  }]\n}];\nexport class ProductRoutingModule {\n  static {\n    this.ɵfac = function ProductRoutingModule_Factory(t) {\n      return new (t || ProductRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProductRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProductRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ProductComponent", "ProductDetailsComponent", "GeneralComponent", "MediaComponent", "SimilarProductsComponent", "BackendComponent", "PricingComponent", "CategoryComponent", "PlantComponent", "SalesDeliveryComponent", "SalesTaxComponent", "BasicTextComponent", "UnitsOfMeasureComponent", "DescriptionComponent", "WebAttributesComponent", "routes", "path", "component", "children", "redirectTo", "pathMatch", "ProductRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ProductComponent } from './product.component';\r\nimport { ProductDetailsComponent } from './product-details/product-details.component';\r\nimport { GeneralComponent } from './product-details/general/general.component';\r\nimport { MediaComponent } from './product-details/media/media.component';\r\nimport { SimilarProductsComponent } from './product-details/similar-products/similar-products.component';\r\nimport { BackendComponent } from './product-details/backend/backend.component';\r\nimport { PricingComponent } from './product-details/pricing/pricing.component';\r\nimport { CategoryComponent } from './product-details/category/category.component';\r\nimport { ClassficationComponent } from './product-details/classfication/classfication.component';\r\nimport { PlantComponent } from './product-details/plant/plant.component';\r\nimport { SalesDeliveryComponent } from './product-details/sales-delivery/sales-delivery.component';\r\nimport { SalesTaxComponent } from './product-details/sales-tax/sales-tax.component';\r\nimport { BasicTextComponent } from './product-details/basic-text/basic-text.component';\r\nimport { UnitsOfMeasureComponent } from './product-details/units-of-measure/units-of-measure.component';\r\nimport { DescriptionComponent } from './product-details/description/description.component';\r\nimport { WebAttributesComponent } from './product-details/web-attributes/web-attributes.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: ProductComponent },\r\n  {\r\n    path: ':id',\r\n    component: ProductDetailsComponent,\r\n    children: [\r\n      { path: 'general', component: GeneralComponent },\r\n      { path: 'backend', component: BackendComponent },\r\n      { path: 'description', component: DescriptionComponent },\r\n      { path: 'pricing', component: PricingComponent },\r\n      { path: 'category', component: CategoryComponent },\r\n      { path: 'media', component: MediaComponent },\r\n      // { path: 'classification', component: ClassficationComponent },\r\n      { path: 'plant', component: PlantComponent },\r\n      { path: 'similar', component: SimilarProductsComponent },\r\n      { path: 'sales-delivery', component: SalesDeliveryComponent },\r\n      { path: 'sales-tax', component: SalesTaxComponent },\r\n      { path: 'basic-text', component: BasicTextComponent },\r\n      { path: 'units-of-measure', component: UnitsOfMeasureComponent },\r\n      { path: 'web-attributes', component: WebAttributesComponent },\r\n      { path: '', redirectTo: 'general', pathMatch: 'full' },\r\n      { path: '**', redirectTo: 'general', pathMatch: 'full' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class ProductRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,iBAAiB,QAAQ,+CAA+C;AAEjF,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,sBAAsB,QAAQ,2DAA2D;AAClG,SAASC,iBAAiB,QAAQ,iDAAiD;AACnF,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,oBAAoB,QAAQ,qDAAqD;AAC1F,SAASC,sBAAsB,QAAQ,2DAA2D;;;AAElG,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEjB;AAAgB,CAAE,EACzC;EACEgB,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEhB,uBAAuB;EAClCiB,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEf;EAAgB,CAAE,EAChD;IAAEc,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEZ;EAAgB,CAAE,EAChD;IAAEW,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEJ;EAAoB,CAAE,EACxD;IAAEG,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEX;EAAgB,CAAE,EAChD;IAAEU,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEV;EAAiB,CAAE,EAClD;IAAES,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEd;EAAc,CAAE;EAC5C;EACA;IAAEa,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAET;EAAc,CAAE,EAC5C;IAAEQ,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEb;EAAwB,CAAE,EACxD;IAAEY,IAAI,EAAE,gBAAgB;IAAEC,SAAS,EAAER;EAAsB,CAAE,EAC7D;IAAEO,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEP;EAAiB,CAAE,EACnD;IAAEM,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEN;EAAkB,CAAE,EACrD;IAAEK,IAAI,EAAE,kBAAkB;IAAEC,SAAS,EAAEL;EAAuB,CAAE,EAChE;IAAEI,IAAI,EAAE,gBAAgB;IAAEC,SAAS,EAAEH;EAAsB,CAAE,EAC7D;IAAEE,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEJ,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE;CAE3D,CACF;AAMD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBtB,YAAY,CAACuB,QAAQ,CAACP,MAAM,CAAC,EAC7BhB,YAAY;IAAA;EAAA;;;2EAEXsB,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAzB,YAAA;IAAA0B,OAAA,GAFrB1B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}