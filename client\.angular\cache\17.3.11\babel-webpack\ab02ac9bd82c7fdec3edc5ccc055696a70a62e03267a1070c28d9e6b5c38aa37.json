{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/tabmenu\";\nimport * as i7 from \"primeng/toast\";\nimport * as i8 from \"primeng/autocomplete\";\nfunction ProductDetailsComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.productDetails == null ? null : ctx_r0.productDetails.product_id);\n  }\n}\nfunction ProductDetailsComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r0.productDetails == null ? null : ctx_r0.productDetails.name, \"\");\n  }\n}\nexport class ProductDetailsComponent {\n  constructor(productService, route, router) {\n    this.productService = productService;\n    this.route = route;\n    this.router = router;\n    this.unsubscribe$ = new Subject();\n    this.items = [];\n    this.activeItem = {};\n    this.productDetails = null;\n    this.filteredProducts = [];\n    this.isExpanded = false;\n    this.expandedRows = {};\n    this.bp_id = '';\n  }\n  ngOnInit() {\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\n    this.makeMenuItems(this.bp_id);\n    this.activeItem = this.items[0];\n    this.route.paramMap.pipe(takeUntil(this.unsubscribe$)).subscribe(params => {\n      const productId = params.get('id');\n      if (productId) {\n        this.loadProductData(productId);\n      }\n    });\n  }\n  makeMenuItems(bp_id) {\n    this.items = [{\n      label: 'General',\n      icon: 'pi pi-list',\n      routerLink: `/backoffice/product/${bp_id}/general`\n    }, {\n      label: 'Backend',\n      icon: 'pi pi-align-justify',\n      routerLink: `/backoffice/product/${bp_id}/backend`\n    }, {\n      label: 'Web Attributes',\n      icon: 'pi pi-align-justify',\n      routerLink: `/backoffice/product/${bp_id}/web-attributes`\n    }, {\n      label: 'Description',\n      icon: 'pi pi-check',\n      routerLink: `/backoffice/product/${bp_id}/description`\n    }, {\n      label: 'Price',\n      icon: 'pi pi-dollar',\n      routerLink: `/backoffice/product/${bp_id}/pricing`\n    }, {\n      label: 'Category',\n      icon: 'pi pi-folder',\n      routerLink: `/backoffice/product/${bp_id}/category`,\n      command: () => this.loadProductData(bp_id)\n    }, {\n      label: 'Media',\n      icon: 'pi pi-image',\n      routerLink: `/backoffice/product/${bp_id}/media`\n    },\n    // {\n    //   label: 'Classification',\n    //   icon: 'pi pi-sort-alt',\n    //   routerLink: `/backoffice/product/${bp_id}/classification`,\n    // },\n    {\n      label: 'Plant',\n      icon: 'pi pi-list',\n      routerLink: `/backoffice/product/${bp_id}/plant`\n    }, {\n      label: 'Recommendation',\n      icon: 'pi pi-th-large',\n      routerLink: `/backoffice/product/${bp_id}/similar`\n    }, {\n      label: 'Basic Text',\n      icon: 'pi pi-pencil',\n      routerLink: `/backoffice/product/${bp_id}/basic-text`\n    }, {\n      label: 'Sales Delivery',\n      icon: 'pi pi-truck',\n      routerLink: `/backoffice/product/${bp_id}/sales-delivery`\n    }, {\n      label: 'Sales Tax',\n      icon: 'pi pi-dollar',\n      routerLink: `/backoffice/product/${bp_id}/sales-tax`\n    }, {\n      label: 'Units Of Measure',\n      icon: 'pi pi-chart-bar',\n      routerLink: `/backoffice/product/${bp_id}/units-of-measure`\n    }];\n  }\n  loadProductData(productId) {\n    this.productService.getProductByID(productId).pipe(takeUntil(this.unsubscribe$)).subscribe({\n      next: response => {\n        this.productDetails = response?.data || null;\n        this.productService.productSubject.next(this.productDetails);\n      },\n      error: error => {\n        console.error('Error fetching data:', error);\n      }\n    });\n  }\n  searchProducts(event) {\n    const query = event.query.toLowerCase();\n    this.productService.getProductByIDName(query).pipe(takeUntil(this.unsubscribe$)).subscribe(response => {\n      const products = response?.data || [];\n      if (products.length) {\n        this.filteredProducts = response.data.map(product => ({\n          documentId: product.documentId,\n          product_id: product.product_id,\n          name: product.name\n        }));\n      } else {\n        this.filteredProducts = [{\n          product_id: 'The requested data does not exist.'\n        }];\n      }\n    });\n  }\n  onProductSelect(product) {\n    const productId = product.value.documentId;\n    if (productId) {\n      const tabName = this.activeItem.routerLink.split('/').pop();\n      this.makeMenuItems(productId);\n      this.router.navigate([`/backoffice/product/${productId}/${tabName}`]);\n    } else {\n      console.error('Product ID is undefined or null');\n    }\n  }\n  goToBack() {\n    this.router.navigate(['/backoffice/product']);\n  }\n  static {\n    this.ɵfac = function ProductDetailsComponent_Factory(t) {\n      return new (t || ProductDetailsComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProductDetailsComponent,\n      selectors: [[\"app-product-details\"]],\n      decls: 14,\n      vars: 9,\n      consts: [[\"position\", \"top-right\", 3, \"life\"], [1, \"grid\"], [1, \"col-12\", \"sm:col-4\"], [1, \"mb-0\"], [4, \"ngIf\"], [\"field\", \"product_id\", \"placeholder\", \"Search Product\", 1, \"p-fluid\", 3, \"ngModelChange\", \"completeMethod\", \"onSelect\", \"ngModel\", \"suggestions\", \"dropdown\"], [1, \"col-12\", \"sm:col-4\", \"flex\", \"sm:justify-content-end\"], [\"icon\", \"pi pi-arrow-left\", \"label\", \"Back\", 1, \"p-button-primary\", \"p-back-button\", 3, \"onClick\"], [3, \"activeItemChange\", \"model\", \"activeItem\", \"scrollable\"], [1, \"grid\", \"py-4\"], [1, \"col-12\"]],\n      template: function ProductDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"p-toast\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtemplate(4, ProductDetailsComponent_span_4_Template, 2, 1, \"span\", 4)(5, ProductDetailsComponent_span_5_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"p-autoComplete\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ProductDetailsComponent_Template_p_autoComplete_ngModelChange_7_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedProduct, $event) || (ctx.selectedProduct = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"completeMethod\", function ProductDetailsComponent_Template_p_autoComplete_completeMethod_7_listener($event) {\n            return ctx.searchProducts($event);\n          })(\"onSelect\", function ProductDetailsComponent_Template_p_autoComplete_onSelect_7_listener($event) {\n            return ctx.onProductSelect($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"p-button\", 7);\n          i0.ɵɵlistener(\"onClick\", function ProductDetailsComponent_Template_p_button_onClick_9_listener() {\n            return ctx.goToBack();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"p-tabMenu\", 8);\n          i0.ɵɵtwoWayListener(\"activeItemChange\", function ProductDetailsComponent_Template_p_tabMenu_activeItemChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.activeItem, $event) || (ctx.activeItem = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"life\", 3000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.productDetails == null ? null : ctx.productDetails.product_id);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.productDetails == null ? null : ctx.productDetails.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedProduct);\n          i0.ɵɵproperty(\"suggestions\", ctx.filteredProducts)(\"dropdown\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"model\", ctx.items);\n          i0.ɵɵtwoWayProperty(\"activeItem\", ctx.activeItem);\n          i0.ɵɵproperty(\"scrollable\", true);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterOutlet, i4.NgControlStatus, i4.NgModel, i5.Button, i6.TabMenu, i7.Toast, i8.AutoComplete],\n      styles: [\".product-info-list {\\n  grid-template-columns: repeat(auto-fill, minmax(460px, 1fr));\\n}\\n  .product-info-list li span {\\n  padding: 0 6px 0 0;\\n  color: var(--text-color) !important;\\n  min-width: 240px;\\n  position: relative;\\n}\\n  .innter-tab .p-tabmenu-nav .p-tabmenuitem {\\n  margin: 0;\\n}\\n  .innter-tab .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link {\\n  padding: 9px 12px;\\n}\\n  .innter-tab .p-tabmenu-nav .p-tabmenuitem.p-highlight .p-menuitem-link {\\n  background: var(--primary-color);\\n  color: var(--surface-0);\\n}\\n@media screen and (max-width: 1440px) {\\n    .product-info-list {\\n    grid-template-columns: repeat(auto-fill, minmax(45%, 1fr));\\n  }\\n}\\n@media screen and (max-width: 1200px) {\\n    .product-info-list {\\n    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));\\n  }\\n}\\n@media screen and (max-width: 576px) {\\n    .product-info-list li {\\n    flex-direction: column;\\n  }\\n    .product-info-list li span {\\n    min-width: 100%;\\n  }\\n    .product-info-list li span::before {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "productDetails", "product_id", "ɵɵtextInterpolate1", "name", "ProductDetailsComponent", "constructor", "productService", "route", "router", "unsubscribe$", "items", "activeItem", "filteredProducts", "isExpanded", "expandedRows", "bp_id", "ngOnInit", "snapshot", "paramMap", "get", "makeMenuItems", "pipe", "subscribe", "params", "productId", "loadProductData", "label", "icon", "routerLink", "command", "getProductByID", "next", "response", "data", "productSubject", "error", "console", "searchProducts", "event", "query", "toLowerCase", "getProductByIDName", "products", "length", "map", "product", "documentId", "onProductSelect", "value", "tabName", "split", "pop", "navigate", "goToBack", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "ActivatedRoute", "Router", "selectors", "decls", "vars", "consts", "template", "ProductDetailsComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "ProductDetailsComponent_span_4_Template", "ProductDetailsComponent_span_5_Template", "ɵɵtwoWayListener", "ProductDetailsComponent_Template_p_autoComplete_ngModelChange_7_listener", "$event", "ɵɵtwoWayBindingSet", "selectedProduct", "ɵɵlistener", "ProductDetailsComponent_Template_p_autoComplete_completeMethod_7_listener", "ProductDetailsComponent_Template_p_autoComplete_onSelect_7_listener", "ProductDetailsComponent_Template_p_button_onClick_9_listener", "ProductDetailsComponent_Template_p_tabMenu_activeItemChange_10_listener", "ɵɵproperty", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\product-details.component.ts", "D:\\Code\\ASAR\\Azure\\CHS\\CHS-SNJYA-Vendor-Portal\\client\\src\\app\\backoffice\\product\\product-details\\product-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ProductService } from '../product.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\ninterface expandedRows {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-product-details',\r\n  templateUrl: './product-details.component.html',\r\n  styleUrl: './product-details.component.scss',\r\n})\r\nexport class ProductDetailsComponent implements OnInit {\r\n  private unsubscribe$ = new Subject<void>();\r\n  public items: MenuItem[] = [];\r\n  public activeItem: MenuItem = {};\r\n  public productDetails: any = null;\r\n  public filteredProducts: any[] = [];\r\n  public selectedProduct: any;\r\n  public isExpanded: boolean = false;\r\n  public expandedRows: expandedRows = {};\r\n  public bp_id: string = '';\r\n\r\n  constructor(\r\n    private productService: ProductService,\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.bp_id = this.route.snapshot.paramMap.get('id') || '';\r\n    this.makeMenuItems(this.bp_id);\r\n    this.activeItem = this.items[0];\r\n    this.route.paramMap\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((params) => {\r\n        const productId = params.get('id');\r\n        if (productId) {\r\n          this.loadProductData(productId);\r\n        }\r\n      });\r\n  }\r\n\r\n  makeMenuItems(bp_id: string) {\r\n    this.items = [\r\n      {\r\n        label: 'General',\r\n        icon: 'pi pi-list',\r\n        routerLink: `/backoffice/product/${bp_id}/general`,\r\n      },\r\n      {\r\n        label: 'Backend',\r\n        icon: 'pi pi-align-justify',\r\n        routerLink: `/backoffice/product/${bp_id}/backend`,\r\n      },\r\n      {\r\n        label: 'Web Attributes',\r\n        icon: 'pi pi-align-justify',\r\n        routerLink: `/backoffice/product/${bp_id}/web-attributes`,\r\n      },\r\n      {\r\n        label: 'Description',\r\n        icon: 'pi pi-check',\r\n        routerLink: `/backoffice/product/${bp_id}/description`,\r\n      },\r\n      {\r\n        label: 'Price',\r\n        icon: 'pi pi-dollar',\r\n        routerLink: `/backoffice/product/${bp_id}/pricing`,\r\n      },\r\n      {\r\n        label: 'Category',\r\n        icon: 'pi pi-folder',\r\n        routerLink: `/backoffice/product/${bp_id}/category`,\r\n        command: () => this.loadProductData(bp_id),\r\n      },\r\n      {\r\n        label: 'Media',\r\n        icon: 'pi pi-image',\r\n        routerLink: `/backoffice/product/${bp_id}/media`,\r\n      },\r\n      // {\r\n      //   label: 'Classification',\r\n      //   icon: 'pi pi-sort-alt',\r\n      //   routerLink: `/backoffice/product/${bp_id}/classification`,\r\n      // },\r\n      {\r\n        label: 'Plant',\r\n        icon: 'pi pi-list',\r\n        routerLink: `/backoffice/product/${bp_id}/plant`,\r\n      },\r\n      {\r\n        label: 'Recommendation',\r\n        icon: 'pi pi-th-large',\r\n        routerLink: `/backoffice/product/${bp_id}/similar`,\r\n      },\r\n      {\r\n        label: 'Basic Text',\r\n        icon: 'pi pi-pencil',\r\n        routerLink: `/backoffice/product/${bp_id}/basic-text`,\r\n      },\r\n      {\r\n        label: 'Sales Delivery',\r\n        icon: 'pi pi-truck',\r\n        routerLink: `/backoffice/product/${bp_id}/sales-delivery`,\r\n      },\r\n      {\r\n        label: 'Sales Tax',\r\n        icon: 'pi pi-dollar',\r\n        routerLink: `/backoffice/product/${bp_id}/sales-tax`,\r\n      },\r\n      {\r\n        label: 'Units Of Measure',\r\n        icon: 'pi pi-chart-bar',\r\n        routerLink: `/backoffice/product/${bp_id}/units-of-measure`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  private loadProductData(productId: string): void {\r\n    this.productService\r\n      .getProductByID(productId)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.productDetails = response?.data || null;\r\n          this.productService.productSubject.next(this.productDetails);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error fetching data:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  searchProducts(event: any): void {\r\n    const query = event.query.toLowerCase();\r\n    this.productService\r\n      .getProductByIDName(query)\r\n      .pipe(takeUntil(this.unsubscribe$))\r\n      .subscribe((response: any) => {\r\n        const products = response?.data || [];\r\n        if (products.length) {\r\n          this.filteredProducts = response.data.map((product: any) => ({\r\n            documentId: product.documentId,\r\n            product_id: product.product_id,\r\n            name: product.name,\r\n          }));\r\n        } else {\r\n          this.filteredProducts = [\r\n            {\r\n              product_id: 'The requested data does not exist.',\r\n            },\r\n          ];\r\n        }\r\n      });\r\n  }\r\n\r\n  onProductSelect(product: any): void {\r\n    const productId = product.value.documentId;\r\n    if (productId) {\r\n      const tabName = this.activeItem.routerLink.split('/').pop();\r\n      this.makeMenuItems(productId);\r\n      this.router.navigate([`/backoffice/product/${productId}/${tabName}`]);\r\n    } else {\r\n      console.error('Product ID is undefined or null');\r\n    }\r\n  }\r\n\r\n  goToBack() {\r\n    this.router.navigate(['/backoffice/product']);\r\n  }\r\n}\r\n", "<p-toast position=\"top-right\" [life]=\"3000\"></p-toast>\r\n\r\n<div class=\"grid\">\r\n  <div class=\"col-12 sm:col-4\">\r\n    <h5 class=\"mb-0\">\r\n      <span *ngIf=\"productDetails?.product_id\">{{\r\n        productDetails?.product_id\r\n      }}</span>\r\n      <span *ngIf=\"productDetails?.name\"> - {{ productDetails?.name }}</span>\r\n    </h5>\r\n  </div>\r\n\r\n  <div class=\"col-12 sm:col-4\">\r\n    <p-autoComplete\r\n      [(ngModel)]=\"selectedProduct\"\r\n      [suggestions]=\"filteredProducts\"\r\n      (completeMethod)=\"searchProducts($event)\"\r\n      (onSelect)=\"onProductSelect($event)\"\r\n      field=\"product_id\"\r\n      [dropdown]=\"false\"\r\n      class=\"p-fluid\"\r\n      placeholder=\"Search Product\"\r\n    />\r\n  </div>\r\n\r\n  <div class=\"col-12 sm:col-4 flex sm:justify-content-end\">\r\n    <p-button\r\n      icon=\"pi pi-arrow-left\"\r\n      class=\"p-button-primary p-back-button\"\r\n      label=\"Back\"\r\n      (onClick)=\"goToBack()\"\r\n    ></p-button>\r\n  </div>\r\n</div>\r\n\r\n<p-tabMenu [model]=\"items\" [(activeItem)]=\"activeItem\" [scrollable]=\"true\"></p-tabMenu>\r\n\r\n<div class=\"grid py-4\">\r\n  <div class=\"col-12\">\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAIA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;ICCnCC,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,GAEvC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFgCH,EAAA,CAAAI,SAAA,EAEvC;IAFuCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAC,UAAA,CAEvC;;;;;IACFR,EAAA,CAAAC,cAAA,WAAmC;IAACD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAnCH,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAS,kBAAA,QAAAH,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAG,IAAA,KAA4B;;;ADOtE,OAAM,MAAOC,uBAAuB;EAWlCC,YACUC,cAA8B,EAC9BC,KAAqB,EACrBC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbR,KAAAC,YAAY,GAAG,IAAIlB,OAAO,EAAQ;IACnC,KAAAmB,KAAK,GAAe,EAAE;IACtB,KAAAC,UAAU,GAAa,EAAE;IACzB,KAAAX,cAAc,GAAQ,IAAI;IAC1B,KAAAY,gBAAgB,GAAU,EAAE;IAE5B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,KAAK,GAAW,EAAE;EAMtB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,KAAK,GAAG,IAAI,CAACR,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;IACzD,IAAI,CAACC,aAAa,CAAC,IAAI,CAACL,KAAK,CAAC;IAC9B,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACH,KAAK,CAACW,QAAQ,CAChBG,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACiB,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAEC,MAAM,IAAI;MACpB,MAAMC,SAAS,GAAGD,MAAM,CAACJ,GAAG,CAAC,IAAI,CAAC;MAClC,IAAIK,SAAS,EAAE;QACb,IAAI,CAACC,eAAe,CAACD,SAAS,CAAC;MACjC;IACF,CAAC,CAAC;EACN;EAEAJ,aAAaA,CAACL,KAAa;IACzB,IAAI,CAACL,KAAK,GAAG,CACX;MACEgB,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,qBAAqB;MAC3BC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,qBAAqB;MAC3BC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,aAAa;MACnBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,cAAc;MACpBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,cAAc;MACpBC,UAAU,EAAE,uBAAuBb,KAAK,WAAW;MACnDc,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACJ,eAAe,CAACV,KAAK;KAC1C,EACD;MACEW,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,aAAa;MACnBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC;IACD;IACA;IACA;IACA;IACA;IACA;MACEW,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gBAAgB;MACtBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,cAAc;MACpBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,aAAa;MACnBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,cAAc;MACpBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,EACD;MACEW,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE,iBAAiB;MACvBC,UAAU,EAAE,uBAAuBb,KAAK;KACzC,CACF;EACH;EAEQU,eAAeA,CAACD,SAAiB;IACvC,IAAI,CAAClB,cAAc,CAChBwB,cAAc,CAACN,SAAS,CAAC,CACzBH,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACiB,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAC;MACTS,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAChC,cAAc,GAAGgC,QAAQ,EAAEC,IAAI,IAAI,IAAI;QAC5C,IAAI,CAAC3B,cAAc,CAAC4B,cAAc,CAACH,IAAI,CAAC,IAAI,CAAC/B,cAAc,CAAC;MAC9D,CAAC;MACDmC,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACN;EAEAE,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK,CAACC,WAAW,EAAE;IACvC,IAAI,CAAClC,cAAc,CAChBmC,kBAAkB,CAACF,KAAK,CAAC,CACzBlB,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAACiB,YAAY,CAAC,CAAC,CAClCa,SAAS,CAAEU,QAAa,IAAI;MAC3B,MAAMU,QAAQ,GAAGV,QAAQ,EAAEC,IAAI,IAAI,EAAE;MACrC,IAAIS,QAAQ,CAACC,MAAM,EAAE;QACnB,IAAI,CAAC/B,gBAAgB,GAAGoB,QAAQ,CAACC,IAAI,CAACW,GAAG,CAAEC,OAAY,KAAM;UAC3DC,UAAU,EAAED,OAAO,CAACC,UAAU;UAC9B7C,UAAU,EAAE4C,OAAO,CAAC5C,UAAU;UAC9BE,IAAI,EAAE0C,OAAO,CAAC1C;SACf,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACS,gBAAgB,GAAG,CACtB;UACEX,UAAU,EAAE;SACb,CACF;MACH;IACF,CAAC,CAAC;EACN;EAEA8C,eAAeA,CAACF,OAAY;IAC1B,MAAMrB,SAAS,GAAGqB,OAAO,CAACG,KAAK,CAACF,UAAU;IAC1C,IAAItB,SAAS,EAAE;MACb,MAAMyB,OAAO,GAAG,IAAI,CAACtC,UAAU,CAACiB,UAAU,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;MAC3D,IAAI,CAAC/B,aAAa,CAACI,SAAS,CAAC;MAC7B,IAAI,CAAChB,MAAM,CAAC4C,QAAQ,CAAC,CAAC,uBAAuB5B,SAAS,IAAIyB,OAAO,EAAE,CAAC,CAAC;IACvE,CAAC,MAAM;MACLb,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAC;IAClD;EACF;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAAC7C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;;;uBA9JWhD,uBAAuB,EAAAX,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/D,EAAA,CAAA6D,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAjE,EAAA,CAAA6D,iBAAA,CAAAG,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAAvBvD,uBAAuB;MAAAwD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfpCzE,EAAA,CAAA2E,SAAA,iBAAsD;UAIlD3E,EAFJ,CAAAC,cAAA,aAAkB,aACa,YACV;UAIfD,EAHA,CAAA4E,UAAA,IAAAC,uCAAA,kBAAyC,IAAAC,uCAAA,kBAGN;UAEvC9E,EADE,CAAAG,YAAA,EAAK,EACD;UAGJH,EADF,CAAAC,cAAA,aAA6B,wBAUzB;UARAD,EAAA,CAAA+E,gBAAA,2BAAAC,yEAAAC,MAAA;YAAAjF,EAAA,CAAAkF,kBAAA,CAAAR,GAAA,CAAAS,eAAA,EAAAF,MAAA,MAAAP,GAAA,CAAAS,eAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG7BjF,EADA,CAAAoF,UAAA,4BAAAC,0EAAAJ,MAAA;YAAA,OAAkBP,GAAA,CAAA9B,cAAA,CAAAqC,MAAA,CAAsB;UAAA,EAAC,sBAAAK,oEAAAL,MAAA;YAAA,OAC7BP,GAAA,CAAApB,eAAA,CAAA2B,MAAA,CAAuB;UAAA,EAAC;UAMxCjF,EAVE,CAAAG,YAAA,EASE,EACE;UAGJH,EADF,CAAAC,cAAA,aAAyD,kBAMtD;UADCD,EAAA,CAAAoF,UAAA,qBAAAG,6DAAA;YAAA,OAAWb,GAAA,CAAAd,QAAA,EAAU;UAAA,EAAC;UAG5B5D,EAFK,CAAAG,YAAA,EAAW,EACR,EACF;UAENH,EAAA,CAAAC,cAAA,oBAA2E;UAAhDD,EAAA,CAAA+E,gBAAA,8BAAAS,wEAAAP,MAAA;YAAAjF,EAAA,CAAAkF,kBAAA,CAAAR,GAAA,CAAAxD,UAAA,EAAA+D,MAAA,MAAAP,GAAA,CAAAxD,UAAA,GAAA+D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAAqBjF,EAAA,CAAAG,YAAA,EAAY;UAGrFH,EADF,CAAAC,cAAA,cAAuB,eACD;UAClBD,EAAA,CAAA2E,SAAA,qBAA+B;UAEnC3E,EADE,CAAAG,YAAA,EAAM,EACF;;;UAzCwBH,EAAA,CAAAyF,UAAA,cAAa;UAK9BzF,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAyF,UAAA,SAAAf,GAAA,CAAAnE,cAAA,kBAAAmE,GAAA,CAAAnE,cAAA,CAAAC,UAAA,CAAgC;UAGhCR,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAyF,UAAA,SAAAf,GAAA,CAAAnE,cAAA,kBAAAmE,GAAA,CAAAnE,cAAA,CAAAG,IAAA,CAA0B;UAMjCV,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAA0F,gBAAA,YAAAhB,GAAA,CAAAS,eAAA,CAA6B;UAK7BnF,EAJA,CAAAyF,UAAA,gBAAAf,GAAA,CAAAvD,gBAAA,CAAgC,mBAId;UAgBbnB,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAyF,UAAA,UAAAf,GAAA,CAAAzD,KAAA,CAAe;UAACjB,EAAA,CAAA0F,gBAAA,eAAAhB,GAAA,CAAAxD,UAAA,CAA2B;UAAClB,EAAA,CAAAyF,UAAA,oBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}